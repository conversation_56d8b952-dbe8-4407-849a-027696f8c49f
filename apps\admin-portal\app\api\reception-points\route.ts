import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Lấy danh sách các điểm nhận tin nhắn
export async function GET(request: NextRequest) {
  try {
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({
        error: 'Tenant ID not found. Please activate your license.'
      }, { status: 400 });
    }

    // Lấy các query parameters
    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const is_active = searchParams.get('is_active') === 'true' ? 
      true : 
      searchParams.get('is_active') === 'false' ? false : 
      undefined;
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    const offset = (page - 1) * limit;

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Truy vấn danh sách điểm nhận tin nhắn
    let query = supabase
      .from('tenant_message_reception_points')
      .select('*', { count: 'exact' })
      .eq('tenant_id', tenant_id);

    // Áp dụng các bộ lọc
    if (search) {
      query = 
        query.or(`name.ilike.%${search}%,code.ilike.%${search}%,description.ilike.%${search}%`);
    }
    if (is_active !== undefined) {
      query = query.eq('is_active', is_active);
    }

    // Phân trang và sắp xếp
    query = query
      .order('priority', { ascending: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Thực hiện truy vấn
    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching reception points:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({
      data,
      meta: {
        total: count || 0,
        page,
        limit,
        pageCount: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error: any) {
    console.error('Error in GET reception points:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}

// POST: Tạo điểm nhận tin nhắn mới
export async function POST(request: NextRequest) {
  try {
    // Lấy dữ liệu từ request body
    const {
      name,
      code,
      description,
      icon_url,
      priority,
      is_active
    } = await request.json();

    // Kiểm tra dữ liệu bắt buộc
    if (!name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }
    if (!code) {
      return NextResponse.json({ error: 'Code is required' }, { status: 400 });
    }

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({
        error: 'Tenant ID not found. Please activate your license.'
      }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra xem code đã tồn tại chưa
    const { data: existingPoint, error: checkError } = await supabase
      .from('tenant_message_reception_points')
      .select('id')
      .eq('tenant_id', tenant_id)
      .eq('code', code)
      .maybeSingle();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking reception point:', checkError);
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    if (existingPoint) {
      return NextResponse.json({
        error: `Reception point with code "${code}" already exists`
      }, { status: 409 });
    }

    // Tạo điểm nhận tin nhắn mới
    const newPoint = {
      tenant_id,
      name,
      code,
      description,
      icon_url,
      priority: priority !== undefined ? priority : 1,
      is_active: is_active !== undefined ? is_active : true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Lưu vào database
    const { data, error } = await supabase
      .from('tenant_message_reception_points')
      .insert(newPoint)
      .select()
      .single();

    if (error) {
      console.error('Error creating reception point:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({
      data,
      message: 'Reception point created successfully'
    }, { status: 201 });
  } catch (error: any) {
    console.error('Error in POST reception point:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}
