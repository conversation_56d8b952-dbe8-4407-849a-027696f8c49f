import { spacing } from '../primitives/spacing';
import { borders } from '../primitives/borders';
import { typography } from '../primitives/typography';

export const inputTokens = {
  // Kích thước
  sizes: {
    sm: {
      padding: `${spacing[1]} ${spacing[2]}`,
      fontSize: typography.fontSizes.sm,
      height: spacing[8]
    },
    md: {
      padding: `${spacing[2]} ${spacing[3]}`,
      fontSize: typography.fontSizes.base,
      height: spacing[10]
    },
    lg: {
      padding: `${spacing[2.5]} ${spacing[4]}`,
      fontSize: typography.fontSizes.md,
      height: spacing[12]
    }
  },
  
  // Border
  borderWidth: borders.borderWidths.thin,
  borderRadius: borders.radii.md,
  
  // Font
  fontFamily: typography.fontFamilies.inter,
  
  // Transition
  transition: 'border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out'
};
