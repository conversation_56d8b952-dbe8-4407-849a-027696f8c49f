import type { Meta, StoryObj } from '@storybook/react';
import { Radio } from './index';
import { useState } from 'react';

const meta = {
  title: 'UI/Form/Radio',
  component: Radio,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'Radio button label',
    },
    helperText: {
      control: 'text',
      description: 'Helper text displayed below the radio button',
    },
    error: {
      control: 'boolean',
      description: 'Whether the radio button is in error state',
    },
    checked: {
      control: 'boolean',
      description: 'Whether the radio button is checked',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the radio button is disabled',
    },
  },
} satisfies Meta<typeof Radio>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Option 1',
    checked: false,
  },
};

export const Checked: Story = {
  args: {
    label: 'Option 1',
    checked: true,
  },
};

export const WithHelperText: Story = {
  args: {
    label: 'Standard shipping',
    helperText: '3-5 business days',
    checked: false,
  },
};

export const WithError: Story = {
  args: {
    label: 'Select an option',
    error: true,
    helperText: 'Please select an option',
    checked: false,
  },
};

export const Disabled: Story = {
  args: {
    label: 'Disabled option',
    disabled: true,
    checked: false,
  },
};

export const DisabledChecked: Story = {
  args: {
    label: 'Disabled checked option',
    disabled: true,
    checked: true,
  },
};

export const RadioGroup: Story = {
  name: 'Radio Group',
  parameters: { controls: { disable: true } },
  render: () => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [selectedValue, setSelectedValue] = useState('option1');

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSelectedValue(e.target.value);
    };

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <Radio
          label="Option 1"
          name="radioGroup"
          value="option1"
          checked={selectedValue === 'option1'}
          onChange={handleChange}
        />
        <Radio
          label="Option 2"
          name="radioGroup"
          value="option2"
          checked={selectedValue === 'option2'}
          onChange={handleChange}
        />
        <Radio
          label="Option 3"
          name="radioGroup"
          value="option3"
          checked={selectedValue === 'option3'}
          onChange={handleChange}
        />
      </div>
    );
  },
};

export const RadioShowcase: Story = {
  name: 'All Radio Variants',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <Radio
        label="Unchecked radio button"
        name="showcase1"
        checked={false}
      />
      
      <Radio
        label="Checked radio button"
        name="showcase1"
        checked={true}
      />
      
      <Radio
        label="With helper text"
        name="showcase2"
        helperText="This is a helper text"
        checked={false}
      />
      
      <Radio
        label="With error"
        name="showcase2"
        error={true}
        helperText="This field has an error"
        checked={false}
      />
      
      <Radio
        label="Disabled unchecked"
        name="showcase3"
        disabled={true}
        checked={false}
      />
      
      <Radio
        label="Disabled checked"
        name="showcase3"
        disabled={true}
        checked={true}
      />
    </div>
  ),
};
