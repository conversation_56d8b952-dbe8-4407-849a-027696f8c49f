'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import styles from './ReceptionPointForm.module.scss';
import { Button } from '@ui';

interface ReceptionPointFormProps {
  initialData?: any;
  onSubmit: (formData: any) => Promise<void>;
  isSubmitting?: boolean;
  isEditing?: boolean;
}

export default function ReceptionPointForm({
  initialData = {},
  onSubmit,
  isSubmitting = false,
  isEditing = false
}: ReceptionPointFormProps) {
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    name: initialData.name || '',
    code: initialData.code || '',
    description: initialData.description || '',
    icon_url: initialData.icon_url || '',
    priority: initialData.priority !== undefined ? initialData.priority : 1,
    is_active: initialData.is_active !== undefined ? initialData.is_active : true,
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'priority') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) || 1 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.code.trim()) {
      newErrors.code = 'Code is required';
    } else if (!/^[a-zA-Z0-9_-]+$/.test(formData.code)) {
      newErrors.code = 'Code can only contain letters, numbers, underscores, and hyphens';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      await onSubmit(formData);
    } catch (err) {
      // Error is handled by parent component
      console.error('Form submission error:', err);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className={styles.form}>
      <div className={styles.formSection}>
        <div className={styles.formGroup}>
          <label htmlFor="name" className={styles.label}>
            Name <span className={styles.required}>*</span>
          </label>
          <input
            id="name"
            name="name"
            type="text"
            value={formData.name}
            onChange={handleChange}
            className={errors.name ? styles.inputError : styles.input}
            placeholder="Enter reception point name"
            required
            disabled={isSubmitting}
          />
          {errors.name && <div className={styles.errorText}>{errors.name}</div>}
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="code" className={styles.label}>
            Code <span className={styles.required}>*</span>
          </label>
          <input
            id="code"
            name="code"
            type="text"
            value={formData.code}
            onChange={handleChange}
            className={errors.code ? styles.inputError : styles.input}
            placeholder="Enter unique code (e.g., main_lobby)"
            required
            disabled={isSubmitting || (isEditing && initialData.code)}
          />
          {errors.code && <div className={styles.errorText}>{errors.code}</div>}
          <div className={styles.helpText}>
            Unique identifier for this reception point. Used in system references.
          </div>
        </div>
        
        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="priority" className={styles.label}>
              Priority
            </label>
            <input
              id="priority"
              name="priority"
              type="number"
              value={formData.priority}
              onChange={handleChange}
              className={styles.input}
              min="1"
              max="100"
              disabled={isSubmitting}
            />
            <div className={styles.helpText}>
              Higher priority points appear first in selection lists (1-100)
            </div>
          </div>
          
          <div className={styles.formGroup}>
            <label className={styles.label}>Status</label>
            <div className={styles.checkboxContainer}>
              <input
                type="checkbox"
                id="is_active"
                name="is_active"
                checked={formData.is_active}
                onChange={handleChange}
                disabled={isSubmitting}
              />
              <label htmlFor="is_active" className={styles.checkboxLabel}>
                Active
              </label>
            </div>
          </div>
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="description" className={styles.label}>
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            className={styles.textarea}
            placeholder="Enter description (optional)"
            rows={4}
            disabled={isSubmitting}
          />
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="icon_url" className={styles.label}>
            Icon URL
          </label>
          <input
            id="icon_url"
            name="icon_url"
            type="text"
            value={formData.icon_url}
            onChange={handleChange}
            className={styles.input}
            placeholder="Enter icon URL (optional)"
            disabled={isSubmitting}
          />
          <div className={styles.helpText}>
            URL to an icon image representing this reception point
          </div>
        </div>
      </div>
      
      <div className={styles.formActions}>
        <Button
          type="button"
          variant="secondary"
		  label="Cancel"
          onClick={() => router.back()}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
		  label="Submit"
          disabled={isSubmitting}
          loading={isSubmitting}
        >
          {isEditing ? 'Update Reception Point' : 'Create Reception Point'}
        </Button>
      </div>
    </form>
  );
}
