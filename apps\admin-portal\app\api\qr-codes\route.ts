import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { createAdminClient } from '../../../lib/supabase/admin';
import { createClient } from '@supabase/supabase-js';

// Function to get tenant_id from license_config.json
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// Tao Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// GET: Lay danh sach QR codes va thong tin gioi han
export async function GET(request: Request) {
  try {
    // Trich xuat query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const location = searchParams.get('location') || '';
    const status = searchParams.get('status') || '';
    const tenantId = searchParams.get('tenantId') || '';
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    
    // Tinh toan offset cho phan trang
    const offset = (page - 1) * limit;
    
    // Tao Supabase client
    const supabase = createSupabaseClient();
    
    // Lay tenant ID tu session hoac su dung gia tri mac dinh
    let activeTenantId = tenantId;
    if (!activeTenantId) {
      // Trong moi truong thuc te, ban se lay tenant ID tu session
      // Day su dung tenant dau tien cho muc dich demo
      const { data: firstTenant } = await supabase
        .from('tenants')
        .select('id')
        .limit(1)
        .single();
        
      if (firstTenant) {
        activeTenantId = firstTenant.id;
      } else {
        return NextResponse.json({ error: 'No active tenant found' }, { status: 404 });
      }
    }
    
    // Xay dung query lay danh sach QR codes
    let query = supabase
  .from('tenant_qr_codes')
  .select(`
    *,
    reception_point:tenant_message_reception_points!tenant_qr_codes_reception_point_id_fkey(id, name, code)
  `, { count: 'exact' })
  .eq('tenant_id', activeTenantId);
      
    // Them cac bo loc
    if (location) {
      query = query.ilike('location', `%${location}%`);
    }
    if (status) {
      query = query.eq('status', status);
    }
    
    // Them sap xep
    if (sortBy && sortOrder) {
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });
    }
    
    // Them phan trang
    query = query.range(offset, offset + limit - 1);
    
    // Thuc hien query
    const { data: qrCodes, error, count } = await query;
    
    if (error) {
      console.error('Error fetching QR codes:', error);
      throw error;
    }
    
    // Lay thong tin gioi han QR codes cua tenant
    const { data: limitInfo } = await supabase.rpc('get_tenant_qr_code_usage', {
      tenant_id_param: activeTenantId
    });
    
    // Tra ve ket qua
    return NextResponse.json({
      data: qrCodes || [],
      meta: {
        total: count || 0,
        page,
        limit,
        pageCount: Math.ceil((count || 0) / limit)
      },
      limits: limitInfo && limitInfo[0]
        ? limitInfo[0]
        : {
            current_count: 0,
            max_allowed: null,
            is_enforced: false,
            usage_percentage: 0,
            remaining: null
          }
    });
    
  } catch (error) {
    console.error('Error in GET QR codes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST: Tao QR code moi
export async function POST(request: Request) {
  try {
    const {
      name,
      description,
      location,
      room_number,
      tenant_id,
      reception_point_id,
      qr_type_id,    
      target_department,
      custom_action,
      is_active
    } = await request.json();
    
    // Kiem tra du lieu bat buoc
    if (!location) {
      return NextResponse.json(
        { error: 'Location is required for QR code' },
        { status: 400 }
      );
    }
    
    // Tao Supabase client
    const supabase = createSupabaseClient();
    
    // Lay tenant ID tu request hoac session
    let activeTenantId = tenant_id;
    if (!activeTenantId) {
      // Trong moi truong thuc te, ban se lay tenant ID tu session
      const { data: firstTenant } = await supabase
        .from('tenants')
        .select('id')
        .limit(1)
        .single();
        
      if (firstTenant) {
        activeTenantId = firstTenant.id;
      } else {
        return NextResponse.json({ error: 'No active tenant found' }, { status: 404 });
      }
    }
    
    // Kiem tra gioi han QR codes
    const { data: limitCheck } = await supabase.rpc('check_qr_code_limit', {
      tenant_id_param: activeTenantId
    });
    
    if (limitCheck === false) {
      return NextResponse.json(
        { error: 'Tenant has reached QR code limit' },
        { status: 400 }
      );
    }
    
    // Tao gia tri duy nhat cho ma QR
    const codeValue = `${Date.now().toString(36)}-${Math.random().toString(36).substr(2, 5)}`;
    
    // Tao QR code moi
    const { data, error } = await supabase
      .from('tenant_qr_codes')
      .insert({
        tenant_id: activeTenantId,
        code_value: codeValue,
        name,
        location,
        description,
        room_number,
        qr_type_id,    
        target_department,
        custom_action,
        reception_point_id,
        is_active: is_active !== undefined ? is_active : true,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        scan_count: 0
      })
      .select()
      .single();
      
    if (error) {
      // Xu ly loi vi pham gioi han tu trigger
      if (error.message.includes('Tenant has reached limit')) {
        return NextResponse.json(
          { error: 'Tenant has reached QR code limit' },
          { status: 400 }
        );
      }
      console.error('Error creating QR code:', error);
      throw error;
    }
    
    // Lay thong tin gioi han QR codes moi nhat
    const { data: limitInfo } = await supabase.rpc('get_tenant_qr_code_usage', {
      tenant_id_param: activeTenantId
    });
    
    // Tra ve QR code tao va thong tin gioi han
    return NextResponse.json({
      data,
      message: 'QR code created successfully',
      limits: limitInfo && limitInfo[0] ? limitInfo[0] : null
    }, { status: 201 });
    
  } catch (error) {
    console.error('Error in POST QR code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}