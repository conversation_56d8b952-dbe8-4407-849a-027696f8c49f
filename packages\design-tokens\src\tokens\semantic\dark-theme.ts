import { colors } from '../primitives/colors';

export const darkTheme = {
  colors: {
    // Brand colors
    primary: colors.yellow['300'],
    secondary: colors.dark['900'],
    accent: colors.blue['500'],
    
    // Text colors
    text: {
      primary: colors.white,
      secondary: colors.gray['400'],
      disabled: colors.gray['600'],
      inverse: colors.dark['975']
    },
    
    // Background colors
    background: {
      primary: colors.dark['950'],
      secondary: colors.dark['900'],
      tertiary: colors.dark['850'],
      inverse: colors.white
    },
    
    // Border colors
    border: {
      primary: colors.dark['850'],
      secondary: colors.dark['800'],
      focus: colors.blue['500']
    },
    
    // Status colors
    status: {
      success: colors.success.light,
      info: colors.info.light,
      warning: colors.warning.light,
      error: colors.error.light
    },
    
    // Specific components
    button: {
      primary: {
        background: colors.yellow['300'],
        text: colors.dark['975'],
        hover: '#EBEA60',
        active: '#DBDA54'
      },
      secondary: {
        background: colors.dark['900'],
        text: colors.white,
        hover: colors.dark['850'],
        active: colors.dark['800']
      },
      accent: {
        background: colors.blue['500'],
        text: colors.white,
        hover: '#0E42A6',
        active: '#0C3A91'
      },
      outline: {
        border: colors.dark['800'],
        text: colors.gray['50'],
        hover: colors.dark['850'],
        active: colors.dark['800']
      }
    },
    
    // Card colors
    card: {
      background: colors.dark['900'],
      border: colors.dark['850'],
      headerBackground: colors.dark['850']
    },
    
    // Form elements
    input: {
      background: colors.dark['900'],
      border: colors.dark['800'],
      text: colors.white,
      placeholder: colors.gray['600'],
      focus: {
        border: colors.blue['500']
      },
      error: {
        border: colors.error.light,
        text: colors.error.light
      }
    }
  }
};
