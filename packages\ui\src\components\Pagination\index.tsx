import React from 'react';
import styles from './Pagination.module.css';

interface PaginationProps {
  currentPage: number;
  pageCount: number;
  onPageChange: (page: number) => void;
  siblingCount?: number;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  pageCount,
  onPageChange,
  siblingCount = 1,
}) => {
  // Generate page numbers array with dots for skipped pages
  const getPageNumbers = () => {
    const totalPageNumbers = siblingCount * 2 + 3; // siblings on both sides + first + last + current
    
    // If page count is less than total pages we want to show, return all pages
    if (pageCount <= totalPageNumbers) {
      return Array.from({ length: pageCount }, (_, i) => i + 1);
    }
    
    // Calculate left and right sibling indices
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, pageCount);
    
    // Should show dots or not
    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < pageCount - 1;
    
    // First page is always shown
    const firstPageIndex = 1;
    // Last page is always shown
    const lastPageIndex = pageCount;
    
    // No dots on left, but dots on right
    if (!shouldShowLeftDots && shouldShowRightDots) {
      const leftItemCount = 3 + 2 * siblingCount;
      const leftRange = Array.from({ length: leftItemCount }, (_, i) => i + 1);
      
      return [...leftRange, 'dots', lastPageIndex];
    }
    
    // Dots on left, but no dots on right
    if (shouldShowLeftDots && !shouldShowRightDots) {
      const rightItemCount = 3 + 2 * siblingCount;
      const rightRange = Array.from(
        { length: rightItemCount },
        (_, i) => pageCount - rightItemCount + i + 1
      );
      
      return [firstPageIndex, 'dots', ...rightRange];
    }
    
    // Dots on both sides
    if (shouldShowLeftDots && shouldShowRightDots) {
      const middleRange = Array.from(
        { length: rightSiblingIndex - leftSiblingIndex + 1 },
        (_, i) => leftSiblingIndex + i
      );
      
      return [firstPageIndex, 'dots', ...middleRange, 'dots', lastPageIndex];
    }
    
    return [];
  };
  
  const pageNumbers = getPageNumbers();
  
  return (
    <div className={styles.container}>
      <button
        className={`${styles.pageButton} ${styles.navButton}`}
        onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
        disabled={currentPage === 1}
        aria-label="Previous page"
      >
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path
            d="M10 12L6 8L10 4"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>
      
      {pageNumbers.map((pageNumber, index) => (
        pageNumber === 'dots' ? (
          <span key={`dots-${index}`} className={styles.dots}>...</span>
        ) : (
          <button
            key={pageNumber}
            className={`${styles.pageButton} ${currentPage === pageNumber ? styles.active : ''}`}
            onClick={() => onPageChange(pageNumber as number)}
            aria-label={`Page ${pageNumber}`}
            aria-current={currentPage === pageNumber ? 'page' : undefined}
          >
            {pageNumber}
          </button>
        )
      ))}
      
      <button
        className={`${styles.pageButton} ${styles.navButton}`}
        onClick={() => onPageChange(Math.min(currentPage + 1, pageCount))}
        disabled={currentPage === pageCount}
        aria-label="Next page"
      >
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path
            d="M6 12L10 8L6 4"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>
    </div>
  );
};
