const fs = require('fs');
const path = require('path');

function generateCSSVariables() {
  let css = ':root {\n';
  
  try {
    // Colors - import light colors
    const lightColors = require('../src/colors/light.ts');
    if (lightColors && lightColors.lightColors) {
      Object.entries(lightColors.lightColors).forEach(([key, value]) => {
        css += `  --color-${key}: ${value};\n`;
      });
    }
  } catch (error) {
    console.log('Colors not found, using defaults');
    // Default colors
    css += `  --color-primary: #FF4D00;\n`;
    css += `  --color-secondary: #EBEBEB;\n`;
    css += `  --color-background: #FFFFFF;\n`;
    css += `  --color-text: #010103;\n`;
    css += `  --color-gray: #7D8491;\n`;
    css += `  --color-black: #010103;\n`;
    css += `  --color-white: #FFFFFF;\n`;
  }
  
  try {
    // Typography
    const typography = require('../src/typography');
    if (typography && typography.typography) {
      const typo = typography.typography;
      
      // Font family
      if (typo.fontFamily) {
        Object.entries(typo.fontFamily).forEach(([key, value]) => {
          css += `  --font-family-${key}: ${value};\n`;
        });
      }
      
      // Font size
      if (typo.fontSize) {
        Object.entries(typo.fontSize).forEach(([key, value]) => {
          css += `  --font-size-${key}: ${value};\n`;
        });
      }
      
      // Font weight
      if (typo.fontWeight) {
        Object.entries(typo.fontWeight).forEach(([key, value]) => {
          css += `  --font-weight-${key}: ${value};\n`;
        });
      }
      
      // Line height
      if (typo.lineHeight) {
        Object.entries(typo.lineHeight).forEach(([key, value]) => {
          css += `  --line-height-${key}: ${value};\n`;
        });
      }
    }
  } catch (error) {
    console.log('Typography not found, using defaults');
    // Default typography
    css += `  --font-family-primary: 'Inter, sans-serif';\n`;
    css += `  --font-size-body: 16px;\n`;
    css += `  --font-size-heading: 24px;\n`;
    css += `  --font-weight-regular: 400;\n`;
    css += `  --font-weight-bold: 700;\n`;
  }
  
  // Common CSS properties
  css += `  --border-radius-sm: 4px;\n`;
  css += `  --border-radius-md: 8px;\n`;
  css += `  --border-radius-lg: 12px;\n`;
  css += `  --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);\n`;
  css += `  --shadow-md: 0 4px 6px rgba(0,0,0,0.1);\n`;
  css += `  --shadow-lg: 0 20px 40px rgba(0,0,0,0.1);\n`;
  css += `  --spacing-xs: 4px;\n`;
  css += `  --spacing-sm: 8px;\n`;
  css += `  --spacing-md: 16px;\n`;
  css += `  --spacing-lg: 24px;\n`;
  css += `  --spacing-xl: 32px;\n`;
  
  // Additional semantic colors for web chat
  css += `  --color-primary-main: var(--color-primary);\n`;
  css += `  --color-primary-light: #FF7A33;\n`;
  css += `  --color-primary-dark: #CC3D00;\n`;
  css += `  --color-background-primary: var(--color-background);\n`;
  css += `  --color-background-secondary: #F8F9FA;\n`;
  css += `  --color-text-primary: var(--color-text);\n`;
  css += `  --color-text-secondary: var(--color-gray);\n`;
  css += `  --color-border-light: #E0E0E0;\n`;
  css += `  --color-border-dark: #CCCCCC;\n`;
  css += `  --color-success: #28A745;\n`;
  css += `  --color-error: #DC3545;\n`;
  css += `  --color-warning: #FFC107;\n`;
  css += `  --color-info: #17A2B8;\n`;
  
  // Font family fallback
  css += `  --font-family-base: var(--font-family-primary, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);\n`;
  
  css += '}\n';
  return css;
}

// Ensure dist/css directory exists
const distDir = path.join(__dirname, '../dist');
const cssDir = path.join(distDir, 'css');

if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

if (!fs.existsSync(cssDir)) {
  fs.mkdirSync(cssDir, { recursive: true });
}

// Generate and write CSS
const cssContent = generateCSSVariables();
fs.writeFileSync(path.join(cssDir, 'variables.css'), cssContent);

console.log('✅ CSS variables generated successfully!');
console.log(`📁 File location: ${path.join(cssDir, 'variables.css')}`);
