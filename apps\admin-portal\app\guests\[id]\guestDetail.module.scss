@import '../../styles/_variables';

.container {
  width: 100%;
  max-width: 1200px;
  padding: 24px;
  margin: 0 auto;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: $primary-color;
  animation: spin 1s linear infinite;
  margin-bottom: $spacing-md;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  flex-wrap: wrap;
  gap: $spacing-md;
}

.titleSection {
  display: flex;
  flex-direction: column;
}

.backLink {
  display: flex;
  align-items: center;
  color: $gray;
  text-decoration: none;
  margin-bottom: $spacing-xs;
  font-size: 14px;
  
  svg {
    margin-right: 8px;
  }
  
  &:hover {
    color: $primary-color;
  }
}

.pageTitle {
  font-size: 24px;
  font-weight: 600;
  color: $black;
  margin: 0;
}

.actions {
  display: flex;
  gap: $spacing-md;
}

.primaryButton, .secondaryButton, .warningButton {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: $border-radius-md;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  text-decoration: none;
  
  svg {
    margin-right: 8px;
  }
}

.primaryButton {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 10%);
  }
}

.secondaryButton {
  background-color: #f0f0f0;
  color: $dark-gray;
  
  &:hover {
    background-color: #e0e0e0;
  }
}

.warningButton {
  background-color: #fff0eb;
  color: #d63c00;
  
  &:hover {
    background-color: #ffe0d6;
  }
}

.guestDetailLayout {
  display: grid;
  gap: $spacing-lg;
  
  @media (min-width: 768px) {
    grid-template-columns: 2fr 1fr;
  }
}

.card {
  background-color: white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  overflow: hidden;
  margin-bottom: $spacing-lg;
}

.cardHeader {
  padding: $spacing-md;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cardTitle {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: $dark-gray;
}

.statusBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: $border-radius-sm;
  font-size: 12px;
  font-weight: 500;
  
  &.active {
    background-color: #e6f7ed;
    color: #00a651;
  }
  
  &.inactive {
    background-color: #f5f5f5;
    color: $gray;
  }
}

.cardBody {
  padding: $spacing-md;
}

.infoGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-md;
  
  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.infoLabel {
  font-size: 12px;
  color: $gray;
}

.infoValue {
  font-size: 14px;
  color: $dark-gray;
  font-weight: 500;
}

.activityList {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.activityItem {
  display: flex;
  align-items: flex-start;
  gap: $spacing-sm;
}

.activityIcon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $dark-gray;
}

.activityContent {
  display: flex;
  flex-direction: column;
}

.activityText {
  font-size: 14px;
  color: $dark-gray;
}

.activityTime {
  font-size: 12px;
  color: $gray;
}

.emptyState {
  color: $gray;
  text-align: center;
  padding: $spacing-md;
}

.quickLinks {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.quickLink {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: $border-radius-sm;
  color: $dark-gray;
  text-decoration: none;
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  text-align: left;
  font-family: inherit;
  
  svg {
    margin-right: 8px;
  }
  
  &:hover {
    background-color: #f5f5f5;
    color: $primary-color;
  }
}
.dangerButton {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: $border-radius-md;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  text-decoration: none;
  background-color: #ffe5e5;
  color: #d63c00;
  
  svg {
    margin-right: 8px;
  }
  
  &:hover {
    background-color: #ffd1d1;
  }
}
.dangerLink {
  color: #d63c00;
  
  &:hover {
    background-color: #ffe5e5;
  }
}