.container {
  padding: 16px;
  
  @media (min-width: 768px) {
    padding: 24px;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  @media (max-width: 767px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  
  @media (min-width: 768px) {
    font-size: 1.75rem;
  }
}

.actions {
  display: flex;
  gap: 12px;
  
  @media (max-width: 767px) {
    width: 100%;
    flex-direction: column;
  }
}

.actionGroup {
  display: flex;
  gap: 8px;
}

.createButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #0ea5e9;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  text-decoration: none;
  transition: background-color 0.2s;
  
  svg {
    width: 16px;
    height: 16px;
    stroke-width: 2px;
  }
  
  &:hover {
    background-color: #0284c7;
  }
}

.secondaryButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: white;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  svg {
    width: 16px;
    height: 16px;
    stroke-width: 2px;
  }
  
  &:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
  }
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
  overflow-x: auto;
  
  &::-webkit-scrollbar {
    height: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 2px;
  }
}

.tabButton {
  padding: 12px 24px;
  font-weight: 500;
  color: #6b7280;
  background: none;
  border: none;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
  
  &:hover:not(.activeTab) {
    color: #111827;
  }
}

.activeTab {
  color: #0ea5e9;
  border-bottom: 2px solid #0ea5e9;
}

.content {
  min-height: 400px;
}

.dashboard {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.statsSection {
  margin-bottom: 16px;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
}
.statsGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 16px;
  
  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.statCard {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.statIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 6px;
  margin-right: 16px;
}

.statInfo {
  display: flex;
  flex-direction: column;
}

.statValue {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.statLabel {
  font-size: 0.875rem;
  color: #6b7280;
}

.recentSection {
  margin-bottom: 24px;
}

.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.viewAllLink {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  color: #0ea5e9;
  text-decoration: none;
  
  svg {
    transition: transform 0.2s;
  }
  
  &:hover {
    text-decoration: underline;
    
    svg {
      transform: translateX(2px);
    }
  }
}

.roomsRow, .areasRow {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 16px;
  
  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @media (min-width: 1280px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.roomCardSmall, .areaCardSmall {
  height: 100%;
}

.redirectSection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.redirectButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #0ea5e9;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  
  svg {
    transition: transform 0.2s;
  }
  
  &:hover {
    background-color: #0284c7;
    
    svg {
      transform: translateX(4px);
    }
  }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  text-align: center;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #e5e7eb;
  
  p {
    color: #6b7280;
    max-width: 400px;
  }
  
  a {
    color: #0ea5e9;
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

// Bổ sung kiểu hiển thị dạng bảng
.tableView {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  th, td {
    padding: 12px 16px;
    text-align: left;
  }
  
  th {
    background-color: #f9fafb;
    font-weight: 500;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
  }
  
  td {
    border-bottom: 1px solid #f3f4f6;
  }
  
  tr:last-child td {
    border-bottom: none;
  }
  
  tbody tr {
    &:hover {
      background-color: #f9fafb;
    }
  }
}

.statusBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  
  &.available {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
  }
  
  &.occupied {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
  }
  
  &.maintenance {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }
  
  &.cleaning {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }
}

.actionLink {
  color: #0ea5e9;
  text-decoration: none;
  font-size: 0.875rem;
  margin-right: 12px;
  
  &:hover {
    text-decoration: underline;
  }
}

