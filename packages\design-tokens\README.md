# LoaLoa Design Tokens

Thư viện này chứa các design tokens sử dụng xuyên suốt dự án LoaLoa.

## <PERSON><PERSON><PERSON> trúc thư viện

- `primitives`: <PERSON><PERSON><PERSON> gi<PERSON> trị cơ bản nhất (colors, typography, spacing, borders, shadows, animations)
- `semantic`: <PERSON><PERSON><PERSON> tokens theo ngữ nghĩa sử dụng (light-theme, dark-theme, studio-theme)
- `components`: Các tokens cho các component cụ thể (button, input, card, ...)

## <PERSON><PERSON>ch sử dụng

### Import tokens

```typescript
// Import tất cả tokens
import * as tokens from '@loaloa/design-tokens';

// Import chỉ một phần cụ thể
import { colors, typography } from '@loaloa/design-tokens';

Sử dụng với CSS-in-JS
Copyimport { lightTheme, buttonTokens } from '@loaloa/design-tokens';

const buttonStyle = {
  backgroundColor: lightTheme.colors.button.primary.background,
  color: lightTheme.colors.button.primary.text,
  padding: buttonTokens.sizes.md.padding,
  borderRadius: buttonTokens.borderRadius,
  fontWeight: buttonTokens.fontWeight,
  transition: buttonTokens.transition,
};
Sử dụng với React Native
Copyimport { StyleSheet } from 'react-native';
import { lightTheme, spacing, buttonTokens } from '@loaloa/design-tokens';

const styles = StyleSheet.create({
  container: {
    padding: parseInt(spacing[4]),
    backgroundColor: lightTheme.colors.background.primary,
  },
  button: {
    backgroundColor: lightTheme.colors.button.primary.background,
    paddingVertical: parseInt(buttonTokens.sizes.md.padding.split(' ')[0]),
    paddingHorizontal: parseInt(buttonTokens.sizes.md.padding.split(' ')[1]),
    borderRadius: parseInt(buttonTokens.borderRadius),
  }
});
Chuyển đổi giữa các theme
Copyimport { lightTheme, darkTheme, studioTheme } from '@loaloa/design-tokens';

// Sử dụng trong component
function MyComponent({ theme }) {
  const currentTheme = theme === 'light' ? lightTheme : 
                       theme === 'dark' ? darkTheme : 
                       studioTheme;
  
  return (
    <div style={{ 
      backgroundColor: currentTheme.colors.background.primary, 
      color: currentTheme.colors.text.primary 
    }}>
      Content
    </div>
  );
}
Quy tắc đặt tên
Primitive tokens: Sử dụng tên trực tiếp và cụ thể (colors.blue[500], typography.fontSizes.xl)

Semantic tokens: Sử dụng tên theo mục đích sử dụng (theme.colors.primary, theme.colors.background.primary)

Component tokens: Sử dụng tên theo component và thuộc tính (buttonTokens.sizes.md, inputTokens.borderRadius)
