@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

.skeleton {
  display: inline-block;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.text {
  height: 16px;
  margin-bottom: 8px;
  width: 100%;
}

.title {
  height: 24px;
  margin-bottom: 12px;
  width: 70%;
}

.circle {
  border-radius: 50%;
}

.rectangle {
  margin-bottom: 12px;
}

.card {
  width: 100%;
  height: 120px;
  margin-bottom: 16px;
}

// User card skeleton
.userCardSkeleton {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.header {
  display: flex;
  gap: 16px;
}

.avatar {
  flex-shrink: 0;
}

.info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

// Table skeleton
.tableSkeleton {
  width: 100%;
}

.header {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  gap: 16px;
}

.body {
  display: flex;
  flex-direction: column;
}

.row {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  gap: 16px;
}
