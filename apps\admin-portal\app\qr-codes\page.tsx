'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { debounce } from 'lodash';
import styles from './qr-codes.module.scss';

import DashboardLayout from '../dashboard-layout';
import { But<PERSON>, SearchBar } from '@ui';
import QrCodeList from '../components/qr-codes/QrCodeList';
import QrCodeFilters from '../components/qr-codes/QrCodeFilters';
import StatsCard from '../components/qr-codes/StatsCard';
import DeleteConfirmModal from '../components/modals/DeleteConfirmModal';

export default function QrCodesPage() {
  const [qrCodes, setQrCodes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState(null);
  const [statsLoading, setStatsLoading] = useState(true);
  
  // Filters
  const [search, setSearch] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [locationFilter, setLocationFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  // QR code types for filter
  const [qrTypes, setQrTypes] = useState([]);
  
  // Delete modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [qrToDelete, setQrToDelete] = useState(null);
  const [deleteError, setDeleteError] = useState(null);

  // Fetch QR code types
  useEffect(() => {
    const fetchQrTypes = async () => {
      try {
        const response = await fetch('/api/qr-code-types');
        if (!response.ok) {
          throw new Error('Failed to fetch QR code types');
        }
        const data = await response.json();
        setQrTypes(data.data || []);
      } catch (err) {
        console.error('Error fetching QR types:', err);
      }
    };
    
    fetchQrTypes();
  }, []);

  // Fetch QR codes with filters
  const fetchQrCodes = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (typeFilter) params.append('type', typeFilter);
      if (locationFilter) params.append('location', locationFilter);
      if (statusFilter) params.append('status', statusFilter);
      params.append('page', page.toString());
      params.append('limit', '10');
      
      const response = await fetch(`/api/qr-codes?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch QR codes');
      }
      
      const result = await response.json();
      setQrCodes(result.data || []);
      setTotalPages(result.meta?.pageCount || 1);
    } catch (err) {
      console.error('Error fetching QR codes:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [search, typeFilter, locationFilter, statusFilter, page]);

  // Fetch stats
  useEffect(() => {
    const fetchStats = async () => {
      setStatsLoading(true);
      try {
        const response = await fetch('/api/qr-codes/stats?period=week');
        if (!response.ok) {
          throw new Error('Failed to fetch QR code stats');
        }
        
        const result = await response.json();
        setStats(result.data);
      } catch (err) {
        console.error('Error fetching stats:', err);
      } finally {
        setStatsLoading(false);
      }
    };
    
    fetchStats();
  }, []);

  useEffect(() => {
    fetchQrCodes();
  }, [fetchQrCodes]);

  // Debounce search
  const handleSearchChange = debounce((value) => {
    setSearch(value);
    setPage(1);
  }, 300);

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    switch(filterType) {
      case 'type':
        setTypeFilter(value);
        break;
      case 'location':
        setLocationFilter(value);
        break;
      case 'status':
        setStatusFilter(value);
        break;
      default:
        break;
    }
    setPage(1);
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  // Handle QR code deletion
  const handleDeleteClick = (qrCode) => {
    setQrToDelete(qrCode);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!qrToDelete) return;
    
    try {
      setDeleteError(null);
      const response = await fetch(`/api/qr-codes/${qrToDelete.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete QR code');
      }
      
      // Reload QR codes and close modal
      fetchQrCodes();
      setShowDeleteModal(false);
      setQrToDelete(null);
    } catch (err) {
      console.error('Error deleting QR code:', err);
      setDeleteError(err.message);
    }
  };

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <div>
            <h1 className={styles.title}>QR Code Management</h1>
            <p className={styles.description}>
              Create and manage QR codes for different areas and purposes
            </p>
          </div>
          <Link href="/qr-codes/create">
            <Button variant="primary" label="Create QR Code">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M8 3.33334V12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3.33334 8H12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Create QR Code
            </Button>
          </Link>
        </div>
        
        {/* Stats Cards */}
        <div className={styles.statsSection}>
          <StatsCard 
            title="Total QR Codes"
            value={stats?.overview?.total_qr_codes || 0}
            icon="qr-code"
            loading={statsLoading}
          />
          <StatsCard 
            title="Total Scans"
            value={stats?.overview?.total_scans || 0}
            icon="scan"
            loading={statsLoading}
          />
          <StatsCard 
            title="Today's Scans"
            value={stats?.daily?.[stats.daily.length - 1]?.scan_count || 0}
            icon="chart"
            trend={'+10%'}
            trendDirection="up"
            loading={statsLoading}
          />
        </div>
        
        {/* Filters */}
        <div className={styles.filtersContainer}>
          <SearchBar 
            placeholder="Search QR codes..." 
            onChange={(e) => handleSearchChange(e.target.value)}
            className={styles.searchBar}
          />
          <QrCodeFilters 
            types={qrTypes}
            onFilterChange={handleFilterChange}
          />
        </div>
        
        {/* QR Code List */}
        <div className={styles.listContainer}>
          <QrCodeList 
            qrCodes={qrCodes}
            loading={loading}
            error={error}
            onDelete={handleDeleteClick}
            onPageChange={handlePageChange}
            currentPage={page}
            totalPages={totalPages}
          />
        </div>
        
        {/* Delete Confirmation Modal */}
        {qrToDelete && (
          <DeleteConfirmModal
            isOpen={showDeleteModal}
            title="Delete QR Code"
            message={`Are you sure you want to delete "${qrToDelete.name}"? This action cannot be undone.`}
            onConfirm={handleConfirmDelete}
            onCancel={() => {
              setShowDeleteModal(false);
              setQrToDelete(null);
              setDeleteError(null);
            }}
            error={deleteError}
          />
        )}
      </div>
    </DashboardLayout>
  );
}
