import React, { useState } from 'react';
import { 
  TopNavigation, 
  Button, 
  Badge,
  BodyRegular, 
  BodySmall,
  Input
} from '@loaloa/ui';
import { 
  HomeIcon, 
  ArrowIcon, 
  SendIcon,
  AttachIcon,
  MicIcon
} from '@loaloa/ui';

// Mock data
const mockMessages = [
  {
    id: 1,
    text: 'Hello! How can I help you today?',
    sender: 'agent',
    timestamp: '10:30 AM'
  },
  {
    id: 2,
    text: 'I need information about your room availability for next weekend.',
    sender: 'user',
    timestamp: '10:31 AM'
  },
  {
    id: 3,
    text: 'Of course, Id be happy to help! For next weekend (May 10-12), we have several room types available including Deluxe King, Double Queen, and our Executive Suite. Would you like specific details about any of these options?',
    sender: 'agent',
    timestamp: '10:32 AM'
  },
  {
    id: 4,
    text: 'Yes, Im interested in the Executive Suite. What amenities are included and whats the price per night?',
    sender: 'user',
    timestamp: '10:33 AM'
  },
  {
    id: 5,
    text: 'Our Executive Suite includes a king-size bed, separate living area, premium bathroom with jacuzzi tub, complimentary breakfast, and access to our Executive Lounge. The rate for next weekend is $299 per night plus tax. Would you like me to check availability for specific dates?',
    sender: 'agent',
    timestamp: '10:34 AM'
  }
];

export interface ChatScreenProps {
  onBack?: () => void;
  variant?: 'light' | 'dark' | 'studio';
}

export const ChatScreen: React.FC<ChatScreenProps> = ({ 
  onBack,
  variant = 'light'
}) => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState(mockMessages);

  const handleSendMessage = () => {
    if (message.trim()) {
      const newMessage = {
        id: messages.length + 1,
        text: message,
        sender: 'user',
        timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
      };
      setMessages([...messages, newMessage]);
      setMessage('');
    }
  };

  // Styles
  const themeColors = {
    light: {
      background: '#F9FAFB',
      userBubble: '#104EC7',
      userText: '#FFFFFF',
      agentBubble: '#EBEBEB',
      agentText: '#010103',
      inputBackground: '#FFFFFF',
      border: '#EBEBEB'
    },
    dark: {
      background: '#161616',
      userBubble: '#104EC7',
      userText: '#FFFFFF',
      agentBubble: '#2E2E2E',
      agentText: '#EBEBEB',
      inputBackground: '#2E2E2E',
      border: '#3E3E3E'
    },
    studio: {
      background: '#16262E',
      userBubble: '#3C7A89',
      userText: '#FFFFFF',
      agentBubble: '#2E4756',
      agentText: '#EBEBEB',
      inputBackground: '#2E4756',
      border: '#3C7A89'
    }
  };

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    backgroundColor: themeColors[variant].background
  };

  const contentStyle: React.CSSProperties = {
    flex: 1,
    padding: '16px',
    overflowY: 'auto',
    display: 'flex',
    flexDirection: 'column',
    gap: '16px'
  };

  const inputContainerStyle: React.CSSProperties = {
    padding: '16px',
    borderTop: `1px solid ${themeColors[variant].border}`,
    backgroundColor: themeColors[variant].inputBackground,
    display: 'flex',
    alignItems: 'center',
    gap: '12px'
  };

  const getMessageStyle = (sender: string): React.CSSProperties => {
    const isUser = sender === 'user';
    
    return {
      display: 'flex',
      flexDirection: 'column',
      alignItems: isUser ? 'flex-end' : 'flex-start',
      maxWidth: '80%',
      alignSelf: isUser ? 'flex-end' : 'flex-start',
    };
  };

  const getBubbleStyle = (sender: string): React.CSSProperties => {
    const isUser = sender === 'user';
    
    return {
      backgroundColor: isUser ? themeColors[variant].userBubble : themeColors[variant].agentBubble,
      color: isUser ? themeColors[variant].userText : themeColors[variant].agentText,
      padding: '12px 16px',
      borderRadius: '16px',
      borderBottomLeftRadius: !isUser ? '4px' : '16px',
      borderBottomRightRadius: isUser ? '4px' : '16px',
      maxWidth: '100%',
      wordBreak: 'break-word'
    };
  };

  const timestampStyle: React.CSSProperties = {
    fontSize: '12px',
    marginTop: '4px',
    opacity: 0.7
  };

  const Logo = (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <div style={{ 
        width: '32px', 
        height: '32px', 
        backgroundColor: '#FF4D00', 
        borderRadius: '4px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontWeight: 'bold',
        fontSize: '18px'
      }}>L</div>
      <span>{'Sunset Resort'}</span>
    </div>
  );

  const RightItems = (
    <Badge variant="primary" label="Online" />
  );
  
  return (
    <div style={containerStyle}>
      <TopNavigation
        logo={Logo}
        links={[]}
        rightItems={RightItems}
        variant={variant}
      />
      
      <div style={contentStyle}>
        {messages.map((msg) => (
          <div key={msg.id} style={getMessageStyle(msg.sender)}>
            <div style={getBubbleStyle(msg.sender)}>
              <BodyRegular>{msg.text}</BodyRegular>
            </div>
            <BodySmall style={timestampStyle}>
              {msg.timestamp}
            </BodySmall>
          </div>
        ))}
      </div>

      <div style={inputContainerStyle}>
        <Button 
          variant="outline"
          size="small"
          icon={<AttachIcon size="small" />}
          label=""
        />

        <Input
          placeholder="Type a message..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          fullWidth
          endIcon={<MicIcon />}
          style={{ flex: 1 }}
        />

        <Button 
          variant="primary"
          size="small"
          icon={<SendIcon size="small" />}
          label=""
          onClick={handleSendMessage}
        />
      </div>
    </div>
  );
};

export default ChatScreen;
