// proxy-server.js
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
const fetch = require('node-fetch');
global.fetch = fetch;

const app = express();
const port = 3010;

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'apikey']
}));
app.use(express.json());
app.use(express.static(path.join(__dirname, '..')));

// Supabase config
const supabaseUrl = 'https://iwzwbrbmojvvvfstbqow.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.vZn916S2nYhXsVO6ublrZ_mV9FxKlrJbKD9mgmrkSUE';
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.qMq8C34LescZuPeuSxredqdsjxsK6YBmkEDKsvzV7mQ';

// API endpoints
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Proxy server is running' });
});

app.get('/api/users', async (req, res) => {
  try {
    const supabase = createClient(supabaseUrl, serviceRoleKey);
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .limit(10);
      
    if (error) throw error;
    
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get('/api/rooms', async (req, res) => {
  try {
    const supabase = createClient(supabaseUrl, serviceRoleKey);
    let data, error;
    
    // Try chat_rooms table first
    try {
      const response = await supabase
        .from('chat_rooms')
        .select('*')
        .limit(10);
        
      data = response.data;
      error = response.error;
    } catch (e) {
      console.log('Error with chat_rooms, trying rooms table...');
    }
    
    // If no data from chat_rooms, try rooms table
    if (!data || error) {
      const response = await supabase
        .from('rooms')
        .select('*')
        .limit(10);
        
      data = response.data;
      error = response.error;
    }
    
    if (error) throw error;
    
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error fetching rooms:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get('/api/participants', async (req, res) => {
  try {
    const supabase = createClient(supabaseUrl, serviceRoleKey);
    const { data, error } = await supabase
      .from('chat_participants')
      .select('*')
      .limit(10);
      
    if (error) throw error;
    
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error fetching participants:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get('/api/messages', async (req, res) => {
  try {
    const supabase = createClient(supabaseUrl, serviceRoleKey);
    const { data, error } = await supabase
      .from('chat_messages')
      .select('*')
      .order('sent_at', { ascending: false })
      .limit(10);
      
    if (error) throw error;
    
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Start server
app.listen(port, () => {
  console.log(`Proxy server running at http://localhost:${port}`);
  console.log(`Access the tester at: http://localhost:${port}/tester2.html or http://localhost:${port}/tester2-fixed-v3.html`);
});