'use client';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';

export default function Home() {
  const router = useRouter();
  const [isLicensed, setIsLicensed] = useState<boolean | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Kiểm tra trạng thái license khi trang được load
  useEffect(() => {
    const checkLicense = async () => {
      try {
        const response = await fetch('/api/license/validate');
        const data = await response.json();
        
        if (data.valid) {
          setIsLicensed(true);
          router.push('/dashboard');
        } else {
          setIsLicensed(false);
          setError(data.message || 'License không hợp lệ');
        }
      } catch (error) {
        console.error('Error checking license:', error);
        setIsLicensed(false);
        setError('Không thể kết n<PERSON>i đến máy chủ xác thực license');
      }
    };
    
    checkLicense();
  }, [router]);

  // Hiện loading khi đang kiểm tra
  if (isLicensed === null) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-24">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-8">LoaLoa Admin Portal</h1>
          <p className="text-lg mb-8">Đang kiểm tra trạng thái license...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-24">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-8">LoaLoa Admin Portal</h1>
        
        {error && (
          <div className="mb-6 p-4 bg-red-100 text-red-700 rounded max-w-md">
            {error}
          </div>
        )}
        
        <p className="text-lg mb-8">
          {isLicensed === false ? 
            'Bạn cần kích hoạt license để tiếp tục.' : 
            'Chuyển hướng đến dashboard...'}
        </p>
        
        <div className="flex flex-col gap-4">
          <Link href="/activate" className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Kích hoạt License
          </Link>
          
          <Link href="/dashboard" className="text-blue-500 hover:underline">
            Truy cập Dashboard (Test Mode)
          </Link>
        </div>
      </div>
    </div>
  );
}