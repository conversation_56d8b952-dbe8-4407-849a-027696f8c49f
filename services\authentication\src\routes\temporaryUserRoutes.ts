import { Router } from 'express';
import { body, param } from 'express-validator';
import * as temporaryUserController from '../controllers/temporaryUserController';
import { authenticate, authorize } from '../middlewares/authMiddleware';

const router = Router();

// Create temporary user (staff access only)
router.post(
  '/',
  authenticate,
  authorize(['admin', 'staff']),
  [
    body('preferred_language').optional().isString(),
    body('hotel_id').optional().isUUID(),
    body('room_number').optional().isString()
  ],
  temporaryUserController.createTemporaryUser
);

// Activate temporary user (public)
router.post(
  '/activate/:qr_token',
  [
    param('qr_token').isString().notEmpty(),
    body('device_id').isString().notEmpty()
  ],
  temporaryUserController.activateTemporaryUser
);

// Get temporary user by device ID (public)
router.get(
  '/device/:device_id',
  [
    param('device_id').isString().notEmpty()
  ],
  temporaryUserController.getTemporaryUserByDevice
);

// Convert temporary user to permanent (public)
router.post(
  '/convert/:temporary_user_id',
  [
    param('temporary_user_id').isUUID(),
    body('email').isEmail(),
    body('password').isLength({ min: 6 }),
    body('full_name').optional().isString()
  ],
  temporaryUserController.convertToPermamentUser
);

export default router;
