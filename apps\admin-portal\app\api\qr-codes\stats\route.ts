import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Lấy thống kê về QR code
export async function GET(request: NextRequest) {
  try {
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }
    
    // Lấy tham số truy vấn
    const searchParams = request.nextUrl.searchParams;
    const period = searchParams.get('period') || 'week'; // 'day', 'week', 'month', 'year'
    const qrCodeId = searchParams.get('qr_code_id');
    const typeId = searchParams.get('type_id');
    const areaId = searchParams.get('area_id');
    const roomId = searchParams.get('room_id');
    
    // Tạo Supabase client
    const supabase = createAdminClient(cookies());
    
    // Tính toán khoảng thời gian
    const now = new Date();
    let startDate: Date;
    
    switch(period) {
      case 'day':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        startDate = new Date(now);
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
    }
    
    // Định dạng thời gian
    const startDateStr = startDate.toISOString();
    
    // Tính toán tổng lượt quét
    const scanQuery = supabase
      .from('tenant_qr_code_scans')
      .select('id', { count: 'exact' })
      .eq('tenant_id', tenant_id)
      .gte('scan_time', startDateStr);
    
    // Áp dụng các bộ lọc nếu có
    if (qrCodeId) scanQuery.eq('qr_code_id', qrCodeId);
    
    // Thực hiện truy vấn đếm lượt quét
    const { count: totalScans, error: scanError } = await scanQuery;
    
    if (scanError) {
      console.error('Error counting QR code scans:', scanError);
      return NextResponse.json({ error: scanError.message }, { status: 500 });
    }
    
    // Lấy dữ liệu phân tích chi tiết hơn
    
    // 1. Thống kê theo loại QR code
    const typeStatsQuery = `
      SELECT 
        qt.id, 
        qt.name, 
        qt.default_action, 
        COUNT(qs.id) as scan_count
      FROM 
        tenant_qr_code_types qt
      LEFT JOIN 
        tenant_qr_codes qc ON qt.id = qc.qr_type_id
      LEFT JOIN 
        tenant_qr_code_scans qs ON qc.id = qs.qr_code_id AND qs.scan_time >= $1
      WHERE 
        qt.tenant_id = $2
      GROUP BY 
        qt.id, qt.name, qt.default_action
      ORDER BY 
        scan_count DESC
    `;
    
    const { data: typeStats, error: typeStatsError } = await supabase.rpc(
      'run_sql',
      { query: typeStatsQuery, params: [startDateStr, tenant_id] }
    );
    
    // 2. Thống kê QR code được quét nhiều nhất
    const topQrCodesQuery = `
      SELECT 
        qc.id, 
        qc.name, 
        qc.qr_type_id,
        qt.name as type_name,
        COUNT(qs.id) as scan_count
      FROM 
        tenant_qr_codes qc
      LEFT JOIN
        tenant_qr_code_types qt ON qc.qr_type_id = qt.id
      LEFT JOIN 
        tenant_qr_code_scans qs ON qc.id = qs.qr_code_id AND qs.scan_time >= $1
      WHERE 
        qc.tenant_id = $2
      GROUP BY 
        qc.id, qc.name, qc.qr_type_id, qt.name
      ORDER BY 
        scan_count DESC
      LIMIT 10
    `;
    
    const { data: topQrCodes, error: topQrCodesError } = await supabase.rpc(
      'run_sql',
      { query: topQrCodesQuery, params: [startDateStr, tenant_id] }
    );
    
    // 3. Thống kê theo khách (loại khách)
    const visitorTypeStatsQuery = `
      SELECT 
        CASE 
          WHEN guest_id IS NOT NULL THEN 'registered' 
          ELSE 'temporary' 
        END as visitor_type,
        COUNT(id) as scan_count
      FROM 
        tenant_qr_code_scans
      WHERE 
        tenant_id = $1 AND
        scan_time >= $2
      GROUP BY 
        visitor_type
    `;
    
    const { data: visitorTypeStats, error: visitorTypeStatsError } = await supabase.rpc(
      'run_sql',
      { query: visitorTypeStatsQuery, params: [tenant_id, startDateStr] }
    );
	    // 4. Thống kê theo thời gian (theo ngày)
    const dailyStatsQuery = `
      SELECT 
        DATE_TRUNC('day', scan_time) as scan_date,
        COUNT(id) as scan_count
      FROM 
        tenant_qr_code_scans
      WHERE 
        tenant_id = $1 AND
        scan_time >= $2
      GROUP BY 
        scan_date
      ORDER BY 
        scan_date
    `;
    
    const { data: dailyStats, error: dailyStatsError } = await supabase.rpc(
      'run_sql',
      { query: dailyStatsQuery, params: [tenant_id, startDateStr] }
    );
    
    // 5. Thống kê theo ngôn ngữ
    const languageStatsQuery = `
      SELECT 
        COALESCE(language, 'unknown') as language,
        COUNT(id) as scan_count
      FROM 
        tenant_qr_code_scans
      WHERE 
        tenant_id = $1 AND
        scan_time >= $2
      GROUP BY 
        language
      ORDER BY 
        scan_count DESC
    `;
    
    const { data: languageStats, error: languageStatsError } = await supabase.rpc(
      'run_sql',
      { query: languageStatsQuery, params: [tenant_id, startDateStr] }
    );
    
    // 6. Thống kê theo phòng và khu vực liên kết
    let locationStats = [];
    
    if (roomId || areaId || !qrCodeId) {
      const locationStatsQuery = `
        SELECT 
          CASE 
            WHEN qc.room_id IS NOT NULL THEN 'room'
            WHEN qc.area_id IS NOT NULL THEN 'area'
            ELSE 'other'
          END as location_type,
          COALESCE(qc.room_id, qc.area_id, '00000000-0000-0000-0000-000000000000') as location_id,
          COALESCE(r.room_number, a.name, 'N/A') as location_name,
          COUNT(qs.id) as scan_count
        FROM 
          tenant_qr_codes qc
        LEFT JOIN 
          tenant_qr_code_scans qs ON qc.id = qs.qr_code_id AND qs.scan_time >= $1
        LEFT JOIN
          tenant_rooms r ON qc.room_id = r.id
        LEFT JOIN
          tenant_areas a ON qc.area_id = a.id
        WHERE 
          qc.tenant_id = $2
          ${roomId ? "AND qc.room_id = '" + roomId + "'" : ""}
          ${areaId ? "AND qc.area_id = '" + areaId + "'" : ""}
        GROUP BY 
          location_type, location_id, location_name
        ORDER BY 
          scan_count DESC
      `;
      
      const { data, error } = await supabase.rpc(
        'run_sql',
        { query: locationStatsQuery, params: [startDateStr, tenant_id] }
      );
      
      if (!error) {
        locationStats = data;
      }
    }
    
    // Tính toán các chỉ số khác
    
    // Lấy tổng số QR code
    const { count: totalQrCodes, error: countError } = await supabase
      .from('tenant_qr_codes')
      .select('id', { count: 'exact' })
      .eq('tenant_id', tenant_id);
    
    // Lấy tổng số loại QR code
    const { count: totalTypes, error: typesCountError } = await supabase
      .from('tenant_qr_code_types')
      .select('id', { count: 'exact' })
      .eq('tenant_id', tenant_id);
    
    // Tổng hợp các thống kê
    const stats = {
      overview: {
        total_qr_codes: totalQrCodes || 0,
        total_scans: totalScans || 0,
        total_types: totalTypes || 0,
        period: period
      },
      by_type: typeStats || [],
      top_qr_codes: topQrCodes || [],
      by_visitor_type: visitorTypeStats || [],
      daily: dailyStats || [],
      by_language: languageStats || [],
      by_location: locationStats || []
    };
    
    // Trả về kết quả
    return NextResponse.json({ data: stats });
    
  } catch (error: any) {
    console.error('Error in QR code stats:', error);
    return NextResponse.json({ 
      error: 'Failed to generate QR code statistics', 
      details: error.message 
    }, { status: 500 });
  }
}
