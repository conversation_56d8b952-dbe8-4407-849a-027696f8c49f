name: Build and Test

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 20.12.2
        
    - name: Install pnpm
      uses: pnpm/action-setup@v3
      with:
        version: 8.15.4
        
    - name: Setup pnpm config
      run: pnpm config set store-dir ${{ github.workspace }}/.pnpm-store
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Build packages
      run: pnpm run build
      
    - name: Lint code
      run: pnpm run lint
      
    - name: Run tests
      run: pnpm run test
