import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Tạo Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  return createClient(supabaseUrl, supabaseKey);
};

// GET: X<PERSON> lý khi khách quét mã QR
export async function GET(
  request: Request,
  { params }: { params: { code: string } }
) {
  try {
    const code = params.code;
    const { searchParams } = new URL(request.url);
    
    // Lấy thông tin khách từ query params nếu có
    const guestId = searchParams.get('guest_id') || null;
    const guestName = searchParams.get('name') || null;
    const guestEmail = searchParams.get('email') || null;
    
    // Tạo Supabase client
    const supabase = createSupabaseClient();
    
    // Tìm QR code dựa trên code_value
    const { data: qrCode, error: qrError } = await supabase
      .from('tenant_qr_codes')
      .select(`
        id,
        tenant_id,
        location,
        description,
        room_number,
        status,
        qr_type
      `)
      .eq('code_value', code)
      .eq('status', 'active')
      .single();
    
    if (qrError || !qrCode) {
      return NextResponse.json(
        { error: 'QR code không hợp lệ hoặc không tồn tại' },
        { status: 404 }
      );
    }
    
    // Nếu QR code có liên kết với phòng, lấy thông tin phòng
    let roomInfo = null;
    if (qrCode.room_number) {
      const { data: room, error: roomError } = await supabase
        .from('tenant_rooms')
        .select('room_type, floor, status')
        .eq('tenant_id', qrCode.tenant_id)
        .eq('room_number', qrCode.room_number)
        .maybeSingle();
      
      if (!roomError && room) {
        roomInfo = room;
      }
    }
    
    // Cập nhật lượt quét và thời gian quét gần nhất
    await supabase
      .from('tenant_qr_codes')
      .update({
        scan_count: qrCode.scan_count ? qrCode.scan_count + 1 : 1,
        last_used: new Date().toISOString()
      })
      .eq('id', qrCode.id);
    
    // Lưu lịch sử quét (có thể tạo bảng qr_code_scans nếu chưa có)
    const { data: scanData, error: scanError } = await supabase
      .from('qr_code_scans')
      .insert({
        qr_code_id: qrCode.id,
        tenant_id: qrCode.tenant_id,
        guest_id: guestId,
        guest_info: {
          name: guestName,
          email: guestEmail
        }
      })
      .select('id')
      .single();
    
    if (scanError) {
      console.error('Error recording scan:', scanError);
    }
    
    // Tạo URL chuyển hướng
    let redirectUrl = `/tenant/${qrCode.tenant_id}/chat`;
    
    // Thêm thông tin phòng và vị trí nếu có
    if (qrCode.room_number) {
      redirectUrl += `?room=${qrCode.room_number}`;
    } else if (qrCode.location) {
      redirectUrl += `?location=${encodeURIComponent(qrCode.location)}`;
    }
    
    // Thêm scan_id để theo dõi
    if (scanData?.id) {
      redirectUrl += redirectUrl.includes('?') ? '&' : '?';
      redirectUrl += `scan=${scanData.id}`;
    }
    
    // Trả về dữ liệu cần thiết
    return NextResponse.json({
      qr_code: {
        id: qrCode.id,
        tenant_id: qrCode.tenant_id,
        location: qrCode.location,
        room_number: qrCode.room_number
      },
      room: roomInfo,
      scan_id: scanData?.id,
      redirect_url: redirectUrl
    });
  } catch (error) {
    console.error('Error processing QR scan:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
