//D:\loaloa\apps\admin-portal\app\api\areas\stats
import { createClient } from '../../../../lib/supabase/server';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Lấy tổng số khu vực
    const { count: totalAreas, error: totalError } = await supabase
      .from('tenant_areas')
      .select('*', { count: 'exact', head: true });
    
    if (totalError && totalError.code !== 'PGRST116') {
      return NextResponse.json({ 
        success: false, 
        error: totalError.message 
      }, { status: 500 });
    }
    
    // Lấy số khu vực theo trạng thái
    const { data: statusData, error: statusError } = await supabase
      .from('tenant_areas')
      .select('is_active');
    
    if (statusError && statusError.code !== 'PGRST116') {
      return NextResponse.json({ 
        success: false, 
        error: statusError.message 
      }, { status: 500 });
    }
    
    // Tính toán thống kê
   // const activeAreas = statusData?.filter(area => area.is_active === true).length || 0;
  //  const inactiveAreas = statusData?.filter(area => area.is_active === false).length || 0;
    
    return NextResponse.json({
      success: true,
      stats: {
        total: totalAreas || 0,
     //   active: activeAreas,
     //   inactive: inactiveAreas
      }
    });
  } catch (error: any) {
    console.error('Error fetching area stats:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal Server Error',
      stats: {
        total: 0,
  //      active: 0,
   //     inactive: 0
      }
    });
  }
}
