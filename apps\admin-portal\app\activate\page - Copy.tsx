'use client';
import { useState, FormEvent } from 'react';
import { useRouter } from 'next/navigation';

export default function ActivatePage() {
  const [licenseKey, setLicenseKey] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const response = await fetch('/api/license/activate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          licenseKey,
          customerName,
          email
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Kích hoạt license thất bại');
      }

      setSuccessMessage(data.message || 'Kích hoạt license thành công!');
      
      // Chuyển hướng sau 2 giây
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
      
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-24">
      <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-6 text-center">Kích hoạt License</h1>
        
        {error && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
            {error}
          </div>
        )}
        
        {successMessage && (
          <div className="mb-4 p-3 bg-green-100 text-green-700 rounded">
            {successMessage}
            <p className="mt-2">Đang chuyển hướng đến dashboard...</p>
          </div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="licenseKey" className="block text-sm font-medium mb-1">
              License Key
            </label>
            <input
              type="text"
              id="licenseKey"
              className="w-full p-2 border rounded"
              value={licenseKey}
              onChange={(e) => setLicenseKey(e.target.value)}
              placeholder="LLHM-XXXXX-XXXXX-XXXXX-XXXXX"
              required
            />
          </div>
          
          <div className="mb-4">
            <label htmlFor="customerName" className="block text-sm font-medium mb-1">
              Tên khách hàng
            </label>
            <input
              type="text"
              id="customerName"
              className="w-full p-2 border rounded"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              required
            />
          </div>
          
          <div className="mb-6">
            <label htmlFor="email" className="block text-sm font-medium mb-1">
              Email
            </label>
            <input
              type="email"
              id="email"
              className="w-full p-2 border rounded"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <button
            type="submit"
            className={`w-full py-2 rounded font-medium ${
              isLoading ? 'bg-gray-400' : 'bg-blue-500 hover:bg-blue-600'
            } text-white`}
            disabled={isLoading}
          >
            {isLoading ? 'Đang kích hoạt...' : 'Kích hoạt'}
          </button>
        </form>
      </div>
    </div>
  );
}