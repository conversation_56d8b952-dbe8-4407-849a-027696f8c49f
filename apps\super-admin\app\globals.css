:root {
  --color-primary: #FF4D00;
  --color-secondary: #EBEBEB;
  --color-background: #FFFFFF;
  --color-background-secondary: #EBEBEB;
  --color-background-tertiary: #F8F9FA;
  --color-background-hover: #F5F5F5;
  --color-text: #010103;
  --color-text-secondary: #7D8491;
  --color-text-disabled: #C4C4C4;
  --color-border: #D4D4D4;
  --color-border-focus: #104EC7;
  --color-sidebar-bg: #16262E;
  
  /* Status colors */
  --color-success: #2E7D32;
  --color-info: #0288D1;
  --color-warning: #F57C00;
  --color-error: #D32F2F;
  
  /* Shadow */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 3px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.10);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  min-height: 100vh;
  overflow-x: hidden;
}

body {
  color: var(--color-text);
  background-color: var(--color-background-tertiary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.5;
}

a {
  color: var(--color-primary);
  text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}

h1 {
  font-size: 1.875rem;
}

h2 {
  font-size: 1.5rem;
}

h3 {
  font-size: 1.25rem;
}

h4 {
  font-size: 1.125rem;
}

h5, h6 {
  font-size: 1rem;
}

button, input, select, textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
}

.grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 16px;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-active {
  background-color: rgba(46, 125, 50, 0.1);
  color: #2E7D32;
}

.status-inactive {
  background-color: rgba(125, 132, 145, 0.1);
  color: #7D8491;
}

.status-pending {
  background-color: rgba(245, 124, 0, 0.1);
  color: #F57C00;
}

.status-error {
  background-color: rgba(211, 47, 47, 0.1);
  color: #D32F2F;
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (max-width: 480px) {
  .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}