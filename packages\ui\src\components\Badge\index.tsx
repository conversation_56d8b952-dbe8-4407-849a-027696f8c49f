import React from 'react';

export interface BadgeProps {
  /**
   * Badge content
   */
  label: string;
  /**
   * Badge variant
   */
  variant?: 'primary' | 'secondary' | 'accent' | 'outline';
  /**
   * Size of the badge
   */
  size?: 'small' | 'medium';
  /**
   * Optional icon to display before label
   */
  icon?: React.ReactNode;
  /**
   * Make the badge rounded
   */
  rounded?: boolean;
  /**
   * Additional CSS properties
   */
  style?: React.CSSProperties;
  /**
   * Optional CSS class name
   */
  className?: string;
}

export const Badge: React.FC<BadgeProps> = ({
  label,
  variant = 'primary',
  size = 'medium',
  icon,
  rounded = false,
  style,
  className,
  ...props
}) => {
  // Colors based on variant
  const variantStyles: Record<string, React.CSSProperties> = {
    primary: {
      backgroundColor: '#FF4D00', // Aerospace Orange
      color: '#FFFFFF',
      border: 'none',
    },
    secondary: {
      backgroundColor: '#EBEBEB', // Antiflash White
      color: '#464646', // Outer Space
      border: 'none',
    },
    accent: {
      backgroundColor: '#104EC7', // Sapphire
      color: '#FFFFFF',
      border: 'none',
    },
    outline: {
      backgroundColor: 'transparent',
      color: '#FF4D00', // Aerospace Orange
      border: '1px solid #FF4D00',
    },
  };

  // Sizing
  const sizeStyles: Record<string, React.CSSProperties> = {
    small: {
      fontSize: '10px',
      padding: '2px 6px',
      fontWeight: 500,
    },
    medium: {
      fontSize: '12px',
      padding: '4px 8px',
      fontWeight: 500,
    },
  };

  // Base style
  const badgeStyle: React.CSSProperties = {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '4px',
    borderRadius: rounded ? '12px' : '4px',
    fontFamily: 'Inter, sans-serif',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
    whiteSpace: 'nowrap',
    ...sizeStyles[size],
    ...variantStyles[variant],
    ...style,
  };

  return (
    <span 
      style={badgeStyle}
      className={className}
      {...props}
    >
      {icon && <span style={{ display: 'flex', alignItems: 'center' }}>{icon}</span>}
      {label}
    </span>
  );
};

export default Badge;
