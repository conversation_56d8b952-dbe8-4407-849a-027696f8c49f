import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Public routes that don't require license validation
const publicRoutes = ['/api/qr-scan', '/qr', '/error']

export function middleware(request: NextRequest) {
  try {
    const path = request.nextUrl.pathname

    // Allow API routes to handle their own validation
    if (path.startsWith('/api/')) {
      return NextResponse.next()
    }

    // Check if current path is a public route
    const isPublicRoute = publicRoutes.some(route => 
      path === route || path.startsWith(`${route}/`)
    )

    if (isPublicRoute) {
      return NextResponse.next()
    }

    // For now, skip license validation in middleware
    // We'll handle it in individual API routes
    return NextResponse.next()
    
  } catch (error) {
    console.error('Web Chat middleware error:', error)
    return NextResponse.redirect(new URL('/error?code=system', request.url))
  }
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
