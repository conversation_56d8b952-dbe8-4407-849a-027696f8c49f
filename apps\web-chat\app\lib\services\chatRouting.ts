import { createServerSupabase } from '@/lib/supabase';

interface QRCodeInfo {
  id: string;
  code_value: string;
  tenant_id: string;
  target_type: string;
  room_number?: string;
  target_id?: string;
  reception_point_id?: string;
}

interface RoutingRule {
  id: string;
  rule_name: string;
  rule_type: string;
  rule_condition: any;
  target_reception_point_id?: string;
  target_user_id?: string;
  target_department?: string;
  priority: number;
  is_active: boolean;
}

interface RoutingContext {
  qr_code: QRCodeInfo;
  guest_language: string;
  source_type: string;
  tenant_id: string;
  current_time: Date;
}

interface RoutingResult {
  reception_point_id?: string;
  assigned_user_id?: string;
  department?: string;
  priority: string;
  routing_rule_used?: string;
}

export class ChatRoutingService {
  private supabase = createServerSupabase();

  /**
   * Main function to determine reception point for new chat session
   * Hàm chính để xác định reception point cho phiên chat mới
   */
  async routeNewChatSession(context: RoutingContext): Promise<RoutingResult> {
    console.log('🎯 Starting chat routing for:', {
      qr_code: context.qr_code.code_value,
      guest_language: context.guest_language,
      tenant_id: context.tenant_id
    });

    try {
      // Step 1: Check if QR code has direct reception point assignment
      // Bước 1: Kiểm tra xem QR code có gán trực tiếp reception point không
      if (context.qr_code.reception_point_id) {
        console.log('✅ QR code has direct reception point:', context.qr_code.reception_point_id);
        return {
          reception_point_id: context.qr_code.reception_point_id,
          priority: 'normal',
          routing_rule_used: 'direct_qr_assignment'
        };
      }

      // Step 2: Get all active routing rules for tenant
      // Bước 2: Lấy tất cả routing rules đang hoạt động cho tenant
      const routingRules = await this.getActiveRoutingRules(context.tenant_id);
      
      if (!routingRules || routingRules.length === 0) {
        console.log('⚠️ No routing rules found, using default routing');
        return await this.getDefaultRouting(context.tenant_id);
      }

      // Step 3: Apply routing rules in priority order
      // Bước 3: Áp dụng routing rules theo thứ tự ưu tiên
      for (const rule of routingRules) {
        const routingResult = await this.applyRoutingRule(rule, context);
        if (routingResult) {
          console.log('✅ Routing rule applied:', rule.rule_name);
          return {
            ...routingResult,
            routing_rule_used: rule.rule_name
          };
        }
      }

      // Step 4: Fallback to default routing
      // Bước 4: Fallback về routing mặc định
      console.log('⚠️ No routing rules matched, using default routing');
      return await this.getDefaultRouting(context.tenant_id);

    } catch (error) {
      console.error('❌ Error in chat routing:', error);
      // Return basic routing on error
      return await this.getDefaultRouting(context.tenant_id);
    }
  }

  /**
   * Get active routing rules for tenant, ordered by priority
   * Lấy các routing rules đang hoạt động cho tenant, sắp xếp theo ưu tiên
   */
  private async getActiveRoutingRules(tenant_id: string): Promise<RoutingRule[]> {
    const { data: rules, error } = await this.supabase
      .from('tenant_chat_routing_rules')
      .select('*')
      .eq('tenant_id', tenant_id)
      .eq('is_active', true)
      .order('priority', { ascending: false })
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Error fetching routing rules:', error);
      return [];
    }

    return rules || [];
  }

  /**
   * Apply a specific routing rule to the context
   * Áp dụng một routing rule cụ thể vào context
   */
  private async applyRoutingRule(rule: RoutingRule, context: RoutingContext): Promise<RoutingResult | null> {
    console.log('🔍 Checking routing rule:', rule.rule_name, rule.rule_type);

    try {
      switch (rule.rule_type) {
        case 'qr_code':
          return await this.applyQRCodeRule(rule, context);
        
        case 'language':
          return await this.applyLanguageRule(rule, context);
        
        case 'time':
          return await this.applyTimeRule(rule, context);
        
        case 'guest':
          return await this.applyGuestRule(rule, context);
        
        case 'custom':
          return await this.applyCustomRule(rule, context);
        
        default:
          console.log('⚠️ Unknown rule type:', rule.rule_type);
          return null;
      }
    } catch (error) {
      console.error('❌ Error applying routing rule:', rule.rule_name, error);
      return null;
    }
  }

  /**
   * Apply QR code based routing rule
   * Áp dụng routing rule dựa trên QR code
   */
  private async applyQRCodeRule(rule: RoutingRule, context: RoutingContext): Promise<RoutingResult | null> {
    const condition = rule.rule_condition;
    
    // Check if QR code matches condition
    if (condition.qr_code_values && Array.isArray(condition.qr_code_values)) {
      if (condition.qr_code_values.includes(context.qr_code.code_value)) {
        return this.createRoutingResult(rule, 'normal');
      }
    }

    // Check if QR target type matches
    if (condition.target_type && condition.target_type === context.qr_code.target_type) {
      return this.createRoutingResult(rule, 'normal');
    }

    // Check if room number matches
    if (condition.room_numbers && Array.isArray(condition.room_numbers) && context.qr_code.room_number) {
      if (condition.room_numbers.includes(context.qr_code.room_number)) {
        return this.createRoutingResult(rule, 'normal');
      }
    }

    return null;
  }

  /**
   * Apply language based routing rule
   * Áp dụng routing rule dựa trên ngôn ngữ
   */
  private async applyLanguageRule(rule: RoutingRule, context: RoutingContext): Promise<RoutingResult | null> {
    const condition = rule.rule_condition;
    
    if (condition.languages && Array.isArray(condition.languages)) {
      if (condition.languages.includes(context.guest_language)) {
        return this.createRoutingResult(rule, 'normal');
      }
    }

    return null;
  }

  /**
   * Apply time based routing rule
   * Áp dụng routing rule dựa trên thời gian
   */
  private async applyTimeRule(rule: RoutingRule, context: RoutingContext): Promise<RoutingResult | null> {
    const condition = rule.rule_condition;
    const currentHour = context.current_time.getHours();
    const currentDay = context.current_time.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Check time ranges
    if (condition.time_ranges && Array.isArray(condition.time_ranges)) {
      for (const timeRange of condition.time_ranges) {
        if (this.isTimeInRange(currentHour, timeRange.start_hour, timeRange.end_hour)) {
          return this.createRoutingResult(rule, 'normal');
        }
      }
    }

    // Check days of week
    if (condition.days_of_week && Array.isArray(condition.days_of_week)) {
      if (condition.days_of_week.includes(currentDay)) {
        return this.createRoutingResult(rule, 'normal');
      }
    }

    return null;
  }

  /**
   * Apply guest based routing rule
   * Áp dụng routing rule dựa trên khách hàng
   */
  private async applyGuestRule(rule: RoutingRule, context: RoutingContext): Promise<RoutingResult | null> {
    // This can be extended based on guest type, VIP status, etc.
    // Có thể mở rộng dựa trên loại khách, trạng thái VIP, v.v.
    return this.createRoutingResult(rule, 'normal');
  }

  /**
   * Apply custom routing rule
   * Áp dụng routing rule tùy chỉnh
   */
  private async applyCustomRule(rule: RoutingRule, context: RoutingContext): Promise<RoutingResult | null> {
    // Custom logic can be implemented here
    // Logic tùy chỉnh có thể được implement ở đây
    return this.createRoutingResult(rule, 'normal');
  }

  /**
   * Create routing result from rule
   * Tạo kết quả routing từ rule
   */
  private createRoutingResult(rule: RoutingRule, priority: string): RoutingResult {
    return {
      reception_point_id: rule.target_reception_point_id || undefined,
      assigned_user_id: rule.target_user_id || undefined,
      department: rule.target_department || undefined,
      priority: priority
    };
  }

  /**
   * Get default routing when no rules match
   * Lấy routing mặc định khi không có rule nào khớp
   */
  private async getDefaultRouting(tenant_id: string): Promise<RoutingResult> {
    try {
      // Get the first active reception point as default
      // Lấy reception point đầu tiên đang hoạt động làm mặc định
      const { data: defaultPoint, error } = await this.supabase
        .from('tenant_message_reception_points')
        .select('id')
        .eq('tenant_id', tenant_id)
        .eq('is_active', true)
        .order('priority', { ascending: false })
        .limit(1)
        .single();

      if (error || !defaultPoint) {
        console.log('⚠️ No default reception point found');
        return { priority: 'normal' };
      }

      console.log('✅ Using default reception point:', defaultPoint.id);
      return {
        reception_point_id: defaultPoint.id,
        priority: 'normal',
        routing_rule_used: 'default_routing'
      };

    } catch (error) {
      console.error('❌ Error getting default routing:', error);
      return { priority: 'normal' };
    }
  }

  /**
   * Check if current time is within specified range
   * Kiểm tra xem thời gian hiện tại có nằm trong khoảng chỉ định không
   */
  private isTimeInRange(currentHour: number, startHour: number, endHour: number): boolean {
    if (startHour <= endHour) {
      return currentHour >= startHour && currentHour <= endHour;
    } else {
      // Handle overnight ranges (e.g., 22:00 - 06:00)
      return currentHour >= startHour || currentHour <= endHour;
    }
  }
}

// Export singleton instance
export const chatRoutingService = new ChatRoutingService();
