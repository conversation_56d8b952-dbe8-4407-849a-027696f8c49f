/**
 * Debug Utilities for Enhanced Chat System
 * Tools to test and debug performance improvements
 */

// Force polling mode for testing
export const forcePollingMode = () => {
  console.log('🔧 Debug: Forcing polling mode...');
  
  // Disable realtime by overriding Supabase channel creation
  if (typeof window !== 'undefined') {
    const originalCreateClient = window.createClientSupabase;
    if (originalCreateClient) {
      window.createClientSupabase = () => {
        const client = originalCreateClient();
        const originalChannel = client.channel.bind(client);
        
        client.channel = (name: string) => {
          const channel = originalChannel(name);
          const originalSubscribe = channel.subscribe.bind(channel);
          
          // Force subscription to fail to trigger polling
          channel.subscribe = (callback?: (status: string) => void) => {
            console.log('🔧 Debug: Forcing subscription failure for', name);
            if (callback) {
              setTimeout(() => callback('CHANNEL_ERROR'), 100);
            }
            return channel;
          };
          
          return channel;
        };
        
        return client;
      };
    }
  }
};

// Test message latency
export const testMessageLatency = async (sendMessageFn: (content: string) => Promise<boolean>) => {
  console.log('📊 Debug: Testing message latency...');
  
  const testMessages = [
    'Test message 1',
    'Test message 2', 
    'Test message 3'
  ];
  
  const latencies: number[] = [];
  
  for (let i = 0; i < testMessages.length; i++) {
    const startTime = Date.now();
    const message = `${testMessages[i]} - ${startTime}`;
    
    console.log(`📤 Debug: Sending test message ${i + 1}: ${message}`);
    
    try {
      const success = await sendMessageFn(message);
      if (success) {
        const latency = Date.now() - startTime;
        latencies.push(latency);
        console.log(`📥 Debug: Message ${i + 1} sent successfully in ${latency}ms`);
      } else {
        console.error(`❌ Debug: Failed to send message ${i + 1}`);
      }
    } catch (error) {
      console.error(`❌ Debug: Error sending message ${i + 1}:`, error);
    }
    
    // Wait 2 seconds between messages
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  if (latencies.length > 0) {
    const avgLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
    const maxLatency = Math.max(...latencies);
    const minLatency = Math.min(...latencies);
    
    console.group('📊 Debug: Latency Test Results');
    console.log(`Average Latency: ${Math.round(avgLatency)}ms`);
    console.log(`Max Latency: ${maxLatency}ms`);
    console.log(`Min Latency: ${minLatency}ms`);
    console.log(`Total Messages: ${latencies.length}`);
    console.groupEnd();
    
    return {
      avgLatency: Math.round(avgLatency),
      maxLatency,
      minLatency,
      messageCount: latencies.length
    };
  }
  
  return null;
};

// Monitor realtime connection
export const monitorRealtimeConnection = () => {
  console.log('🔍 Debug: Monitoring realtime connection...');
  
  const checkInterval = setInterval(() => {
    // Check for realtime indicators in DOM
    const realtimeIndicators = document.querySelectorAll('[class*="connected"], [class*="realtime"]');
    const pollingIndicators = document.querySelectorAll('[class*="polling"], [class*="fallback"]');
    
    console.log(`🔍 Debug: Realtime indicators: ${realtimeIndicators.length}, Polling indicators: ${pollingIndicators.length}`);
    
    // Check console for realtime logs
    const hasRealtimeLogs = performance.getEntriesByType('navigation').length > 0;
    console.log(`🔍 Debug: Navigation entries: ${performance.getEntriesByType('navigation').length}`);
    
  }, 5000);
  
  // Stop monitoring after 30 seconds
  setTimeout(() => {
    clearInterval(checkInterval);
    console.log('🔍 Debug: Stopped monitoring realtime connection');
  }, 30000);
  
  return checkInterval;
};

// Compare polling intervals
export const comparePollingIntervals = () => {
  console.log('⚖️ Debug: Comparing polling intervals...');
  
  const intervals = {
    original: 5000,
    enhanced: 1000,
    adaptiveMax: 4000
  };
  
  console.group('⚖️ Polling Interval Comparison');
  console.log(`Original: ${intervals.original}ms (5 seconds)`);
  console.log(`Enhanced: ${intervals.enhanced}ms (1 second)`);
  console.log(`Enhanced Max: ${intervals.adaptiveMax}ms (4 seconds when idle)`);
  console.log(`Improvement: ${Math.round(((intervals.original - intervals.enhanced) / intervals.original) * 100)}% faster`);
  console.groupEnd();
  
  return intervals;
};

// Simulate network delay
export const simulateNetworkDelay = (delayMs: number = 2000) => {
  console.log(`🌐 Debug: Simulating ${delayMs}ms network delay...`);
  
  if (typeof window !== 'undefined') {
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      console.log(`🌐 Debug: Adding ${delayMs}ms delay to fetch request`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
      return originalFetch(...args);
    };
    
    // Restore original fetch after 60 seconds
    setTimeout(() => {
      window.fetch = originalFetch;
      console.log('🌐 Debug: Restored original fetch function');
    }, 60000);
  }
};

// Check if enhanced version is being used
export const checkEnhancedVersion = () => {
  console.log('🔍 Debug: Checking if enhanced version is being used...');
  
  const currentUrl = window.location.pathname;
  const isEnhanced = currentUrl.includes('/enhanced') || currentUrl.includes('/chat-enhanced');
  
  console.group('🔍 Enhanced Version Check');
  console.log(`Current URL: ${currentUrl}`);
  console.log(`Using Enhanced Version: ${isEnhanced ? 'YES' : 'NO'}`);
  
  if (!isEnhanced) {
    console.log('⚠️ You are using the original version!');
    console.log('🔄 To test enhanced version:');
    
    if (currentUrl.includes('/staff/dashboard')) {
      console.log('  Navigate to: /staff/dashboard/enhanced');
    } else if (currentUrl.includes('/chat/')) {
      const sessionId = currentUrl.split('/chat/')[1];
      console.log(`  Navigate to: /chat-enhanced/${sessionId}`);
    }
  } else {
    console.log('✅ Enhanced version is active!');
    
    // Check for enhanced features
    const enhancedFeatures = [
      'realtimeMonitor',
      'performanceTester',
      'connectionStatus',
      'performancePanel'
    ];
    
    enhancedFeatures.forEach(feature => {
      const elements = document.querySelectorAll(`[class*="${feature}"]`);
      console.log(`  ${feature}: ${elements.length > 0 ? '✅' : '❌'} (${elements.length} elements)`);
    });
  }
  
  console.groupEnd();
  
  return isEnhanced;
};

// Auto-run debug checks
export const runQuickDebug = () => {
  console.group('🚀 Quick Debug Check');
  
  checkEnhancedVersion();
  comparePollingIntervals();
  
  console.log('');
  console.log('🔧 Available debug functions:');
  console.log('- forcePollingMode() - Force polling to test fallback');
  console.log('- testMessageLatency(sendFn) - Test message send latency');
  console.log('- monitorRealtimeConnection() - Monitor connection status');
  console.log('- simulateNetworkDelay(ms) - Add artificial delay');
  
  console.groupEnd();
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.debugUtils = {
    forcePollingMode,
    testMessageLatency,
    monitorRealtimeConnection,
    comparePollingIntervals,
    simulateNetworkDelay,
    checkEnhancedVersion,
    runQuickDebug
  };
  
  console.log('🔧 Debug utilities loaded! Use debugUtils.runQuickDebug() to start');
}

export default {
  forcePollingMode,
  testMessageLatency,
  monitorRealtimeConnection,
  comparePollingIntervals,
  simulateNetworkDelay,
  checkEnhancedVersion,
  runQuickDebug
};
