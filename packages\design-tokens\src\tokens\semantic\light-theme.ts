import { colors } from '../primitives/colors';

export const lightTheme = {
  colors: {
    // Brand colors
    primary: colors.orange['500'],
    secondary: colors.gray['50'],
    accent: colors.blue['500'],
    
    // Text colors
    text: {
      primary: colors.dark['975'],
      secondary: colors.gray['600'],
      disabled: colors.gray['400'],
      inverse: colors.white
    },
    
    // Background colors
    background: {
      primary: colors.white,
      secondary: colors.gray['50'],
      tertiary: colors.gray['100'],
      inverse: colors.dark['975']
    },
    
    // Border colors
    border: {
      primary: colors.gray['200'],
      secondary: colors.gray['300'],
      focus: colors.blue['500']
    },
    
    // Status colors
    status: {
      success: colors.success.DEFAULT,
      info: colors.info.DEFAULT,
      warning: colors.warning.DEFAULT,
      error: colors.error.DEFAULT
    },
    
    // Specific components
    button: {
      primary: {
        background: colors.orange['500'],
        text: colors.white,
        hover: '#E64500',
        active: '#CC3D00'
      },
      secondary: {
        background: colors.gray['50'],
        text: colors.dark['975'],
        hover: colors.gray['100'],
        active: colors.gray['200']
      },
      accent: {
        background: colors.blue['500'],
        text: colors.white,
        hover: '#0E42A6',
        active: '#0C3A91'
      },
      outline: {
        border: colors.gray['300'],
        text: colors.dark['900'],
        hover: colors.gray['100'],
        active: colors.gray['200']
      }
    },
    
    // Card colors
    card: {
      background: colors.white,
      border: colors.gray['200'],
      headerBackground: colors.gray['50']
    },
    
    // Form elements
    input: {
      background: colors.white,
      border: colors.gray['300'],
      text: colors.dark['975'],
      placeholder: colors.gray['400'],
      focus: {
        border: colors.blue['500']
      },
      error: {
        border: colors.error.DEFAULT,
        text: colors.error.DEFAULT
      }
    }
  }
};
