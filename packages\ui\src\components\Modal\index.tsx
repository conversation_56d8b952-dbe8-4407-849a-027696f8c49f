import React, { ReactNode, useEffect } from 'react';

export interface ModalProps {
  /**
   * Control whether the modal is open
   */
  isOpen: boolean;
  /**
   * Modal title
   */
  title?: ReactNode;
  /**
   * Modal content
   */
  children: ReactNode;
  /**
   * Footer content (usually buttons)
   */
  footer?: ReactNode;
  /**
   * <PERSON><PERSON> for closing the modal
   */
  onClose: () => void;
  /**
   * Modal size
   */
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  /**
   * Theme variant
   */
  variant?: 'light' | 'dark' | 'studio';
  /**
   * Whether to close on overlay click
   */
  closeOnOverlayClick?: boolean;
  /**
   * Whether to close on escape key
   */
  closeOnEsc?: boolean;
  /**
   * Additional CSS properties for modal container
   */
  style?: React.CSSProperties;
  /**
   * Optional CSS class name
   */
  className?: string;
  /**
   * Center the modal vertically
   */
  centered?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  title,
  children,
  footer,
  onClose,
  size = 'medium',
  variant = 'light',
  closeOnOverlayClick = true,
  closeOnEsc = true,
  style,
  className,
  centered = true,
  ...props
}) => {
  // Theme-based colors
  const themeColors = {
    light: {
      background: '#FFFFFF',
      text: '#010103',
      borderColor: '#EBEBEB',
      overlayBackground: 'rgba(0, 0, 0, 0.5)',
      closeButtonColor: '#464646',
      footerBackground: '#F9FAFB',
    },
    dark: {
      background: '#1E1E1E',
      text: '#EBEBEB',
      borderColor: '#161616',
      overlayBackground: 'rgba(0, 0, 0, 0.75)',
      closeButtonColor: '#EBEBEB',
      footerBackground: '#161616',
    },
    studio: {
      background: '#16262E',
      text: '#EBEBEB',
      borderColor: '#2E4756',
      overlayBackground: 'rgba(0, 0, 0, 0.75)',
      closeButtonColor: '#EBEBEB',
      footerBackground: '#2E4756',
    },
  };

  // Size styles
  const sizeStyles: Record<string, React.CSSProperties> = {
    small: {
      width: '400px',
      maxWidth: '100%',
    },
    medium: {
      width: '600px',
      maxWidth: '100%',
    },
    large: {
      width: '800px',
      maxWidth: '100%',
    },
    fullscreen: {
      width: '100%',
      height: '100%',
      maxWidth: '100%',
      maxHeight: '100%',
      margin: 0,
      borderRadius: 0,
    },
  };

  // Effect for ESC key handler
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (isOpen && closeOnEsc && event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen && closeOnEsc) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, closeOnEsc, onClose]);

  // Effect to prevent body scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      document.body.style.paddingRight = '15px'; // To prevent layout shift
    } else {
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
    }

    return () => {
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
    };
  }, [isOpen]);

  if (!isOpen) {
    return null;
  }

  // Handle overlay click
  const handleOverlayClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (closeOnOverlayClick && event.target === event.currentTarget) {
      onClose();
    }
  };

  // Close icon
  const CloseIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4 12L12 4M4 4L12 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  // Overlay style
  const overlayStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: themeColors[variant].overlayBackground,
    display: 'flex',
    alignItems: centered ? 'center' : 'flex-start',
    justifyContent: 'center',
    zIndex: 1000,
    padding: '48px 16px',
    overflow: 'auto',
    animation: 'fadeIn 0.3s ease',
  };

  // Modal container style
  const modalContainerStyle: React.CSSProperties = {
    backgroundColor: themeColors[variant].background,
    color: themeColors[variant].text,
    borderRadius: '8px',
    boxShadow: '0 4px 24px rgba(0, 0, 0, 0.2)',
    display: 'flex',
    flexDirection: 'column',
    ...sizeStyles[size],
    ...style,
    animation: 'scaleIn 0.3s ease',
  };

  // Ensure fullscreen modal is positioned correctly
  if (size === 'fullscreen') {
    overlayStyle.padding = 0;
    centered = false;
  }

  // Modal header style
  const headerStyle: React.CSSProperties = {
    padding: '16px 24px',
    borderBottom: title ? `1px solid ${themeColors[variant].borderColor}` : 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  };

  // Modal title style
  const titleStyle: React.CSSProperties = {
    margin: 0,
    fontSize: '18px',
    fontWeight: 600,
  };

  // Close button style
  const closeButtonStyle: React.CSSProperties = {
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    padding: '8px',
    color: themeColors[variant].closeButtonColor,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '4px',
  };

  // Modal body style
  const bodyStyle: React.CSSProperties = {
    padding: '24px',
    overflowY: 'auto',
    flexGrow: 1,
  };

  // Modal footer style
  const footerStyle: React.CSSProperties = {
    padding: '16px 24px',
    borderTop: footer ? `1px solid ${themeColors[variant].borderColor}` : 'none',
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '12px',
    backgroundColor: footer ? themeColors[variant].footerBackground : 'transparent',
    borderBottomLeftRadius: '8px',
    borderBottomRightRadius: '8px',
  };

  return (
    <div
      style={overlayStyle}
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      className={className}
      {...props}
    >
      <div style={modalContainerStyle}>
        {title && (
          <div style={headerStyle}>
            <div style={titleStyle}>{title}</div>
            <button
              onClick={onClose}
              style={closeButtonStyle}
              aria-label="Close"
            >
              <CloseIcon />
            </button>
          </div>
        )}
        
        {!title && (
          <button
            onClick={onClose}
            style={{
              ...closeButtonStyle,
              position: 'absolute',
              top: '16px',
              right: '16px',
              zIndex: 1,
            }}
            aria-label="Close"
          >
            <CloseIcon />
          </button>
        )}
        
        <div style={bodyStyle}>
          {children}
        </div>
        
        {footer && (
          <div style={footerStyle}>
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default Modal;
