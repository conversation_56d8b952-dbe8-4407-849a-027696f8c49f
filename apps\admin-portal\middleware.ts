import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { hasValidLicenseConfig, getLicenseConfig } from '@loaloa/license-client';

// List of public routes
const publicRoutes = ['/activate', '/login'];

export function middleware(request: NextRequest) {
  try {
    // Get current path
    const path = request.nextUrl.pathname;

    // Skip middleware for API routes
    if (path.startsWith('/api/')) {
      return NextResponse.next();
    }

    // Check if current path is a public route
    const isPublicRoute = publicRoutes.some(route => path === route || path.startsWith(`${route}/`));

    // Allow access to public routes without checking license
    if (isPublicRoute) {
      return NextResponse.next();
    }

    // Check license config using shared license client
    const hasValidLicense = hasValidLicenseConfig();

    // If no valid license and not on activate page, redirect to activate
    if (!hasValidLicense && path !== '/activate') {
      return NextResponse.redirect(new URL('/activate', request.url));
    }

    // Check authentication token
    const token = request.cookies.get('token');

    // If not authenticated and not on login page, redirect to login
    if (!token && path !== '/login') {
      const url = new URL('/login', request.url);
      url.searchParams.set('returnUrl', path);
      return NextResponse.redirect(url);
    }

    return NextResponse.next();
  } catch (error) {
    console.error('Middleware error:', error);
    // On error, redirect to activate page for safety
    return NextResponse.redirect(new URL('/activate', request.url));
  }
}

// Apply middleware to routes that need protection
export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
