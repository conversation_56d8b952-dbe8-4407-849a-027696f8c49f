'use client';
import React, { useState, useEffect } from 'react';
import { Button, Table, Card } from '@loaloa/ui';
import { DashboardLayout } from '@loaloa/ui/src/components/Layout/DashboardLayout';
import { HomeIcon, BuildingIcon, UsersIcon, LicenseIcon, SettingsIcon } from '@loaloa/ui/src/components/Icons/icons';
import Link from 'next/link';
import styles from './page.module.scss';
import { TableSkeleton } from '../../components/SkeletonLoader';
import { useDebounce } from '../../hooks/useDebounce';
import ErrorBoundary from '../../components/ErrorBoundary';
import UserTable from '../../components/UserTable';
import { useUsers } from '../../hooks/useUsers';
import { useUserActions } from '../../hooks/useUserActions';
import { useCallback } from 'react';

export default function Users() {
  // State cho phân trang và filter
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [status, setStatus] = useState<'active' | 'inactive' | 'all'>('all');
  const [search, setSearch] = useState('');
  const [searchInput, setSearchInput] = useState('');
const debouncedSearch = useDebounce(searchInput, 500);
  // Fetch dữ liệu sử dụng hook useUsers
  const { users, meta, isLoading, isError, mutate } = useUsers(
    { page, limit },
    { status, search }
  );
  
  // User actions
  const { deactivateUser, activateUser, isLoading: isActionLoading, error: actionError } = useUserActions();
  
  // Xử lý thay đổi trang
const handlePageChange = useCallback((newPage: number) => {
  setPage(newPage);
}, []);
  
  // Xử lý thay đổi trạng thái user
const handleStatusChange = useCallback(async (userId: string, currentStatus: boolean) => {
  if (currentStatus) {
    // Nếu đang active, thì deactivate
    const success = await deactivateUser(userId);
    if (success) {
      mutate(); // Refresh data
    }
  } else {
    // Nếu đang inactive, thì activate
    const success = await activateUser(userId);
    if (success) {
      mutate(); // Refresh data
    }
  }
}, [deactivateUser, activateUser, mutate]);
  
  // Các thành phần sidebar
  const sidebarItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      href: '/',
      icon: <HomeIcon />,
    },
    {
      id: 'tenants',
      label: 'Tenants',
      href: '/tenants',
      icon: <BuildingIcon />,
    },
    {
      id: 'users',
      label: 'Users',
      href: '/users',
      icon: <UsersIcon />,
      active: true,
    },
    {
      id: 'licenses',
      label: 'Licenses',
      href: '/licenses',
      icon: <LicenseIcon />,
    },
    {
      id: 'settings',
      label: 'Settings',
      href: '/settings',
      icon: <SettingsIcon />,
    },
  ];

  // Cấu hình các cột cho bảng
  const columns = [
    {
      header: 'User',
      accessor: (user) => (
        <div>
          <div><Link href={`/users/${user.id}`} className={styles.tenantLink}>
            {user.full_name}
          </Link></div>
          <div className={styles.userEmail}>{user.email}</div>
        </div>
      ),
      width: '22%',
    },
    {
      header: 'Role',
      accessor: (user) => (
        <span className={`${styles.roleBadge} ${styles[user.role]}`}>
          {user.role.replace('_', ' ')}
        </span>
      ),
      width: '12%',
    },
    {
      header: 'Tenants',
      accessor: (user) => (
        <div className={styles.tenantsCell}>
          {user.tenants && user.tenants.map((tenant, i) => (
            <div key={i}>
              <Link href={`/tenants/${tenant.id}`} className={styles.tenantLink}>
                {tenant.name}
              </Link>
              <span className={styles.tenantBadge}>{tenant.role}</span>
            </div>
          ))}
        </div>
      ),
      width: '25%',
    },
    {
      header: 'Status',
      accessor: (user) => {
        const statusClass = user.is_active ? 'status-active' : 'status-inactive';
        return <span className={`status-badge ${statusClass}`}>{user.is_active ? 'Active' : 'Inactive'}</span>;
      },
      width: '10%',
    },
    {
      header: 'Created',
      accessor: (user) => new Date(user.created_at).toLocaleDateString(),
      width: '10%',
    },
    {
      header: 'Last Login',
      accessor: (user) => user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never',
      width: '10%',
    },
    {
      header: 'Actions',
      accessor: (user) => (
        <div className={styles.actions}>
          <Button
            size="small"
            variant="outline"
            label="View"
            onClick={() => window.location.href = `/users/${user.id}`}
          />
          {!user.is_active ? (
            <Button 
              size="small" 
              variant="success" 
              label="Activate" 
              onClick={() => handleStatusChange(user.id, false)}
              disabled={isActionLoading}
            />
          ) : (
            <Button 
              size="small" 
              variant="danger" 
              label="Deactivate" 
              onClick={() => handleStatusChange(user.id, true)}
              disabled={isActionLoading}
            />
          )}
        </div>
      ),
      width: '14%',
    },
  ];
  
  // Tính toán số liệu thống kê
  const statsData = {
    totalUsers: meta?.total || 0,
    activeUsers: users.filter(u => u.is_active).length,
    superAdmins: users.filter(u => u.role === 'super_admin').length,
    inactiveUsers: users.filter(u => !u.is_active).length
  };
// Cập nhật search khi debouncedSearch thay đổi
useEffect(() => {
  setSearch(debouncedSearch);
}, [debouncedSearch]);


  return (
    <DashboardLayout
      sidebarItems={sidebarItems}
      title="User Management"
      username="Admin User"
      breadcrumbs={[
        { label: 'Dashboard', href: '/' },
        { label: 'Users' }
      ]}
    >
<ErrorBoundary>
      <div className={styles.usersContainer}>
        <div className={styles.pageHeader}>
          <div className={styles.titleSection}>
            <h1 className={styles.pageTitle}>Users</h1>
            <p className={styles.pageDescription}>
              Manage all users and their tenant associations in the LoaLoa platform
            </p>
          </div>
          <div className={styles.actions}>
            <Button variant="primary" label="Create New User" onClick={() => window.location.href = '/users/new'} />
          </div>
        </div>
        
        {/* Thêm thanh search và filter */}
        <div className={styles.filtersContainer}>
          <div className={styles.searchBar}>
            <input 
  		type="text" 
  		placeholder="Search users..." 
  		value={searchInput}
  		onChange={(e) => setSearchInput(e.target.value)}
  		className={styles.searchInput}
		/>
          </div>
          
          <div className={styles.filters}>
            <select 
              value={status} 
              onChange={(e) => setStatus(e.target.value as 'active' | 'inactive' | 'all')}
              className={styles.filterSelect}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>

        <div className={styles.statsCards}>
          <Card className={styles.statCard}>
            <h3 className={styles.statTitle}>Total Users</h3>
            <div className={styles.statValue}>{statsData.totalUsers}</div>
            {meta && <div className={`${styles.statChange} ${styles.positive}`}>
              {/* Trong thực tế, bạn sẽ cần thêm logic để xác định sự thay đổi */}
              Updated as of today
            </div>}
          </Card>
          <Card className={styles.statCard}>
            <h3 className={styles.statTitle}>Active Users</h3>
            <div className={styles.statValue}>{statsData.activeUsers}</div>
            {meta && <div className={styles.statFooter}>
              {Math.round((statsData.activeUsers / statsData.totalUsers) * 100)}% of total users
            </div>}
          </Card>
          <Card className={styles.statCard}>
            <h3 className={styles.statTitle}>Super Admins</h3>
            <div className={styles.statValue}>{statsData.superAdmins}</div>
            <div className={`${styles.statChange} ${styles.positive}`}>
              System administrators
            </div>
          </Card>
          <Card className={styles.statCard}>
            <h3 className={styles.statTitle}>Inactive Users</h3>
            <div className={styles.statValue}>{statsData.inactiveUsers}</div>
            {meta && <div className={styles.statFooter}>
              {Math.round((statsData.inactiveUsers / statsData.totalUsers) * 100)}% of total users
            </div>}
          </Card>
        </div>

        {/* Hiển thị lỗi nếu có */}
        {isError && (
          <div className={styles.errorMessage}>
            Error loading users: {(isError as any).info || 'Unknown error'}
          </div>
        )}
        
        {/* Hiển thị lỗi action nếu có */}
        {actionError && (
          <div className={styles.errorMessage}>
            {actionError}
          </div>
        )}

       {isLoading ? (
  <TableSkeleton rowCount={5} columnCount={7} />
) : (
  <Table
    columns={columns}
    data={users}
    pagination={{
      currentPage: page,
      totalPages: meta?.pageCount || 1,
      onPageChange: handlePageChange
    }}
  />
)}
      </div>
</ErrorBoundary>
    </DashboardLayout>
  );
}
