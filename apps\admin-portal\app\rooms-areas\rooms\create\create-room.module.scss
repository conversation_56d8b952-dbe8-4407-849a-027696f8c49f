.container {
  padding: 16px;
  
  @media (min-width: 768px) {
    padding: 24px;
  }
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #6b7280;
  text-decoration: none;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f3f4f6;
    color: #111827;
  }
  
  svg {
    stroke-width: 2px;
  }
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-left: 16px;
  
  @media (min-width: 768px) {
    font-size: 1.75rem;
  }
}

.formContainer {
  max-width: 800px;
}

.error {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 24px;
  font-size: 0.875rem;
  
  p {
    margin: 0;
  }
}

.successContainer {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.successCard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 32px;
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.successIcon {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  
  svg {
    background-color: rgba(16, 185, 129, 0.1);
    border-radius: 50%;
    padding: 12px;
  }
}

.successTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 12px;
}

.successDesc {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 24px;
}

.successActions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  @media (min-width: 640px) {
    flex-direction: row;
    justify-content: center;
  }
}

.primaryButton {
  padding: 10px 20px;
  background-color: #0ea5e9;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  
  &:hover {
    background-color: #0284c7;
  }
}

.secondaryButton {
  padding: 10px 20px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  
  &:hover {
    background-color: #059669;
  }
}

.outlineButton {
  padding: 10px 20px;
  background-color: white;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  
  &:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
  }
}
