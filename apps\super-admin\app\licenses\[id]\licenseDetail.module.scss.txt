.licenseDetail {
  padding: 24px;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .licenseInfo {
      .licenseKey {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        font-family: monospace;
      }
      
      .licenseMeta {
        display: flex;
        gap: 12px;
        align-items: center;
        
        .customerName {
          font-size: 16px;
          color: #666;
        }
      }
    }
    
    .headerActions {
      display: flex;
      gap: 12px;
    }
  }
  
  .tabsContainer {
    .tabContent {
      margin-top: 16px;
    }
    
    .alertBadge {
      margin-left: 8px;
      background-color: #ef4444;
      color: white;
      font-size: 12px;
      padding: 2px 6px;
      border-radius: 10px;
    }
  }
  
  .detailsGrid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    
    .detailItem {
      display: flex;
      flex-direction: column;
      
      &.fullWidth {
        grid-column: 1 / -1;
      }
      
      .detailLabel {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }
      
      .detailValue {
        font-size: 16px;
        
        code {
          font-family: monospace;
          background-color: #f3f4f6;
          padding: 2px 4px;
          border-radius: 4px;
          font-size: 14px;
        }
        
        .fingerprint {
          word-break: break-all;
        }
      }
      
      .revocationReason {
        color: #b91c1c;
      }
    }
  }
  
  .cloneAlerts {
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
    }
  }
  
  .noCloneAlerts {
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
    }
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  
  .spinner {
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
  
  h2 {
    font-size: 24px;
    margin-bottom: 16px;
  }
  
  p {
    margin-bottom: 24px;
    color: #666;
  }
}