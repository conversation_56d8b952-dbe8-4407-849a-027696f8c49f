.container {
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  .title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
  }
}

.content {
  display: flex;
  gap: 2rem;
}

.sidebar {
  flex: 0 0 320px;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  overflow: hidden;
  
  .sidebarHeader {
    padding: 1rem;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .sidebarTitle {
      margin: 0;
      font-size: 1rem;
      font-weight: 600;
      color: #475569;
    }
    
    .pointCount {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      background-color: #e2e8f0;
      border-radius: 1rem;
      color: #475569;
    }
  }
  
  .pointsList {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 640px;
    overflow-y: auto;
    
    .pointItem {
      padding: 1rem;
      cursor: pointer;
      border-bottom: 1px solid #e2e8f0;
      transition: background-color 0.2s;
      
      &:hover {
        background-color: #f1f5f9;
      }
      
      &.active {
        background-color: #e0f2fe;
        border-left: 4px solid #0ea5e9;
      }
      
      .pointInfo {
        margin-bottom: 0.5rem;
        
        .pointName {
          margin: 0 0 0.25rem;
          font-size: 0.9375rem;
          font-weight: 500;
          color: #334155;
        }
        
        .pointCode {
          font-size: 0.8125rem;
          color: #64748b;
        }
      }
      
      .pointStats {
        display: flex;
        gap: 1rem;
        
        .statItem {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.75rem;
          
          .statLabel {
            color: #64748b;
          }
          
          .statValue {
            font-weight: 600;
            color: #475569;
          }
        }
      }
    }
    
    .emptyState {
      padding: 2rem;
      text-align: center;
      color: #64748b;
      font-size: 0.9375rem;
    }
  }
}

.mainContent {
  flex: 1;
  min-width: 0;
  
  .pointHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    
    .pointHeaderInfo {
      .pointHeaderTitle {
        margin: 0 0 0.5rem;
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
      }
      
      .pointHeaderDesc {
        margin: 0;
        color: #64748b;
        font-size: 0.9375rem;
      }
    }
    
    .pointHeaderActions {
      display: flex;
      gap: 0.75rem;
    }
  }
  
  .staffListSection {
    .sectionTitle {
      font-size: 1rem;
      font-weight: 600;
      color: #334155;
      margin: 0 0 1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      
      .staffCount {
        font-size: 0.75rem;
        font-weight: 500;
        background-color: #e2e8f0;
        color: #475569;
        padding: 0.125rem 0.5rem;
        border-radius: 1rem;
      }
    }
    
    .loadingState {
      padding: 1rem 0;
    }
    
    .emptyStaffState {
      padding: 2rem;
      text-align: center;
      background-color: #f8fafc;
      border: 1px dashed #cbd5e1;
      border-radius: 0.5rem;
      margin-bottom: 2rem;
      
      p {
        margin: 0 0 1rem;
        color: #64748b;
      }
    }
    
    .staffList {
      margin-bottom: 2rem;
    }
    
    .staffTable {
      width: 100%;
      border-collapse: collapse;
      
      th {
        padding: 0.75rem 1rem;
        text-align: left;
        font-weight: 500;
        color: #475569;
        background-color: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
        font-size: 0.8125rem;
      }
      
      .nameColumn {
        width: 30%;
      }
      
      .emailColumn {
        width: 30%;
      }
      
      .priorityColumn {
        width: 10%;
      }
      
      .primaryColumn {
        width: 15%;
      }
      
      .actionsColumn {
        width: 15%;
      }
      
      td {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #e2e8f0;
        font-size: 0.9375rem;
      }
      
      .staffInfo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }
      
      .avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .avatarPlaceholder {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #0ea5e9;
        color: white;
        font-weight: 600;
      }
      
      .staffName {
        font-weight: 500;
        color: #334155;
      }
      
      .priorityInput {
        width: 60px;
        padding: 0.25rem;
        text-align: center;
        border: 1px solid #cbd5e1;
        border-radius: 0.25rem;
      }
      
      .primaryBadge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        background-color: #0ea5e9;
        color: white;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
      }
      
      .setPrimaryButton {
        padding: 0.25rem 0.5rem;
        background-color: transparent;
        border: 1px solid #0ea5e9;
        color: #0ea5e9;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background-color: #0ea5e9;
          color: white;
        }
      }
	  .removeButton {
        padding: 0.25rem 0.5rem;
        background-color: transparent;
        border: 1px solid #ef4444;
        color: #ef4444;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background-color: #ef4444;
          color: white;
        }
      }
    }
  }
  
  .unassignedSection {
    margin-top: 2rem;
    
    .unassignedList {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 1rem;
    }
    
    .unassignedItem {
      padding: 1rem;
      border: 1px solid #e2e8f0;
      border-radius: 0.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .staffInfo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        
        .avatar {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        
        .avatarPlaceholder {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #94a3b8;
          color: white;
          font-weight: 600;
        }
        
        div {
          display: flex;
          flex-direction: column;
        }
        
        .staffName {
          font-weight: 500;
          color: #334155;
          font-size: 0.9375rem;
        }
        
        .staffEmail {
          font-size: 0.8125rem;
          color: #64748b;
        }
      }
      
      .assignButton {
        padding: 0.25rem 0.75rem;
        background-color: transparent;
        border: 1px solid #10b981;
        color: #10b981;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background-color: #10b981;
          color: white;
        }
      }
    }
  }
  
  .noSelectionState {
    height: 100%;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .noSelectionMessage {
      text-align: center;
      color: #64748b;
      
      svg {
        margin-bottom: 1rem;
      }
      
      p {
        margin: 0;
        font-size: 0.9375rem;
        
        a {
          color: #0ea5e9;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
