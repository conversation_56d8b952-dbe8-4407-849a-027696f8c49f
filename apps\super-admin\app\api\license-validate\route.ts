import { NextRequest, NextResponse } from 'next/server';
import { LicenseService } from '../../services/LicenseService';

// API không yêu cầu xác thực để client on-premise có thể gọi
export async function POST(request: NextRequest) {
  try {
    const { licenseKey, hardwareFingerprint } = await request.json();
    
    // Validate input
    if (!licenseKey || !hardwareFingerprint) {
      return NextResponse.json(
        { error: 'License key and hardware fingerprint are required' },
        { status: 400 }
      );
    }
    
    // Xác thực license
    const result = await LicenseService.validateLicense(
      licenseKey,
      hardwareFingerprint
    );
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('License validation error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to validate license' },
      { status: 500 }
    );
  }
}