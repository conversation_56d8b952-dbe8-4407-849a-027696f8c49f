<body>
  <div class="container">
    <h1>LoaLoa Chat - Test Final</h1>
    
    <!-- Step 1: Connect to Supabase -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">1</span> Connect to Supabase</h3>
        <span class="status status-pending" id="supabase-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label for="supabase-url">Supabase URL</label>
          <input type="text" id="supabase-url" value="https://iwzwbrbmojvvvfstbqow.supabase.co">
        </div>
        <div class="form-group">
          <label for="supabase-key">Supabase Key (Anon)</label>
          <input type="text" id="supabase-key" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3endicmJtb2p2dnZmc3RicW93Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMjkyNjEsImV4cCI6MjA2MTkwNTI2MX0.tyVtaSclUKC5fGh7I7Ohpm7c4FniXphYe34-cxBvo6E">
        </div>
        <div class="form-group">
          <label for="service-role-key">Service Role Key (Optional, for admin operations)</label>
          <input type="password" id="service-role-key" placeholder="Service Role Key (optional)">
        </div>
        <button class="btn btn-primary" id="connect-supabase">Connect to Supabase</button>
        <div id="connection-details" class="user-info" style="display: none;"></div>
      </div>
    </div>
    
    <!-- Step 2: Validate JWT Token -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">2</span> Validate JWT Token</h3>
        <span class="status status-pending" id="jwt-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="note" style="background-color: #ffeaa7; padding: 15px; border-radius: 4px; margin-bottom: 15px;">
          <strong>Need a JWT Token?</strong> Run this command in your terminal:<br>
          <code>cd D:\loaloa\services\chat</code><br>
          <code>npx ts-node src\tests\generate-token.ts</code>
        </div>
        <div class="form-group">
          <label for="jwt-token">JWT Token</label>
          <textarea id="jwt-token" placeholder="Paste your JWT token here"></textarea>
        </div>
        <button class="btn btn-primary" id="validate-token">Validate Token</button>
        <div id="token-info" class="user-info" style="display: none;"></div>
      </div>
    </div>
    
    <!-- Step 3: Database Explorer -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">3</span> Database Explorer</h3>
        <span class="status status-pending" id="db-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="tab-header">
          <button class="tab-btn active" data-tab="users">Users</button>
          <button class="tab-btn" data-tab="rooms">Chat Rooms</button>
          <button class="tab-btn" data-tab="participants">Participants</button>
          <button class="tab-btn" data-tab="messages">Messages</button>
        </div>
        
        <div class="tab-content">
          <!-- Users Tab -->
          <div class="tab-panel active" id="users-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-users">Load Users</button>
              <span id="users-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="users-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Email</th>
                    <th>Created At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="3">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Rooms Tab -->
          <div class="tab-panel" id="rooms-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-rooms">Load Chat Rooms</button>
              <span id="rooms-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="rooms-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Created At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="4">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Participants Tab -->
          <div class="tab-panel" id="participants-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-participants">Load Participants</button>
              <span id="participants-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="participants-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Room ID</th>
                    <th>User ID</th>
                    <th>Role</th>
                    <th>Joined At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="5">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Messages Tab -->
          <div class="tab-panel" id="messages-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-messages">Load Messages</button>
              <span id="messages-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="messages-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Room ID</th>
                    <th>Sender ID</th>
                    <th>Content</th>
                    <th>Language</th>
                    <th>Sent At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="6">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    
   <!-- STEP 4: USER-ROOM MANAGEMENT -->
<div class="section">
  <div class="section-header">
    <h2>4 User-Room Management</h2>
    <div class="status" id="manage-status">Pending</div>
  </div>
  <div class="section-content">
    <div class="tabs">
      <button class="tab-btn active" data-tab="user1-manage">User 1</button>
      <button class="tab-btn" data-tab="user2-manage">User 2</button>
    </div>
    
    <!-- User 1 Management Panel -->
    <div class="tab-panel active" id="user1-manage-panel">
      <div class="card">
        <div class="form-grid">
          <div class="form-group">
            <label for="select-user1">Select User 1</label>
            <select id="select-user1" class="form-control">
              <option value="">-- Select User --</option>
            </select>
          </div>
          <div class="form-group">
            <label for="select-room1">Select Room</label>
            <select id="select-room1" class="form-control">
              <option value="">-- Select Room --</option>
            </select>
          </div>
        </div>
        <div class="button-row">
          <button id="check-participation1" class="button">Check Participation</button>
          <button id="join-room1" class="button button-primary">Join Room</button>
        </div>
        <div id="participation-status1" class="info-panel" style="display: none;">
          <div class="info-content">
            <p class="status-message"></p>
            <div class="details"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- User 2 Management Panel -->
    <div class="tab-panel" id="user2-manage-panel">
      <div class="card">
        <div class="form-grid">
          <div class="form-group">
            <label for="select-user2">Select User 2</label>
            <select id="select-user2" class="form-control">
              <option value="">-- Select User --</option>
            </select>
          </div>
          <div class="form-group">
            <label for="select-room2">Select Room</label>
            <select id="select-room2" class="form-control">
              <option value="">-- Select Room --</option>
            </select>
          </div>
        </div>
        <div class="button-row">
          <button id="check-participation2" class="button">Check Participation</button>
          <button id="join-room2" class="button button-primary">Join Room</button>
        </div>
        <div id="participation-status2" class="info-panel" style="display: none;">
          <div class="info-content">
            <p class="status-message"></p>
            <div class="details"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

    
    <!-- Step 5: WebSocket Connection -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">5</span> WebSocket Connection</h3>
        <span class="status status-pending" id="ws-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label for="ws-url">WebSocket Server URL</label>
          <input type="text" id="ws-url" value="http://localhost:3002">
        </div>
        <div class="form-group">
          <label for="ws-room-id">Room ID to Join</label>
          <input type="text" id="ws-room-id" placeholder="Room ID">
        </div>
        <div class="form-group">
          <button class="btn btn-primary" id="connect-ws">Connect WebSocket</button>
          <button class="btn btn-secondary" id="ws-join-room">Join Room</button>
          <button class="btn btn-secondary" id="show-debug" data-state="show">Hide Debug Console</button>
        </div>
        <div class="console" id="debug-console">
          <!-- Debug messages will appear here -->
        </div>
      </div>
    </div>
    
    <!-- STEP 6: TEST CHAT INTERFACE -->
<div class="section">
  <div class="section-header">
    <h2>6 Test Chat Interface</h2>
    <div class="status" id="chat-status">Pending</div>
  </div>
  <div class="section-content">
    <div class="chat-container">
      <!-- User 1 (Vietnamese) -->
      <div class="chat-box">
        <div class="chat-header">
          <h3>User 1 (Vietnamese)</h3>
          <div class="user-info">
            <div><strong>ID:</strong> <span id="user1-id"></span></div>
            <div><strong>Email:</strong> <span id="user1-email">Not loaded</span></div>
            <div><strong>Status:</strong> <span id="user1-status">Disconnected</span></div>
          </div>
        </div>
        <div class="chat-messages" id="user1-messages"></div>
        <div class="chat-controls">
          <div class="form-group language-group">
            <label for="user1-language">Translate to:</label>
            <select id="user1-language" class="form-control language-selector" disabled>
              <option value="vi">Vietnamese (Default)</option>
              <option value="en">English</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="zh">Chinese</option>
              <option value="ja">Japanese</option>
            </select>
          </div>
          <div class="input-group">
            <input type="text" id="user1-input" class="form-control" placeholder="Nhập tin nhắn...">
            <button id="user1-send" class="button button-primary">Send</button>
          </div>
          <button id="user1-join" class="button">Join as Participant</button>
        </div>
      </div>
      <!-- User 2 (English) -->
      <div class="chat-box">
        <div class="chat-header">
          <h3>User 2 (English)</h3>
          <div class="user-info">
            <div><strong>ID:</strong> <span id="user2-id"></span></div>
            <div><strong>Email:</strong> <span id="user2-email">Not loaded</span></div>
            <div><strong>Status:</strong> <span id="user2-status">Disconnected</span></div>
          </div>
        </div>
        <div class="chat-messages" id="user2-messages"></div>
        <div class="chat-controls">
          <div class="form-group language-group">
            <label for="user2-language">Translate to:</label>
            <select id="user2-language" class="form-control language-selector" disabled>
              <option value="en">English (Default)</option>
              <option value="vi">Vietnamese</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="zh">Chinese</option>
              <option value="ja">Japanese</option>
            </select>
          </div>
          <div class="input-group">
            <input type="text" id="user2-input" class="form-control" placeholder="Type a message...">
            <button id="user2-send" class="button button-primary">Send</button>
          </div>
          <button id="user2-join" class="button">Join as Participant</button>
        </div>
      </div>
    </div>
  </div>
</div>
  <!-- STEP 7: TRANSLATION API -->
<div class="section">
  <div class="section-header">
    <h2>7 Translation API</h2>
    <div class="status" id="translation-api-status">Pending</div>
  </div>
  <div class="section-content">
    <div class="card">
      <div class="form-group">
        <label for="api-key">MyMemory API Key (Optional)</label>
        <input type="text" id="api-key" class="form-control" placeholder="Để trống nếu muốn sử dụng API miễn phí">
      </div>
      <div class="button-row">
        <button id="check-api-status" class="button button-primary">Check API Status</button>
      </div>
      <div id="api-status-result" class="info-panel" style="display: none;">
        <div class="info-content">
          <p class="status-message"></p>
          <div class="details"></div>
        </div>
      </div>
    </div>
  </div>
</div>