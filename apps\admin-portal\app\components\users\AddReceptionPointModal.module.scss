.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: white;
  border-radius: 0.5rem;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-height: calc(100vh - 4rem);
  display: flex;
  flex-direction: column;
  
  form {
    display: flex;
    flex-direction: column;
    flex: 1;
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  
  h2 {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    color: #111827;
  }
  
  .closeButton {
    background: transparent;
    border: none;
    font-size: 1.5rem;
    line-height: 1;
    color: #6b7280;
    cursor: pointer;
    padding: 0;
    
    &:hover {
      color: #111827;
    }
    
    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
}

.modalBody {
  padding: 1.5rem;
  overflow-y: auto;
}

.modalFooter {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.formGroup {
  margin-bottom: 1.5rem;
  
  label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
  }
  
  select {
    width: 100%;
    padding: 0.625rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.875rem;
    color: #374151;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5);
    }
    
    &:disabled {
      background-color: #f3f4f6;
      cursor: not-allowed;
    }
  }
}

.checkboxGroup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  
  input[type="checkbox"] {
    cursor: pointer;
    
    &:disabled {
      cursor: not-allowed;
    }
  }
  
  label {
    font-size: 0.875rem;
    cursor: pointer;
  }
}

.helpText {
  font-size: 0.875rem;
  color: #6b7280;
  
  p {
    margin: 0 0 0.75rem;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .primaryNote {
    color: #1d4ed8;
    background-color: #dbeafe;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    border-left: 3px solid #3b82f6;
  }
}

.error {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1.5rem;
  border-left: 3px solid #ef4444;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  
  .spinner {
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    width: 1.5rem;
    height: 1.5rem;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.noPoints {
  text-align: center;
  padding: 2rem;
  
  p {
    margin: 0 0 0.5rem;
  }
  
  .hint {
    font-size: 0.875rem;
    color: #9ca3af;
  }
}

.formGroup {
  margin-bottom: 1.25rem;
  
  label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #374151;
    
    .required {
      color: #ef4444;
      margin-left: 0.25rem;
    }
  }
  
  input[type="number"],
  input[type="text"],
  select {
    width: 100%;
    padding: 0.625rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    
    &:focus {
      outline: none;
      border-color: #2563eb;
      ring: 2px solid rgba(37, 99, 235, 0.2);
    }
    
    &:disabled {
      background-color: #f3f4f6;
      cursor: not-allowed;
    }
  }
  
  .fieldDescription {
    margin-top: 0.375rem;
    font-size: 0.75rem;
    color: #6b7280;
  }
  
  .checkboxContainer {
    display: flex;
    align-items: center;
    
    input[type="checkbox"] {
      margin-right: 0.5rem;
    }
    
    label {
      margin-bottom: 0;
      font-weight: 400;
      cursor: pointer;
    }
  }
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}
