'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import styles from './RoomForm.module.scss';
import ReceptionPointDropdown from '../ReceptionPointDropdown';

interface ReceptionPoint {
  id: string;
  name: string;
  code: string;
}

interface RoomFormProps {
  initialData?: {
    id?: string;
    room_number: string;
    room_type: string;
    floor: string;
    room_category: string;
    description?: string;
    status?: string;
    is_active?: boolean;
    reception_point_id?: string; // Thêm trường reception_point_id
  };
  onSubmit: (formData: any) => Promise<void>;
  isEditing?: boolean;
}

export default function RoomForm({
  initialData,
  onSubmit,
  isEditing = false,
}: RoomFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [receptionPoints, setReceptionPoints] = useState<ReceptionPoint[]>([]);
  const [formData, setFormData] = useState({
    room_number: initialData?.room_number || '',
    room_type: initialData?.room_type || 'Standard Room',
    floor: initialData?.floor || '1',
    room_category: initialData?.room_category || 'Standard',
    description: initialData?.description || '',
    status: initialData?.status || 'available',
    is_active: initialData?.is_active !== false, // Default to true if not specified
    reception_point_id: initialData?.reception_point_id || '', // Thêm trường reception_point_id
  });

  useEffect(() => {
    // Fetch reception points when component mounts
    const fetchReceptionPoints = async () => {
      try {
        const response = await fetch('/api/reception-points?is_active=true');
        if (!response.ok) {
          throw new Error('Failed to fetch reception points');
        }
        
        const data = await response.json();
        setReceptionPoints(data.data || []);
      } catch (err) {
        console.error('Error fetching reception points:', err);
        // Don't block the form if reception points can't be fetched
      }
    };
    
    fetchReceptionPoints();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      await onSubmit(formData);
      // Navigate back after successful submission
      if (!isEditing) {
        router.push('/rooms-areas/rooms');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while saving the room.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form className={styles.form} onSubmit={handleSubmit}>
      {error && <div className={styles.error}>{error}</div>}
      
      <div className={styles.formGrid}>
        <div className={styles.formGroup}>
          <label htmlFor="room_number" className={styles.label}>
            Room Number <span className={styles.required}>*</span>
          </label>
          <input
            id="room_number"
            name="room_number"
            type="text"
            placeholder="Enter room number"
            value={formData.room_number}
            onChange={handleChange}
            className={styles.input}
            required
            disabled={isEditing} // Room number should not be editable
          />
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="room_type" className={styles.label}>
            Room Type <span className={styles.required}>*</span>
          </label>
          <select
            id="room_type"
            name="room_type"
            value={formData.room_type}
            onChange={handleChange}
            className={styles.select}
            required
          >
            <option value="Standard Room">Standard Room</option>
            <option value="Deluxe Room">Deluxe Room</option>
            <option value="Suite">Suite</option>
            <option value="Executive Suite">Executive Suite</option>
            <option value="Family Room">Family Room</option>
            <option value="Twin Room">Twin Room</option>
          </select>
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="floor" className={styles.label}>
            Floor <span className={styles.required}>*</span>
          </label>
          <input
            id="floor"
            name="floor"
            type="text"
            placeholder="Enter floor number"
            value={formData.floor}
            onChange={handleChange}
            className={styles.input}
            required
          />
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="room_category" className={styles.label}>
            Category <span className={styles.required}>*</span>
          </label>
          <select
            id="room_category"
            name="room_category"
            value={formData.room_category}
            onChange={handleChange}
            className={styles.select}
            required
          >
            <option value="Standard">Standard</option>
            <option value="Deluxe">Deluxe</option>
            <option value="Suite">Suite</option>
            <option value="Family">Family</option>
            <option value="VIP">VIP</option>
          </select>
        </div>

        {/* Reception Point Selection */}
        <div className={styles.formGroup}>
  <label htmlFor="reception_point_id" className={styles.label}>
    Message Reception Point
  </label>
  <ReceptionPointDropdown
    value={formData.reception_point_id}
    onChange={(value) => setFormData(prev => ({ ...prev, reception_point_id: value }))}
    placeholder="-- Select Reception Point --"
    disabled={loading}
  />
  <p className={styles.helpText}>
    Select where messages from guests in this room should be routed
  </p>
</div>
        
        {isEditing && (
          <div className={styles.formGroup}>
            <label htmlFor="status" className={styles.label}>
              Status
            </label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
              className={styles.select}
            >
              <option value="available">Available</option>
              <option value="occupied">Occupied</option>
              <option value="maintenance">Maintenance</option>
              <option value="cleaning">Cleaning</option>
            </select>
          </div>
        )}
        
        <div className={styles.formGroupFull}>
          <label htmlFor="description" className={styles.label}>
            Description
          </label>
          <textarea
            id="description"
            name="description"
            placeholder="Enter room description"
            value={formData.description}
            onChange={handleChange}
            className={styles.textarea}
            rows={4}
          />
        </div>
        
        {isEditing && (
          <div className={styles.formGroupCheckbox}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="is_active"
                checked={formData.is_active}
                onChange={handleChange}
                className={styles.checkbox}
              />
              <span>Active</span>
            </label>
          </div>
        )}
      </div>
      
      <div className={styles.buttons}>
        <button 
          type="button" 
          onClick={() => router.back()} 
          className={styles.cancelButton}
          disabled={loading}
        >
          Cancel
        </button>
        <button 
          type="submit" 
          className={styles.submitButton}
          disabled={loading}
        >
          {loading ? 'Saving...' : isEditing ? 'Update Room' : 'Create Room'}
        </button>
      </div>
    </form>
  );
}
