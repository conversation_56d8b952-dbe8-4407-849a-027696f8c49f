import type { Meta, StoryObj } from '@storybook/react';
import { Tooltip } from './index';
import { Button } from '../Button';
import { Badge } from '../Badge';

const meta = {
  title: 'UI/Tooltip',
  component: Tooltip,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    content: {
      control: 'text',
      description: 'Tooltip content',
    },
    position: {
      control: 'select',
      options: ['top', 'right', 'bottom', 'left'],
      description: 'Tooltip position',
    },
    variant: {
      control: 'select',
      options: ['light', 'dark', 'studio'],
      description: 'Tooltip background variant',
    },
    delay: {
      control: 'number',
      description: 'Delay before showing tooltip (ms)',
    },
    arrow: {
      control: 'boolean',
      description: 'Whether to show an arrow',
    },
    maxWidth: {
      control: 'text',
      description: 'Maximum width of the tooltip',
    },
  },
} satisfies Meta<typeof Tooltip>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    content: 'This is a tooltip',
    position: 'top',
    children: <Button variant="primary" label="Hover me" />,
  },
};

export const WithDifferentPositions: Story = {
  args: {
    content: 'Tooltip content',
    children: <Button variant="primary" label="Hover me" />,
  },
  render: (args) => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '100px', alignItems: 'center' }}>
      <Tooltip {...args} position="top">
        <Button variant="primary" label="Top Tooltip" />
      </Tooltip>
      <div style={{ display: 'flex', gap: '100px' }}>
        <Tooltip {...args} position="left">
          <Button variant="primary" label="Left Tooltip" />
        </Tooltip>
        <Tooltip {...args} position="right">
          <Button variant="primary" label="Right Tooltip" />
        </Tooltip>
      </div>
      <Tooltip {...args} position="bottom">
        <Button variant="primary" label="Bottom Tooltip" />
      </Tooltip>
    </div>
  ),
};

export const WithDifferentVariants: Story = {
  name: 'Theme Variants',
  args: {
    content: 'Tooltip content',
    position: 'top',
  },
  render: (args) => (
    <div style={{ display: 'flex', gap: '24px' }}>
      <Tooltip {...args} variant="light">
        <Button variant="primary" label="Light Variant" />
      </Tooltip>
      <Tooltip {...args} variant="dark">
        <Button variant="primary" label="Dark Variant" />
      </Tooltip>
      <Tooltip {...args} variant="studio">
        <Button variant="primary" label="Studio Variant" />
      </Tooltip>
    </div>
  ),
};

export const WithHtmlContent: Story = {
  name: 'Rich Content',
  args: {
    content: (
      <div>
        <strong>Rich HTML Content</strong>
        <p style={{ margin: '8px 0' }}>This tooltip contains <em>HTML content</em>.</p>
        <Badge variant="primary" label="Badge" />
      </div>
    ),
    position: 'bottom',
    children: <Button variant="primary" label="Hover me" />,
    maxWidth: 300,
  },
};

export const WithoutArrow: Story = {
  args: {
    content: 'Tooltip without arrow',
    position: 'top',
    arrow: false,
    children: <Button variant="primary" label="Hover me" />,
  },
};

export const WithCustomizedDelay: Story = {
  args: {
    content: 'Tooltip with 1 second delay',
    position: 'top',
    delay: 1000,
    children: <Button variant="primary" label="Hover me" />,
  },
};

export const OnDifferentElements: Story = {
  name: 'On Various Elements',
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <Tooltip content="Tooltip on button">
        <Button variant="primary" label="Button with Tooltip" />
      </Tooltip>
      
      <Tooltip content="Tooltip on text">
        <span style={{ cursor: 'help', borderBottom: '1px dotted #333' }}>
          Hover over this text to see a tooltip
        </span>
      </Tooltip>
      
      <Tooltip content="Tooltip on icon">
        <div 
          style={{ 
            cursor: 'pointer', 
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '32px',
            height: '32px',
            backgroundColor: '#f5f5f5',
            borderRadius: '50%'
          }}
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8 10.6667V8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8 5.33333H8.00667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </Tooltip>
      
      <Tooltip content="Tooltip on Badge">
        <Badge variant="accent" label="Hover me" />
      </Tooltip>
    </div>
  ),
};

export const WithLongContent: Story = {
  args: {
    content: 'This is a tooltip with very long content that will wrap to multiple lines. It demonstrates how the tooltip handles lengthy text content gracefully.',
    position: 'top',
    maxWidth: 200,
    children: <Button variant="primary" label="Hover for long content" />,
  },
};
