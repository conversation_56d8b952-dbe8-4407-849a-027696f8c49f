🗄️ DATABASE SCHEMA
Core Tables:
1. temporary_users
<PERSON><PERSON><PERSON> đ<PERSON>ch: <PERSON><PERSON><PERSON><PERSON> lý người dùng tạm thời khi quét QR code
Columns:
id (UUID, PK) - ID duy nhất
qr_code_id (VARCHAR) - ID của QR code được quét
device_id (VARCHAR) - ID thiết bị để tracking
preferred_language (VARCHAR) - Ngôn ngữ ưa thích
created_at, expires_at - Thời gian tạo và hết hạn
is_activated (BOOLEAN) - Trạng thái kích hoạt
room_number (VARCHAR) - <PERSON><PERSON> phòng (nếu có)
hotel_id (UUID) - ID khách sạn
metadata (JSONB) - D<PERSON> liệu bổ sung
2. tenant_chat_sessions
Mục đích: Quản lý phiên chat giữa guest và staff
Columns:
id (UUID, PK) - ID phiên chat
tenant_id (UUID, FK) - ID tenant
guest_id (UUID, FK) - <PERSON> khách hàng
status (VARCHAR) - <PERSON><PERSON><PERSON><PERSON> thái: active, ended, pending
guest_language (VARCHAR) - <PERSON>ôn ngữ khách
staff_language (VARCHAR) - <PERSON>ôn ngữ nhân viên
auto_translate (BOOLEAN) - Tự động dịch
source_type (VARCHAR) - Nguồn: qr_code, direct, guest_app
source_qr_code_id (UUID, FK) - QR code nguồn
reception_point_id (UUID, FK) - Điểm tiếp nhận
priority (VARCHAR) - Độ ưu tiên: low, normal, high, urgent
created_at, updated_at, ended_at - Timestamps
3. tenant_chat_messages
Mục đích: Lưu trữ tin nhắn chat với tính năng dịch thuật
Columns:
id (UUID, PK) - ID tin nhắn
chat_session_id (UUID, FK) - ID phiên chat
sender_type (VARCHAR) - Loại người gửi: guest, staff
sender_id (UUID) - ID người gửi
content (TEXT) - Nội dung hiển thị
original_content (TEXT) - Nội dung gốc
translated_content (TEXT) - Nội dung đã dịch
original_language (VARCHAR) - Ngôn ngữ gốc
translated_language (VARCHAR) - Ngôn ngữ đích
is_translated (BOOLEAN) - Đã dịch hay chưa
translation_provider (VARCHAR) - Nhà cung cấp dịch thuật
translation_confidence (NUMERIC) - Độ tin cậy dịch thuật
show_translation (BOOLEAN) - Hiển thị bản dịch
created_at - Thời gian tạo
metadata (JSONB) - Dữ liệu bổ sung
4. tenant_typing_status
Mục đích: Theo dõi trạng thái đang gõ tin nhắn
Columns:
id (UUID, PK)
session_id (UUID, FK) - ID phiên chat
user_id (UUID) - ID người dùng
user_type (VARCHAR) - guest hoặc staff
is_typing (BOOLEAN) - Đang gõ hay không
last_typing_at (TIMESTAMP) - Thời điểm gõ gần nhất
tenant_id (UUID, FK)
5. tenant_message_attachments
Mục đích: File đính kèm trong tin nhắn
Columns:
id (UUID, PK)
message_id (UUID, FK) - ID tin nhắn
file_url (TEXT) - URL file
file_name (VARCHAR) - Tên file
file_type (VARCHAR) - Loại file
file_size (INTEGER) - Kích thước
mime_type (VARCHAR) - MIME type
uploaded_by (UUID) - Người upload
tenant_id (UUID, FK)
6. tenant_translation_settings
Mục đích: Cấu hình dịch thuật cho tenant
Columns:
id (UUID, PK)
tenant_id (UUID, FK, UNIQUE)
provider (VARCHAR) - google, azure, aws
api_key_encrypted (TEXT) - API key đã mã hóa
supported_languages (JSONB) - Ngôn ngữ được hỗ trợ
default_guest_language (VARCHAR) - Ngôn ngữ mặc định khách
default_staff_language (VARCHAR) - Ngôn ngữ mặc định nhân viên
auto_detect_language (BOOLEAN) - Tự động phát hiện ngôn ngữ
translation_enabled (BOOLEAN) - Bật/tắt dịch thuật
7. tenant_web_sessions
Mục đích: Quản lý phiên web client
Columns:
id (UUID, PK)
session_token (VARCHAR, UNIQUE) - Token phiên
tenant_id (UUID, FK)
guest_id (UUID, FK) - Khách đã checkin
temporary_user_id (UUID, FK) - Người dùng tạm thời
user_agent (TEXT) - Thông tin trình duyệt
ip_address (INET) - Địa chỉ IP
device_fingerprint (VARCHAR) - Dấu vân tay thiết bị
browser_info (JSONB) - Thông tin trình duyệt
preferred_language (VARCHAR) - Ngôn ngữ ưa thích
is_mobile (BOOLEAN) - Thiết bị di động
last_activity (TIMESTAMP) - Hoạt động cuối
session_data (JSONB) - Dữ liệu phiên
expires_at (TIMESTAMP) - Thời gian hết hạn
8. tenant_voice_messages
Mục đích: Tin nhắn thoại (cho tương lai)
Columns:
id (UUID, PK)
message_id (UUID, FK)
voice_file_url (TEXT) - URL file audio
duration_seconds (INTEGER) - Thời lượng
transcription (TEXT) - Văn bản chuyển đổi
transcription_language (VARCHAR) - Ngôn ngữ transcription
is_transcribed (BOOLEAN) - Đã chuyển đổi chưa
processing_status (VARCHAR) - pending, processing, completed, failed
uploaded_by (UUID) - Người upload
tenant_id (UUID, FK)
Foreign Key Relationships:
temporary_users.hotel_id → tenants.id
tenant_chat_sessions.tenant_id → tenants.id
tenant_chat_sessions.guest_id → tenant_guests.id
tenant_chat_sessions.reception_point_id → tenant_message_reception_points.id
tenant_chat_messages.chat_session_id → tenant_chat_sessions.id
tenant_typing_status.session_id → tenant_chat_sessions.id
tenant_message_attachments.message_id → tenant_chat_messages.id
tenant_translation_settings.tenant_id → tenants.id
tenant_web_sessions.guest_id → tenant_guests.id
tenant_web_sessions.temporary_user_id → temporary_users.id
tenant_voice_messages.message_id → tenant_chat_messages.id
📁 FOLDER STRUCTURE
D:/loaloa/apps/web-chat/
├── app/
│   ├── layout.tsx                    # Root layout
│   ├── page.tsx                      # Homepage
│   ├── chat/
│   │   └── [session]/
│   │       ├── page.tsx              # Chat page (dynamic route)
│   │       └── chat-page.module.scss # Chat page styles
│   ├── qr/
│   │   └── demo/
│   │       ├── page.tsx              # QR demo page
│   │       └── qr-demo.module.scss   # QR demo styles
│   ├── components/
│   │   ├── chat/
│   │   │   └── ui/
│   │   │       ├── ChatInterface.tsx           # Main chat interface
│   │   │       └── ChatInterface.module.scss  # Chat interface styles
│   │   └── language/
│   │       ├── LanguageSelector.tsx            # Language selector component
│   │       ├── LanguageSelector.module.scss   # Language selector styles
│   │       ├── TranslationToggle.tsx           # Translation toggle component
│   │       └── TranslationToggle.module.scss  # Translation toggle styles
│   ├── hooks/
│   │   └── useChat.ts                # Chat hook for real-time messaging
│   ├── utils/                        # Utility functions (planned)
│   ├── api/                          # API endpoints (planned)
│   │   ├── temp-users/              # Temporary users API
│   │   ├── chat-sessions/           # Chat sessions API
│   │   ├── messages/                # Messages API
│   │   └── translation/             # Translation API
│   └── styles/
│       ├── globals.scss             # Global styles
│       └── home.module.scss         # Homepage styles
├── package.json                     # Web chat dependencies
├── next.config.js                   # Next.js configuration
└── tsconfig.json                    # TypeScript configuration
🧩 COMPONENTS
1. ChatInterface Component
Location: app/components/chat/ui/ChatInterface.tsx

Props:

sessionId: string - ID của phiên chat
guestId: string - ID của khách hàng
guestLanguage?: string - Ngôn ngữ khách (default: 'en')
Features:

Real-time messaging với mock data
Auto-scroll to bottom
Online/offline status detection
Typing indicators
Message timestamp formatting
Language settings panel
Staff information display
State Management:

Messages list
Session data
Connection status
Typing state
Language preferences
Auto-translation toggle
2. LanguageSelector Component
Location: app/components/language/LanguageSelector.tsx

Props:

currentLanguage: string - Ngôn ngữ hiện tại
onLanguageChange: (code: string) => void - Callback khi thay đổi
availableLanguages?: Language[] - Danh sách ngôn ngữ có sẵn
showNativeName?: boolean - Hiển thị tên bản địa
compact?: boolean - Chế độ compact
disabled?: boolean - Vô hiệu hóa
Features:

Dropdown với search functionality
12 ngôn ngữ mặc định (EN, VI, KO, JA, ZH, TH, ID, MS, ES, FR, DE, AR)
Responsive design
Click-outside to close
Keyboard navigation support
Flag icons và native names
Supported Languages:

Copyconst defaultLanguages = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'vi', name: 'Vietnamese', nativeName: 'Tiếng Việt', flag: '🇻🇳' },
  { code: 'ko', name: 'Korean', nativeName: '한국어', flag: '🇰🇷' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語', flag: '🇯🇵' },
  { code: 'zh', name: 'Chinese', nativeName: '中文', flag: '🇨🇳' },
  { code: 'th', name: 'Thai', nativeName: 'ไทย', flag: '🇹🇭' },
  { code: 'id', name: 'Indonesian', nativeName: 'Bahasa Indonesia', flag: '🇮🇩' },
  { code: 'ms', name: 'Malay', nativeName: 'Bahasa Melayu', flag: '🇲🇾' },
  { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },
  { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷' },
  { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪' },
  { code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦', rtl: true }
]
3. TranslationToggle Component
Location: app/components/language/TranslationToggle.tsx

Props:

enabled: boolean - Trạng thái bật/tắt
onToggle: (enabled: boolean) => void - Callback khi toggle
guestLanguage: string - Ngôn ngữ khách
staffLanguage: string - Ngôn ngữ nhân viên
disabled?: boolean - Vô hiệu hóa
showLanguages?: boolean - Hiển thị dual language
Features:

Toggle switch với animation
Dual language display với flags
Visual flow indication (Guest ⇄ Staff)
Status indicator (ON/OFF)
Responsive design
Disabled state support
🔧 HOOKS
useChat Hook
Location: app/hooks/useChat.ts

Purpose: Quản lý real-time chat functionality

Parameters:

sessionId: string - ID phiên chat
guestId: string - ID khách hàng
Returns:

Copy{
  messages: ChatMessage[],
  session: ChatSession | null,
  loading: boolean,
  connected: boolean,
  isTyping: boolean,
  sendMessage: (content: string) => Promise<void>,
  startTyping: () => void,
  stopTyping: () => void,
  refresh: () => Promise<void>
}
Features:

Mock data cho development
Auto-staff reply simulation
Typing indicators
Connection status management
Message validation
Error handling
Types:

Copyinterface ChatMessage {
  id: string
  chat_session_id: string
  sender_type: 'guest' | 'staff'
  sender_id: string
  content: string
  original_content?: string
  translated_content?: string
  original_language?: string
  translated_language?: string
  is_translated: boolean
  created_at: string
  metadata?: any
}

interface ChatSession {
  id: string
  tenant_id: string
  guest_id?: string
  status: 'active' | 'ended' | 'pending'
  guest_language?: string
  staff_language?: string
  auto_translate: boolean
  source_type?: string
  created_at: string
  updated_at: string
}
🌐 API ENDPOINTS (Planned)
1. Temporary Users API
Endpoint: /api/temp-users

POST - Tạo temporary user mới

Copy// Request
{
  qr_code_id: string,
  device_id: string,
  preferred_language: string,
  metadata?: any
}

// Response
{
  id: string,
  session_token: string,
  expires_at: string
}
2. Chat Sessions API
Endpoint: /api/chat-sessions

POST - Tạo phiên chat mới

Copy// Request
{
  guest_id?: string,
  temporary_user_id?: string,
  guest_language: string,
  source_type: string,
  source_qr_code_id?: string,
  reception_point_id?: string
}

// Response
{
  id: string,
  status: string,
  created_at: string
}
GET /api/chat-sessions/[id] - Lấy thông tin phiên chat PUT /api/chat-sessions/[id] - Cập nhật phiên chat DELETE /api/chat-sessions/[id] - Kết thúc phiên chat

3. Messages API
Endpoint: /api/messages

POST - Gửi tin nhắn mới

Copy// Request
{
  chat_session_id: string,
  sender_type: 'guest' | 'staff',
  sender_id: string,
  content: string,
  original_language?: string
}

// Response
{
  id: string,
  content: string,
  translated_content?: string,
  is_translated: boolean,
  created_at: string
}
GET /api/messages?session_id=[id] - Lấy tin nhắn theo phiên WebSocket /api/messages/ws - Real-time messaging

4. Translation API
Endpoint: /api/translation

POST - Dịch văn bản

Copy// Request
{
  text: string,
  from_language: string,
  to_language: string,
  provider?: 'google' | 'azure' | 'aws'
}

// Response
{
  translated_text: string,
  confidence: number,
  provider: string
}
POST /api/translation/detect - Phát hiện ngôn ngữ GET /api/translation/languages - Lấy danh sách ngôn ngữ hỗ trợ

🎨 STYLING ARCHITECTURE
Global Styles
CSS Variables: Sử dụng design tokens từ @loaloa/design-tokens
SCSS Modules: Component-specific styling
Responsive Design: Mobile-first approach
Color Scheme: Orange primary (#FF4D00), neutral grays
Key Style Files:
globals.scss - Global reset, base styles, utility classes
home.module.scss - Homepage specific styles
chat-page.module.scss - Chat page layout và navigation
ChatInterface.module.scss - Main chat interface styling
LanguageSelector.module.scss - Language selector dropdown
TranslationToggle.module.scss - Translation toggle switch
Design Tokens:
Copy--color-primary: #FF4D00
--color-primary-light: #FF7A33
--color-primary-dark: #CC3D00
--color-background-primary: #FFFFFF
--color-background-secondary: #F8F9FA
--color-text-primary: #333333
--color-text-secondary: #6C757D
--border-radius-md: 8px
--border-radius-lg: 12px
--shadow-md: 0 4px 6px rgba(0,0,0,0.1)
--shadow-lg: 0 20px 40px rgba(0,0,0,0.1)
🛣️ ROUTING
Pages:
/ - Homepage với feature cards
/qr/demo - QR code demo page với language selection
/chat/[session] - Dynamic chat page với URL params:
guest - Guest ID
lang - Guest language
qr - QR code ID (optional)
Navigation Flow:
Homepage → Click "Try Demo" → QR Demo Page
QR Demo → Select language + Start Chat → Chat Page
Chat Page → Language settings panel → Update preferences
⚙️ CONFIGURATION
Next.js Config:
Copy// next.config.js
{
  output: 'standalone',
  transpilePackages: ['@loaloa/ui', '@loaloa/design-tokens'],
  webpack: (config) => {
    config.resolve.alias['@'] = path.resolve(__dirname, './app')
    return config
  }
}
Package Dependencies:
Copy{
  "dependencies": {
    "@loaloa/design-tokens": "workspace:*",
    "@loaloa/ui": "workspace:*",
    "next": "^14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@supabase/supabase-js": "^2.38.4"
  },
  "scripts": {
    "dev": "next dev -p 3002",
    "build": "next build",
    "start": "next start -p 3002"
  }
}
🚀 CURRENT STATUS
✅ Completed:
✅ Project structure setup
✅ Database schema design
✅ Homepage với feature showcase
✅ QR demo page với language selection
✅ Chat page với dynamic routing
✅ ChatInterface component với mock data
✅ LanguageSelector với 12 languages
✅ TranslationToggle component
✅ useChat hook cho state management
✅ Responsive design cho mobile
✅ Language settings integration
✅ Navigation flow hoàn chỉnh
🚧 In Progress:
Real-time messaging với WebSocket
API endpoints implementation
Translation service integration
File upload functionality
⏳ Planned:
Voice message support
Message search và pagination
Emoji picker
Message reactions
Offline support
PWA configuration
Performance optimization
Testing suite
Deployment setup
🧪 TESTING
Current Test URLs:
Homepage: http://localhost:3002
QR Demo: http://localhost:3002/qr/demo
Chat (auto-generated): http://localhost:3002/chat/new?guest=guest-123&lang=en&qr=demo-qr-001
Test Scenarios:
Language Selection: Test all 12 languages trong LanguageSelector
Translation Toggle: Enable/disable auto-translation
Chat Flow: Send messages, receive auto-replies
Responsive: Test trên mobile devices
Navigation: Back button, End chat functionality
Error Handling: Invalid session IDs, connection errors
🔄 NEXT STEPS
Priority 1 - API Implementation:
Tạo API endpoints cho chat sessions
Implement real-time messaging với Supabase
Translation service integration
Database operations
Priority 2 - Advanced Features:
File upload với attachments
Voice message support
Message status indicators
Chat history pagination
Priority 3 - Optimization:
Performance monitoring
Error tracking
Offline support
PWA features
File này được tạo vào: 2025-01-23
Phiên bản: v1.0
Tác giả: LoaLoa Development Team
Mục đích: Documentation cho phiên chat tiếp theo