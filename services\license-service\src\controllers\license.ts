import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcrypt';
import { db } from '../services/db';
import { emailService } from '../services/email';
import logger from '../utils/logger';
import { LicenseActivityType, LicenseStatus, CloneStatus, EmailVerificationPayload } from '../types';

// Helper function to generate a license key
const generateLicenseKey = (): string => {
  const prefix = 'LLHM';
  const segments = [
    prefix,
    ...Array(4).fill(0).map(() => 
      Math.random().toString(36).substring(2, 7).toUpperCase()
    )
  ];
  
  return segments.join('-');
};

export const licenseController = {
  // Create a new license
  async create(req: Request, res: Response) {
    try {
      const {
        customerName,
        customerEmail,
        expiryDays = 365,
        isActive = true,
        metadata = {}
      } = req.body;
      
      if (!customerName) {
        return res.status(400).json({
          success: false,
          message: 'Customer name is required'
        });
      }
      
      // Generate a new license key
      const licenseKey = generateLicenseKey();
      
      // Calculate expiry date
      const issueDate = new Date();
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + expiryDays);
      
      // Create license
      const license = await db.licenses.create({
        license_key: licenseKey,
        customer_name: customerName,
        customer_email: customerEmail,
        issue_date: issueDate.toISOString(),
        expiry_date: expiryDate.toISOString(),
        is_active: isActive,
        check_in_count: 0,
        metadata
      });
      
      if (!license) {
        return res.status(500).json({
          success: false,
          message: 'Failed to create license'
        });
      }
      
      // Log license creation
      await db.activities.create({
        license_id: license.id,
        activity_type: LicenseActivityType.ACTIVATION,
        details: { message: 'License created' }
      });
      
      return res.status(201).json({
        success: true,
        message: 'License created successfully',
        data: license
      });
    } catch (error) {
      logger.error('Error creating license', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while creating the license'
      });
    }
  },
  
  // Get license by ID
  async getById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      const license = await db.licenses.getById(id);
      
      if (!license) {
        return res.status(404).json({
          success: false,
          message: 'License not found'
        });
      }
      
      return res.json({
        success: true,
        data: license
      });
    } catch (error) {
      logger.error('Error getting license by ID', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while fetching the license'
      });
    }
  },
  
  // Get all licenses with pagination
  async getAll(req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const status = req.query.status as string;
      const search = req.query.search as string;
      
      const { data, count } = await db.licenses.listAll(page, limit, status, search);
      
      // Calculate days remaining and status for each license
      const licensesWithStatus = data.map(license => {
        const expiryDate = new Date(license.expiry_date);
        const currentDate = new Date();
        const diffDays = Math.ceil((expiryDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
        
        let status = LicenseStatus.NOT_ACTIVATED;
        
        if (!license.is_active) {
          status = LicenseStatus.REVOKED;
        } else if (diffDays <= 0) {
          status = LicenseStatus.EXPIRED;
        } else if (diffDays <= 30) {
          status = LicenseStatus.EXPIRING_SOON;
        } else if (license.activation_date) {
          status = LicenseStatus.ACTIVE;
        }
        
        return {
          ...license,
          days_remaining: Math.max(0, diffDays),
          status
        };
      });
      
      return res.json({
        success: true,
        data: licensesWithStatus,
        meta: {
          total: count,
          page,
          limit,
          pageCount: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      logger.error('Error getting all licenses', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while fetching licenses'
      });
    }
  },
  
  // Update license
  async update(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const updates = req.body;
      
      // Don't allow license_key to be updated
      if (updates.license_key) {
        delete updates.license_key;
      }
      
      const license = await db.licenses.getById(id);
      
      if (!license) {
        return res.status(404).json({
          success: false,
          message: 'License not found'
        });
      }
      
      const updatedLicense = await db.licenses.update(id, updates);
      
      if (!updatedLicense) {
        return res.status(500).json({
          success: false,
          message: 'Failed to update license'
        });
      }
      
      // Log license update
      await db.activities.create({
        license_id: id,
        activity_type: LicenseActivityType.ACTIVATION,
        details: { message: 'License updated', updates }
      });
      
      return res.json({
        success: true,
        message: 'License updated successfully',
        data: updatedLicense
      });
    } catch (error) {
      logger.error('Error updating license', { error });
      return res.status(500).json({
        success: false,
         message: 'An error occurred while updating the license'
      });
    }
  },
  
  // Revoke license
  async revoke(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      
      const license = await db.licenses.getById(id);
      
      if (!license) {
        return res.status(404).json({
          success: false,
          message: 'License not found'
        });
      }
      
      const updatedLicense = await db.licenses.update(id, {
        is_active: false,
        revocation_reason: reason || 'License revoked by admin'
      });
      
      if (!updatedLicense) {
        return res.status(500).json({
          success: false,
          message: 'Failed to revoke license'
        });
      }
      
      // Log license revocation
      await db.activities.create({
        license_id: id,
        activity_type: LicenseActivityType.REVOCATION,
        details: { reason }
      });
      
      return res.json({
        success: true,
        message: 'License revoked successfully',
        data: updatedLicense
      });
    } catch (error) {
      logger.error('Error revoking license', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while revoking the license'
      });
    }
  },
  
  // Extend license expiry date
  async extend(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { days } = req.body;
      
      if (!days || days <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Valid number of days is required'
        });
      }
      
      const license = await db.licenses.getById(id);
      
      if (!license) {
        return res.status(404).json({
          success: false,
          message: 'License not found'
        });
      }
      
      // Calculate new expiry date
      const currentExpiryDate = new Date(license.expiry_date);
      const newExpiryDate = new Date(currentExpiryDate);
      newExpiryDate.setDate(newExpiryDate.getDate() + days);
      
      const updatedLicense = await db.licenses.update(id, {
        expiry_date: newExpiryDate.toISOString()
      });
      
      if (!updatedLicense) {
        return res.status(500).json({
          success: false,
          message: 'Failed to extend license'
        });
      }
      
      // Log license extension
      await db.activities.create({
        license_id: id,
        activity_type: LicenseActivityType.ACTIVATION,
        details: { message: `License extended by ${days} days` }
      });
      
      return res.json({
        success: true,
        message: `License extended by ${days} days`,
        data: updatedLicense
      });
    } catch (error) {
      logger.error('Error extending license', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while extending the license'
      });
    }
  },
  
  // Activate a license
  async activate(req: Request, res: Response) {
    try {
      const { license_key, customer_name, customer_email, hardware_fingerprint } = req.body;
      const ip = req.ip || req.socket.remoteAddress;
      
      // Validate required fields
      if (!license_key || !customer_name || !customer_email || !hardware_fingerprint) {
        return res.status(400).json({
          success: false,
          message: 'License key, customer name, email, and hardware fingerprint are required'
        });
      }
      
      // Get the license
      const license = await db.licenses.getByKey(license_key);
      
      if (!license) {
        return res.status(404).json({
          success: false,
          message: 'License not found or invalid'
        });
      }
      
      if (!license.is_active) {
        return res.status(403).json({
          success: false,
          message: 'This license has been revoked'
        });
      }
      
      // Check if license is expired
      const expiryDate = new Date(license.expiry_date);
      if (expiryDate < new Date()) {
        return res.status(403).json({
          success: false,
          message: 'This license has expired'
        });
      }
      
      // Check if customer name matches
      if (license.customer_name !== customer_name) {
        return res.status(403).json({
          success: false,
          message: 'Customer name does not match license'
        });
      }
      
      // Check if license already activated with a different hardware fingerprint
      if (license.activation_date && license.hardware_fingerprint && 
          license.hardware_fingerprint !== hardware_fingerprint) {
        // Detect potential clone
        await db.clones.create({
          license_id: license.id,
          original_fingerprint: license.hardware_fingerprint,
          clone_fingerprint: hardware_fingerprint,
          status: CloneStatus.DETECTED
        });
        
        // Log clone detection
        await db.activities.create({
          license_id: license.id,
          activity_type: LicenseActivityType.VIOLATION,
          hardware_fingerprint,
          ip_address: ip,
          details: { 
            message: 'Potential clone detected',
            original_fingerprint: license.hardware_fingerprint
          }
        });
        
        // For the MVP, we'll still allow activation but log the potential clone
        logger.warn('Potential clone detected', {
          license_id: license.id,
          license_key,
          original_fingerprint: license.hardware_fingerprint,
          new_fingerprint: hardware_fingerprint
        });
      }
      
      // Create activation token
      const activationToken = uuidv4();
      const tokenHash = await bcrypt.hash(activationToken, 10);
      
      // Create activation record
      const activation = await db.activations.create({
        license_id: license.id,
        activation_token: tokenHash,
        activation_email: customer_email,
        hardware_fingerprint,
        ip_address: ip
      });
      
      if (!activation) {
        return res.status(500).json({
          success: false,
          message: 'Failed to create activation record'
        });
      }
      
      // Create email verification payload
      const emailPayload: EmailVerificationPayload = {
        activationId: activation.id,
        token: activationToken,
        licenseId: license.id,
        email: customer_email
      };
      
      // Send activation email
      const emailSent = await emailService.sendActivationEmail(
        customer_email,
        customer_name,
        license_key,
        emailPayload
      );
      
      if (!emailSent) {
        return res.status(500).json({
          success: false,
          message: 'Failed to send activation email'
        });
      }
      
      // Log activation request
      await db.activities.create({
        license_id: license.id,
        activity_type: LicenseActivityType.ACTIVATION,
        hardware_fingerprint,
        ip_address: ip,
        details: {
          message: 'Activation email sent',
          email: customer_email
        }
      });
      
      return res.json({
        success: true,
        message: 'Activation email sent. Please check your email to complete activation.',
        activation_id: activation.id,
        requires_email_verification: true
      });
    } catch (error) {
      logger.error('Error activating license', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while activating the license'
      });
    }
  },
  
  // Verify email activation
  async verifyActivation(req: Request, res: Response) {
    try {
      const { activationId, token } = req.body;
      
      if (!activationId || !token) {
        return res.status(400).json({
          success: false,
          message: 'Activation ID and token are required'
        });
      }
      
      // Get activation record
      const activation = await db.activations.getById(activationId);
      
      if (!activation) {
        return res.status(404).json({
          success: false,
          message: 'Activation record not found'
        });
      }
      
      if (activation.is_used) {
        return res.status(403).json({
          success: false,
          message: 'Activation link has already been used'
        });
      }
      
      // Check if expired
      const expiresAt = new Date(activation.expires_at);
      if (expiresAt < new Date()) {
        return res.status(403).json({
          success: false,
          message: 'Activation link has expired'
        });
      }
      
      // Verify token
      const isValidToken = await bcrypt.compare(token, activation.activation_token);
      
      if (!isValidToken) {
        return res.status(403).json({
          success: false,
          message: 'Invalid activation token'
        });
      }
      
      // Get license
      const license = await db.licenses.getById(activation.license_id);
      
      if (!license) {
        return res.status(404).json({
          success: false,
          message: 'License not found'
        });
      }
      
      if (!license.is_active) {
        return res.status(403).json({
          success: false,
          message: 'This license has been revoked'
        });
      }
      
      // Update license with hardware fingerprint and activation date
      const now = new Date().toISOString();
      const updatedLicense = await db.licenses.update(license.id, {
        hardware_fingerprint: activation.hardware_fingerprint,
        activation_date: now,
        last_check_in: now
      });
      
      if (!updatedLicense) {
        return res.status(500).json({
          success: false,
          message: 'Failed to update license'
        });
      }
      
      // Mark activation as used
      await db.activations.markAsUsed(activationId);
      
      // Log successful activation
      await db.activities.create({
        license_id: license.id,
        activity_type: LicenseActivityType.ACTIVATION,
        hardware_fingerprint: activation.hardware_fingerprint,
        ip_address: activation.ip_address,
        details: {
          message: 'License activated successfully',
          email: activation.activation_email
        }
      });
      
      return res.json({
        success: true,
        message: 'License activated successfully',
        license: updatedLicense
      });
    } catch (error) {
      logger.error('Error verifying activation', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while verifying activation'
      });
    }
  },
  
  // Check-in a license
  async checkIn(req: Request, res: Response) {
    try {
      const { license_key, hardware_fingerprint } = req.body;
      const ip = req.ip || req.socket.remoteAddress;
      
      if (!license_key || !hardware_fingerprint) {
        return res.status(400).json({
          success: false,
          message: 'License key and hardware fingerprint are required'
        });
      }
      
      // Get the license
      const license = await db.licenses.getByKey(license_key);
      
      if (!license) {
        return res.status(404).json({
          success: false,
          message: 'License not found or invalid'
        });
      }
      
      if (!license.is_active) {
        return res.status(403).json({
          success: false,
          message: 'This license has been revoked',
          is_valid: false
        });
      }
      
      // Check if license is expired
      const expiryDate = new Date(license.expiry_date);
      if (expiryDate < new Date()) {
        return res.status(403).json({
          success: false,
          message: 'This license has expired',
          is_valid: false
        });
      }
      
      // Check for hardware fingerprint mismatch
      if (license.hardware_fingerprint && license.hardware_fingerprint !== hardware_fingerprint) {
        // Detect potential clone
        await db.clones.create({
          license_id: license.id,
          original_fingerprint: license.hardware_fingerprint,
          clone_fingerprint: hardware_fingerprint,
          status: CloneStatus.DETECTED
        });
        
        // Log clone detection
        await db.activities.create({
          license_id: license.id,
          activity_type: LicenseActivityType.VIOLATION,
          hardware_fingerprint,
          ip_address: ip,
          details: {
            message: 'Potential clone detected during check-in',
            original_fingerprint: license.hardware_fingerprint
          }
        });
        
        // For MVP, we still allow check-in but log the clone
        logger.warn('Potential clone detected during check-in', {
          license_id: license.id,
          license_key,
          original_fingerprint: license.hardware_fingerprint,
          new_fingerprint: hardware_fingerprint
        });
      }
      
      // Update last_check_in and increment check_in_count
      const updatedLicense = await db.licenses.update(license.id, {
        last_check_in: new Date().toISOString(),
        check_in_count: (license.check_in_count || 0) + 1
      });
      
      if (!updatedLicense) {
        return res.status(500).json({
          success: false,
          message: 'Failed to update license',
          is_valid: false
        });
      }
      
      // Log check-in
      await db.activities.create({
        license_id: license.id,
        activity_type: LicenseActivityType.CHECK_IN,
        hardware_fingerprint,
        ip_address: ip,
        details: {
          message: 'License checked in successfully',
          system_info: req.body.system_info || {}
        }
      });
      
      // Calculate days remaining
      const currentDate = new Date();
      const diffDays = Math.ceil((expiryDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
      
      return res.json({
        success: true,
        message: 'License checked in successfully',
        is_valid: true,
        days_remaining: Math.max(0, diffDays)
      });
    } catch (error) {
      logger.error('Error checking in license', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while checking in the license',
        is_valid: false
      });
    }
  },
  
  // Validate a license
  async validate(req: Request, res: Response) {
    try {
      const { license_key, hardware_fingerprint } = req.body;
      const ip = req.ip || req.socket.remoteAddress;
      
      if (!license_key || !hardware_fingerprint) {
        return res.status(400).json({
          success: false,
          message: 'License key and hardware fingerprint are required',
          is_valid: false
        });
      }
      
      // Get the license
      const license = await db.licenses.getByKey(license_key);
      
      if (!license) {
        return res.status(404).json({
          success: false,
          message: 'License not found or invalid',
          is_valid: false
        });
      }
      
      if (!license.is_active) {
        return res.status(403).json({
          success: false,
          message: 'This license has been revoked',
          is_valid: false
        });
      }
      
      // Check if license is expired
      const expiryDate = new Date(license.expiry_date);
      if (expiryDate < new Date()) {
        return res.status(403).json({
          success: false,
          message: 'This license has expired',
          is_valid: false
        });
      }
      
      // Check for hardware fingerprint mismatch
      let hardwareMatch = true;
      if (license.hardware_fingerprint && license.hardware_fingerprint !== hardware_fingerprint) {
        hardwareMatch = false;
        
        // Log validation with mismatched hardware
        await db.activities.create({
          license_id: license.id,
          activity_type: LicenseActivityType.WARNING,
          hardware_fingerprint,
          ip_address: ip,
          details: {
            message: 'License validation with mismatched hardware fingerprint',
            original_fingerprint: license.hardware_fingerprint
          }
        });
      } else {
        // Log successful validation
        await db.activities.create({
          license_id: license.id,
          activity_type: LicenseActivityType.CHECK_IN,
          hardware_fingerprint,
          ip_address: ip,
          details: {
            message: 'License validated successfully'
          }
        });
      }
      
      // Calculate days remaining
      const currentDate = new Date();
      const diffDays = Math.ceil((expiryDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
      
      return res.json({
        success: true,
        message: hardwareMatch ? 'License is valid' : 'License is valid but hardware fingerprint does not match',
        is_valid: true,
        hardware_match: hardwareMatch,
        license: {
          ...license,
          days_remaining: Math.max(0, diffDays)
        }
      });
    } catch (error) {
      logger.error('Error validating license', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while validating the license',
        is_valid: false
      });
    }
  },
  
  // Get license activities
  async activities(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      
      const license = await db.licenses.getById(id);
      
      if (!license) {
        return res.status(404).json({
          success: false,
          message: 'License not found'
        });
      }
      
      const { data, count } = await db.activities.listByLicenseId(id, page, limit);
      
      return res.json({
        success: true,
        data,
        meta: {
          total: count,
          page,
          limit,
          pageCount: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      logger.error('Error getting license activities', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while fetching license activities'
      });
    }
  },
  
  // Get license clones
  async clones(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      
      const license = await db.licenses.getById(id);
      
      if (!license) {
        return res.status(404).json({
          success: false,
          message: 'License not found'
        });
      }
      
      const { data, count } = await db.clones.listByLicenseId(id, page, limit);
      
      return res.json({
        success: true,
        data,
        meta: {
          total: count,
          page,
          limit,
          pageCount: Math.ceil(count / limit)
        }
      });
    } catch (error) {
      logger.error('Error getting license clones', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while fetching license clones'
      });
    }
  },
  
  // Update clone status
  async updateCloneStatus(req: Request, res: Response) {
    try {
      const { id, cloneId } = req.params;
      const { status } = req.body;
      
      if (!Object.values(CloneStatus).includes(status)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid clone status'
        });
      }
      
      const license = await db.licenses.getById(id);
      
      if (!license) {
        return res.status(404).json({
          success: false,
          message: 'License not found'
        });
      }
      
      const updatedClone = await db.clones.update(cloneId, status);
      
      if (!updatedClone) {
        return res.status(500).json({
          success: false,
          message: 'Failed to update clone status'
        });
      }
      
      // If clone was confirmed, take action based on status
      if (status === CloneStatus.CONFIRMED) {
        // For MVP we don't automatically revoke the license
        // but we could add that logic here if desired
        
        // Log clone confirmation
        await db.activities.create({
          license_id: id,
          activity_type: LicenseActivityType.VIOLATION,
          hardware_fingerprint: updatedClone.clone_fingerprint,
          details: {
            message: 'Clone confirmed by admin',
            original_fingerprint: updatedClone.original_fingerprint
          }
        });
      }
      
      return res.json({
        success: true,
        message: 'Clone status updated successfully',
        data: updatedClone
      });
    } catch (error) {
      logger.error('Error updating clone status', { error });
      return res.status(500).json({
        success: false,
        message: 'An error occurred while updating clone status'
      });
    }
  }
};