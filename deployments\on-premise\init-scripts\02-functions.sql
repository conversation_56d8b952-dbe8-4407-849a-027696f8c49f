-- <PERSON><PERSON><PERSON> tạo mới schema cho tenant
CREATE OR REPLACE FUNCTION public.create_tenant_schema() RETURNS TRIGGER AS $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Tạo tên schema từ tenant name (loại bỏ khoảng trắng và ký tự đặc biệt)
    schema_name := 'tenant_' || regexp_replace(lower(NEW.name), '[^a-z0-9]', '_', 'g');
    
    -- Kiểm tra nếu schema đã tồn tại thì thêm ID để đảm bảo duy nhất
    IF EXISTS (SELECT 1 FROM public.tenant_schemas WHERE schema_name = schema_name) THEN
        schema_name := schema_name || '_' || replace(NEW.id::text, '-', '');
    END IF;
    
    -- Tạo schema mới
    EXECUTE 'CREATE SCHEMA IF NOT EXISTS ' || schema_name;
    
    -- <PERSON><PERSON><PERSON> thông tin schema vào bảng quản lý
    INSERT INTO public.tenant_schemas (tenant_id, schema_name)
    VALUES (NEW.id, schema_name);
    
    -- Tạo các bảng trong schema mới
    PERFORM public.create_tenant_tables(schema_name);
    
    RETURN NEW;
END;

$$ LANGUAGE plpgsql;

-- Hàm tạo các bảng trong schema tenant
CREATE OR REPLACE FUNCTION public.create_tenant_tables(schema_name TEXT) RETURNS VOID AS $$
BEGIN
    -- Tạo bảng users
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.users (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            auth_id UUID UNIQUE,
            email VARCHAR(255) UNIQUE,
            full_name VARCHAR(255),
            role VARCHAR(50),
            is_active BOOLEAN DEFAULT TRUE,
            last_login TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        )', schema_name);
    
    -- Tạo bảng chat_rooms
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.chat_rooms (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name VARCHAR(255),
            description TEXT,
            created_by UUID REFERENCES %I.users(id),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        )', schema_name, schema_name);
    
    -- Tạo bảng chat_messages
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.chat_messages (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            room_id UUID REFERENCES %I.chat_rooms(id),
            user_id UUID REFERENCES %I.users(id),
            content TEXT NOT NULL,
            language VARCHAR(10),
            created_at TIMESTAMPTZ DEFAULT NOW()
        )', schema_name, schema_name, schema_name);
    
    -- Tạo bảng translations
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.translations (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            message_id UUID REFERENCES %I.chat_messages(id),
            language VARCHAR(10) NOT NULL,
            translated_content TEXT NOT NULL,
            created_at TIMESTAMPTZ DEFAULT NOW()
        )', schema_name, schema_name);
END;

$$ LANGUAGE plpgsql;

-- Hàm tạo license key
CREATE OR REPLACE FUNCTION public.generate_license_key() RETURNS TEXT AS $$
DECLARE
    key_parts TEXT[];
    i INTEGER;
    result TEXT;
BEGIN
    key_parts := ARRAY['LLHM'];
    
    -- Tạo 4 phần ngẫu nhiên, mỗi phần 5 ký tự
    FOR i IN 1..4 LOOP
        key_parts := array_append(
            key_parts, 
            upper(
                substring(
                    md5(random()::text) from 1 for 5
                )
            )
        );
    END LOOP;
    
    -- Ghép các phần lại với nhau bằng dấu gạch ngang
    result := array_to_string(key_parts, '-');
    
    RETURN result;
END;

$$ LANGUAGE plpgsql;

-- Hàm quản lý dịch tự động
CREATE OR REPLACE FUNCTION public.manage_automatic_translations() RETURNS TRIGGER AS $$
DECLARE
    target_langs TEXT[];
    source_lang TEXT;
    r RECORD;
BEGIN
    -- Lấy ngôn ngữ nguồn của tin nhắn
    source_lang := NEW.language;
    
    -- Ví dụ danh sách các ngôn ngữ cần dịch (trong thực tế sẽ lấy từ cài đặt)
    target_langs := ARRAY['en', 'vi', 'ja', 'ko', 'zh'];
    
    -- Loại bỏ ngôn ngữ nguồn khỏi danh sách đích
    target_langs := array_remove(target_langs, source_lang);
    
    -- Tạo các bản ghi dịch (trong môi trường thực tế sẽ gọi dịch vụ dịch)
    FOREACH r IN ARRAY target_langs LOOP
        -- Ở đây chúng ta sẽ giả lập việc dịch, trong thực tế bạn sẽ gọi API dịch
        EXECUTE format(
            'INSERT INTO %I.translations (message_id, language, translated_content)
             VALUES ($1, $2, $3)',
            TG_TABLE_SCHEMA
        ) USING NEW.id, r, 'Translated: ' || NEW.content || ' (' || r || ')';
    END LOOP;
    
    RETURN NEW;
END;

$$ LANGUAGE plpgsql;
