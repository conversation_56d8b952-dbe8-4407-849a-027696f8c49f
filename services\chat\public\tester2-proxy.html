<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Loa<PERSON>oa Chat Tester (Proxy Version)</title>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <style>
    body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
    .panel { background: white; padding: 15px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    .panel-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
    h3 { margin: 0; }
    .status { font-weight: bold; padding: 4px 8px; border-radius: 4px; font-size: 14px; }
    .pending { background: #ffeaa7; color: #d35400; }
    .success { background: #c4e6ca; color: #2d6a4f; }
    .error { background: #ffcccc; color: #cc0000; }
    button { background: #4CAF50; color: white; border: none; padding: 8px 12px; cursor: pointer; margin-right: 5px; border-radius: 4px; }
    .log { background: #f5f5f5; border: 1px solid #ddd; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; margin-top: 10px; }
  </style>
</head>
<body>
  <h1>LoaLoa Chat Tester (Proxy Version)</h1>
  
  <div class="panel">
    <div class="panel-header">
      <h3>Database Connection</h3>
      <span class="status pending" id="db-status">Pending</span>
    </div>
    <div>
      <button id="load-users">Load Users</button>
      <button id="load-rooms">Load Rooms</button>
      <button id="load-participants">Load Participants</button>
      <button id="load-messages">Load Messages</button>
    </div>
    <div class="log" id="db-log"></div>
  </div>
  
  <div class="panel">
    <div class="panel-header">
      <h3>WebSocket Connection</h3>
      <span class="status pending" id="ws-status">Pending</span>
    </div>
    <div>
      <button id="connect-ws">Connect WebSocket</button>
      <button id="join-room">Join Room</button>
    </div>
    <div class="log" id="ws-log"></div>
  </div>
  
  <script>
    // DOM Elements
    const dbStatus = document.getElementById('db-status');
    const wsStatus = document.getElementById('ws-status');
    const dbLog = document.getElementById('db-log');
    const wsLog = document.getElementById('ws-log');
    
    // Config
    const config = {
      proxyURL: 'http://localhost:3010',
      wsURL: 'http://localhost:3002',
      roomId: 'c636ea40-1d87-4982-a6a3-86fa0805e258',
      userId1: '973c8e99-9b06-437a-9ab5-e4bdf2aa4b53',
      userId2: 'a9813ae-9a46-4dc9-9fa3-6f04062f7e50'
    };
    
    // Add log entry
    function addLog(element, message) {
      const entry = document.createElement('div');
      entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      element.appendChild(entry);
      element.scrollTop = element.scrollHeight;
    }
    
    // Update status
    function updateStatus(element, status) {
      element.className = `status ${status}`;
      element.textContent = status.charAt(0).toUpperCase() + status.slice(1);
    }
    
    // Load Users
    document.getElementById('load-users').addEventListener('click', async () => {
      try {
        addLog(dbLog, 'Loading users...');
        const response = await fetch(`${config.proxyURL}/api/users`);
        const data = await response.json();
        
        if (data.success) {
          updateStatus(dbStatus, 'success');
          addLog(dbLog, `Loaded ${data.data.length} users successfully`);
          addLog(dbLog, JSON.stringify(data.data.slice(0, 2)));
        } else {
          updateStatus(dbStatus, 'error');
          addLog(dbLog, `Error loading users: ${data.error}`);
        }
      } catch (error) {
        updateStatus(dbStatus, 'error');
        addLog(dbLog, `Error: ${error.message}`);
      }
    });
    
    // Load Rooms
    document.getElementById('load-rooms').addEventListener('click', async () => {
      try {
        addLog(dbLog, 'Loading rooms...');
        const response = await fetch(`${config.proxyURL}/api/rooms`);
        const data = await response.json();
        
        if (data.success) {
          updateStatus(dbStatus, 'success');
          addLog(dbLog, `Loaded ${data.data.length} rooms successfully`);
          addLog(dbLog, JSON.stringify(data.data.slice(0, 2)));
        } else {
          updateStatus(dbStatus, 'error');
          addLog(dbLog, `Error loading rooms: ${data.error}`);
        }
      } catch (error) {
        updateStatus(dbStatus, 'error');
        addLog(dbLog, `Error: ${error.message}`);
      }
    });
    
    // Load Participants
    document.getElementById('load-participants').addEventListener('click', async () => {
      try {
        addLog(dbLog, 'Loading participants...');
        const response = await fetch(`${config.proxyURL}/api/participants`);
        const data = await response.json();
        
        if (data.success) {
          updateStatus(dbStatus, 'success');
          addLog(dbLog, `Loaded ${data.data.length} participants successfully`);
          addLog(dbLog, JSON.stringify(data.data.slice(0, 2)));
        } else {
          updateStatus(dbStatus, 'error');
          addLog(dbLog, `Error loading participants: ${data.error}`);
        }
      } catch (error) {
        updateStatus(dbStatus, 'error');
        addLog(dbLog, `Error: ${error.message}`);
      }
    });
    
    // Load Messages
    document.getElementById('load-messages').addEventListener('click', async () => {
      try {
        addLog(dbLog, 'Loading messages...');
        const response = await fetch(`${config.proxyURL}/api/messages`);
        const data = await response.json();
        
        if (data.success) {
          updateStatus(dbStatus, 'success');
          addLog(dbLog, `Loaded ${data.data.length} messages successfully`);
          addLog(dbLog, JSON.stringify(data.data.slice(0, 1)));
        } else {
          updateStatus(dbStatus, 'error');
          addLog(dbLog, `Error loading messages: ${data.error}`);
        }
      } catch (error) {
        updateStatus(dbStatus, 'error');
        addLog(dbLog, `Error: ${error.message}`);
      }
    });
    
    // WebSocket variables
    let socket = null;
    
    // Connect WebSocket
    document.getElementById('connect-ws').addEventListener('click', () => {
      try {
        addLog(wsLog, `Connecting to WebSocket server at ${config.wsURL}...`);
        
        // Create new socket connection
        socket = io(config.wsURL);
        
        socket.on('connect', () => {
          updateStatus(wsStatus, 'success');
          addLog(wsLog, 'Connected to WebSocket server!');
          
          // Setup user
          socket.emit('setup', {
            userId: config.userId1,
            preferredLanguage: 'vi',
            deviceId: `test-device-${Date.now()}`
          }, (response) => {
            if (response.success) {
              addLog(wsLog, `Setup success: ${JSON.stringify(response.data)}`);
            } else {
              addLog(wsLog, `Setup failed: ${response.error}`);
            }
          });
        });
        
        socket.on('disconnect', () => {
          updateStatus(wsStatus, 'error');
          addLog(wsLog, 'Disconnected from WebSocket server');
        });
        
        socket.on('error', (error) => {
          updateStatus(wsStatus, 'error');
          addLog(wsLog, `WebSocket error: ${JSON.stringify(error)}`);
        });
        
        socket.on('message_received', (data) => {
          addLog(wsLog, `Received message: ${JSON.stringify(data.message)}`);
        });
      } catch (error) {
        updateStatus(wsStatus, 'error');
        addLog(wsLog, `Error connecting to WebSocket: ${error.message}`);
      }
    });
    
    // Join Room
    document.getElementById('join-room').addEventListener('click', () => {
      if (!socket || !socket.connected) {
        addLog(wsLog, 'Please connect to WebSocket first');
        return;
      }
      
      addLog(wsLog, `Joining room ${config.roomId}...`);
      
      socket.emit('join_room', config.roomId, (response) => {
        if (response.success) {
          addLog(wsLog, `Successfully joined room: ${JSON.stringify(response.data.room)}`);
        } else {
          addLog(wsLog, `Failed to join room: ${response.error}`);
        }
      });
    });
  </script>
</body>
</html>