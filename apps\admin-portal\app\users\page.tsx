'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import styles from './users.module.scss';
import { debounce } from 'lodash';

import DashboardLayout from '../dashboard-layout';
import { Button, SearchBar, StatusBadge, Pagination, DeleteConfirmModal } from '@ui';

// Định nghĩa kiểu cho User
interface User {
  id: string;
  email: string;
  display_name: string | null;
  avatar_url: string | null;
  phone: string | null;
  role: string;
  is_active: boolean;
  joined_at: string;
  last_login_at: string | null;
  expiry_date: string | null;
}

// Định nghĩa kiểu cho pagination metadata
interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  pageCount: number;
}

const UsersPage = () => {
  // State
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState<string>('');
  const [role, setRole] = useState<string>('');
  const [status, setStatus] = useState<string>('');
  const [page, setPage] = useState<number>(1);
  const [meta, setMeta] = useState<PaginationMeta>({
    total: 0,
    page: 1,
    limit: 10,
    pageCount: 0
  });
  const [userToDelete, setUserToDelete] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  
  const router = useRouter();
  
  // Function để lấy danh sách người dùng
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Tạo query params
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', '10');
      
      if (search) params.append('search', search);
      if (role) params.append('role', role);
      if (status) params.append('status', status);
      
      // Gọi API
      const response = await fetch(`/api/users?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch users');
      }
      
      const data = await response.json();
      setUsers(data.data || []);
      setMeta(data.meta || { total: 0, page: 1, limit: 10, pageCount: 0 });
    } catch (err: any) {
      setError(err.message);
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  }, [page, search, role, status]);
  
  // Debounce search để tránh gọi API quá nhiều lần
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearch(value);
      setPage(1); // Reset về trang đầu tiên khi search
    }, 500),
    []
  );
  
  // Handle search change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(e.target.value);
  };
  
  // Handle filter change
  const handleRoleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setRole(e.target.value);
    setPage(1);
  };
  
  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatus(e.target.value);
    setPage(1);
  };
  
  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };
  
  // Delete user
  const handleDeleteClick = (userId: string) => {
    setUserToDelete(userId);
    setShowDeleteModal(true);
    setDeleteError(null);
  };
  
  const handleConfirmDelete = async () => {
    if (!userToDelete) return;
    
    try {
      const response = await fetch(`/api/users/${userToDelete}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete user');
      }
      
      // Reload users after delete
      fetchUsers();
      setShowDeleteModal(false);
      setUserToDelete(null);
    } catch (err: any) {
      setDeleteError(err.message);
      console.error('Error deleting user:', err);
    }
  };
  
  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setUserToDelete(null);
    setDeleteError(null);
  };
  
  // Fetch users on component mount and when dependencies change
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);
  
  // Format role text for display
  const formatRole = (role: string) => {
    return role.charAt(0).toUpperCase() + role.slice(1);
  };
  
  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Users Management</h1>
          <p className={styles.description}>Manage user accounts and permissions within your property</p>
        </div>
        
        <div className={styles.controls}>
          <div className={styles.searchAndFilters}>
            <SearchBar 
              placeholder="Search by name or email..." 
              onChange={handleSearchChange} 
              className={styles.search}
            />
            
            <div className={styles.filters}>
              <div className={styles.filterItem}>
                <label>Role:</label>
                <select onChange={handleRoleChange} value={role}>
                  <option value="">All Roles</option>
                  <option value="admin">Admin</option>
                  <option value="manager">Manager</option>
                  <option value="user">User</option>
                </select>
              </div>
              
              <div className={styles.filterItem}>
                <label>Status:</label>
                <select onChange={handleStatusChange} value={status}>
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
          </div>
          
          <Link href="/users/create">
            <Button variant="primary" label="Add New">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M8 3.33334V12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3.33334 8H12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Add User
            </Button>
          </Link>
        </div>
        
        {error && (
          <div className={styles.error}>
            <p>{error}</p>
            <button onClick={fetchUsers}>Try Again</button>
          </div>
        )}
        
        <div className={styles.tableWrapper}>
          <table className={styles.table}>
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Status</th>
                <th>Joined</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={6} className={styles.loading}>Loading...</td>
                </tr>
              ) : users.length === 0 ? (
                <tr>
                  <td colSpan={6} className={styles.noData}>
                    <div className={styles.emptyState}>
                      <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="8" r="5"/>
                        <path d="M20 21v-2a7 7 0 0 0-14 0v2"/>
                      </svg>
                      <h3>No users found</h3>
                      <p>
                        {search || role || status ? 
                          'No users match your selected filters.' : 
                          'Start by adding your first user.'}
                      </p>
                      <Link href="/users/create">
                        <Button variant="primary" label="Add First User">Add First User</Button>
                      </Link>
                    </div>
                  </td>
                </tr>
              ) : (
                users.map((user) => (
                  <tr key={user.id}>
                    <td>
                      <div className={styles.userInfo}>
                        <div className={styles.avatar}>
                          {user.avatar_url ? (
                            <img src={user.avatar_url} alt={user.display_name || ''} />
                          ) : (
                            <span>{(user.display_name || user.email.charAt(0)).charAt(0).toUpperCase()}</span>
                          )}
                        </div>
                        <span>{user.display_name || user.email.split('@')[0]}</span>
                      </div>
                    </td>
                    <td>{user.email}</td>
                    <td>
                      <span className={`${styles.role} ${styles[user.role || 'user']}`}>
                        {formatRole(user.role || 'user')}
                      </span>
                    </td>
                    <td>
                      <StatusBadge status={user.is_active ? 'active' : 'inactive'}>
                        {user.is_active ? 'Active' : 'Inactive'}
                      </StatusBadge>
                    </td>
                    <td>{new Date(user.joined_at).toLocaleDateString()}</td>
                    <td>
                      <div className={styles.actions}>
                        <Link href={`/users/${user.id}`} className={styles.viewButton}>
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M1.33334 8C1.33334 8 3.33334 3.33334 8.00001 3.33334C12.6667 3.33334 14.6667 8 14.6667 8C14.6667 8 12.6667 12.6667 8.00001 12.6667C3.33334 12.6667 1.33334 8 1.33334 8Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M8 10C9.10457 10 10 9.10457 10 8C10 6.89543 9.10457 6 8 6C6.89543 6 6 6.89543 6 8C6 9.10457 6.89543 10 8 10Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                          View
                        </Link>
                        <Link href={`/users/${user.id}/edit`} className={styles.editButton}>
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M8.66667 3.33334L10.6667 1.33334L14.6667 5.33334L12.6667 7.33334M8.66667 3.33334L2.29921 9.70079C2.10838 9.89163 2 10.149 2 10.4176V14H5.58239C5.85097 14 6.10838 13.8916 6.29921 13.7008L12.6667 7.33334" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                          Edit
                        </Link>
                        <button 
                          className={styles.deleteButton}
                          onClick={() => handleDeleteClick(user.id)}
                          disabled={user.role === 'admin'}
                        >
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M2 4H3.33333H14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M5.33334 4V2.66667C5.33334 2.31305 5.47382 1.97391 5.72387 1.72386C5.97392 1.47381 6.31305 1.33334 6.66668 1.33334H9.33334C9.68697 1.33334 10.0261 1.47381 10.2762 1.72386C10.5262 1.97391 10.6667 2.31305 10.6667 2.66667V4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M12.6667 4V13.3333C12.6667 13.687 12.5262 14.0261 12.2762 14.2762C12.0261 14.5262 11.687 14.6667 11.3333 14.6667H4.66668C4.31305 14.6667 3.97392 14.5262 3.72387 14.2762C3.47382 14.0261 3.33334 13.687 3.33334 13.3333V4H12.6667Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        
        {meta.pageCount > 1 && (
          <div className={styles.pagination}>
            <Pagination
              currentPage={meta.page}
              pageCount={meta.pageCount}
              onPageChange={handlePageChange}
            />
          </div>
        )}
        
        {/* Delete Modal */}
        <DeleteConfirmModal
          isOpen={showDeleteModal}
          title="Delete User"
          message="Are you sure you want to remove this user from your tenant? This action cannot be undone."
          onConfirm={handleConfirmDelete}
          onCancel={handleCancelDelete}
          error={deleteError}
        />
      </div>
    </DashboardLayout>
  );
};

export default UsersPage;