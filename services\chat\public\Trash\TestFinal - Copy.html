<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LoaLoa Chat - Test Final</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <style>
    :root {
      --primary: #4CAF50;
      --primary-dark: #45a049;
      --secondary: #2196F3;
      --secondary-dark: #0b7dda;
      --warning: #ff9800;
      --danger: #f44336;
      --success: #4CAF50;
      --dark: #333;
      --light: #f5f5f5;
      --border: #ddd;
    }
    * { box-sizing: border-box; }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f9f9f9;
      color: #333;
      line-height: 1.6;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 15px;
    }
    h1, h2, h3, h4 {
      color: var(--dark);
      margin-top: 0;
    }
    .card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      overflow: hidden;
    }
    .card-header {
      background: var(--light);
      padding: 15px 20px;
      border-bottom: 1px solid var(--border);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .card-body {
      padding: 20px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"],
    input[type="password"],
    textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--border);
      border-radius: 4px;
      font-size: 14px;
    }
    textarea {
      min-height: 100px;
      font-family: monospace;
    }
    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      margin-right: 5px;
      margin-bottom: 5px;
    }
    .btn-primary {
      background-color: var(--primary);
      color: white;
    }
    .btn-primary:hover {
      background-color: var(--primary-dark);
    }
    .btn-secondary {
      background-color: var(--secondary);
      color: white;
    }
    .btn-secondary:hover {
      background-color: var(--secondary-dark);
    }
    .btn-warning {
      background-color: var(--warning);
      color: white;
    }
    .btn-danger {
      background-color: var(--danger);
      color: white;
    }
    .status {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 20px;
      font-weight: 600;
      font-size: 12px;
    }
    .status-pending {
      background-color: #ffeaa7;
      color: #d35400;
    }
    .status-success {
      background-color: #c4e6ca;
      color: #2d6a4f;
    }
    .status-error {
      background-color: #ffcccc;
      color: #cc0000;
    }
    .grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 20px;
    }
    @media (min-width: 768px) {
      .grid-2 {
        grid-template-columns: 1fr 1fr;
      }
    }
    .tab-header {
      display: flex;
      border-bottom: 1px solid var(--border);
    }
    .tab-btn {
      padding: 10px 20px;
      cursor: pointer;
      border: none;
      background: none;
      font-weight: 500;
      border-bottom: 2px solid transparent;
    }
    .tab-btn.active {
      border-bottom: 2px solid var(--primary);
      color: var(--primary);
    }
    .tab-content {
      padding: 20px 0;
    }
    .tab-panel {
      display: none;
    }
    .tab-panel.active {
      display: block;
    }
    .data-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    .data-table th,
    .data-table td {
      padding: 10px;
      border: 1px solid var(--border);
      text-align: left;
    }
    .data-table th {
      background: var(--light);
      font-weight: 600;
    }
    .data-table tr:nth-child(even) {
      background-color: rgba(0, 0, 0, 0.02);
    }
    .console {
      background: #272822;
      color: #f8f8f2;
      font-family: monospace;
      padding: 15px;
      border-radius: 4px;
      height: 200px;
      overflow-y: auto;
    }
    .console-line {
      margin-bottom: 5px;
      word-wrap: break-word;
    }
    .timestamp {
      color: #a6e22e;
    }
    .chat-container {
      display: flex;
      gap: 20px;
    }
    .chat-box {
      flex: 1;
      border: 1px solid var(--border);
      border-radius: 8px;
      overflow: hidden;
    }
    .chat-header {
      background: var(--light);
      padding: 10px 15px;
      border-bottom: 1px solid var(--border);
    }
    .chat-messages {
      height: 300px;
      padding: 15px;
      overflow-y: auto;
      background: white;
    }
    .chat-input {
      display: flex;
      padding: 10px;
      border-top: 1px solid var(--border);
    }
    .chat-input input {
      flex: 1;
      margin-right: 10px;
    }
    .message {
      margin-bottom: 10px;
      padding: 8px 12px;
      border-radius: 8px;
      max-width: 70%;
      word-break: break-word;
    }
    .message.sent {
      background: #e3f2fd;
      margin-left: auto;
      text-align: right;
    }
    .message.received {
      background: #f1f1f1;
    }
    .message-meta {
      font-size: 12px;
      color: #666;
    }
    .user-info {
      margin-top: 10px;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 4px;
    }
    .step-number {
      display: inline-block;
      width: 24px;
      height: 24px;
      background: var(--primary);
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 24px;
      font-weight: bold;
      margin-right: 8px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>LoaLoa Chat - Test Final</h1>
    
    <!-- Step 1: Connect to Supabase -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">1</span> Connect to Supabase</h3>
        <span class="status status-pending" id="supabase-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label for="supabase-url">Supabase URL</label>
          <input type="text" id="supabase-url" value="https://iwzwbrbmojvvvfstbqow.supabase.co">
        </div>
        <div class="form-group">
          <label for="supabase-key">Supabase Key (Anon)</label>
          <input type="text" id="supabase-key" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3endicmJtb2p2dnZmc3RicW93Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMjkyNjEsImV4cCI6MjA2MTkwNTI2MX0.tyVtaSclUKC5fGh7I7Ohpm7c4FniXphYe34-cxBvo6E">
        </div>
        <div class="form-group">
          <label for="service-role-key">Service Role Key (Optional, for admin operations)</label>
          <input type="password" id="service-role-key" placeholder="Service Role Key (optional)">
        </div>
        <button class="btn btn-primary" id="connect-supabase">Connect to Supabase</button>
        <div id="connection-details" class="user-info" style="display: none;"></div>
      </div>
    </div>
    
    <!-- Step 2: Validate JWT Token -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">2</span> Validate JWT Token</h3>
        <span class="status status-pending" id="jwt-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="note" style="background-color: #ffeaa7; padding: 15px; border-radius: 4px; margin-bottom: 15px;">
          <strong>Need a JWT Token?</strong> Run this command in your terminal:<br>
          <code>cd D:\loaloa\services\chat</code><br>
          <code>npx ts-node src\tests\generate-token.ts</code>
        </div>
        <div class="form-group">
          <label for="jwt-token">JWT Token</label>
          <textarea id="jwt-token" placeholder="Paste your JWT token here"></textarea>
        </div>
        <button class="btn btn-primary" id="validate-token">Validate Token</button>
        <div id="token-info" class="user-info" style="display: none;"></div>
      </div>
    </div>
    
    <!-- Step 3: Database Explorer -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">3</span> Database Explorer</h3>
        <span class="status status-pending" id="db-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="tab-header">
          <button class="tab-btn active" data-tab="users">Users</button>
          <button class="tab-btn" data-tab="rooms">Chat Rooms</button>
          <button class="tab-btn" data-tab="participants">Participants</button>
          <button class="tab-btn" data-tab="messages">Messages</button>
        </div>
        
        <div class="tab-content">
          <!-- Users Tab -->
          <div class="tab-panel active" id="users-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-users">Load Users</button>
              <span id="users-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="users-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Email</th>
                    <th>Created At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="3">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Rooms Tab -->
          <div class="tab-panel" id="rooms-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-rooms">Load Chat Rooms</button>
              <span id="rooms-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="rooms-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Created At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="4">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Participants Tab -->
          <div class="tab-panel" id="participants-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-participants">Load Participants</button>
              <span id="participants-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="participants-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Room ID</th>
                    <th>User ID</th>
                    <th>Role</th>
                    <th>Joined At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="5">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Messages Tab -->
          <div class="tab-panel" id="messages-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-messages">Load Messages</button>
              <span id="messages-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="messages-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Room ID</th>
                    <th>Sender ID</th>
                    <th>Content</th>
                    <th>Language</th>
                    <th>Sent At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="6">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Step 4: User-Room Management -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">4</span> User-Room Management</h3>
        <span class="status status-pending" id="manage-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="grid grid-2">
          <div>
            <div class="form-group">
              <label for="select-user">Select User</label>
              <select id="select-user" class="form-control">
                <option value="">Select User</option>
              </select>
            </div>
          </div>
          <div>
            <div class="form-group">
              <label for="select-room">Select Room</label>
              <select id="select-room" class="form-control">
                <option value="">Select Room</option>
              </select>
            </div>
          </div>
        </div>
        <div class="form-group">
          <button class="btn btn-primary" id="check-participation">Check Participation</button>
          <button class="btn btn-secondary" id="join-room">Join Room</button>
        </div>
        <div id="participation-status" class="user-info" style="display: none;"></div>
      </div>
    </div>
    
    <!-- Step 5: WebSocket Connection -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">5</span> WebSocket Connection</h3>
        <span class="status status-pending" id="ws-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label for="ws-url">WebSocket Server URL</label>
          <input type="text" id="ws-url" value="http://localhost:3002">
        </div>
        <div class="form-group">
          <label for="ws-room-id">Room ID to Join</label>
          <input type="text" id="ws-room-id" placeholder="Room ID">
        </div>
        <div class="form-group">
          <button class="btn btn-primary" id="connect-ws">Connect WebSocket</button>
          <button class="btn btn-secondary" id="ws-join-room">Join Room</button>
          <button class="btn btn-secondary" id="show-debug" data-state="show">Hide Debug Console</button>
        </div>
        <div class="console" id="debug-console">
          <!-- Debug messages will appear here -->
        </div>
      </div>
    </div>
    
    <!-- Step 6: Test Chat Interface -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">6</span> Test Chat Interface</h3>
        <span class="status status-pending" id="chat-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="chat-container">
          <!-- User 1 Chat -->
          <div class="chat-box">
            <div class="chat-header">
              <h4>User 1 (Vietnamese)</h4>
              <div class="user-details">
                <div class="form-group">
                  <label for="user1-id">User ID</label>
                  <input type="text" id="user1-id" placeholder="User ID">
                </div>
                <div class="form-group">
                  <p><strong>Email:</strong> <span id="user1-email">Not loaded</span></p>
                </div>
                <div class="form-group">
                  <p><strong>Status:</strong> <span id="user1-status">Disconnected</span></p>
                </div>
                <div class="form-group">
                  <button class="btn btn-secondary" id="user1-join">Join as Participant</button>
                </div>
              </div>
            </div>
            <div class="chat-messages" id="user1-messages">
              <!-- Messages will appear here -->
            </div>
            <div class="chat-input">
              <input type="text" id="user1-input" placeholder="Type a message...">
              <button class="btn btn-primary" id="user1-send">Send</button>
            </div>
          </div>
          
          <!-- User 2 Chat -->
          <div class="chat-box">
            <div class="chat-header">
              <h4>User 2 (English)</h4>
              <div class="user-details">
                <div class="form-group">
                  <label for="user2-id">User ID</label>
                  <input type="text" id="user2-id" placeholder="User ID" value="a9813ae-9a46-4dc9-9fa3-6f04062f7e50">
                </div>
                <div class="form-group">
                  <p><strong>Email:</strong> <span id="user2-email">Not loaded</span></p>
                </div>
                <div class="form-group">
                  <p><strong>Status:</strong> <span id="user2-status">Disconnected</span></p>
                </div>
                <div class="form-group">
                  <button class="btn btn-secondary" id="user2-join">Join as Participant</button>
                </div>
              </div>
            </div>
            <div class="chat-messages" id="user2-messages">
              <!-- Messages will appear here -->
            </div>
            <div class="chat-input">
              <input type="text" id="user2-input" placeholder="Type a message...">
              <button class="btn btn-primary" id="user2-send">Send</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Global variables
    let supabaseClient = null;
    let serviceClient = null;
    let jwtToken = '';
    let userId = '';
    let socket1 = null;
    let socket2 = null;
    let currentRoomId = '';
    
    // DOM references
    const statuses = {
      supabase: document.getElementById('supabase-status'),
      jwt: document.getElementById('jwt-status'),
      db: document.getElementById('db-status'),
      manage: document.getElementById('manage-status'),
      ws: document.getElementById('ws-status'),
      chat: document.getElementById('chat-status')
    };
    
    // Helper function to update status
    function updateStatus(key, status, message = '') {
      if (statuses[key]) {
        statuses[key].className = `status status-${status}`;
        statuses[key].textContent = status.charAt(0).toUpperCase() + status.slice(1);
        
        if (message && status === 'error') {
          console.error(message);
          addDebugMessage(message, true);
        }
      }
    }
    
    // Helper function to add debug messages
    function addDebugMessage(message, isError = false) {
      const debugConsole = document.getElementById('debug-console');
      const lineElement = document.createElement('div');
      lineElement.className = 'console-line';
      
      const timestamp = document.createElement('span');
      timestamp.className = 'timestamp';
      timestamp.textContent = `[${new Date().toLocaleTimeString()}] `;
      
      const messageText = document.createTextNode(message);
      
      lineElement.appendChild(timestamp);
      lineElement.appendChild(messageText);
      
      if (isError) {
        lineElement.style.color = '#f92672'; // Red color for errors
      }
      
      debugConsole.appendChild(lineElement);
      debugConsole.scrollTop = debugConsole.scrollHeight;
    }
    
    // Format date for display
    function formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString();
    }
    
    // Truncate text for table display
    function truncateText(text, length = 30) {
      if (!text) return '';
      return text.length > length ? text.substring(0, length) + '...' : text;
    }
    
    // Load table data
    function loadTableData(tableId, data, columns) {
      const table = document.getElementById(tableId);
      const tbody = table.getElementsByTagName('tbody')[0];
      tbody.innerHTML = '';
      
      if (data && data.length > 0) {
        data.forEach(item => {
          const row = document.createElement('tr');
          columns.forEach(col => {
            const cell = document.createElement('td');
            
            // Handle different data types
            if (col.format === 'date') {
              cell.textContent = formatDate(item[col.field]);
            } else if (col.format === 'truncate') {
              cell.textContent = truncateText(item[col.field], col.length || 30);
              if (item[col.field] && item[col.field].length > (col.length || 30)) {
                cell.title = item[col.field]; // Show full text on hover
              }
            } else {
              cell.textContent = item[col.field] || '';
            }
            
            row.appendChild(cell);
          });
          tbody.appendChild(row);
        });
      } else {
        const row = document.createElement('tr');
        const cell = document.createElement('td');
        cell.colSpan = columns.length;
        cell.textContent = 'No data available';
        cell.style.textAlign = 'center';
        row.appendChild(cell);
        tbody.appendChild(row);
      }
    }
    
    // Populate dropdown
    function populateDropdown(selectId, data, valueField, textField) {
      const select = document.getElementById(selectId);
      const defaultOption = select.options[0];
      select.innerHTML = '';
      select.appendChild(defaultOption);
      
      if (data && data.length > 0) {
        data.forEach(item => {
          const option = document.createElement('option');
          option.value = item[valueField];
          option.textContent = item[textField] || item[valueField];
          select.appendChild(option);
        });
      }
    }
    
    // ===== STEP 1: CONNECT TO SUPABASE =====
    document.getElementById('connect-supabase').addEventListener('click', async () => {
      try {
        const supabaseUrl = document.getElementById('supabase-url').value;
        const supabaseKey = document.getElementById('supabase-key').value;
        const serviceRoleKey = document.getElementById('service-role-key').value;
        
        if (!supabaseUrl || !supabaseKey) {
          updateStatus('supabase', 'error', 'URL and key are required');
          return;
        }
        
        addDebugMessage(`Connecting to Supabase at ${supabaseUrl}...`);
        
        // Check if Supabase library is available
        if (typeof supabase === 'undefined') {
          updateStatus('supabase', 'error', 'Supabase library not loaded');
          addDebugMessage('Supabase library not loaded', true);
          return;
        }
        
        // Create client with anon key
        supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
        
        // If service role key is provided, create admin client
        if (serviceRoleKey) {
          serviceClient = supabase.createClient(supabaseUrl, serviceRoleKey);
        }
        
        // Test connection
        const { data, error } = await supabaseClient.from('users').select('count(*)');
        
        if (error) {
          throw error;
        }
        
        updateStatus('supabase', 'success');
        addDebugMessage('Successfully connected to Supabase');
        
        // Show connection details
        document.getElementById('connection-details').style.display = 'block';
        document.getElementById('connection-details').innerHTML = `
          <p><strong>Connection Status:</strong> Success</p>
          <p><strong>URL:</strong> ${supabaseUrl}</p>
          <p><strong>Service Role Key:</strong> ${serviceRoleKey ? 'Provided ✓' : 'Not provided'}</p>
        `;
        
        // Enable database tabs
        updateStatus('db', 'pending');
      } catch (err) {
        updateStatus('supabase', 'error');
        addDebugMessage(`Failed to connect to Supabase: ${err.message}`, true);
        
        document.getElementById('connection-details').style.display = 'block';
        document.getElementById('connection-details').innerHTML = `
          <p><strong>Connection Status:</strong> <span style="color: red;">Failed</span></p>
          <p><strong>Error:</strong> ${err.message}</p>
          <p>Please check your URL and API key and try again.</p>
        `;
      }
    });
    
    // ===== STEP 2: VALIDATE JWT TOKEN =====
    document.getElementById('validate-token').addEventListener('click', () => {
      const token = document.getElementById('jwt-token').value.trim();
      
      if (!token) {
        updateStatus('jwt', 'error', 'Please enter a JWT token');
        return;
      }
      
      try {
        // Decode JWT token to get payload
        const base64Url = token.split('.')[1];
        if (!base64Url) {
          updateStatus('jwt', 'error', 'Invalid JWT token format');
          addDebugMessage('Invalid JWT token format', true);
          return;
        }
        
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        
        const payload = JSON.parse(jsonPayload);
        
        // Check if token is expired
        const currentTime = Math.floor(Date.now() / 1000);
        const expiryDate = new Date(payload.exp * 1000);
        
        if (payload.exp < currentTime) {
          updateStatus('jwt', 'error', 'Token has expired');
          addDebugMessage('JWT token has expired', true);
          
          document.getElementById('token-info').style.display = 'block';
          document.getElementById('token-info').innerHTML = `
            <p style="color: red;"><strong>Token Status:</strong> Expired</p>
            <p><strong>Expiration Date:</strong> ${expiryDate.toLocaleString()}</p>
            <p><strong>Current Time:</strong> ${new Date().toLocaleString()}</p>
            <p>Please generate a new token using the command provided.</p>
          `;
          return;
        }
        
        // Save token and userId
        jwtToken = token;
        userId = payload.userId;
        
         // Update user1 ID field
        document.getElementById('user1-id').value = userId;
        
        updateStatus('jwt', 'success');
        addDebugMessage(`Valid JWT token for user: ${userId}`);
        
        // Display token info
        document.getElementById('token-info').style.display = 'block';
        document.getElementById('token-info').innerHTML = `
          <p><strong>Token Status:</strong> <span style="color: green;">Valid</span></p>
          <p><strong>User ID:</strong> ${payload.userId || 'Not found'}</p>
          <p><strong>Email:</strong> ${payload.email || 'Not found'}</p>
          <p><strong>Roles:</strong> ${payload.roles ? payload.roles.join(', ') : 'None'}</p>
          <p><strong>Language:</strong> ${payload.preferredLanguage || 'Not specified'}</p>
          <p><strong>Expiry:</strong> ${expiryDate.toLocaleString()}</p>
        `;
        
        // Fetch user email
        fetchUserEmail(userId, 'user1-email');
      } catch (err) {
        updateStatus('jwt', 'error');
        addDebugMessage(`Invalid JWT token: ${err.message}`, true);
        
        document.getElementById('token-info').style.display = 'block';
        document.getElementById('token-info').innerHTML = `
          <p style="color: red;"><strong>Token Status:</strong> Invalid</p>
          <p><strong>Error:</strong> ${err.message}</p>
          <p>Please check the token format and try again.</p>
        `;
      }
    });
    
    // Fetch user email by ID
    async function fetchUserEmail(userId, elementId) {
      if (!supabaseClient || !userId) {
        document.getElementById(elementId).textContent = 'Connect to Supabase first';
        return;
      }
      
      try {
        const { data, error } = await supabaseClient
          .from('users')
          .select('email')
          .eq('id', userId)
          .single();
          
        if (error) {
          document.getElementById(elementId).textContent = 'User not found';
          return;
        }
        
        if (data && data.email) {
          document.getElementById(elementId).textContent = data.email;
          return data.email;
        } else {
          document.getElementById(elementId).textContent = 'No email available';
          return null;
        }
      } catch (err) {
        document.getElementById(elementId).textContent = 'Error fetching user data';
        console.error('Error fetching user email:', err);
        return null;
      }
    }
    
    // ===== STEP 3: DATABASE EXPLORER =====
    // Tab switching
    document.querySelectorAll('.tab-btn').forEach(button => {
      button.addEventListener('click', () => {
        // Remove active class from all tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
          btn.classList.remove('active');
        });
        
        // Hide all panels
        document.querySelectorAll('.tab-panel').forEach(panel => {
          panel.classList.remove('active');
        });
        
        // Add active class to clicked tab
        button.classList.add('active');
        
        // Show corresponding panel
        const tabId = button.getAttribute('data-tab');
        document.getElementById(`${tabId}-panel`).classList.add('active');
      });
    });
    
    // Load Users
    document.getElementById('load-users').addEventListener('click', async () => {
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      try {
        addDebugMessage('Loading users...');
        
        // Use service client if available for better access
        const client = serviceClient || supabaseClient;
        
        const { data, error } = await client
          .from('users')
          .select('*')
          .limit(20);
          
        if (error) {
          throw error;
        }
        
        updateStatus('db', 'success');
        addDebugMessage(`Loaded ${data.length} users`);
        document.getElementById('users-count').textContent = `${data.length} users loaded`;
        
        // Define columns for users table
        const columns = [
          { field: 'id', label: 'ID' },
          { field: 'email', label: 'Email' },
          { field: 'created_at', label: 'Created At', format: 'date' }
        ];
        
        loadTableData('users-table', data, columns);
        populateDropdown('select-user', data, 'id', 'email');
        
        // Cache user data for later use
        window.userData = data;
      } catch (err) {
        updateStatus('db', 'error');
        addDebugMessage(`Failed to load users: ${err.message}`, true);
      }
    });
    
    // Load Chat Rooms
    document.getElementById('load-rooms').addEventListener('click', async () => {
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      try {
        addDebugMessage('Loading chat rooms...');
        
        const client = serviceClient || supabaseClient;
        let data, error;
        
        // Try 'chat_rooms' first
        try {
          const response = await client
            .from('chat_rooms')
            .select('*')
            .limit(20);
            
          data = response.data;
          error = response.error;
          
          if (!error && data) {
            addDebugMessage('Successfully loaded rooms from chat_rooms table');
          }
        } catch (e) {
          addDebugMessage('Error with chat_rooms table, trying rooms table...');
        }
        
        // If no data from 'chat_rooms', try 'rooms'
        if (!data || error) {
          const response = await client
            .from('rooms')
            .select('*')
            .limit(20);
            
          data = response.data;
          error = response.error;
          
          if (!error && data) {
            addDebugMessage('Successfully loaded rooms from rooms table');
          }
        }
        
        if (error) {
          throw error;
        }
        
        updateStatus('db', 'success');
        addDebugMessage(`Loaded ${data.length} rooms`);
        document.getElementById('rooms-count').textContent = `${data.length} rooms loaded`;
        
        // Define columns for rooms table
        const columns = [
          { field: 'id', label: 'ID' },
          { field: 'name', label: 'Name' },
          { field: 'room_type', label: 'Type' },
          { field: 'created_at', label: 'Created At', format: 'date' }
        ];
        
        loadTableData('rooms-table', data, columns);
        populateDropdown('select-room', data, 'id', 'name');
        
        // Update room ID field for WebSocket
        if (data && data.length > 0) {
          document.getElementById('ws-room-id').value = data[0].id;
        }
        
        // Cache room data for later use
        window.roomData = data;
      } catch (err) {
        updateStatus('db', 'error');
        addDebugMessage(`Failed to load rooms: ${err.message}`, true);
      }
    });
    
    // Load Participants
    document.getElementById('load-participants').addEventListener('click', async () => {
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      try {
        addDebugMessage('Loading participants...');
        
        const client = serviceClient || supabaseClient;
        const { data, error } = await client
          .from('chat_participants')
          .select('*')
          .limit(20);
          
        if (error) {
          throw error;
        }
        
        updateStatus('db', 'success');
        addDebugMessage(`Loaded ${data.length} participants`);
        document.getElementById('participants-count').textContent = `${data.length} participants loaded`;
        
        // Define columns for participants table
        const columns = [
          { field: 'id', label: 'ID' },
          { field: 'chat_room_id', label: 'Room ID' },
          { field: 'user_id', label: 'User ID' },
          { field: 'participant_role', label: 'Role' },
          { field: 'joined_at', label: 'Joined At', format: 'date' }
        ];
        
        loadTableData('participants-table', data, columns);
        
        // Cache participant data for later use
        window.participantData = data;
      } catch (err) {
        updateStatus('db', 'error');
        addDebugMessage(`Failed to load participants: ${err.message}`, true);
      }
    });
    
    // Load Messages
    document.getElementById('load-messages').addEventListener('click', async () => {
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      try {
        addDebugMessage('Loading messages...');
        
        const client = serviceClient || supabaseClient;
        const { data, error } = await client
          .from('chat_messages')
          .select('*')
          .order('sent_at', { ascending: false })
          .limit(20);
          
        if (error) {
          throw error;
        }
        
        updateStatus('db', 'success');
        addDebugMessage(`Loaded ${data.length} messages`);
        document.getElementById('messages-count').textContent = `${data.length} messages loaded`;
        
        // Define columns for messages table
        const columns = [
          { field: 'id', label: 'ID' },
          { field: 'chat_room_id', label: 'Room ID' },
          { field: 'sender_id', label: 'Sender ID' },
          { field: 'content', label: 'Content', format: 'truncate', length: 40 },
          { field: 'original_language', label: 'Language' },
          { field: 'sent_at', label: 'Sent At', format: 'date' }
        ];
        
        loadTableData('messages-table', data, columns);
        
        // Cache message data for later use
        window.messageData = data;
      } catch (err) {
        updateStatus('db', 'error');
        addDebugMessage(`Failed to load messages: ${err.message}`, true);
      }
    });
    
    // ===== STEP 4: USER-ROOM MANAGEMENT =====
    // Check Participation
    document.getElementById('check-participation').addEventListener('click', async () => {
      const userId = document.getElementById('select-user').value;
      const roomId = document.getElementById('select-room').value;
      
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      if (!userId || !roomId) {
        addDebugMessage('Please select both user and room', true);
        document.getElementById('participation-status').style.display = 'block';
        document.getElementById('participation-status').innerHTML = `
          <p style="color: orange;"><strong>Error:</strong> Please select both user and room</p>
        `;
        return;
      }
      
      try {
        addDebugMessage(`Checking if user ${userId} is in room ${roomId}...`);
        
        const client = serviceClient || supabaseClient;
        const { data, error } = await client
          .from('chat_participants')
          .select('*')
          .eq('user_id', userId)
          .eq('chat_room_id', roomId)
          .eq('is_active', true);
          
        if (error) {
          throw error;
        }
        
        updateStatus('manage', 'success');
        
        document.getElementById('participation-status').style.display = 'block';
        
        if (data && data.length > 0) {
          addDebugMessage('User is active in this room');
          document.getElementById('participation-status').innerHTML = `
            <p style="color: green;"><strong>Participation Status:</strong> Active</p>
            <p><strong>Role:</strong> ${data[0].participant_role || 'Not specified'}</p>
            <p><strong>Joined:</strong> ${formatDate(data[0].joined_at)}</p>
          `;
        } else {
          addDebugMessage('User is NOT active in this room');
          document.getElementById('participation-status').innerHTML = `
            <p style="color: orange;"><strong>Participation Status:</strong> Not Active</p>
            <p>This user is not currently a participant in this room.</p>
            <p>Use the 'Join Room' button to add this user to the room.</p>
          `;
        }
      } catch (err) {
        updateStatus('manage', 'error');
        addDebugMessage(`Failed to check participation: ${err.message}`, true);
        document.getElementById('participation-status').style.display = 'block';
        document.getElementById('participation-status').innerHTML = `
          <p style="color: red;"><strong>Error:</strong> ${err.message}</p>
        `;
      }
    });
    
    // Join Room
    document.getElementById('join-room').addEventListener('click', async () => {
      const userId = document.getElementById('select-user').value;
      const roomId = document.getElementById('select-room').value;
      
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      if (!userId || !roomId) {
        addDebugMessage('Please select both user and room', true);
        document.getElementById('participation-status').style.display = 'block';
        document.getElementById('participation-status').innerHTML = `
          <p style="color: orange;"><strong>Error:</strong> Please select both user and room</p>
        `;
        return;
      }
      
      try {
        addDebugMessage(`Adding user ${userId} to room ${roomId}...`);
        
        const client = serviceClient || supabaseClient;
        
        // First check if user is already in the room
        const { data: existingData, error: checkError } = await client
          .from('chat_participants')
          .select('*')
          .eq('user_id', userId)
          .eq('chat_room_id', roomId);
          
        if (checkError) {
          throw checkError;
        }
        
        let result;
        
        if (existingData && existingData.length > 0) {
          // Update existing participant record to active
          const { data, error } = await client
            .from('chat_participants')
            .update({
              is_active: true,
              left_at: null
            })
            .eq('id', existingData[0].id)
            .select();
            
          if (error) throw error;
          result = data;
          addDebugMessage('Reactivated existing participant record');
        } else {
          // Insert new participant record
          const { data, error } = await client
            .from('chat_participants')
            .insert([
              {
                chat_room_id: roomId,
                user_id: userId,
                participant_role: 'member',
                is_active: true
              }
            ])
            .select();
            
          if (error) throw error;
          result = data;
          addDebugMessage('Created new participant record');
        }
        
        updateStatus('manage', 'success');
        
        document.getElementById('participation-status').style.display = 'block';
        document.getElementById('participation-status').innerHTML = `
          <p style="color: green;"><strong>Success:</strong> User added to room successfully</p>
          <p><strong>Participant ID:</strong> ${result[0].id}</p>
          <p><strong>Role:</strong> ${result[0].participant_role || 'member'}</p>
          <p><strong>Joined:</strong> ${formatDate(result[0].joined_at || new Date())}</p>
        `;
        
        // Reload participants data
        document.getElementById('load-participants').click();
      } catch (err) {
        updateStatus('manage', 'error');
        addDebugMessage(`Failed to add user to room: ${err.message}`, true);
        document.getElementById('participation-status').style.display = 'block';
        document.getElementById('participation-status').innerHTML = `
          <p style="color: red;"><strong>Error:</strong> ${err.message}</p>
        `;
      }
    });
    
    // ===== STEP 5: WEBSOCKET CONNECTION =====
    document.getElementById('connect-ws').addEventListener('click', () => {
      const wsUrl = document.getElementById('ws-url').value;
      const roomId = document.getElementById('ws-room-id').value;
      
      if (!wsUrl) {
        addDebugMessage('WebSocket URL is required', true);
        return;
      }
      
      if (!jwtToken) {
        addDebugMessage('JWT Token is required. Please validate a token first.', true);
        return;
      }
      
      connectWebSocket(wsUrl);
    });
    
    function connectWebSocket(serverUrl) {
      try {
        addDebugMessage(`Connecting to WebSocket server at ${serverUrl}...`);
        
        // Disconnect existing connections if any
        if (socket1 && socket1.connected) {
          socket1.disconnect();
        }
        
        if (socket2 && socket2.connected) {
          socket2.disconnect();
        }
        
        // Connect User 1
        socket1 = io(serverUrl, {
          auth: { token: jwtToken }
        });
        
        socket1.on('connect', () => {
          addDebugMessage('User 1 connected to WebSocket server');
          document.getElementById('user1-status').textContent = 'Connected';
          
          // Setup User 1
          const user1Id = document.getElementById('user1-id').value;
          
          socket1.emit('setup', {
            userId: user1Id,
            preferredLanguage: 'vi',
            deviceId: `test-device-user1-${Date.now()}`
          }, (response) => {
            if (response.success) {
              addDebugMessage(`User 1 setup success: ${JSON.stringify(response.data)}`);
            } else {
              addDebugMessage(`User 1 setup failed: ${response.error}`, true);
            }
          });
        });
        
        // Connect User 2
        socket2 = io(serverUrl, {
          auth: { token: jwtToken }
        });
        
        socket2.on('connect', () => {
          addDebugMessage('User 2 connected to WebSocket server');
          document.getElementById('user2-status').textContent = 'Connected';
          
          // Setup User 2
          const user2Id = document.getElementById('user2-id').value;
          
          socket2.emit('setup', {
            userId: user2Id,
            preferredLanguage: 'en',
            deviceId: `test-device-user2-${Date.now()}`
          }, (response) => {
            if (response.success) {
              addDebugMessage(`User 2 setup success: ${JSON.stringify(response.data)}`);
            } else {
              addDebugMessage(`User 2 setup failed: ${response.error}`, true);
            }
          });
        });
        
        // Message received handlers
        socket1.on('message_received', (data) => {
          addDebugMessage(`User 1 received message: ${JSON.stringify(data.message)}`);
          displayMessage('user1-messages', data.message, false);
        });
        
        socket2.on('message_received', (data) => {
          addDebugMessage(`User 2 received message: ${JSON.stringify(data.message)}`);
          displayMessage('user2-messages', data.message, false);
        });
        
        // Translation received handlers
        socket1.on('translation_received', (data) => {
          addDebugMessage(`User 1 received translation: ${JSON.stringify(data)}`);
        });
        
        socket2.on('translation_received', (data) => {
          addDebugMessage(`User 2 received translation: ${JSON.stringify(data)}`);
        });
        
        // Error handlers
        socket1.on('error', (error) => {
          addDebugMessage(`User 1 error: ${JSON.stringify(error)}`, true);
        });
        
        socket2.on('error', (error) => {
          addDebugMessage(`User 2 error: ${JSON.stringify(error)}`, true);
        });
        
        // Disconnect handlers
        socket1.on('disconnect', () => {
          addDebugMessage('User 1 disconnected from server');
          document.getElementById('user1-status').textContent = 'Disconnected';
        });
        
        socket2.on('disconnect', () => {
          addDebugMessage('User 2 disconnected from server');
          document.getElementById('user2-status').textContent = 'Disconnected';
        });
        
        updateStatus('ws', 'success');
      } catch (err) {
        updateStatus('ws', 'error');
        addDebugMessage(`Failed to connect to WebSocket: ${err.message}`, true);
      }
    }
    
    // Join room with WebSocket
    document.getElementById('ws-join-room').addEventListener('click', () => {
      const roomId = document.getElementById('ws-room-id').value;
      
      if (!roomId) {
        addDebugMessage('Please enter a Room ID', true);
        return;
      }
      
      if (!socket1 || !socket1.connected || !socket2 || !socket2.connected) {
        addDebugMessage('WebSocket is not connected. Please connect first.', true);
        return;
      }
      
      currentRoomId = roomId;
      
      // User 1 join room
      socket1.emit('join_room', roomId, (response) => {
        if (response.success) {
          addDebugMessage(`User 1 joined room ${roomId}: ${JSON.stringify(response.data.room)}`);
          updateStatus('chat', 'success');
        } else {
          addDebugMessage(`User 1 failed to join room: ${response.error}`, true);
        }
      });
      
      // User 2 join room
      socket2.emit('join_room', roomId, (response) => {
        if (response.success) {
          addDebugMessage(`User 2 joined room ${roomId}: ${JSON.stringify(response.data.room)}`);
          updateStatus('chat', 'success');
        } else {
          addDebugMessage(`User 2 failed to join room: ${response.error}`, true);
        }
      });
    });
    
    // Show/Hide debug console
    document.getElementById('show-debug').addEventListener('click', (event) => {
      const button = event.target;
      const state = button.getAttribute('data-state');
      const debugConsole = document.getElementById('debug-console');
      
      if (state === 'show') {
        debugConsole.style.display = 'none';
        button.setAttribute('data-state', 'hide');
        button.textContent = 'Show Debug Console';
      } else {
        debugConsole.style.display = 'block';
        button.setAttribute('data-state', 'show');
        button.textContent = 'Hide Debug Console';
      }
    });
    
    // ===== STEP 6: TEST CHAT INTERFACE =====
    // Join as participant for User 1
    document.getElementById('user1-join').addEventListener('click', async () => {
      const user1Id = document.getElementById('user1-id').value;
      const roomId = currentRoomId || document.getElementById('ws-room-id').value;
      
      if (!user1Id) {
        addDebugMessage('User 1 ID is required', true);
        return;
      }
      
      if (!roomId) {
        addDebugMessage('Please select a Room ID first', true);
        return;
      }
      
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      try {
        addDebugMessage(`Adding User 1 (${user1Id}) to room ${roomId}...`);
        
        const client = serviceClient || supabaseClient;
        
        // First check if user is already in the room
        const { data: existingData, error: checkError } = await client
          .from('chat_participants')
          .select('*')
          .eq('user_id', user1Id)
          .eq('chat_room_id', roomId);
          
        if (checkError) {
          throw checkError;
        }
        
        let result;
        
        if (existingData && existingData.length > 0) {
          // Update existing participant record to active
          const { data, error } = await client
            .from('chat_participants')
            .update({
              is_active: true,
              left_at: null
            })
            .eq('id', existingData[0].id)
            .select();
            
          if (error) throw error;
          result = data;
          addDebugMessage('User 1: Reactivated existing participant record');
        } else {
          // Insert new participant record
          const { data, error } = await client
            .from('chat_participants')
            .insert([
              {
                chat_room_id: roomId,
                user_id: user1Id,
                participant_role: 'member',
                is_active: true
              }
            ])
            .select();
            
          if (error) throw error;
          result = data;
          addDebugMessage('User 1: Created new participant record');
        }
        
        addDebugMessage('User 1 added to room successfully');
      } catch (err) {
        addDebugMessage(`Failed to add User 1 to room: ${err.message}`, true);
      }
    });
    
    // Join as participant for User 2
    document.getElementById('user2-join').addEventListener('click', async () => {
      const user2Id = document.getElementById('user2-id').value;
      const roomId = currentRoomId || document.getElementById('ws-room-id').value;
      
      if (!user2Id) {
        addDebugMessage('User 2 ID is required', true);
        return;
      }
      
      if (!roomId) {
        addDebugMessage('Please select a Room ID first', true);
        return;
      }
      
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      try {
        addDebugMessage(`Adding User 2 (${user2Id}) to room ${roomId}...`);
        
        const client = serviceClient || supabaseClient;
        
        // First check if user is already in the room
        const { data: existingData, error: checkError } = await client
          .from('chat_participants')
          .select('*')
          .eq('user_id', user2Id)
          .eq('chat_room_id', roomId);
          
        if (checkError) {
          throw checkError;
        }
        
        let result;
        
        if (existingData && existingData.length > 0) {
          // Update existing participant record to active
          const { data, error } = await client
            .from('chat_participants')
            .update({
              is_active: true,
              left_at: null
            })
            .eq('id', existingData[0].id)
            .select();
            
          if (error) throw error;
          result = data;
          addDebugMessage('User 2: Reactivated existing participant record');
        } else {
          // Insert new participant record
          const { data, error } = await client
            .from('chat_participants')
            .insert([
              {
                chat_room_id: roomId,
                user_id: user2Id,
                participant_role: 'member',
                is_active: true
              }
            ])
            .select();
            
          if (error) throw error;
          result = data;
          addDebugMessage('User 2: Created new participant record');
        }
        
        addDebugMessage('User 2 added to room successfully');
        fetchUserEmail(user2Id, 'user2-email');
      } catch (err) {
        addDebugMessage(`Failed to add User 2 to room: ${err.message}`, true);
      }
    });
    
    // Send message from User 1
    document.getElementById('user1-send').addEventListener('click', () => {
      sendMessageUser1();
    });
    
    document.getElementById('user1-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessageUser1();
      }
    });
    
    // Send message from User 2
    document.getElementById('user2-send').addEventListener('click', () => {
      sendMessageUser2();
    });
    
    document.getElementById('user2-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessageUser2();
      }
    });
    
    // Send message function for User 1
    function sendMessageUser1() {
      const input = document.getElementById('user1-input');
      const content = input.value.trim();
      const userId = document.getElementById('user1-id').value;
      
      if (!content) {
        return;
      }
      
      if (!currentRoomId) {
        addDebugMessage('Please join a room first', true);
        return;
      }
      
      if (!socket1 || !socket1.connected) {
        addDebugMessage('WebSocket connection for User 1 is not established', true);
        return;
      }
      
      addDebugMessage(`User 1 sending message: ${content}`);
      
      socket1.emit('send_message', {
        roomId: currentRoomId,
        content,
        originalLanguage: 'vi'
      }, async (response) => {
        if (response && response.success) {
          addDebugMessage('User 1 sent message successfully');
          displayMessage('user1-messages', response.data.message, true);
          input.value = '';
        } else {
          addDebugMessage(`User 1 failed to send message: ${response ? response.error : 'Unknown error'}`, true);
          
          // If socket fails, try to send directly to database
          if (supabaseClient) {
            try {
              const client = serviceClient || supabaseClient;
              const { data, error } = await client
                .from('chat_messages')
                .insert([
                  {
                    chat_room_id: currentRoomId,
                    sender_id: userId,
                    content,
                    original_language: 'vi',
                    sent_at: new Date().toISOString(),
                    is_translated: false
                  }
                ])
                .select();
                
              if (error) {
                throw error;
              }
              
              addDebugMessage('User 1 message saved directly to database');
              displayMessage('user1-messages', data[0], true);
              input.value = '';
            } catch (err) {
              addDebugMessage(`Failed to save User 1 message to database: ${err.message}`, true);
            }
          }
        }
      });
    }
    
    // Send message function for User 2
    function sendMessageUser2() {
      const input = document.getElementById('user2-input');
      const content = input.value.trim();
      const userId = document.getElementById('user2-id').value;
if (!content) {
        return;
      }
      
      if (!currentRoomId) {
        addDebugMessage('Please join a room first', true);
        return;
      }
      
      if (!socket2 || !socket2.connected) {
        addDebugMessage('WebSocket connection for User 2 is not established', true);
        return;
      }
      
      addDebugMessage(`User 2 sending message: ${content}`);
      
      socket2.emit('send_message', {
        roomId: currentRoomId,
        content,
        originalLanguage: 'en'
      }, async (response) => {
        if (response && response.success) {
          addDebugMessage('User 2 sent message successfully');
          displayMessage('user2-messages', response.data.message, true);
          input.value = '';
        } else {
          addDebugMessage(`User 2 failed to send message: ${response ? response.error : 'Unknown error'}`, true);
          
          // If socket fails, try to send directly to database
          if (supabaseClient) {
            try {
              const client = serviceClient || supabaseClient;
              const { data, error } = await client
                .from('chat_messages')
                .insert([
                  {
                    chat_room_id: currentRoomId,
                    sender_id: userId,
                    content,
                    original_language: 'en',
                    sent_at: new Date().toISOString(),
                    is_translated: false
                  }
                ])
                .select();
                
              if (error) {
                throw error;
              }
              
              addDebugMessage('User 2 message saved directly to database');
              displayMessage('user2-messages', data[0], true);
              input.value = '';
            } catch (err) {
              addDebugMessage(`Failed to save User 2 message to database: ${err.message}`, true);
            }
          }
        }
      });
    }
    
    // Display message in chat window
    function displayMessage(containerId, message, isSent) {
      const container = document.getElementById(containerId);
      const messageDiv = document.createElement('div');
      messageDiv.className = `message ${isSent ? 'sent' : 'received'}`;
      messageDiv.dataset.messageId = message.id;
      
      // Message content
      const contentDiv = document.createElement('div');
      contentDiv.textContent = message.content;
      messageDiv.appendChild(contentDiv);
      
      // Message metadata
      const metaDiv = document.createElement('div');
      metaDiv.className = 'message-meta';
      metaDiv.textContent = `[${message.original_language}] ${new Date(message.sent_at).toLocaleTimeString()}`;
      messageDiv.appendChild(metaDiv);
      
      container.appendChild(messageDiv);
      container.scrollTop = container.scrollHeight;
    }
    
    // Initialize the page
    document.addEventListener('DOMContentLoaded', async () => {
      // Check if user2 ID is filled
      const user2Id = document.getElementById('user2-id').value;
      if (user2Id) {
        fetchUserEmail(user2Id, 'user2-email');
      }
      
      // Set default room ID
      if (window.roomData && window.roomData.length > 0) {
        document.getElementById('ws-room-id').value = window.roomData[0].id;
        currentRoomId = window.roomData[0].id;
      }
    });
  </script>
</body>
</html>