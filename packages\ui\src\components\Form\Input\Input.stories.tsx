import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Input } from './index';

const meta = {
  title: 'UI/Form/Input',
  component: Input,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      options: ['text', 'password', 'email', 'number', 'tel', 'url'],
      description: 'Input type',
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
      description: 'Input size',
    },
    placeholder: {
      control: 'text',
      description: 'Input placeholder',
    },
    label: {
      control: 'text',
      description: 'Input label',
    },
    helperText: {
      control: 'text',
      description: 'Helper text displayed below the input',
    },
    errorText: {
      control: 'text',
      description: 'Error message displayed when error is true',
    },
    error: {
      control: 'boolean',
      description: 'Whether the input is in error state',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the input is disabled',
    },
    fullWidth: {
      control: 'boolean',
      description: 'Whether the input is full width',
    },
  },
} satisfies Meta<typeof Input>;

export default meta;
type Story = StoryObj<typeof meta>;

// Icon components for stories
const SearchIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.33333 12.6667C10.2789 12.6667 12.6667 10.2789 12.6667 7.33333C12.6667 4.38781 10.2789 2 7.33333 2C4.38781 2 2 4.38781 2 7.33333C2 10.2789 4.38781 12.6667 7.33333 12.6667Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M14 14L11 11" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const LockIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.6667 7.33333H3.33333C2.59695 7.33333 2 7.93029 2 8.66667V13.3333C2 14.0697 2.59695 14.6667 3.33333 14.6667H12.6667C13.403 14.6667 14 14.0697 14 13.3333V8.66667C14 7.93029 13.403 7.33333 12.6667 7.33333Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M4.66667 7.33333V4.66667C4.66667 3.78261 5.01786 2.93477 5.64298 2.30964C6.2681 1.68452 7.11595 1.33333 8 1.33333C8.88405 1.33333 9.7319 1.68452 10.357 2.30964C10.9821 2.93477 11.3333 3.78261 11.3333 4.66667V7.33333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export const Default: Story = {
  args: {
    label: 'Text Input',
    placeholder: 'Enter text',
    type: 'text',
  },
};

export const Password: Story = {
  args: {
    label: 'Password',
    placeholder: 'Enter password',
    type: 'password',
    helperText: 'Must be at least 8 characters long',
  },
};

export const WithIcon: Story = {
  args: {
    label: 'Search',
    placeholder: 'Search...',
    type: 'text',
    startIcon: <SearchIcon />,
  },
};

export const WithEndIcon: Story = {
  args: {
    label: 'Password',
    placeholder: 'Enter password',
    type: 'password',
    endIcon: <LockIcon />,
  },
};

export const WithError: Story = {
  args: {
    label: 'Email',
    placeholder: 'Enter email',
    type: 'email',
    error: true,
    errorText: 'Please enter a valid email address',
    value: 'invalid-email',
  },
};

export const Disabled: Story = {
  args: {
    label: 'Disabled Input',
    placeholder: 'This input is disabled',
    disabled: true,
  },
};

export const Small: Story = {
  args: {
    label: 'Small Input',
    placeholder: 'Small size',
    size: 'small',
  },
};

export const Large: Story = {
  args: {
    label: 'Large Input',
    placeholder: 'Large size',
    size: 'large',
  },
};

export const FullWidth: Story = {
  args: {
    label: 'Full Width Input',
    placeholder: 'Full width input',
    fullWidth: true,
  },
};

export const InputShowcase: Story = {
  name: 'All Input Variants',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px', maxWidth: '500px' }}>
      <Input 
        label="Default input"
        placeholder="Enter text"
      />
      
      <Input 
        label="With helper text"
        placeholder="Enter text"
        helperText="This is a helper text"
      />
      
      <Input 
        label="With error"
        placeholder="Enter text"
        error={true}
        errorText="This field is required"
        value="Invalid input"
      />
      
      <Input 
        label="Disabled input"
        placeholder="This input is disabled"
        disabled={true}
      />
      
      <Input 
        label="Password input"
        placeholder="Enter password"
        type="password"
      />
      
      <Input 
        label="With start icon"
        placeholder="Search..."
        startIcon={<SearchIcon />}
      />
      
      <Input 
        label="With end icon"
        placeholder="Enter password"
        type="password"
        endIcon={<LockIcon />}
      />
      
      <div style={{ display: 'flex', gap: '16px' }}>
        <Input 
          label="Small size"
          placeholder="Small"
          size="small"
        />
        
        <Input 
          label="Medium size"
          placeholder="Medium"
          size="medium"
        />
        
        <Input 
          label="Large size"
          placeholder="Large"
          size="large"
        />
      </div>
      
      <Input 
        label="Full width input"
        placeholder="This input takes full width"
        fullWidth={true}
      />
    </div>
  ),
};