'use client';

import { useState } from 'react';
import Link from 'next/link';
import styles from './RoomCard.module.scss';

interface RoomCardProps {
  id: string;
  roomNumber: string;
  roomType: string;
  roomCategory: string;
  floor: string;
  status: 'available' | 'occupied' | 'maintenance' | 'cleaning';
  guest?: {
    id: string;
    full_name: string;
    check_in: string;
    check_out: string | null;
  };
  onViewQR?: () => void;
}

export default function RoomCard({
  id,
  roomNumber,
  roomType,
  roomCategory,
  floor,
  status,
  guest,
  onViewQR,
}: RoomCardProps) {
  const [showActions, setShowActions] = useState(false);

  const getStatusClass = () => {
    switch (status) {
      case 'available':
        return styles.available;
      case 'occupied':
        return styles.occupied;
      case 'maintenance':
        return styles.maintenance;
      case 'cleaning':
        return styles.cleaning;
      default:
        return '';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'available':
        return 'Trống';
      case 'occupied':
        return '<PERSON><PERSON> khách';
      case 'maintenance':
        return 'Bảo trì';
      case 'cleaning':
        return '<PERSON>ang dọn';
      default:
        return '';
    }
  };

  return (
    <div 
      className={`${styles.roomCard} ${getStatusClass()}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className={styles.roomHeader}>
        <div className={styles.roomInfo}>
          <h3 className={styles.roomNumber}>{`Phòng ${roomNumber}`}</h3>
          <span className={styles.roomType}>{roomType}</span>
        </div>
        <span className={`${styles.statusBadge} ${getStatusClass()}`}>
          {getStatusText()}
        </span>
      </div>
      
      <div className={styles.roomDetails}>
        <div className={styles.detailItem}>
          <span className={styles.label}>Loại phòng:</span>
          <span className={styles.value}>{roomCategory}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.label}>Tầng:</span>
          <span className={styles.value}>{floor}</span>
        </div>
        
        {guest && (
          <div className={styles.guestInfo}>
            <div className={styles.detailItem}>
              <span className={styles.label}>Khách:</span>
              <span className={styles.value}>{guest.full_name}</span>
            </div>
            <div className={styles.detailItem}>
              <span className={styles.label}>Check-in:</span>
              <span className={styles.value}>
                {new Date(guest.check_in).toLocaleDateString('vi-VN')}
              </span>
            </div>
            {guest.check_out && (
              <div className={styles.detailItem}>
                <span className={styles.label}>Check-out dự kiến:</span>
                <span className={styles.value}>
                  {new Date(guest.check_out).toLocaleDateString('vi-VN')}
                </span>
              </div>
            )}
          </div>
        )}
      </div>
      
      <div className={`${styles.roomActions} ${showActions ? styles.visible : ''}`}>
        <Link href={`/rooms-areas/rooms/${id}`} className={styles.actionButton}>
          Chi tiết
        </Link>
        <Link href={`/rooms-areas/rooms/${id}/edit`} className={styles.actionButton}>
          Chỉnh sửa
        </Link>
        {onViewQR && (
          <button onClick={onViewQR} className={styles.actionButton}>
            Xem mã QR
          </button>
        )}
      </div>
    </div>
  );
}
