'use client';

import { useState, useEffect } from 'react';
import styles from './NotificationCenter.module.scss';

interface Notification {
  id: string;
  type: 'new_message' | 'new_chat' | 'urgent' | 'system';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface NotificationCenterProps {
  onNotificationClick?: (notification: Notification) => void;
}

export default function NotificationCenter({ onNotificationClick }: NotificationCenterProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    // Load mock notifications - Tải thông báo mẫu
    loadMockNotifications();
    
    // Simulate real-time notifications - <PERSON><PERSON> phỏng thông báo thời gian thực
    const interval = setInterval(() => {
      if (Math.random() > 0.8) { // 20% chance every 5 seconds
        addNewNotification();
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // Update unread count - Cập nhật số lượng chưa đọc
    const unread = notifications.filter(n => !n.read).length;
    setUnreadCount(unread);
  }, [notifications]);

  const loadMockNotifications = () => {
    const mockNotifications: Notification[] = [
      {
        id: 'notif-1',
        type: 'urgent',
        title: 'Urgent Request',
        message: 'Guest in Room 305 needs immediate assistance with air conditioning',
        timestamp: new Date(Date.now() - 300000).toISOString(),
        read: false,
        priority: 'urgent'
      },
      {
        id: 'notif-2',
        type: 'new_chat',
        title: 'New Chat Session',
        message: 'Maria Garcia started a new conversation from Restaurant',
        timestamp: new Date(Date.now() - 600000).toISOString(),
        read: false,
        priority: 'normal'
      },
      {
        id: 'notif-3',
        type: 'new_message',
        title: 'New Message',
        message: 'Kim Junho sent a message in Room 512 chat',
        timestamp: new Date(Date.now() - 900000).toISOString(),
        read: true,
        priority: 'normal'
      }
    ];

    setNotifications(mockNotifications);
  };

  const addNewNotification = () => {
    const newNotification: Notification = {
      id: `notif-${Date.now()}`,
      type: Math.random() > 0.5 ? 'new_message' : 'new_chat',
      title: Math.random() > 0.5 ? 'New Message' : 'New Chat Request',
      message: `Guest ${Math.random() > 0.5 ? 'John' : 'Sarah'} needs assistance`,
      timestamp: new Date().toISOString(),
      read: false,
      priority: Math.random() > 0.7 ? 'high' : 'normal'
    };

    setNotifications(prev => [newNotification, ...prev]);
    
    // Show browser notification if supported
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(newNotification.title, {
        body: newNotification.message,
        icon: '/favicon.ico',
        badge: '/favicon.ico'
      });
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, read: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id);
    onNotificationClick?.(notification);
  };

  const formatTime = (timestamp: string) => {
    const now = new Date();
    const notifTime = new Date(timestamp);
    const diffMs = now.getTime() - notifTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return `${Math.floor(diffMins / 1440)}d ago`;
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_message': return '💬';
      case 'new_chat': return '🔔';
      case 'urgent': return '🚨';
      case 'system': return '⚙️';
      default: return '📢';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '#ef4444';
      case 'high': return '#f97316';
      case 'normal': return '#3b82f6';
      case 'low': return '#6b7280';
      default: return '#6b7280';
    }
  };

  return (
    <div className={styles.notificationCenter}>
      <button
        className={styles.notificationButton}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className={styles.bellIcon}>🔔</span>
        {unreadCount > 0 && (
          <span className={styles.notificationBadge}>
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className={styles.notificationDropdown}>
          <div className={styles.notificationHeader}>
            <h3>Notifications</h3>
            <div className={styles.headerActions}>
              {unreadCount > 0 && (
                <button
                  className={styles.markAllRead}
                  onClick={markAllAsRead}
                >
                  Mark all read
                </button>
              )}
              <button
                className={styles.closeButton}
                onClick={() => setIsOpen(false)}
              >
                ✕
              </button>
            </div>
          </div>

          <div className={styles.notificationList}>
            {notifications.length === 0 ? (
              <div className={styles.emptyState}>
                <span className={styles.emptyIcon}>🔔</span>
                <p>No notifications yet</p>
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`${styles.notificationItem} ${
                    !notification.read ? styles.unread : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className={styles.notificationIcon}>
                    {getNotificationIcon(notification.type)}
                  </div>
                  
                  <div className={styles.notificationContent}>
                    <div className={styles.notificationTitle}>
                      {notification.title}
                      <span
                        className={styles.priorityDot}
                        style={{ backgroundColor: getPriorityColor(notification.priority) }}
                      />
                    </div>
                    <div className={styles.notificationMessage}>
                      {notification.message}
                    </div>
                    <div className={styles.notificationTime}>
                      {formatTime(notification.timestamp)}
                    </div>
                  </div>

                  <div className={styles.notificationActions}>
                    <button
                      className={styles.deleteButton}
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteNotification(notification.id);
                      }}
                    >
                      ×
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>

          {notifications.length > 0 && (
            <div className={styles.notificationFooter}>
              <button className={styles.viewAllButton}>
                View All Notifications
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
