import useSWR from 'swr';
import { User } from '@/services/UserService';

const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    const error = new Error('An error occurred while fetching the data.');
    const data = await response.json();
    (error as any).info = data.error;
    (error as any).status = response.status;
    throw error;
  }
  return response.json();
};

export interface UserResponse {
  data: User;
}

export function useUser(id: string | null) {
  const { data, error, mutate, isLoading } = useSWR<UserResponse>(
    id ? `/api/users/${id}` : null,
    fetcher
  );
  
  return {
    user: data?.data,
    isLoading,
    isError: error,
    mutate,
  };
}
