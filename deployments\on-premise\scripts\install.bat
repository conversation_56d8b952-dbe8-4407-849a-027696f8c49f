@echo off
REM Script cài đặt hệ thống LoaLoa On-Premise cho Windows

REM Kiểm tra Docker và Docker Compose
where docker >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Docker không được tìm thấy. Vui lòng cài đặt Docker trước khi tiếp tục.
    exit /b 1
)

where docker-compose >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Docker Compose không được tìm thấy. Vui lòng cài đặt Docker Compose trước khi tiếp tục.
    exit /b 1
)

REM Tạo file .env nếu chưa tồn tại
if not exist .env (
    echo Tạo file .env...
   REM Tạo các khóa ngẫu nhiên bằng PowerShell
    powershell -Command "$env:POSTGRES_PASSWORD = -join ((48..57) + (65..90) + (97..122) | Get-Random -Count 24 | ForEach-Object {[char]$_}); Write-Output $env:POSTGRES_PASSWORD" > temp.txt
    set /p POSTGRES_PASSWORD=<temp.txt
    
    powershell -Command "$env:SUPABASE_JWT_SECRET = -join ((48..57) + (65..90) + (97..122) | Get-Random -Count 32 | ForEach-Object {[char]$_}); Write-Output $env:SUPABASE_JWT_SECRET" > temp.txt
    set /p SUPABASE_JWT_SECRET=<temp.txt
    
    powershell -Command "$env:SUPABASE_ANON_KEY = -join ((48..57) + (65..90) + (97..122) | Get-Random -Count 24 | ForEach-Object {[char]$_}); Write-Output $env:SUPABASE_ANON_KEY" > temp.txt
    set /p SUPABASE_ANON_KEY=<temp.txt
    
    powershell -Command "$env:SUPABASE_SERVICE_ROLE_KEY = -join ((48..57) + (65..90) + (97..122) | Get-Random -Count 24 | ForEach-Object {[char]$_}); Write-Output $env:SUPABASE_SERVICE_ROLE_KEY" > temp.txt
    set /p SUPABASE_SERVICE_ROLE_KEY=<temp.txt
    
    del temp.txt
    
    echo POSTGRES_PASSWORD=%POSTGRES_PASSWORD%> .env
    echo POSTGRES_USER=postgres>> .env
    echo SUPABASE_JWT_SECRET=%SUPABASE_JWT_SECRET%>> .env
    echo SUPABASE_ANON_KEY=%SUPABASE_ANON_KEY%>> .env
    echo SUPABASE_SERVICE_ROLE_KEY=%SUPABASE_SERVICE_ROLE_KEY%>> .env
    echo LICENSE_SERVER_URL=https://license.loaloa.app>> .env
    
    echo File .env đã được tạo với các khóa ngẫu nhiên.
)

REM Khởi chạy hệ thống
echo Khởi động hệ thống LoaLoa On-Premise...
docker-compose up -d

REM Kiểm tra trạng thái các container
echo Kiểm tra trạng thái các container...
docker-compose ps

echo Cài đặt hoàn tất. Vui lòng truy cập Admin Portal tại http://localhost:3000 để kích hoạt license.