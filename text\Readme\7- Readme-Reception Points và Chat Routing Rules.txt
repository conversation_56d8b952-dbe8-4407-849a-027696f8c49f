Tài liệu hướng dẫn: Reception Points và Chat Routing Rules
Tổng quan
Module Reception Points và Chat Routing Rules trong hệ thống LoaLoa cung cấp cơ chế định tuyến tin nhắn từ khách hàng đến nhân viên phù hợp dựa trên các quy tắc và điều kiện đã thiết lập. Hệ thống này giúp đảm bảo tin nhắn được xử lý bởi đúng người có thẩm quyền, cải thiện trải nghiệm khách hàng và hiệu quả vận hành cho khách sạn/resort.

Các thành phần chính
1. Reception Points (Điểm nhận tin nhắn)
Là điểm đầu vào cho các tin nhắn từ khách hàng
Được gán cho phòng hoặc khu vực cụ thể
Có thể phân công nhân viên để xử lý tin nhắn từ điểm này
2. Chat Routing Rules (Quy tắc định tuyến chat)
Quyết định tin nhắn sẽ được chuyển đến đâu dựa trên các điều kiện
Các loại quy tắc: QR Code, Guest Type, Time, Language, Custom
Có thể định tuyến đến: Department, User cụ thể, hoặc Reception Point
3. Staff Assignments (Phân công nhân viên)
Phân công nhân viên vào các bộ phận khác nhau
Xác định thời gian làm việc và mức độ ưu tiên
Liên kết với Reception Points để xử lý tin nhắn
Cấu trúc cơ sở dữ liệu
Bảng tenant_message_reception_points
CREATE TABLE tenant_message_reception_points (
  id UUID DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(50) NOT NULL,
  description TEXT,
  icon_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  priority INTEGER DEFAULT 1,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
  UNIQUE (tenant_id, code)
);

-- Comments
COMMENT ON TABLE tenant_message_reception_points IS 'Các điểm nhận tin nhắn, dùng để định tuyến tin nhắn từ khách đến nhân viên phù hợp';
COMMENT ON COLUMN tenant_message_reception_points.name IS 'Tên hiển thị của điểm nhận tin nhắn';
COMMENT ON COLUMN tenant_message_reception_points.code IS 'Mã định danh cho điểm nhận tin nhắn, dùng trong các phép dẫn chiếu';
COMMENT ON COLUMN tenant_message_reception_points.description IS 'Mô tả chi tiết về điểm nhận tin nhắn';
COMMENT ON COLUMN tenant_message_reception_points.icon_url IS 'URL biểu tượng (nếu có) của điểm nhận tin nhắn';
COMMENT ON COLUMN tenant_message_reception_points.is_active IS 'Trạng thái hoạt động của điểm nhận tin nhắn';
COMMENT ON COLUMN tenant_message_reception_points.priority IS 'Độ ưu tiên để sắp xếp các điểm nhận tin nhắn';
Bảng tenant_chat_routing_rules
CREATE TABLE tenant_chat_routing_rules (
  id UUID DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  rule_name VARCHAR(100) NOT NULL,
  rule_type VARCHAR(50) NOT NULL,
  rule_condition JSONB NOT NULL,
  target_department VARCHAR(50),
  target_user_id UUID REFERENCES tenant_users(id),
  target_reception_point_id UUID REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL,
  priority INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Comments
COMMENT ON TABLE tenant_chat_routing_rules IS 'Quy tắc định tuyến chat từ khách hàng tới nhân viên hoặc điểm nhận tin nhắn';
COMMENT ON COLUMN tenant_chat_routing_rules.rule_name IS 'Tên quy tắc định tuyến';
COMMENT ON COLUMN tenant_chat_routing_rules.rule_type IS 'Loại quy tắc: qr_code, guest, time, language, custom';
COMMENT ON COLUMN tenant_chat_routing_rules.rule_condition IS 'Điều kiện áp dụng quy tắc, dạng JSON tùy thuộc vào rule_type';
COMMENT ON COLUMN tenant_chat_routing_rules.target_department IS 'Bộ phận đích để định tuyến';
COMMENT ON COLUMN tenant_chat_routing_rules.target_user_id IS 'ID của nhân viên cụ thể để định tuyến';
COMMENT ON COLUMN tenant_chat_routing_rules.target_reception_point_id IS 'Điểm nhận tin nhắn đích khi quy tắc này khớp';
COMMENT ON COLUMN tenant_chat_routing_rules.priority IS 'Mức độ ưu tiên của quy tắc';
COMMENT ON COLUMN tenant_chat_routing_rules.is_active IS 'Trạng thái kích hoạt của quy tắc';
Bảng tenant_user_reception_points
CREATE TABLE tenant_user_reception_points (
  id UUID DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  tenant_user_id UUID NOT NULL REFERENCES tenant_users(id) ON DELETE CASCADE,
  reception_point_id UUID NOT NULL REFERENCES tenant_message_reception_points(id) ON DELETE CASCADE,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  UNIQUE (tenant_user_id, reception_point_id)
);

-- Comments
COMMENT ON TABLE tenant_user_reception_points IS 'Liên kết giữa nhân viên và các điểm nhận tin nhắn mà họ phụ trách';
COMMENT ON COLUMN tenant_user_reception_points.is_primary IS 'Chỉ định đây có phải là điểm nhận chính của nhân viên hay không';
Functions và Triggers
Triggers để tự động cập nhật trường updated_at
-- Tạo function cập nhật trường updated_at cho tenant_message_reception_points
CREATE OR REPLACE FUNCTION update_tenant_message_reception_points_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;

$$ LANGUAGE plpgsql;

-- Tạo trigger cho bảng tenant_message_reception_points
CREATE TRIGGER update_tenant_message_reception_points_updated_at
BEFORE UPDATE ON tenant_message_reception_points
FOR EACH ROW
EXECUTE FUNCTION update_tenant_message_reception_points_updated_at();

-- Tạo function cập nhật trường updated_at cho tenant_chat_routing_rules
CREATE OR REPLACE FUNCTION update_tenant_chat_routing_rules_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;

$$ LANGUAGE plpgsql;

-- Tạo trigger cho bảng tenant_chat_routing_rules
CREATE TRIGGER update_tenant_chat_routing_rules_updated_at
BEFORE UPDATE ON tenant_chat_routing_rules
FOR EACH ROW
EXECUTE FUNCTION update_tenant_chat_routing_rules_updated_at();

COMMENT ON FUNCTION update_tenant_message_reception_points_updated_at() IS 'Tự động cập nhật trường updated_at khi có thay đổi';
COMMENT ON FUNCTION update_tenant_chat_routing_rules_updated_at() IS 'Tự động cập nhật trường updated_at khi có thay đổi';
Function RPC để định tuyến chat
CREATE OR REPLACE FUNCTION route_chat_message(
    p_tenant_id UUID,
    p_source_type TEXT, -- 'qr_code', 'room', 'area'
    p_source_id TEXT,
    p_language_code TEXT DEFAULT 'en',
    p_guest_type TEXT DEFAULT 'regular',
    p_room_type TEXT DEFAULT NULL
)
RETURNS TABLE (
    target_type TEXT, -- 'department', 'user', 'reception_point'
    target_id UUID,
    target_name TEXT,
    priority INTEGER
) AS $$
DECLARE
    v_current_hour INTEGER;
    v_current_day TEXT;
    v_rule_found BOOLEAN := FALSE;
BEGIN
    -- Lấy giờ và ngày hiện tại
    v_current_hour := EXTRACT(HOUR FROM CURRENT_TIME);
    v_current_day := LOWER(TO_CHAR(CURRENT_DATE, 'day'));
    v_current_day := TRIM(v_current_day);  -- Loại bỏ khoảng trắng

    -- Ưu tiên quy tắc QR Code theo nguồn
    IF p_source_type = 'qr_code' THEN
        RETURN QUERY
        SELECT 
            CASE
                WHEN r.target_department IS NOT NULL THEN 'department'
                WHEN r.target_user_id IS NOT NULL THEN 'user'
                WHEN r.target_reception_point_id IS NOT NULL THEN 'reception_point'
            END AS target_type,
            COALESCE(r.target_user_id, r.target_reception_point_id) AS target_id,
            COALESCE(
                ud.display_name,
                rp.name,
                r.target_department
            ) AS target_name,
            r.priority
        FROM tenant_chat_routing_rules r
        LEFT JOIN tenant_users_details ud ON r.target_user_id = ud.tenant_user_id
        LEFT JOIN tenant_message_reception_points rp ON r.target_reception_point_id = rp.id
        WHERE 
            r.tenant_id = p_tenant_id
            AND r.is_active = TRUE
            AND r.rule_type = 'qr_code'
            AND (r.rule_condition->>'qr_code_id' = p_source_id OR r.rule_condition->>'qr_code_id' IS NULL OR r.rule_condition->>'qr_code_id' = '')
        ORDER BY r.priority DESC, r.created_at DESC
        LIMIT 1;
        
        -- Kiểm tra xem đã tìm thấy quy tắc nào chưa
        IF FOUND THEN
            v_rule_found := TRUE;
            RETURN;
        END IF;
    END IF;

    -- Các quy tắc định tuyến khác có logic tương tự
    -- (Đã rút gọn để readme không quá dài)

    -- Nếu không có quy tắc nào phù hợp, sử dụng bộ phận mặc định là Reception
    IF NOT v_rule_found THEN
        RETURN QUERY
        SELECT 
            'department'::TEXT AS target_type,
            NULL::UUID AS target_id,
            'reception'::TEXT AS target_name,
            0 AS priority;
    END IF;
END;

$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION route_chat_message IS 'Định tuyến tin nhắn chat dựa trên các quy tắc và điều kiện đã thiết lập';
View hiển thị staff được gán vào reception point
CREATE OR REPLACE VIEW tenant_reception_point_staff_view AS
SELECT 
    t.id AS tenant_id,
    trp.id AS reception_point_id,
    trp.name AS reception_point_name,
    trp.code AS reception_point_code,
    tu.id AS user_id,
    tud.display_name,
    tud.email,
    tu.role,
    turp.is_primary,
    turp.created_at AS assigned_at
FROM 
    tenant_message_reception_points trp
    JOIN tenant_user_reception_points turp ON trp.id = turp.reception_point_id
    JOIN tenant_users tu ON turp.tenant_user_id = tu.id
    JOIN tenant_users_details tud ON tu.id = tud.tenant_user_id
    JOIN tenants t ON trp.tenant_id = t.id
WHERE 
    trp.is_active = true
    AND tu.is_active = true;

COMMENT ON VIEW tenant_reception_point_staff_view IS 'Hiển thị nhân viên được gán vào từng điểm nhận tin nhắn';
Cấu trúc thư mục các trang và components
D:\loaloa\apps\admin-portal\app\
├── components\
│   ├── chat-routing\
│   │   ├── ChatRoutingRuleForm.tsx
│   │   └── ChatRoutingRuleForm.module.scss
│   ├── reception-points\
│   │   ├── ReceptionPointForm.tsx
│   │   └── ReceptionPointForm.module.scss
│   └── rooms\
│       ├── ReceptionPointInfo.tsx
│       └── ReceptionPointInfo.module.scss
├── reception-points\
│   ├── page.tsx
│   ├── reception-points.module.scss
│   ├── create\
│   │   ├── page.tsx
│   │   └── create-point.module.scss
│   └── [id]\
│       ├── page.tsx
│       ├── reception-point-detail.module.scss
│       └── edit\
│           ├── page.tsx
│           └── edit-point.module.scss
└── chat-routing-rules\
    ├── page.tsx
    ├── chat-routing-rules.module.scss
    ├── create\
    │   ├── page.tsx
    │   └── create-rule.module.scss
    └── [id]\
        ├── page.tsx
        ├── rule-detail.module.scss
        └── edit\
            ├── page.tsx
            └── edit-rule.module.scss
API Endpoints
Reception Points
GET /api/reception-points - Lấy danh sách các điểm nhận tin nhắn
POST /api/reception-points - Tạo điểm nhận tin nhắn mới
GET /api/reception-points/{id} - Lấy thông tin chi tiết điểm nhận tin nhắn
PUT /api/reception-points/{id} - Cập nhật điểm nhận tin nhắn
DELETE /api/reception-points/{id} - Xóa điểm nhận tin nhắn
Chat Routing Rules
GET /api/chat-routing-rules - Lấy danh sách các quy tắc định tuyến
POST /api/chat-routing-rules - Tạo quy tắc định tuyến mới
GET /api/chat-routing-rules/{id} - Lấy thông tin chi tiết quy tắc
PUT /api/chat-routing-rules/{id} - Cập nhật quy tắc định tuyến
DELETE /api/chat-routing-rules/{id} - Xóa quy tắc định tuyến
User Reception Points
GET /api/users/{id}/reception-points - Lấy các điểm nhận tin nhắn của nhân viên
POST /api/users/{id}/reception-points - Gán nhân viên vào điểm nhận tin nhắn
PUT /api/users/{id}/reception-points/{linkId} - Cập nhật liên kết nhân viên-điểm nhận
DELETE /api/users/{id}/reception-points/{linkId} - Xóa liên kết nhân viên-điểm nhận
Luồng hoạt động (Workflow)
Thiết lập Reception Points:

Tạo các điểm nhận tin nhắn cho các khu vực khác nhau của khách sạn/resort
Gán điểm nhận tin nhắn cho phòng và khu vực tương ứng
Phân công nhân viên:

Liên kết nhân viên với các điểm nhận tin nhắn
Đảm bảo mỗi điểm nhận tin nhắn có ít nhất một nhân viên được gán
Thiết lập Chat Routing Rules:

Tạo các quy tắc định tuyến với điều kiện và mức ưu tiên phù hợp
Quy tắc có thể định tuyến đến bộ phận, nhân viên cụ thể hoặc điểm nhận tin nhắn
Khi khách gửi tin nhắn:

Hệ thống xác định nguồn của tin nhắn (QR code, phòng, khu vực)
Function route_chat_message áp dụng các quy tắc để tìm đích đến phù hợp
Tin nhắn được chuyển đến bộ phận/nhân viên/điểm nhận tin nhắn tương ứng
Các lưu ý khi triển khai
Ưu tiên của quy tắc:

Quy tắc có priority cao hơn sẽ được ưu tiên đánh giá trước
Trong cùng mức ưu tiên, quy tắc được tạo gần đây nhất sẽ được ưu tiên
Loại quy tắc:

qr_code: Dựa trên mã QR cụ thể
guest: Dựa trên loại khách (VIP, regular...) và/hoặc loại phòng
time: Dựa trên giờ trong ngày và ngày trong tuần
language: Dựa trên ngôn ngữ của khách
custom: Điều kiện tùy chỉnh dựa trên JSON
Đích đến của tin nhắn:

department: Gửi đến bộ phận cụ thể (reception, housekeeping...)
user: Gửi đến nhân viên cụ thể theo ID
reception_point: Gửi đến điểm nhận tin nhắn cụ thể
Định tuyến mặc định:

Nếu không có quy tắc nào khớp, tin nhắn sẽ được gửi đến bộ phận reception
Hướng dẫn sử dụng
1. Quản lý Reception Points
Tạo mới Reception Point:
Truy cập vào trang Reception Points (/reception-points)
Nhấp vào nút "Create Reception Point"
Điền các thông tin cần thiết:
Name: Tên hiển thị của điểm nhận tin nhắn
Code: Mã định danh duy nhất (chỉ chữ cái, số, gạch ngang và gạch dưới)
Description: Mô tả chi tiết về điểm nhận tin nhắn (tùy chọn)
Priority: Mức độ ưu tiên (1-100)
Status: Trạng thái kích hoạt
Nhấp "Create Reception Point" để lưu
Liên kết Reception Point với Room hoặc Area:
Truy cập vào trang Room hoặc Area cần gán
Chọn "Edit" để chỉnh sửa
Trong form chỉnh sửa, chọn Reception Point từ dropdown
Lưu thay đổi
2. Quản lý Chat Routing Rules
Tạo mới Chat Routing Rule:
Truy cập vào trang Chat Routing Rules (/chat-routing-rules)
Nhấp vào nút "Create Routing Rule"
Điền các thông tin cơ bản:
Rule Name: Tên quy tắc
Rule Type: Loại quy tắc (QR Code, Guest Type, Time, Language, Custom)
Priority: Mức độ ưu tiên (1-100)
Thiết lập điều kiện dựa trên loại quy tắc đã chọn
Chọn đích đến của tin nhắn:
Department: Chọn bộ phận
Specific Staff: Chọn nhân viên cụ thể
Reception Point: Chọn điểm nhận tin nhắn
Nhấp "Create Rule" để lưu
3. Phân công nhân viên vào Reception Point
Truy cập vào trang chi tiết nhân viên
Tìm phần "Reception Points"
Nhấp vào "Assign to Reception Point"
Chọn Reception Point từ dropdown
Tùy chọn đánh dấu là "Primary Reception Point" nếu cần
Lưu thay đổi
Thử nghiệm định tuyến
Để thử nghiệm việc định tuyến tin nhắn, bạn có thể:

Tạo các Reception Points khác nhau (ví dụ: Lobby, Restaurant, Spa)
Gán các Reception Points này cho các phòng và khu vực khác nhau
Phân công nhân viên vào các Reception Points
Tạo các quy tắc định tuyến với các điều kiện và đích đến khác nhau
Thử gửi tin nhắn từ các nguồn khác nhau (QR code, phòng, khu vực) và xác nhận rằng tin nhắn được định tuyến đúng theo các quy tắc đã thiết lập
Lưu ý bổ sung
Các quy tắc định tuyến được đánh giá theo thứ tự ưu tiên giảm dần
Chỉ quy tắc đầu tiên khớp với điều kiện sẽ được áp dụng
Đảm bảo mỗi điểm nhận tin nhắn có ít nhất một nhân viên được phân công để tránh tin nhắn không có người xử lý
Các quy tắc không kích hoạt (is_active = false) sẽ không được sử dụng trong quá trình định tuyến