'use client'

import { useState } from 'react'

export default function TestQRPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const testQRCode = async (code: string) => {
    try {
      setLoading(true)
      setError(null)
      setResult(null)

      console.log('Testing QR code:', code)

      const response = await fetch(`/api/qr-scan/${code}?lang=en&device=test-device`)
      const data = await response.json()

      console.log('Response:', data)

      if (response.ok) {
        setResult(data)
      } else {
        setError(data.error || 'Unknown error')
      }

    } catch (err) {
      console.error('Error:', err)
      setError(err instanceof Error ? err.message : 'Network error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">QR Code Test</h1>
        
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">Test QR Codes</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => testQRCode('DEMO-QR-001')}
              disabled={loading}
              className="bg-blue-500 text-white p-4 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              Test Demo QR
            </button>
            
            <button
              onClick={() => testQRCode('mbblsqjm-i24r2')}
              disabled={loading}
              className="bg-green-500 text-white p-4 rounded hover:bg-green-600 disabled:opacity-50"
            >
              Test Business Center QR
            </button>
            
            <button
              onClick={() => testQRCode('invalid-qr-code')}
              disabled={loading}
              className="bg-red-500 text-white p-4 rounded hover:bg-red-600 disabled:opacity-50"
            >
              Test Invalid QR
            </button>
          </div>
        </div>

        {loading && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-700 mr-2"></div>
              Testing QR code...
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <strong>Error:</strong> {error}
          </div>
        )}

        {result && (
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">Result:</h3>
            <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
              {JSON.stringify(result, null, 2)}
            </pre>
            
            {result.success && result.redirect_url && (
              <div className="mt-4">
                <a
                  href={result.redirect_url}
                  className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600"
                >
                  Go to Chat
                </a>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
