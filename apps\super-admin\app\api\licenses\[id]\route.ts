import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '../../../../utils/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log('GET /api/licenses/[id] - Start', { id: params.id });
  try {
    const licenseId = params.id;
    
    const supabase = await createClient();
    
    // Lấy thông tin license
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('id', licenseId)
      .single();
    
    if (error) {
      console.error('Error fetching license:', error);
      return NextResponse.json(
        { error: `Failed to fetch license: ${error.message}` },
        { status: 500 }
      );
    }
    
    if (!license) {
      return NextResponse.json(
        { error: 'License not found' },
        { status: 404 }
      );
    }
    
    // Lấy số lượng hoạt động và cảnh báo clone nếu có
    const [activitiesResponse, clonesResponse] = await Promise.all([
      supabase
        .from('license_activities')
        .select('count', { count: 'exact' })
        .eq('license_id', licenseId),
      
      supabase
        .from('license_clones')
        .select('count', { count: 'exact', head: true })
        .eq('license_id', licenseId),
    ]);
    
    // Xác định trạng thái và số ngày còn lại
    const expiryDate = new Date(license.expiry_date);
    const currentDate = new Date();
    const daysRemaining = Math.ceil((expiryDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
    
    let status = 'ACTIVE';
    if (expiryDate < currentDate) {
      status = 'EXPIRED';
    } else if (!license.is_active) {
      status = 'REVOKED';
    } else if (!license.activation_date) {
      status = 'NOT_ACTIVATED';
    } else if (daysRemaining <= 30) {
      status = 'EXPIRING_SOON';
    }
    
    // Bổ sung thông tin vào license
    const enhancedLicense = {
      ...license,
      status,
      days_remaining: daysRemaining > 0 ? daysRemaining : 0,
      activity_count: activitiesResponse.count || 0,
      clone_alerts: clonesResponse.count || 0,
      unreviewed_clone_alerts: 0 // Có thể làm thêm query để lấy số lượng cảnh báo chưa xử lý
    };
    
    return NextResponse.json({ data: enhancedLicense });
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch license details' },
      { status: 500 }
    );
  }
}

// PUT API để cập nhật license (đổi trạng thái hoặc gia hạn)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log('PUT /api/licenses/[id] - Start', { id: params.id });
  try {
    const licenseId = params.id;
    const updateData = await request.json();
    console.log('Update data:', updateData);
    
    // Validate input
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: 'No data provided for update' },
        { status: 400 }
      );
    }
    
    const supabase = await createClient();
    
    // Chuẩn bị dữ liệu cập nhật
    const updatePayload: Record<string, any> = {};
    
    // Xử lý các trường hợp cập nhật
    if (updateData.is_active !== undefined) {
      updatePayload.is_active = updateData.is_active;
    }
    
    if (updateData.additionalDays) {
      // Gia hạn license
      const { data: existingLicense } = await supabase
        .from('licenses')
        .select('expiry_date')
        .eq('id', licenseId)
        .single();
      
      if (existingLicense) {
        let expiryDate = new Date(existingLicense.expiry_date);
        
        // Nếu đã hết hạn, gia hạn từ ngày hiện tại
        if (expiryDate < new Date()) {
          expiryDate = new Date();
        }
        
        expiryDate.setDate(expiryDate.getDate() + updateData.additionalDays);
        updatePayload.expiry_date = expiryDate.toISOString();
      }
    }
    
    if (updateData.revocation_reason) {
      updatePayload.revocation_reason = updateData.revocation_reason;
      updatePayload.is_active = false;
    }
    
    // Luôn cập nhật updated_at
    updatePayload.updated_at = new Date().toISOString();
    
    // Thực hiện cập nhật
    const { data: updatedLicense, error } = await supabase
      .from('licenses')
      .update(updatePayload)
      .eq('id', licenseId)
      .select('*')
      .single();
    
    if (error) {
      console.error('Error updating license:', error);
      return NextResponse.json(
        { error: `Failed to update license: ${error.message}` },
        { status: 500 }
      );
    }
    
    // Thêm hoạt động vào bảng license_activities
    let activityType = 'CHECK_IN';
    let activityDetails = { action: 'update' };
    
    if (updateData.is_active === true) {
      activityType = 'ACTIVATION';
      activityDetails = { action: 'activate', message: 'License activated by admin' };
    } else if (updateData.is_active === false) {
      activityType = 'REVOCATION';
      activityDetails = { 
        action: 'deactivate', 
        reason: updateData.revocation_reason || 'Deactivated by admin',
        message: 'License deactivated by admin' 
      };
    } else if (updateData.additionalDays) {
      activityType = 'CHECK_IN';
      activityDetails = { 
        action: 'extend', 
        days_added: updateData.additionalDays,
        message: `License extended by ${updateData.additionalDays} days` 
      };
    }
    
    await supabase
      .from('license_activities')
      .insert({
        license_id: licenseId,
        activity_type: activityType,
        details: activityDetails
      });
    
    // Xác định trạng thái và số ngày còn lại
    const expiryDate = new Date(updatedLicense.expiry_date);
    const currentDate = new Date();
    const daysRemaining = Math.ceil((expiryDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
    
    let status = 'ACTIVE';
    if (expiryDate < currentDate) {
      status = 'EXPIRED';
    } else if (!updatedLicense.is_active) {
      status = 'REVOKED';
    } else if (!updatedLicense.activation_date) {
      status = 'NOT_ACTIVATED';
    } else if (daysRemaining <= 30) {
      status = 'EXPIRING_SOON';
    }
    
    // Trả về license đã cập nhật với thông tin bổ sung
    const enhancedLicense = {
      ...updatedLicense,
      status,
      days_remaining: daysRemaining > 0 ? daysRemaining : 0
    };
    
    return NextResponse.json({ data: enhancedLicense });
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update license' },
      { status: 500 }
    );
  }
}