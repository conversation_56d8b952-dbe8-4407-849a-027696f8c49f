import fs from 'fs';
import path from 'path';
import { 
  getLicenseConfig as getSharedLicenseConfig, 
  validateLicense as validateSharedLicense, 
  getCurrentTenantId as getSharedTenantId,
  hasValidLicenseConfig as hasSharedValidLicense,
  type LicenseConfig as SharedLicenseConfig
} from '@loaloa/license-client';

// Keep original interface for backward compatibility
export interface LicenseConfig {
  licenseKey: string;
  customerName: string;
  email: string;
  tenant_id: string;
}

/**
 * Get license config - now uses shared license client
 * Falls back to local file for backward compatibility
 */
export function getLicenseConfig(): LicenseConfig | null {
  try {
    // First try shared license client (reads from monorepo root)
    const sharedConfig = getSharedLicenseConfig();
    if (sharedConfig) {
      return sharedConfig;
    }

    // Fallback to local file (legacy support)
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error reading license config:', error);
    return null;
  }
}

/**
 * Get tenant ID - enhanced with shared license validation
 */
export async function getTenantId(): Promise<string> {
  try {
    // First try shared license client
    const tenantId = await getSharedTenantId();
    if (tenantId) {
      return tenantId;
    }

    // Fallback to local config
    const config = getLicenseConfig();
    if (!config || !config.tenant_id) {
      throw new Error('Cannot find Tenant ID. Please activate your license.');
    }
    return config.tenant_id;
  } catch (error) {
    console.error('Error getting tenant ID:', error);
    throw new Error('Cannot find Tenant ID. Please activate your license.');
  }
}

/**
 * Update license config - now updates both local and shared locations
 */
export function updateLicenseConfig(updates: Partial<LicenseConfig>): boolean {
  try {
    // Update local config for backward compatibility
    const localConfigPath = path.resolve('./license_config.json');
    let localConfig: LicenseConfig;
    
    if (fs.existsSync(localConfigPath)) {
      const fileContent = fs.readFileSync(localConfigPath, 'utf8');
      localConfig = JSON.parse(fileContent);
    } else {
      localConfig = {
        licenseKey: "",
        customerName: "",
        email: "",
        tenant_id: ""
      };
    }
    
    // Update local config
    const updatedLocalConfig = { ...localConfig, ...updates };
    fs.writeFileSync(localConfigPath, JSON.stringify(updatedLocalConfig, null, 2));

    // Update shared config at monorepo root
    const sharedConfigPath = path.resolve('../../license_config.json');
    let sharedConfig: LicenseConfig;
    
    if (fs.existsSync(sharedConfigPath)) {
      const fileContent = fs.readFileSync(sharedConfigPath, 'utf8');
      sharedConfig = JSON.parse(fileContent);
    } else {
      sharedConfig = {
        licenseKey: "",
        customerName: "",
        email: "",
        tenant_id: ""
      };
    }
    
    // Update shared config
    const updatedSharedConfig = { ...sharedConfig, ...updates };
    fs.writeFileSync(sharedConfigPath, JSON.stringify(updatedSharedConfig, null, 2));
    
    console.log('License config updated in both local and shared locations');
    return true;
  } catch (error) {
    console.error('Error updating license config:', error);
    return false;
  }
}

/**
 * Check if has valid license - enhanced with shared validation
 */
export function hasValidLicense(): boolean {
  try {
    // First check shared license client
    if (hasSharedValidLicense()) {
      return true;
    }

    // Fallback to local config check
    const config = getLicenseConfig();
    return !!config?.licenseKey && !!config?.tenant_id;
  } catch (error) {
    console.error('Error checking license validity:', error);
    return false;
  }
}

/**
 * Validate license with database - new function using shared client
 */
export async function validateLicense() {
  try {
    return await validateSharedLicense();
  } catch (error) {
    console.error('Error validating license:', error);
    return {
      success: false,
      error: 'Failed to validate license'
    };
  }
}

// Legacy compatibility exports
export { getLicenseConfig as getSharedLicenseConfig };
