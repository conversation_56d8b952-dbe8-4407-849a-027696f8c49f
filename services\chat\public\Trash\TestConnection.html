<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Supabase Connection Test</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <style>
    body { font-family: 'Segoe UI', sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    .panel { background: white; padding: 15px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    h3 { margin-top: 0; }
    .status { margin-bottom: 10px; }
    .success { color: green; }
    .error { color: red; }
    .note { color: #666; }
    input { width: 100%; padding: 8px; margin-bottom: 10px; box-sizing: border-box; }
    button { background-color: #4CAF50; color: white; border: none; padding: 8px 12px; cursor: pointer; border-radius: 4px; }
    button:hover { background-color: #45a049; }
    .log { height: 200px; background: #f9f9f9; border: 1px solid #ddd; padding: 10px; overflow-y: auto; font-family: monospace; margin-top: 10px; }
    .explanation { background: #e3f2fd; padding: 10px; border-radius: 4px; margin: 15px 0; }
  </style>
</head>
<body>
  <h1>Supabase Connection Test</h1>
  
  <div class="explanation">
    <p><strong>Về lỗi kết nối Supabase từ trang HTML:</strong></p>
    <ol>
      <li>Khi mở file HTML trực tiếp (file://), các yêu cầu mạng bị hạn chế bởi trình duyệt</li>
      <li>Để khắc phục, bạn cần mở file thông qua một máy chủ web (http://)</li>
      <li>Các API keys công khai nên được thiết lập RLS (Row Level Security) phù hợp</li>
    </ol>
  </div>
  
  <!-- Panel 1: Trực tiếp từ HTML -->
  <div class="panel">
    <h3>Phương pháp 1: Kết nối trực tiếp từ HTML</h3>
    <div class="note">Phương pháp này có thể gặp lỗi CORS khi mở file trực tiếp</div>
    <div>
      <input type="text" id="supabase-url-1" placeholder="Supabase URL" value="https://iwzwbrbmojvvvfstbqow.supabase.co">
      <input type="password" id="supabase-key-1" placeholder="Supabase Key (anon public key)" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3endicmJtb2p2dnZmc3RicW93Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMjkyNjEsImV4cCI6MjA2MTkwNTI2MX0.tyVtaSclUKC5fGh7I7Ohpm7c4FniXphYe34-cxBvo6E">
      <button id="connect-direct">Kết nối trực tiếp</button>
      <div class="status" id="status-1"></div>
    </div>
  </div>
  
  <!-- Panel 2: Thông qua Proxy -->
  <div class="panel">
    <h3>Phương pháp 2: Kết nối thông qua Máy chủ Chat hiện có (API Gateway)</h3>
    <div class="note">Phương pháp này sử dụng máy chủ chat của bạn như một proxy</div>
    <div>
      <input type="text" id="server-url" placeholder="Chat Server URL" value="http://localhost:3002">
      <button id="connect-proxy">Kết nối qua Proxy</button>
      <div class="status" id="status-2"></div>
    </div>
  </div>
  
  <!-- Panel 3: Service Role Key -->
  <div class="panel">
    <h3>Phương pháp 3: Sử dụng Service Role Key (chỉ dùng cho thử nghiệm)</h3>
    <div class="note">Phương pháp này không được khuyến khích trong môi trường sản xuất</div>
    <div>
      <input type="text" id="supabase-url-3" placeholder="Supabase URL" value="https://uvfosdvduemcktgayllz.supabase.co">
      <input type="password" id="supabase-key-3" placeholder="Service Role Key" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3endicmJtb2p2dnZmc3RicW93Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjMyOTI2MSwiZXhwIjoyMDYxOTA1MjYxfQ.qMq8C34LescZuPeuSxredqdsjxsK6YBmkEDKsvzV7mQ">
      <button id="connect-service">Kết nối với Service Key</button>
      <div class="status" id="status-3"></div>
    </div>
  </div>
  
  <div class="panel">
    <h3>Kết quả truy vấn</h3>
    <button id="fetch-users" disabled>Lấy danh sách Users</button>
    <button id="fetch-rooms" disabled>Lấy danh sách Rooms</button>
    <div class="log" id="log"></div>
  </div>
  
  <div class="explanation">
    <p><strong>Giải pháp đề xuất:</strong></p>
    <ol>
      <li>Chạy Chat Server với <code>npm run dev</code> ở port 3002</li>
      <li>Sử dụng phương pháp 2 (Kết nối thông qua Máy chủ Chat)</li>
      <li>Mở file này qua URL: <code>http://localhost:3002/public/TestConnection.html</code></li>
    </ol>
    <p>Khi máy chủ chat đang chạy, nó sẽ phục vụ cả API endpoints và các tệp tĩnh trong thư mục public.</p>
  </div>
  
  <script>
    // Biến toàn cục để lưu trữ client Supabase
    let supabaseClient = null;
    let activeMethod = '';
    
    // Ghi log
    function log(message, isError = false) {
      const logElement = document.getElementById('log');
      const entry = document.createElement('div');
      entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      if (isError) entry.style.color = 'red';
      logElement.appendChild(entry);
      logElement.scrollTop = logElement.scrollHeight;
      console.log(message);
    }
    
    // Cập nhật trạng thái
    function updateStatus(id, message, isSuccess = true) {
      const statusElement = document.getElementById(id);
      statusElement.textContent = message;
      statusElement.className = `status ${isSuccess ? 'success' : 'error'}`;
    }
    
    // Phương pháp 1: Kết nối trực tiếp
    document.getElementById('connect-direct').addEventListener('click', async () => {
      try {
        const url = document.getElementById('supabase-url-1').value;
        const key = document.getElementById('supabase-key-1').value;
        
        if (!url || !key) {
          updateStatus('status-1', 'Vui lòng nhập URL và Key', false);
          return;
        }
        
        log(`Đang kết nối trực tiếp đến Supabase tại ${url}...`);
        
        // Kiểm tra xem biến supabase có tồn tại chưa
        if (typeof supabase === 'undefined') {
          log('Thư viện Supabase JS chưa được tải. Đang tải...', true);
          updateStatus('status-1', 'Lỗi thư viện Supabase chưa tải xong', false);
          return;
        }
        
        // Tạo client mới
        supabaseClient = supabase.createClient(url, key);
        
        // Kiểm tra kết nối bằng cách gọi một hàm API đơn giản
        log('Đang kiểm tra kết nối bằng cách truy vấn phiên bản Supabase...');
        const { data, error } = await supabaseClient.rpc('version');
        
        if (error) {
          throw error;
        }
        
        log(`Kết nối thành công! Phiên bản: ${data}`);
        updateStatus('status-1', 'Kết nối thành công!', true);
        
        // Bật các nút truy vấn
        document.getElementById('fetch-users').disabled = false;
        document.getElementById('fetch-rooms').disabled = false;
        activeMethod = 'direct';
        
      } catch (error) {
        log(`Lỗi kết nối: ${error.message}`, true);
        updateStatus('status-1', `Lỗi kết nối: ${error.message}`, false);
        
        if (error.message.includes('fetch') || error.message.includes('network')) {
          log('Lỗi này có thể do chính sách CORS hoặc do mở file trực tiếp từ hệ thống file. Hãy thử mở file qua máy chủ HTTP.', true);
        }
      }
    });
    
    // Phương pháp 2: Kết nối qua Proxy
    document.getElementById('connect-proxy').addEventListener('click', async () => {
      try {
        const serverUrl = document.getElementById('server-url').value;
        
        if (!serverUrl) {
          updateStatus('status-2', 'Vui lòng nhập URL máy chủ', false);
          return;
        }
        
        log(`Đang kết nối đến máy chủ Chat tại ${serverUrl}...`);
        
        // Kiểm tra xem máy chủ có hoạt động không bằng cách gọi một endpoint đơn giản
        const response = await fetch(`${serverUrl}/api/health`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        log(`Kết nối thành công đến máy chủ Chat! Status: ${data.status || 'OK'}`);
        updateStatus('status-2', 'Kết nối thành công!', true);
        
        // Bật các nút truy vấn
        document.getElementById('fetch-users').disabled = false;
        document.getElementById('fetch-rooms').disabled = false;
        activeMethod = 'proxy';
        
      } catch (error) {
        log(`Lỗi kết nối đến máy chủ Chat: ${error.message}`, true);
        updateStatus('status-2', `Lỗi kết nối: ${error.message}`, false);
        
        if (error.message.includes('fetch') || error.message.includes('network')) {
          log('Có thể máy chủ chat chưa được khởi động. Hãy chạy "npm run dev" trên service chat.', true);
        }
      }
    });
    
    // Phương pháp 3: Sử dụng Service Role Key
    document.getElementById('connect-service').addEventListener('click', async () => {
      try {
        const url = document.getElementById('supabase-url-3').value;
        const key = document.getElementById('supabase-key-3').value;
        
        if (!url || !key) {
          updateStatus('status-3', 'Vui lòng nhập URL và Service Key', false);
          return;
        }
        
        log(`Đang kết nối với Supabase sử dụng Service Role Key...`);
        
        // Kiểm tra xem biến supabase có tồn tại chưa
        if (typeof supabase === 'undefined') {
          log('Thư viện Supabase JS chưa được tải. Đang tải...', true);
          updateStatus('status-3', 'Lỗi thư viện Supabase chưa tải xong', false);
          return;
        }
        
        // Tạo client mới với service role key
        supabaseClient = supabase.createClient(url, key);
        
        // Kiểm tra kết nối bằng cách thực hiện một truy vấn đơn giản
        const { data, error } = await supabaseClient.from('users').select('count(*)');
        
        if (error) {
          throw error;
        }
        
        log(`Kết nối thành công với Service Role Key! Có thể truy cập vào database.`);
        updateStatus('status-3', 'Kết nối thành công!', true);
        
        // Bật các nút truy vấn
        document.getElementById('fetch-users').disabled = false;
        document.getElementById('fetch-rooms').disabled = false;
        activeMethod = 'service';
        
      } catch (error) {
        log(`Lỗi kết nối với Service Role Key: ${error.message}`, true);
        updateStatus('status-3', `Lỗi kết nối: ${error.message}`, false);
        
        if (error.message.includes('fetch') || error.message.includes('network')) {
          log('Lỗi này có thể do chính sách CORS hoặc do mở file trực tiếp từ hệ thống file. Hãy thử mở file qua máy chủ HTTP.', true);
        }
      }
    });
    
    // Lấy danh sách Users
    document.getElementById('fetch-users').addEventListener('click', async () => {
      if (!activeMethod) {
        log('Vui lòng kết nối đến Supabase trước', true);
        return;
      }
      
      try {
        log('Đang lấy danh sách users...');
        
        if (activeMethod === 'proxy') {
          // Sử dụng máy chủ Chat như một proxy
          const serverUrl = document.getElementById('server-url').value;
          const response = await fetch(`${serverUrl}/api/users`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          const data = await response.json();
          log(`Lấy thành công ${data.data ? data.data.length : 0} users từ máy chủ Chat`);
          log(JSON.stringify(data.data || data, null, 2));
        } else {
          // Sử dụng client Supabase trực tiếp
          const { data, error } = await supabaseClient
            .from('users')
            .select('*')
            .limit(5);
            
          if (error) {
            throw error;
          }
          
          log(`Lấy thành công ${data.length} users từ Supabase`);
          log(JSON.stringify(data, null, 2));
        }
      } catch (error) {
        log(`Lỗi khi lấy users: ${error.message}`, true);
      }
    });
    
    // Lấy danh sách Rooms
    document.getElementById('fetch-rooms').addEventListener('click', async () => {
      if (!activeMethod) {
        log('Vui lòng kết nối đến Supabase trước', true);
        return;
      }
      
      try {
        log('Đang lấy danh sách chat rooms...');
        
        if (activeMethod === 'proxy') {
          // Sử dụng máy chủ Chat như một proxy
          const serverUrl = document.getElementById('server-url').value;
          const response = await fetch(`${serverUrl}/api/rooms`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          });
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          const data = await response.json();
          log(`Lấy thành công ${data.data ? data.data.length : 0} chat rooms từ máy chủ Chat`);
          log(JSON.stringify(data.data || data, null, 2));
        } else {
          // Thử truy vấn với tên bảng 'chat_rooms'
          try {
            const { data, error } = await supabaseClient
              .from('chat_rooms')
              .select('*')
              .limit(5);
              
            if (error) {
              throw error;
            }
            
            log(`Lấy thành công ${data.length} chat rooms từ bảng 'chat_rooms'`);
            log(JSON.stringify(data, null, 2));
          } catch (roomsError) {
            // Thử lại với tên bảng 'rooms'
            log(`Không tìm thấy bảng 'chat_rooms', đang thử với 'rooms'...`);
            try {
              const { data, error } = await supabaseClient
                .from('rooms')
                .select('*')
                .limit(5);
                
              if (error) {
                throw error;
              }
              
              log(`Lấy thành công ${data.length} chat rooms từ bảng 'rooms'`);
              log(JSON.stringify(data, null, 2));
            } catch (error) {
              throw new Error(`Không tìm thấy bảng chat rooms: ${error.message}`);
            }
          }
        }
      } catch (error) {
        log(`Lỗi khi lấy chat rooms: ${error.message}`, true);
      }
    });
  </script>
</body>
</html>
