import jwt from 'jsonwebtoken';
import { TokenPayload, AuthTokens } from '../types';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'default_secret_key';
const JWT_ACCESS_EXPIRATION = process.env.JWT_ACCESS_EXPIRATION || '15m';
const JWT_REFRESH_EXPIRATION = process.env.JWT_REFRESH_EXPIRATION || '7d';

// Hàm tạo token đơn giản
export const generateToken = (
  payload: TokenPayload,
  expiresIn: string = JWT_ACCESS_EXPIRATION
): string => {
  return jwt.sign(payload, JWT_SECRET, { expiresIn });
};

// Tạo cả access token và refresh token
export const generateAuthTokens = (payload: TokenPayload): AuthTokens => {
  const access_token = generateToken(payload, JWT_ACCESS_EXPIRATION);
  const refresh_token = generateToken(payload, JWT_REFRESH_EXPIRATION);

  return {
    access_token,
    refresh_token,
    expires_in: getExpirationInSeconds(JWT_ACCESS_EXPIRATION)
  };
};

// Xác thực token
export const verifyToken = (token: string): TokenPayload | null => {
  try {
    return jwt.verify(token, JWT_SECRET) as TokenPayload;
  } catch (error) {
    return null;
  }
};

// Làm mới access token dựa trên refresh token
export const refreshAccessToken = (refreshToken: string): string | null => {
  try {
    const payload = verifyToken(refreshToken);
    if (!payload) return null;

    return generateToken(payload);
  } catch (error) {
    return null;
  }
};

// Helper function để chuyển đổi chuỗi thời hạn thành giây
const getExpirationInSeconds = (expiration: string): number => {
  const unit = expiration.charAt(expiration.length - 1);
  const value = parseInt(expiration.slice(0, -1), 10);

  switch (unit) {
    case 's': return value;
    case 'm': return value * 60;
    case 'h': return value * 60 * 60;
    case 'd': return value * 24 * 60 * 60;
    default: return 900; // Mặc định 15 phút
  }
};
