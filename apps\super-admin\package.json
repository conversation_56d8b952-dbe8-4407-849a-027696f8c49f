{"name": "super-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@loaloa/design-tokens": "*", "@loaloa/ui": "*", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "next": "14.1.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.5.2", "swr": "^2.3.3", "zod": "^3.24.4"}, "devDependencies": {"@types/node": "^20.12.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.57.0", "eslint-config-next": "14.1.3", "sass": "^1.71.0", "typescript": "^5.4.5"}}