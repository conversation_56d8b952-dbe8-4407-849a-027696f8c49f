import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Checkbox } from './index';
import { useState } from 'react';

const meta = {
  title: 'UI/Form/Checkbox',
  component: Checkbox,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'Checkbox label',
    },
    helperText: {
      control: 'text',
      description: 'Helper text displayed below the checkbox',
    },
    error: {
      control: 'boolean',
      description: 'Whether the checkbox is in error state',
    },
    checked: {
      control: 'boolean',
      description: 'Whether the checkbox is checked',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the checkbox is disabled',
    },
  },
} satisfies Meta<typeof Checkbox>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Option 1',
    checked: false,
  },
};

export const Checked: Story = {
  args: {
    label: 'Option 1',
    checked: true,
  },
};

export const WithHelperText: Story = {
  args: {
    label: 'Subscribe to newsletter',
    helperText: 'We will send you weekly updates',
    checked: false,
  },
};

export const WithError: Story = {
  args: {
    label: 'Accept terms and conditions',
    error: true,
    helperText: 'You must accept the terms to continue',
    checked: false,
  },
};

export const Disabled: Story = {
  args: {
    label: 'Disabled option',
    disabled: true,
    checked: false,
  },
};

export const DisabledChecked: Story = {
  args: {
    label: 'Disabled checked option',
    disabled: true,
    checked: true,
  },
};

export const CheckboxGroup: Story = {
  name: 'Checkbox Group',
  parameters: { controls: { disable: true } },
  render: () => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [checkedItems, setCheckedItems] = useState({
      option1: false,
      option2: true,
      option3: false,
    });

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setCheckedItems({
        ...checkedItems,
        [e.target.name]: e.target.checked,
      });
    };

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <Checkbox
          label="Option 1"
          name="option1"
          checked={checkedItems.option1}
          onChange={handleChange}
        />
        <Checkbox
          label="Option 2"
          name="option2"
          checked={checkedItems.option2}
          onChange={handleChange}
        />
        <Checkbox
          label="Option 3"
          name="option3"
          checked={checkedItems.option3}
          onChange={handleChange}
        />
      </div>
    );
  },
};

export const CheckboxShowcase: Story = {
  name: 'All Checkbox Variants',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <Checkbox
        label="Unchecked checkbox"
        checked={false}
      />
      
      <Checkbox
        label="Checked checkbox"
        checked={true}
      />
      
      <Checkbox
        label="With helper text"
        helperText="This is a helper text"
        checked={false}
      />
      
      <Checkbox
        label="With error"
        error={true}
        helperText="This field has an error"
        checked={false}
      />
      
      <Checkbox
        label="Disabled unchecked"
        disabled={true}
        checked={false}
      />
      
      <Checkbox
        label="Disabled checked"
        disabled={true}
        checked={true}
      />
    </div>
  ),
};
