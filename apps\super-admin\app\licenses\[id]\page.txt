'use client';

import React, { useState } from 'react';
import { DashboardLayout, Button, Card, Badge, Tabs } from '@loaloa/ui';
import { HomeIcon, BuildingIcon, UsersIcon, LicenseIcon, SettingsIcon } from '@loaloa/ui/src/components/Icons/icons';
import { useParams } from 'next/navigation';
import styles from './licenseDetail.module.scss';

// Components
import LicenseStatusBadge from '../../../components/licenses/LicenseStatusBadge';
import LicenseActivityTable from '../../../components/licenses/LicenseActivityTable';
import ExtendLicenseModal from '../../../components/licenses/ExtendLicenseModal';
import RevokeLicenseModal from '../../../components/licenses/RevokeLicenseModal';

// Hooks
import { useLicense } from '../../../hooks/useLicenses';

export default function LicenseDetail() {
  const params = useParams();
  const licenseId = params?.id as string;
  
  // State
  const [activeTab, setActiveTab] = useState('details');
  const [showExtendModal, setShowExtendModal] = useState(false);
  const [showRevokeModal, setShowRevokeModal] = useState(false);
  
  // Fetch license data
  const { license, isLoading, isError, mutate } = useLicense(licenseId);
  
  // Sidebar items
  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', href: '/', icon: <HomeIcon /> },
    { id: 'tenants', label: 'Tenants', href: '/tenants', icon: <BuildingIcon /> },
    { id: 'users', label: 'Users', href: '/users', icon: <UsersIcon /> },
    { id: 'licenses', label: 'Licenses', href: '/licenses', icon: <LicenseIcon />, active: true },
    { id: 'settings', label: 'Settings', href: '/settings', icon: <SettingsIcon /> },
  ];
  
  if (isLoading) {
    return (
      <DashboardLayout
        sidebarItems={sidebarItems}
        title="License Details"
        username="Admin User"
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Licenses', href: '/licenses' },
          { label: 'Loading...' }
        ]}
      >
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>Loading license details...</p>
        </div>
      </DashboardLayout>
    );
  }
  
  if (isError || !license) {
    return (
      <DashboardLayout
        sidebarItems={sidebarItems}
        title="License Not Found"
        username="Admin User"
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Licenses', href: '/licenses' },
          { label: 'Not Found' }
        ]}
      >
        <div className={styles.errorContainer}>
          <h2>License Not Found</h2>
          <p>The license you are looking for does not exist or has been removed.</p>
          <Button
		    label="Return to Licenses"
            variant="primary"
            onClick={() => window.location.href = '/licenses'}
          >
            Return to Licenses
          </Button>
        </div>
      </DashboardLayout>
    );
  }
  
  return (
    <DashboardLayout
      sidebarItems={sidebarItems}
      title={`License: ${license.license_key}`}
      username="Admin User"
      breadcrumbs={[
        { label: 'Dashboard', href: '/' },
        { label: 'Licenses', href: '/licenses' },
        { label: license.license_key }
      ]}
    >
      <div className={styles.licenseDetail}>
        {/* Header */}
        <div className={styles.header}>
          <div className={styles.licenseInfo}>
            <h1 className={styles.licenseKey}>{license.license_key}</h1>
            <div className={styles.licenseMeta}>
              <LicenseStatusBadge status={license.status} />
              <span className={styles.customerName}>{license.customer_name}</span>
            </div>
          </div>
          <div className={styles.headerActions}>
            {license.is_active && (
              <>
                <Button
                  variant="outline"
				  label="Extend License"
                  onClick={() => setShowExtendModal(true)}
                >
                  Extend License
                </Button>
                <Button
                  variant="danger"
				  label="Revoke License"
                  onClick={() => setShowRevokeModal(true)}
                >
                  Revoke License
                </Button>
              </>
            )}
          </div>
        </div>
  {/* Tabs */}
        <div className={styles.tabsContainer}>
          <Tabs
            tabs={[
              { id: 'details', label: 'Details' },
              { id: 'activity', label: 'Activity' },
              { 
                id: 'clones', 
                label: <>
                  Clone Alerts
                  {license.unreviewed_clone_alerts > 0 && (
                    <Badge
                      className={styles.alertBadge}
                      variant="danger"
                    >
                      {license.unreviewed_clone_alerts}
                    </Badge>
                  )}
                </> 
              }
            ]}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />
          
          <div className={styles.tabContent}>
            {activeTab === 'details' && (
              <Card>
                <div className={styles.detailsGrid}>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Customer Name</span>
                    <span className={styles.detailValue}>{license.customer_name}</span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>License Key</span>
                    <span className={styles.detailValue}>
                      <code>{license.license_key}</code>
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Status</span>
                    <span className={styles.detailValue}>
                      <LicenseStatusBadge status={license.status} />
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Issue Date</span>
                    <span className={styles.detailValue}>
                      {new Date(license.issue_date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Expiry Date</span>
                    <span className={styles.detailValue}>
                      {new Date(license.expiry_date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Days Remaining</span>
                    <span className={styles.detailValue}>
                      {license.days_remaining > 0 ? license.days_remaining : 'Expired'}
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Activation Date</span>
                    <span className={styles.detailValue}>
                      {license.activation_date 
                        ? new Date(license.activation_date).toLocaleDateString()
                        : 'Not Activated'
                      }
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Last Check-in</span>
                    <span className={styles.detailValue}>
                      {license.last_check_in
                        ? new Date(license.last_check_in).toLocaleString()
                        : 'Never'
                      }
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Check-in Count</span>
                    <span className={styles.detailValue}>{license.check_in_count}</span>
                  </div>
                  {!license.is_active && license.revocation_reason && (
                    <div className={`${styles.detailItem} ${styles.fullWidth}`}>
                      <span className={styles.detailLabel}>Revocation Reason</span>
                      <span className={`${styles.detailValue} ${styles.revocationReason}`}>
                        {license.revocation_reason}
                      </span>
                    </div>
                  )}
                  {license.hardware_fingerprint && (
                    <div className={`${styles.detailItem} ${styles.fullWidth}`}>
                      <span className={styles.detailLabel}>Hardware Fingerprint</span>
                      <span className={styles.detailValue}>
                        <code className={styles.fingerprint}>{license.hardware_fingerprint}</code>
                      </span>
                    </div>
                  )}
                </div>
              </Card>
            )}
            
            {activeTab === 'activity' && (
              <LicenseActivityTable licenseId={license.id} />
            )}
            
            {activeTab === 'clones' && (
              <Card>
                {license.clone_alerts > 0 ? (
                  <div className={styles.cloneAlerts}>
                    <h3>Clone Alerts</h3>
                    {/* Clone alerts component goes here */}
                    <p>This license has {license.clone_alerts} potential clone detection(s).</p>
                    <p>Please review and take appropriate action.</p>
                  </div>
                ) : (
                  <div className={styles.noCloneAlerts}>
                    <h3>No Clone Alerts</h3>
                    <p>No potential license clones have been detected.</p>
                  </div>
                )}
              </Card>
            )}
          </div>
        </div>
      </div>
      
      {/* Modals */}
      {showExtendModal && (
        <ExtendLicenseModal
          licenseId={license.id}
          currentExpiryDate={license.expiry_date}
          onClose={() => setShowExtendModal(false)}
          onSuccess={() => {
            setShowExtendModal(false);
            mutate();
          }}
        />
      )}
      
      {showRevokeModal && (
        <RevokeLicenseModal
          licenseId={license.id}
          licenseKey={license.license_key}
          onClose={() => setShowRevokeModal(false)}
          onSuccess={() => {
            setShowRevokeModal(false);
            mutate();
          }}
        />
      )}
    </DashboardLayout>
  );
}