import React from 'react';
import styles from './styles.module.scss';

interface SkeletonLoaderProps {
  type?: 'text' | 'title' | 'circle' | 'rectangle' | 'card';
  width?: string;
  height?: string;
  count?: number;
  className?: string;
  style?: React.CSSProperties;
}

export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  type = 'text',
  width,
  height,
  count = 1,
  className = '',
  style = {},
}) => {
  const elements = [];

  for (let i = 0; i < count; i++) {
    let skeletonClass = styles.skeleton;
    let elementStyle = { ...style };

    switch (type) {
      case 'title':
        skeletonClass = `${skeletonClass} ${styles.title}`;
        break;
      case 'circle':
        skeletonClass = `${skeletonClass} ${styles.circle}`;
        break;
      case 'rectangle':
        skeletonClass = `${skeletonClass} ${styles.rectangle}`;
        break;
      case 'card':
        skeletonClass = `${skeletonClass} ${styles.card}`;
        break;
      default:
        skeletonClass = `${skeletonClass} ${styles.text}`;
    }

    if (width) elementStyle.width = width;
    if (height) elementStyle.height = height;

    elements.push(
      <div
        key={i}
        className={`${skeletonClass} ${className}`}
        style={elementStyle}
      />
    );
  }

  return <>{elements}</>;
};

export const UserCardSkeleton: React.FC = () => {
  return (
    <div className={styles.userCardSkeleton}>
      <div className={styles.header}>
        <div className={styles.avatar}>
          <SkeletonLoader type="circle" width="80px" height="80px" />
        </div>
        <div className={styles.info}>
          <SkeletonLoader type="title" width="200px" />
          <SkeletonLoader type="text" width="150px" />
          <SkeletonLoader type="text" width="100px" />
        </div>
      </div>
      <div className={styles.content}>
        <SkeletonLoader type="rectangle" width="100%" height="120px" count={2} />
      </div>
    </div>
  );
};

export const TableSkeleton: React.FC<{ rowCount?: number; columnCount?: number }> = ({ 
  rowCount = 5, 
  columnCount = 5 
}) => {
  return (
    <div className={styles.tableSkeleton}>
      <div className={styles.header}>
        {Array(columnCount).fill(0).map((_, i) => (
          <SkeletonLoader key={`header-${i}`} type="text" width={`${100 / columnCount}%`} />
        ))}
      </div>
      <div className={styles.body}>
        {Array(rowCount).fill(0).map((_, rowIndex) => (
          <div key={`row-${rowIndex}`} className={styles.row}>
            {Array(columnCount).fill(0).map((_, colIndex) => (
              <SkeletonLoader 
                key={`cell-${rowIndex}-${colIndex}`} 
                type="text" 
                width={`${100 / columnCount}%`} 
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};
