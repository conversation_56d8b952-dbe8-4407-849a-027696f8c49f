import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_KEY || '';

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or API key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyMigration(filePath: string): Promise<void> {
  console.log(`Applying migration: ${path.basename(filePath)}`);
  
  try {
    // Đọc file SQL
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Chia thành các câu lệnh riêng biệt
    const statements = sql
      .replace(/\/\*[\s\S]*?\*\/|--.*$/gm, '') // <PERSON><PERSON><PERSON> bỏ comments
      .split(';')
      .filter(statement => statement.trim() !== '');
    
    // Thực thi từng câu lệnh
    for (const statement of statements) {
      if (!statement.trim()) continue;
      
      try {
        // Chú ý: Trong môi trường thực tế, bạn nên sử dụng phương pháp an toàn hơn
        // Đây chỉ là ví dụ đơn giản để học
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        
        // Thay vì gọi Supabase rpc (có thể không hỗ trợ), chúng ta chỉ in ra
        // SQL và hướng dẫn người dùng chạy trong SQL Editor của Supabase
        console.log('Please run this SQL in Supabase SQL Editor');
      } catch (error) {
        console.error('Error executing statement:', error);
      }
    }
  } catch (error) {
    console.error(`Error applying migration ${filePath}:`, error);
    throw error;
  }
}

async function applyMigrations() {
  try {
    // Lấy tất cả các file migration theo thứ tự
    const migrationDir = path.join(__dirname);
    const migrationFiles = fs
      .readdirSync(migrationDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    console.log(`Found ${migrationFiles.length} migration files`);
    
    // Áp dụng từng migration
    for (const file of migrationFiles) {
      await applyMigration(path.join(migrationDir, file));
    }
    
    console.log('All migrations applied successfully');
    
    // In hướng dẫn
    console.log('\nTo apply these migrations:');
    console.log('1. Go to Supabase dashboard: https://app.supabase.com/');
    console.log('2. Select your project');
    console.log('3. Go to "SQL Editor"');
    console.log('4. Copy and paste each SQL file content');
    console.log('5. Run the SQL commands');
  } catch (error) {
    console.error('Failed to apply migrations:', error);
    process.exit(1);
  }
}

// Chạy migrations
applyMigrations()
  .then(() => console.log('Done'))
  .catch(err => console.error('Migration failed:', err));
