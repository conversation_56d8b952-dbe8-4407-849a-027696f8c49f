import useSWR from 'swr';

interface License {
  id: string;
  license_key: string;
  customer_name: string;
  issue_date: string;
  expiry_date: string;
  is_active: boolean;
  activation_date?: string;
  last_check_in?: string;
  check_in_count: number;
  hardware_fingerprint?: string;
  activity_count?: number;
  clone_alerts?: number;
  unreviewed_clone_alerts?: number;
  status: 'ACTIVE' | 'EXPIRED' | 'REVOKED' | 'NOT_ACTIVATED' | 'EXPIRING_SOON';
  days_remaining: number;
  product_id: string;
}

interface PaginationOptions {
  page: number;
  limit: number;
}

interface FilterOptions {
  status?: 'ACTIVE' | 'EXPIRED' | 'REVOKED' | 'NOT_ACTIVATED' | 'EXPIRING_SOON' | 'ALL';
  search?: string;
}

interface LicensesResponse {
  data: License[];
  meta: {
    total: number;
    page: number;
    limit: number;
    pageCount: number;
  };
}

// Custom fetcher with error handling
const fetcher = async (url: string) => {
  console.log('Fetching:', url);
  const response = await fetch(url);
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'API request failed');
  }
  
  return response.json();
};

export function useLicenses(
  pagination: PaginationOptions = { page: 1, limit: 10 },
  filter: FilterOptions = {}
) {
  // Build query string
  const queryParams = new URLSearchParams();
  queryParams.append('page', pagination.page.toString());
  queryParams.append('limit', pagination.limit.toString());
  
  if (filter.status && filter.status !== 'ALL') {
    queryParams.append('status', filter.status);
  }
  
  if (filter.search) {
    queryParams.append('search', filter.search);
  }
  
  const url = `/api/licenses?${queryParams.toString()}`;
  console.log('useLicenses hook - URL:', url);
  
  // Fetch data using SWR
  const { data, error, mutate, isLoading } = useSWR<LicensesResponse>(
    url, 
    fetcher,
    { 
      revalidateOnFocus: false,
      dedupingInterval: 5000
    }
  );
  
  console.log('useLicenses hook - Result:', { 
    data: data?.data?.length || 0, 
    error, 
    isLoading,
    meta: data?.meta
  });
  
  return {
    licenses: data?.data || [],
    meta: data?.meta,
    isLoading,
    isError: error,
    mutate
  };
}

export function useLicense(id: string) {
  const { data, error, mutate, isLoading } = useSWR<{ data: License }>(
    id ? `/api/licenses/${id}` : null,
    async (url) => {
      const response = await fetch(url);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch license');
      }
      return response.json();
    },
    { revalidateOnFocus: false }
  );
  
  return {
    license: data?.data,
    isLoading,
    isError: error,
    mutate
  };
}