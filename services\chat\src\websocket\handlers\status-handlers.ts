import { Server as SocketIOServer, Socket } from 'socket.io';
import { SocketEvents, SocketResponse } from '../../types';
import * as messageStatusService from '../../services/message-status-service';

export const registerStatusHandlers = (io: SocketIOServer, socket: Socket) => {
  // Handler cho event MESSAGE_READ
  socket.on(SocketEvents.MESSAGE_READ, async (data: { messageId: string }, callback?: (response: SocketResponse) => void) => {
    try {
      const userId = socket.data.userId;
      const temporaryUserId = socket.data.temporaryUserId;
      
      if (!userId && !temporaryUserId) {
        const response: SocketResponse = {
          success: false,
          error: 'Authentication required'
        };
        if (callback) callback(response);
        return;
      }
      
      // Cập nhật trạng thái đã đọc
      const status = await messageStatusService.markAsRead(
        data.messageId,
        userId,
        temporaryUserId
      );
      
      if (!status) {
        const response: SocketResponse = {
          success: false,
          error: 'Failed to mark message as read'
        };
        if (callback) callback(response);
        return;
      }
      
      // Thông báo cho người dùng cập nhật thành công
      const response: SocketResponse = {
        success: true,
        data: { status }
      };
      if (callback) callback(response);
      
      // Lấy thông tin phòng chat từ tin nhắn
      const roomId = status.roomId;
      
      // Thông báo cho những người khác trong phòng
      socket.to(roomId).emit(SocketEvents.MESSAGE_READ, {
        messageId: data.messageId,
        userId,
        temporaryUserId,
        readAt: status.readAt
      });
      
    } catch (error) {
      console.error('Error in MESSAGE_READ handler:', error);
      const response: SocketResponse = {
        success: false,
        error: 'Failed to mark message as read'
      };
      if (callback) callback(response);
    }
  });
  
  // Handler cho event TYPING_START
  socket.on(SocketEvents.TYPING_START, (roomId: string) => {
    const userId = socket.data.userId;
    const temporaryUserId = socket.data.temporaryUserId;
    
    if (!userId && !temporaryUserId) {
      return;
    }
    
    // Thông báo cho những người khác trong phòng
    socket.to(roomId).emit(SocketEvents.TYPING_START, {
      userId,
      temporaryUserId,
      socketId: socket.id
    });
  });
  
  // Handler cho event TYPING_END
  socket.on(SocketEvents.TYPING_END, (roomId: string) => {
    const userId = socket.data.userId;
    const temporaryUserId = socket.data.temporaryUserId;
    
    if (!userId && !temporaryUserId) {
      return;
    }
    
    // Thông báo cho những người khác trong phòng
    socket.to(roomId).emit(SocketEvents.TYPING_END, {
      userId,
      temporaryUserId,
      socketId: socket.id
    });
  });
};
