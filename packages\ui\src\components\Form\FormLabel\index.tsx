import React from 'react';
import styles from './FormLabel.module.scss';

export interface FormLabelProps {
  children: React.ReactNode;
  htmlFor?: string;
  className?: string;
  required?: boolean;
}

export const FormLabel: React.FC<FormLabelProps> = ({ 
  children, 
  htmlFor, 
  className,
  required = false 
}) => {
  return (
    <label 
      htmlFor={htmlFor} 
      className={`${styles.formLabel} ${className || ''}`}
    >
      {children}
      {required && <span className={styles.requiredIndicator}>*</span>}
    </label>
  );
};

export default FormLabel;
