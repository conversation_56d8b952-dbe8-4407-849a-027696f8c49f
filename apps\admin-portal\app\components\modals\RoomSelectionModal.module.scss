.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  width: 500px;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  
  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  line-height: 1;
  
  &:hover {
    color: #333;
  }
}

.modalContent {
  padding: 20px;
  overflow-y: auto;
  max-height: 60vh;
}

.error {
  background-color: #fef1f2;
  color: #e11d48;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
}

.filters {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  flex: 1;
  
  label {
    font-size: 13px;
    margin-bottom: 4px;
    color: #666;
  }
  
  select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
    }
  }
}

.roomList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.roomItem {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #f3f4f6;
  }
  
  &.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
  }
  
  &.unavailable {
    opacity: 0.6;
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
}

.roomNumber {
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 4px;
}

.roomInfo {
  display: flex;
  flex-direction: column;
  font-size: 13px;
  color: #666;
  
  .roomType {
    margin-bottom: 2px;
  }
}

.roomStatus {
  font-size: 11px;
  text-transform: capitalize;
  color: #dc2626;
  margin-top: 6px;
  display: block;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
  gap: 12px;
}

.cancelButton, 
.confirmButton {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancelButton {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #374151;
  
  &:hover {
    background-color: #e5e7eb;
  }
}

.confirmButton {
  background-color: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;
  
  &:hover {
    background-color: #2563eb;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.loading, 
.noRooms {
  text-align: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
}
