import supabase from '../utils/supabase';
import { MessageTranslation, ChatMessage, ChatParticipant } from '../types';
import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

const TRANSLATION_SERVICE_URL = process.env.TRANSLATION_SERVICE_URL || 'http://localhost:3003/api/translate';

interface TranslationResult {
  messageId: string;
  language: string;
  translatedContent: string;
}

/**
 * Dịch tin nhắn sang ngôn ngữ khác
 */
export const translateMessage = async (
  messageId: string,
  targetLanguage: string
): Promise<TranslationResult | null> => {
  try {
    // Kiểm tra xem bản dịch đã tồn tại chưa
    const { data: existingTranslation, error: checkError } = await supabase
      .from('message_translations')
      .select('*')
      .eq('message_id', messageId)
      .eq('language', targetLanguage)
      .single();
    
    if (existingTranslation) {
      return {
        messageId,
        language: targetLanguage,
        translatedContent: existingTranslation.translated_content
      };
    }
    
    // Lấy nội dung và ngôn ngữ gốc của tin nhắn
    const { data: message, error: messageError } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('id', messageId)
      .single();
    
    if (messageError || !message) {
      console.error('Error fetching message:', messageError);
      return null;
    }
    
    // Kiểm tra nếu ngôn ngữ đích trùng với ngôn ngữ gốc, không cần dịch
    if (message.original_language === targetLanguage) {
      return {
        messageId,
        language: targetLanguage,
        translatedContent: message.content
      };
    }
    
    // Trong môi trường thực tế, gọi dịch vụ dịch thuật
    // Ở đây chúng ta giả lập bằng cách thêm prefix cho nội dung
    let translatedContent = '';
    
    try {
      // Gọi dịch vụ dịch thuật (khi có)
      const response = await axios.post(TRANSLATION_SERVICE_URL, {
        text: message.content,
        source: message.original_language,
        target: targetLanguage
      });
      
      translatedContent = response.data.translatedText || `[${targetLanguage}] ${message.content}`;
    } catch (translationError) {
      console.error('Error calling translation service:', translationError);
      // Sử dụng phiên bản giả lập nếu dịch vụ không phản hồi
      translatedContent = `[${targetLanguage}] ${message.content}`;
    }
    
    // Lưu bản dịch vào cơ sở dữ liệu
    const { data: savedTranslation, error: saveError } = await supabase
      .from('message_translations')
      .insert([{
        message_id: messageId,
        language: targetLanguage,
        translated_content: translatedContent,
        is_machine_translation: true
      }])
      .select()
      .single();
    
    if (saveError) {
      console.error('Error saving translation:', saveError);
      // Vẫn trả về bản dịch mặc dù không lưu được
      return {
        messageId,
        language: targetLanguage,
        translatedContent
      };
    }
    
    return {
      messageId,
      language: targetLanguage,
      translatedContent
    };
  } catch (error) {
    console.error('Error in translateMessage:', error);
    return null;
  }
};

/**
 * Dịch tin nhắn sang các ngôn ngữ của tất cả người tham gia trong phòng
 */
export const translateMessageForRoom = async (
  messageId: string,
  roomId: string,
  sourceLanguage: string
): Promise<TranslationResult[]> => {
  try {
    // Lấy danh sách ngôn ngữ ưu tiên của tất cả người tham gia
    const { data: participants, error: participantsError } = await supabase
      .from('chat_participants')
      .select('preferred_language')
      .eq('chat_room_id', roomId)
      .eq('is_active', true);
    
    if (participantsError || !participants || participants.length === 0) {
      return [];
    }
    
    // Tạo danh sách các ngôn ngữ cần dịch (unique)
    const targetLanguages = [...new Set(
      participants
        .filter(p => p.preferred_language && p.preferred_language !== sourceLanguage)
        .map(p => p.preferred_language)
    )];
    
    if (targetLanguages.length === 0) {
      return [];
    }
    
    // Dịch sang mỗi ngôn ngữ
    const translationPromises = targetLanguages.map(language => 
      translateMessage(messageId, language)
    );
    
    const translations = await Promise.all(translationPromises);
    
    // Lọc ra các kết quả thành công
    return translations.filter((t): t is TranslationResult => t !== null);
  } catch (error) {
    console.error('Error in translateMessageForRoom:', error);
    return [];
  }
};

/**
 * Lấy bản dịch của tin nhắn
 */
export const getMessageTranslation = async (
  messageId: string,
  language: string
): Promise<MessageTranslation | null> => {
  try {
    const { data, error } = await supabase
      .from('message_translations')
      .select('*')
      .eq('message_id', messageId)
      .eq('language', language)
      .single();
    
    if (error) {
      return null;
    }
    
    return data as MessageTranslation;
  } catch (error) {
    console.error('Error in getMessageTranslation:', error);
    return null;
  }
};
