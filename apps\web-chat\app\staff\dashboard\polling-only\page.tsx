'use client';

/**
 * Polling-Only Staff Dashboard - Pure polling implementation for testing
 * No realtime, just fast polling to prove the concept
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import styles from '../dashboard.module.scss';

interface ChatSession {
  id: string;
  guest_name: string;
  room_number?: string;
  language: string;
  status: 'active' | 'pending' | 'waiting';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  last_message: string;
  last_message_time: string;
  unread_count: number;
  source: string;
}

interface ChatMessage {
  id: string;
  content: string;
  sender_type: 'guest' | 'staff';
  sender_name: string;
  timestamp: string;
}

interface User {
  id: string;
  name: string;
  tenant_id: string;
}

export default function PollingOnlyStaffDashboard() {
  // Core state
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeChatSessions, setActiveChatSessions] = useState<ChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [pollingInterval, setPollingInterval] = useState<number>(1000); // 1 second
  const [lastMessageId, setLastMessageId] = useState<string | null>(null);

  // Refs
  const router = useRouter();
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Utility functions
  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Session list refresh
  const refreshSessionList = useCallback(async () => {
    if (!user) return;

    try {
      const response = await fetch(`/api/chat-sessions?tenant_id=${user.tenant_id}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.sessions)) {
          const transformedSessions: ChatSession[] = data.sessions.map((session: any) => ({
            id: session.id,
            guest_name: session.qr_info?.room_number ?
              `Room ${session.qr_info.room_number} Guest` :
              'Guest User',
            room_number: session.qr_info?.room_number || undefined,
            language: session.guest_language?.toUpperCase() || 'EN',
            status: session.status as 'active' | 'pending' | 'waiting',
            priority: session.priority as 'low' | 'normal' | 'high' | 'urgent',
            last_message: 'Loading...',
            last_message_time: formatTimeAgo(session.updated_at),
            unread_count: 0,
            source: session.qr_info?.location || session.reception_point?.name || 'Direct'
          }));

          setActiveChatSessions(transformedSessions);
          console.log(`✅ Polling-Only: Session list refreshed (${transformedSessions.length} sessions)`);
        }
      }
    } catch (error) {
      console.error('❌ Polling-Only: Error refreshing session list:', error);
    }
  }, [user]);

  // Check for new messages in selected session
  const checkForNewMessages = useCallback(async () => {
    if (!selectedSession) return;

    try {
      const response = await fetch(`/api/messages?session_id=${selectedSession}&limit=50`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.messages)) {
          const latestMessage = data.messages[data.messages.length - 1];
          
          if (latestMessage && latestMessage.id !== lastMessageId) {
            console.log(`📥 Polling-Only: New message detected! ${latestMessage.id}`);
            setMessages(data.messages);
            setLastMessageId(latestMessage.id);
            setTimeout(scrollToBottom, 100);
            
            // Reset to fast polling when new message detected
            setPollingInterval(1000);
          }
        }
      }
    } catch (err) {
      console.error('❌ Polling-Only: Failed to check messages:', err);
    }
  }, [selectedSession, lastMessageId]);

  // Start polling
  const startPolling = useCallback(() => {
    if (pollingIntervalRef.current) return;
    
    console.log(`🔄 Polling-Only: Starting polling with ${pollingInterval}ms interval`);
    
    const poll = async () => {
      if (!document.hidden && user) {
        console.log('📡 Polling-Only: Polling tick...');
        await Promise.all([
          refreshSessionList(),
          checkForNewMessages()
        ]);
      }
    };
    
    // Start immediately
    poll();
    
    // Then continue with interval
    pollingIntervalRef.current = setInterval(poll, pollingInterval);
    console.log('✅ Polling-Only: Polling started');
  }, [user, pollingInterval, refreshSessionList, checkForNewMessages]);

  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
      console.log('⏹️ Polling-Only: Polling stopped');
    }
  }, []);

  // Send message
  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!selectedSession || !content.trim()) return false;

    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: selectedSession,
          sender_type: 'staff',
          sender_name: user?.name || 'Staff',
          content: content.trim()
        }),
      });

      if (response.ok) {
        console.log('✅ Polling-Only: Message sent successfully');
        
        // Immediately check for new messages after sending
        setTimeout(checkForNewMessages, 100);
        
        return true;
      }
      
      throw new Error('Failed to send message');
    } catch (err) {
      console.error('❌ Polling-Only: Failed to send message:', err);
      return false;
    }
  }, [selectedSession, user, checkForNewMessages]);

  // Load messages for selected session
  const loadMessages = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/messages?session_id=${sessionId}&limit=50`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.messages)) {
          setMessages(data.messages);
          const latestMessage = data.messages[data.messages.length - 1];
          if (latestMessage) {
            setLastMessageId(latestMessage.id);
          }
          setTimeout(scrollToBottom, 100);
        }
      }
    } catch (err) {
      console.error('❌ Polling-Only: Failed to load messages:', err);
    }
  }, []);

  // Handle session selection
  const handleSessionSelect = useCallback((sessionId: string) => {
    setSelectedSession(sessionId);
    setLastMessageId(null); // Reset to detect new messages
    loadMessages(sessionId);
  }, [loadMessages]);

  // Adjust polling interval
  const adjustPollingInterval = (newInterval: number) => {
    setPollingInterval(newInterval);
    stopPolling();
    console.log(`🔧 Polling-Only: Interval changed to ${newInterval}ms`);
  };

  // Initialize
  useEffect(() => {
    console.log('🚀 Polling-Only Staff Dashboard: Starting...');
    
    const token = localStorage.getItem('staff_token');
    const userData = localStorage.getItem('staff_user');

    if (!token || !userData) {
      router.push('/staff');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
      console.log('✅ Polling-Only: User loaded:', parsedUser.name);
    } catch (error) {
      console.error('Error parsing user data:', error);
      router.push('/staff');
    } finally {
      setLoading(false);
    }
  }, [router]);

  // Start polling when user is loaded
  useEffect(() => {
    if (user) {
      startPolling();
    }

    return () => {
      stopPolling();
    };
  }, [user, startPolling, stopPolling]);

  // Restart polling when interval changes
  useEffect(() => {
    if (user) {
      stopPolling();
      setTimeout(startPolling, 100);
    }
  }, [pollingInterval, user, startPolling, stopPolling]);

  if (loading) {
    return <div className={styles.loading}>Loading Polling-Only Dashboard...</div>;
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h1>Polling-Only Staff Dashboard</h1>
        <div className={styles.connectionStatus}>
          <span className={`${styles.statusIndicator} ${styles.connected}`}>
            🟡 Polling ({pollingInterval}ms)
          </span>
          <div style={{ marginLeft: '1rem' }}>
            <button onClick={() => adjustPollingInterval(500)} style={{ margin: '0 2px', padding: '2px 6px', fontSize: '12px' }}>0.5s</button>
            <button onClick={() => adjustPollingInterval(1000)} style={{ margin: '0 2px', padding: '2px 6px', fontSize: '12px' }}>1s</button>
            <button onClick={() => adjustPollingInterval(2000)} style={{ margin: '0 2px', padding: '2px 6px', fontSize: '12px' }}>2s</button>
            <button onClick={() => adjustPollingInterval(5000)} style={{ margin: '0 2px', padding: '2px 6px', fontSize: '12px' }}>5s</button>
          </div>
        </div>
      </div>

      <div className={styles.content}>
        {/* Session List */}
        <div className={styles.sidebar}>
          <div className={styles.sessionList}>
            {activeChatSessions.map((session) => (
              <div 
                key={session.id} 
                className={`${styles.sessionItem} ${selectedSession === session.id ? styles.selected : ''}`}
                onClick={() => handleSessionSelect(session.id)}
              >
                <div className={styles.sessionHeader}>
                  <span className={styles.guestName}>{session.guest_name}</span>
                  <span className={styles.language}>{session.language}</span>
                </div>
                <div className={styles.lastMessage}>{session.last_message}</div>
                <div className={styles.sessionMeta}>
                  <span className={styles.time}>{session.last_message_time}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Chat Area */}
        <div className={styles.chatArea}>
          {selectedSession ? (
            <>
              <div className={styles.messagesContainer}>
                {messages.map((message) => (
                  <div key={message.id} className={`${styles.message} ${styles[message.sender_type]}`}>
                    <div className={styles.messageContent}>
                      <span className={styles.senderName}>{message.sender_name}</span>
                      <p>{message.content}</p>
                      <span className={styles.timestamp}>{formatTimeAgo(message.timestamp)}</span>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
              
              <div className={styles.messageInput}>
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message..."
                  onKeyPress={async (e) => {
                    if (e.key === 'Enter' && newMessage.trim()) {
                      const success = await sendMessage(newMessage);
                      if (success) {
                        setNewMessage('');
                      }
                    }
                  }}
                />
                <button onClick={async () => {
                  if (newMessage.trim()) {
                    const success = await sendMessage(newMessage);
                    if (success) {
                      setNewMessage('');
                    }
                  }
                }}>
                  Send
                </button>
              </div>
            </>
          ) : (
            <div className={styles.noSelection}>
              <p>Select a chat session to start messaging</p>
              <p style={{ fontSize: '14px', color: '#666', marginTop: '10px' }}>
                Current polling interval: {pollingInterval}ms
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
