import React from 'react';
import styles from './Skeleton.module.scss';

interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  borderRadius?: string;
  className?: string;
  count?: number;
  circle?: boolean;
  style?: React.CSSProperties;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '1rem',
  borderRadius = '0.25rem',
  className = '',
  count = 1,
  circle = false,
  style = {},
}) => {
  const elements = [];
  
  const finalStyle: React.CSSProperties = {
    width,
    height,
    borderRadius: circle ? '50%' : borderRadius,
    ...style,
  };

  for (let i = 0; i < count; i++) {
    elements.push(
      <div
        key={i}
        className={`${styles.skeleton} ${className}`}
        style={{
          ...finalStyle,
          marginBottom: i !== count - 1 ? '0.75rem' : undefined,
        }}
        aria-hidden="true"
      />
    );
  }

  return <>{elements}</>;
};

export default Skeleton;
