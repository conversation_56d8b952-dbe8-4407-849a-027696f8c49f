.loadingContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  z-index: 9999;
  background: linear-gradient(90deg, #FF4D00, #FF9500);
  overflow: hidden;
}

.spinner {
  width: 80px;
  text-align: center;
  position: fixed;
  top: 20px;
  right: 20px;
}

.spinner > div {
  width: 12px;
  height: 12px;
  background-color: #FF4D00;
  border-radius: 100%;
  display: inline-block;
  animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  margin: 0 4px;
}

.spinner .bounce1 {
  animation-delay: -0.32s;
}

.spinner .bounce2 {
  animation-delay: -0.16s;
}

@keyframes sk-bouncedelay {
  0%, 80%, 100% { 
    transform: scale(0);
  } 40% { 
    transform: scale(1.0);
  }
}