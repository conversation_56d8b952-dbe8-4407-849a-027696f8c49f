@import '../styles/_variables';

.sidebar {
  width: 240px;
  height: 100vh;
  background-color: white;
  border-right: 1px solid #eee;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  
  &.collapsed {
    width: 70px;
  }
}

.logoContainer {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid #eee;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logoImage {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.logoText {
  margin-left: 12px;
  font-size: 18px;
  font-weight: 600;
  color: $primary-color;
}

.collapseButton {
  background: none;
  border: none;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: $gray;
  padding: 0;
  
  &:hover {
    color: $dark-gray;
  }
}

.navigation {
  display: flex;
  flex-direction: column;
  padding: 16px 0;
  flex-grow: 1;
  overflow-y: auto;
}

.navLink {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: $dark-gray;
  text-decoration: none;
  transition: all 0.2s;
  
  svg {
    margin-right: 12px;
    flex-shrink: 0;
  }
  
  &:hover {
    background-color: #f9f9f9;
    color: $primary-color;
  }
  
  &.active {
    background-color: #f0f9ff;
    color: $primary-color;
    border-right: 2px solid $primary-color;
    
    svg {
      color: $primary-color;
    }
  }
}

.userSection {
  padding: 16px;
  border-top: 1px solid #eee;
}

.userInfo {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.userAvatar {
  width: 36px;
  height: 36px;
  background-color: $primary-color;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.userDetails {
  margin-left: 12px;
}

.userName {
  font-size: 14px;
  font-weight: 500;
  color: $dark-gray;
  margin: 0;
}

.userRole {
  font-size: 12px;
  color: $gray;
  margin: 2px 0 0;
}

.logoutButton {
  display: flex;
  align-items: center;
  padding: 8px;
  color: $dark-gray;
  text-decoration: none;
  transition: color 0.2s;
  
  svg {
    margin-right: 8px;
  }
  
  &:hover {
    color: #d63c00;
  }
}
