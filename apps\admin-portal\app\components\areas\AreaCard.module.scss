.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: transform 0.2s, box-shadow 0.2s;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.imageContainer {
  height: 140px;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.header {
  padding: 16px 16px 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  flex: 1;
}

.type {
  font-size: 0.75rem;
  padding: 4px 8px;
  border-radius: 9999px;
  font-weight: 500;
  
  &.restaurant {
    background-color: #e0f2fe;
    color: #0284c7;
  }
  
  &.pool {
    background-color: #dcfce7;
    color: #059669;
  }
  
  &.spa {
    background-color: #fce7f3;
    color: #db2777;
  }
  
  &.gym {
    background-color: #fee2e2;
    color: #ef4444;
  }
  
  &.lobby {
    background-color: #f3e8ff;
    color: #9333ea;
  }
  
  &.bar {
    background-color: #f7fee7;
    color: #65a30d;
  }
  
  &.other {
    background-color: #f1f5f9;
    color: #64748b;
  }
}

.details {
  padding: 8px 16px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detailItem {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
}

.label {
  color: #6b7280;
}

.value {
  font-weight: 500;
  color: #111827;
}

.actions {
  padding: 16px;
  display: flex;
  gap: 8px;
  border-top: 1px solid #f3f4f6;
}

.viewButton,
.editButton {
  flex: 1;
  padding: 8px 0;
  text-align: center;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s;
}

.viewButton {
  background-color: #e5e7eb;
  color: #4b5563;
  
  &:hover {
    background-color: #d1d5db;
    color: #111827;
  }
}

.editButton {
  background-color: #dbeafe;
  color: #3b82f6;
  
  &:hover {
    background-color: #bfdbfe;
    color: #1d4ed8;
  }
}