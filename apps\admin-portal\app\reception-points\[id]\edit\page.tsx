'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '../../../dashboard-layout';
import styles from './edit-point.module.scss';
import { <PERSON><PERSON>, But<PERSON> } from '@ui';
import ReceptionPointForm from '../../../components/reception-points/ReceptionPointForm';

interface EditPointParams {
  params: {
    id: string;
  };
}

export default function EditReceptionPointPage({ params }: EditPointParams) {
  const router = useRouter();
  const pointId = params.id;
  
  const [point, setPoint] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPoint = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/reception-points/${pointId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Reception point not found');
          }
          throw new Error('Failed to fetch reception point');
        }
        
        const data = await response.json();
        setPoint(data.data);
      } catch (err) {
        console.error('Error fetching reception point:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (pointId) {
      fetchPoint();
    }
  }, [pointId]);

  const handleSubmit = async (formData: any) => {
    try {
      setSubmitting(true);
      setError(null);
      
      const response = await fetch(`/api/reception-points/${pointId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to update reception point');
      }
      
      // Navigate back to detail page
      router.push(`/reception-points/${pointId}`);
    } catch (err: any) {
      console.error('Error updating reception point:', err);
      setError(err.message || 'An error occurred while updating the reception point');
      // Don't set submitting to false here, the form component will handle it
      throw err; // Re-throw to let the form component know about the error
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading reception point...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (error && !point) {
    return (
      <DashboardLayout>
        <div className={styles.error}>
          <Alert variant="error" title="Error" closable={false}>
            {error}
          </Alert>
          <Button
            variant="secondary" 
            onClick={() => router.push('/reception-points')}
          >
            Back to Reception Points
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Link href={`/reception-points/${pointId}`} className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M10 15.8333L4.16667 10L10 4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to Details
          </Link>
          <h1 className={styles.title}>Edit Reception Point</h1>
        </div>
        
        {error && (
          <Alert variant="error" title="Error" closable onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        <div className={styles.formContainer}>
          {point && (
            <ReceptionPointForm 
              initialData={point}
              onSubmit={handleSubmit}
              isSubmitting={submitting}
              isEditing={true}
            />
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
