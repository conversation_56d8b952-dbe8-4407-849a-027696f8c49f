'use client';

import Link from 'next/link';
import styles from './AreaCard.module.scss';

interface AreaCardProps {
  id: string;
  name: string;
  areaType: string;
  floor?: string;
  location?: string;
  staffCount?: number;
  openingHours?: string;
  closingHours?: string;
  imageUrl?: string;
}

export default function AreaCard({
  id,
  name,
  areaType,
  floor,
  location,
  staffCount,
  openingHours,
  closingHours,
  imageUrl,
}: AreaCardProps) {
  const getAreaTypeText = () => {
    switch (areaType) {
      case 'restaurant':
        return 'Nhà hàng';
      case 'pool':
        return 'Hồ bơi';
      case 'spa':
        return 'Spa';
      case 'gym':
        return 'Phòng tập';
      case 'lobby':
        return 'Sảnh';
      case 'bar':
        return 'Quầy bar';
      default:
        return 'Khác';
    }
  };

  return (
    <div className={styles.card}>
      {imageUrl && (
        <div className={styles.imageContainer}>
          <img src={imageUrl} alt={name} className={styles.image} />
        </div>
      )}
      
      <div className={styles.header}>
        <h3 className={styles.name}>{name}</h3>
        <span className={`${styles.type} ${styles[areaType]}`}>
          {getAreaTypeText()}
        </span>
      </div>
      
      <div className={styles.details}>
        {location && (
          <div className={styles.detailItem}>
            <span className={styles.label}>Vị trí:</span>
            <span className={styles.value}>{location}</span>
          </div>
        )}
        
        {floor && (
          <div className={styles.detailItem}>
            <span className={styles.label}>Tầng:</span>
            <span className={styles.value}>{floor}</span>
          </div>
        )}
        
        {(openingHours || closingHours) && (
          <div className={styles.detailItem}>
            <span className={styles.label}>Giờ hoạt động:</span>
            <span className={styles.value}>
              {openingHours || '--'} - {closingHours || '--'}
            </span>
          </div>
        )}
        
        {staffCount !== undefined && (
          <div className={styles.detailItem}>
            <span className={styles.label}>Số nhân viên:</span>
            <span className={styles.value}>{staffCount}</span>
          </div>
        )}
      </div>
      
      <div className={styles.actions}>
        <Link href={`/rooms-areas/areas/${id}`} className={styles.viewButton}>
          Chi tiết
        </Link>
        <Link href={`/rooms-areas/areas/${id}/edit`} className={styles.editButton}>
          Chỉnh sửa
        </Link>
      </div>
    </div>
  );
}