import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabase } from '@/lib/supabase'

// POST /api/chat-sessions/check - Check for existing active sessions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      qr_code_value, 
      device_id, 
      temp_user_id,
      check_type = 'room_device' // room_device, area_24h, device_only
    } = body

    console.log('🔍 Checking for existing sessions:', {
      qr_code_value,
      device_id: device_id?.substring(0, 8) + '...',
      check_type
    })

    if (!qr_code_value || !device_id) {
      return NextResponse.json(
        { error: 'qr_code_value and device_id are required' },
        { status: 400 }
      )
    }

    const supabase = createServerSupabase()

    // Get QR code info first
    const { data: qrCode, error: qrError } = await supabase
      .from('tenant_qr_codes')
      .select(`
        id,
        tenant_id,
        code_value,
        room_number,
        target_type,
        location,
        description
      `)
      .eq('code_value', qr_code_value)
      .eq('is_active', true)
      .single()

    if (qrError || !qrCode) {
      return NextResponse.json(
        { error: 'QR code not found' },
        { status: 404 }
      )
    }

    let existingSession = null
    let sessionQuery = supabase
      .from('tenant_chat_sessions')
      .select(`
        *,
        tenant_qr_codes(
          code_value,
          room_number,
          target_type,
          location
        )
      `)
      .eq('tenant_id', qrCode.tenant_id)
      .in('status', ['active', 'pending'])

    // Different logic based on QR type and check type
    if (qrCode.target_type === 'room' && qrCode.room_number) {
      // For rooms: check for active sessions in same room with same device
      console.log('🏨 Checking room-based session for room:', qrCode.room_number)
      
      // First, get temporary users for this device and QR
      const { data: tempUsers, error: tempUserError } = await supabase
        .from('temporary_users')
        .select('id')
        .eq('device_id', device_id)
        .eq('qr_code_id', qr_code_value)

      if (tempUsers && tempUsers.length > 0) {
        const tempUserIds = tempUsers.map(u => u.id)
        
        // Find active sessions for these temp users
        const { data: sessions, error: sessionError } = await supabase
          .from('tenant_chat_sessions')
          .select(`
            *,
            tenant_qr_codes(
              code_value,
              room_number,
              target_type,
              location
            )
          `)
          .eq('tenant_id', qrCode.tenant_id)
          .in('status', ['active', 'pending'])
          .eq('source_qr_code_id', qrCode.id)
          .order('created_at', { ascending: false })

        if (sessions && sessions.length > 0) {
          // Check if any session is for the same room and not checked out
          existingSession = sessions.find(session => 
            session.tenant_qr_codes?.room_number === qrCode.room_number &&
            !session.room_checkout_at
          )
        }
      }

    } else {
      // For areas: check for sessions within 24 hours
      console.log('🏢 Checking area-based session for location:', qrCode.location)
      
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      
      const { data: tempUsers, error: tempUserError } = await supabase
        .from('temporary_users')
        .select('id')
        .eq('device_id', device_id)
        .gte('created_at', twentyFourHoursAgo)

      if (tempUsers && tempUsers.length > 0) {
        const tempUserIds = tempUsers.map(u => u.id)
        
        const { data: sessions, error: sessionError } = await supabase
          .from('tenant_chat_sessions')
          .select(`
            *,
            tenant_qr_codes(
              code_value,
              room_number,
              target_type,
              location
            )
          `)
          .eq('tenant_id', qrCode.tenant_id)
          .in('status', ['active', 'pending'])
          .eq('source_qr_code_id', qrCode.id)
          .gte('created_at', twentyFourHoursAgo)
          .order('created_at', { ascending: false })

        if (sessions && sessions.length > 0) {
          existingSession = sessions[0] // Most recent session
        }
      }
    }

    const response = {
      success: true,
      has_existing_session: !!existingSession,
      qr_info: {
        id: qrCode.id,
        code_value: qrCode.code_value,
        room_number: qrCode.room_number,
        target_type: qrCode.target_type,
        location: qrCode.location
      },
      existing_session: existingSession ? {
        id: existingSession.id,
        status: existingSession.status,
        created_at: existingSession.created_at,
        guest_language: existingSession.guest_language,
        room_info: existingSession.tenant_qr_codes ? {
          room_number: existingSession.tenant_qr_codes.room_number,
          location: existingSession.tenant_qr_codes.location
        } : null,
        can_reuse: true,
        reuse_reason: qrCode.target_type === 'room' 
          ? 'same_room_device' 
          : 'area_24h_window'
      } : null,
      recommendation: existingSession 
        ? 'reuse_existing' 
        : 'create_new'
    }

    console.log('✅ Session check completed:', {
      has_existing: response.has_existing_session,
      recommendation: response.recommendation,
      session_id: existingSession?.id
    })

    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ Error checking sessions:', error)
    return NextResponse.json(
      { error: 'Internal server error: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    )
  }
}

// GET /api/chat-sessions/check - Get session statistics
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tenant_id = searchParams.get('tenant_id')
    const room_number = searchParams.get('room_number')
    const days = parseInt(searchParams.get('days') || '7')

    if (!tenant_id) {
      return NextResponse.json(
        { error: 'tenant_id is required' },
        { status: 400 }
      )
    }

    const supabase = createServerSupabase()
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()

    let query = supabase
      .from('tenant_chat_sessions')
      .select(`
        id,
        status,
        created_at,
        ended_at,
        checkout_reason,
        tenant_qr_codes(room_number, target_type, location)
      `)
      .eq('tenant_id', tenant_id)
      .gte('created_at', startDate)

    if (room_number) {
      // Get QR codes for this room first
      const { data: roomQRs } = await supabase
        .from('tenant_qr_codes')
        .select('id')
        .eq('tenant_id', tenant_id)
        .eq('room_number', room_number)

      if (roomQRs && roomQRs.length > 0) {
        const qrIds = roomQRs.map(qr => qr.id)
        query = query.in('source_qr_code_id', qrIds)
      }
    }

    const { data: sessions, error } = await query.order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching session stats:', error)
      return NextResponse.json(
        { error: 'Failed to fetch session statistics' },
        { status: 500 }
      )
    }

    // Calculate statistics
    const stats = {
      total_sessions: sessions?.length || 0,
      active_sessions: sessions?.filter(s => s.status === 'active').length || 0,
      completed_sessions: sessions?.filter(s => s.status === 'completed').length || 0,
      room_sessions: sessions?.filter(s => s.tenant_qr_codes?.target_type === 'room').length || 0,
      area_sessions: sessions?.filter(s => s.tenant_qr_codes?.target_type !== 'room').length || 0,
      avg_duration_minutes: 0,
      checkout_reasons: {}
    }

    // Calculate average duration for completed sessions
    const completedSessions = sessions?.filter(s => s.ended_at) || []
    if (completedSessions.length > 0) {
      const totalDuration = completedSessions.reduce((sum, session) => {
        const start = new Date(session.created_at).getTime()
        const end = new Date(session.ended_at).getTime()
        return sum + (end - start)
      }, 0)
      stats.avg_duration_minutes = Math.round(totalDuration / completedSessions.length / 60000)
    }

    // Count checkout reasons
    sessions?.forEach(session => {
      if (session.checkout_reason) {
        stats.checkout_reasons[session.checkout_reason] = 
          (stats.checkout_reasons[session.checkout_reason] || 0) + 1
      }
    })

    return NextResponse.json({
      success: true,
      stats,
      period_days: days,
      room_number: room_number || 'all'
    })

  } catch (error) {
    console.error('Error getting session stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
