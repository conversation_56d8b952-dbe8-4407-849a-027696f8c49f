'use client';
import { useState, useEffect } from 'react';
import styles from './AssignStaffModal.module.scss';
import { Modal, Button, Alert } from '@ui';

interface AssignStaffModalProps {
  receptionPointId: string;
  onClose: () => void;
  onSave: () => void;
}

export default function AssignStaffModal({
  receptionPointId,
  onClose,
  onSave
}: AssignStaffModalProps) {
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [availableStaff, setAvailableStaff] = useState<any[]>([]);
  const [receptionPoint, setReceptionPoint] = useState<any>(null);
  
  const [formData, setFormData] = useState({
    user_id: '',
    priority: 1,
    is_primary: false
  });
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch reception point details
        const pointResponse = await fetch(`/api/reception-points/${receptionPointId}`);
        if (!pointResponse.ok) {
          throw new Error('Failed to fetch reception point details');
        }
        const pointData = await pointResponse.json();
        setReceptionPoint(pointData.data);
        
        // Fetch unassigned staff
        const staffResponse = await fetch(`/api/reception-points/${receptionPointId}/unassigned-staff`);
        if (!staffResponse.ok) {
          throw new Error('Failed to fetch unassigned staff');
        }
        const staffData = await staffResponse.json();
        setAvailableStaff(staffData.data || []);
        
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [receptionPointId]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: name === 'priority' ? parseInt(value) || 1 : value }));
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.user_id) {
      setError('Please select a staff member');
      return;
    }
    
    try {
      setSubmitting(true);
      setError(null);
      
      const response = await fetch(`/api/users/${formData.user_id}/reception-points`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reception_point_id: receptionPointId,
          priority: formData.priority,
          is_primary: formData.is_primary
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to assign staff');
      }
      
      onSave();
    } catch (err) {
      console.error('Error assigning staff:', err);
      setError(err instanceof Error ? err.message : 'Failed to assign staff');
      setSubmitting(false);
    }
  };
  
  return (
    <Modal isOpen={true} title="Assign Staff to Reception Point" onClose={onClose}>
      {loading ? (
        <div className={styles.loading}>Loading...</div>
      ) : (
        <form onSubmit={handleSubmit}>
          {error && (
            <Alert variant="error" title="Error" onClose={() => setError(null)}>
              {error}
            </Alert>
          )}
          
          <div className={styles.receptionPointInfo}>
            <h3 className={styles.receptionPointName}>
              {receptionPoint?.name || 'Reception Point'}
            </h3>
            <div className={styles.receptionPointCode}>{receptionPoint?.code}</div>
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="user_id">
              Select Staff <span className={styles.required}>*</span>
            </label>
            
            {availableStaff.length === 0 ? (
              <div className={styles.noStaffMessage}>
                All staff members are already assigned to this reception point
              </div>
            ) : (
              <select
                id="user_id"
                name="user_id"
                value={formData.user_id}
                onChange={handleInputChange}
                disabled={submitting}
              >
                <option value="">Select a staff member</option>
                {availableStaff.map(staff => (
                  <option key={staff.id} value={staff.user_id}>
                    {staff.display_name} ({staff.email})
                  </option>
                ))}
              </select>
            )}
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="priority">Priority</label>
            <input
              type="number"
              id="priority"
              name="priority"
              min="1"
              max="100"
              value={formData.priority}
              onChange={handleInputChange}
              disabled={submitting}
            />
            <div className={styles.helpText}>
              Higher priority staff will receive messages first (1-100)
            </div>
          </div>
          
          <div className={styles.formGroup}>
            <div className={styles.checkboxContainer}>
              <input
                type="checkbox"
                id="is_primary"
                name="is_primary"
                checked={formData.is_primary}
                onChange={handleInputChange}
                disabled={submitting}
              />
              <label htmlFor="is_primary">Set as primary staff</label>
            </div>
            <div className={styles.helpText}>
              Primary staff take precedence over others for this reception point
            </div>
          </div>
          
          <div className={styles.formActions}>
            <Button
              type="button"
              variant="secondary"
              onClick={onClose}
              disabled={submitting}
              label="Cancel"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={submitting}
              disabled={submitting || availableStaff.length === 0}
              label="Assign"
            >
              Assign
            </Button>
          </div>
        </form>
      )}
    </Modal>
  );
}
