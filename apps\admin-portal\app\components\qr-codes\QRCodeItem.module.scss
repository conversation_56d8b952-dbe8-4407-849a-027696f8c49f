.qrCodeItem {
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f9fafb;
  }
}

.qrCodeImage {
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    border: 1px solid #e5e7eb;
    border-radius: 4px;
  }
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.editButton, .downloadButton, .deleteButton, .infoButton {
  padding: 0.25rem;
  height: auto;
}

.editButton:hover {
  color: #3b82f6;
}

.downloadButton:hover {
  color: #10b981;
}

.deleteButton:hover {
  color: #ef4444;
}