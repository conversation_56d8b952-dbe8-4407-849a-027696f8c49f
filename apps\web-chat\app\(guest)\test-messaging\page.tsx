'use client'

import { useState, useEffect } from 'react'
import { useChat } from '../../hooks/useChat'
import { useChat as useRealtimeChat } from '../../hooks/useChat.realtime'

export default function TestMessagingPage() {
  const [sessionId, setSessionId] = useState('')
  const [testMessage, setTestMessage] = useState('')
  const [showComparison, setShowComparison] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Current chat hook (now with realtime)
  const currentChat = useChat({
    sessionId: sessionId,
    guestId: 'test-guest-current',
    autoTranslate: false,
    guestLanguage: 'en'
  })

  // Pure realtime chat hook (from separate file)
  const realtimeChat = useRealtimeChat({
    sessionId: sessionId,
    guestId: 'test-guest-realtime',
    autoTranslate: false,
    guestLanguage: 'en'
  })

  const sendTestMessage = async (type: 'current' | 'realtime') => {
    if (!testMessage.trim()) return

    const message = `[${type.toUpperCase()}] ${testMessage} - ${new Date().toLocaleTimeString()}`

    if (type === 'current') {
      await currentChat.sendMessage(message)
    } else {
      await realtimeChat.sendMessage(message)
    }

    setTestMessage('')
  }

  const createTestSessionWithDemo = async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('🔄 Creating demo test session...')

      // Use demo QR code
      const qrResponse = await fetch('/api/qr-scan/DEMO-QR-001?lang=en&device=test-device-demo-' + Date.now())

      if (!qrResponse.ok) {
        throw new Error('Failed to scan demo QR code')
      }

      const qrData = await qrResponse.json()
      console.log('✅ Demo QR scan result:', qrData)

      if (qrData.success && qrData.redirect_url) {
        // Extract session ID from redirect URL
        const urlParts = qrData.redirect_url.split('/')
        const sessionIdFromUrl = urlParts[urlParts.indexOf('chat') + 1]?.split('?')[0]

        if (sessionIdFromUrl && sessionIdFromUrl !== 'new') {
          setSessionId(sessionIdFromUrl)
          setShowComparison(true)
          console.log('✅ Using existing demo session:', sessionIdFromUrl)
          return
        }

        // Create new session with demo data
        const tempUserId = qrData.temp_user_id
        const response = await fetch('/api/chat-sessions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            temporary_user_id: tempUserId,
            guest_language: 'en',
            source_type: 'qr_code',
            source_qr_code_id: 'DEMO-QR-001'
          })
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            setSessionId(data.session.id)
            setShowComparison(true)
            console.log('✅ Created new demo session:', data.session.id)
          } else {
            throw new Error(data.error || 'Failed to create demo session')
          }
        } else {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.error || `HTTP ${response.status}`)
        }
      } else {
        throw new Error(qrData.error || 'Demo QR scan failed')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      console.error('❌ Failed to create demo test session:', err)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const createTestSession = async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('🔄 Creating test session...')

      // Use the existing temp user ID you provided
      const existingTempUserId = '248bce5f-11b0-44eb-a72d-59c2597a541b'

      // Try to create session directly with existing temp user
      const response = await fetch('/api/chat-sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          temporary_user_id: existingTempUserId,
          guest_language: 'en',
          source_type: 'qr_code',
          source_qr_code_id: 'DEMO-QR-001' // Use demo QR that we know exists
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setSessionId(data.session.id)
          setShowComparison(true)
          console.log('✅ Created session with existing temp user:', data.session.id)
        } else {
          throw new Error(data.error || 'Failed to create session')
        }
      } else {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      console.error('❌ Failed to create test session:', err)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Realtime vs Polling Comparison</h1>
        
        {!showComparison ? (
          <div className="bg-white p-8 rounded-lg shadow text-center">
            <h2 className="text-xl font-semibold mb-4">Create Test Session</h2>
            <p className="text-gray-600 mb-6">
              Create a test chat session to compare realtime messaging implementations
            </p>

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <strong>Error:</strong> {error}
              </div>
            )}

            <button
              onClick={createTestSession}
              disabled={loading}
              className="bg-orange-500 text-white px-6 py-3 rounded hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <span className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                  Creating Session...
                </>
              ) : (
                'Start Test'
              )}
            </button>

            <div className="mt-4 text-sm text-gray-500">
              <p>This will use QR code: <code className="bg-gray-100 px-2 py-1 rounded">mbblsqjm-i24r2</code></p>
              <p>Business Center area for testing</p>
            </div>

            <div className="mt-4">
              <button
                onClick={() => createTestSessionWithDemo()}
                disabled={loading}
                className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 text-sm"
              >
                {loading ? 'Creating...' : 'Try with Demo QR'}
              </button>
              <p className="text-xs text-gray-400 mt-1">Fallback option using demo data</p>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Session Info */}
            <div className="bg-white p-4 rounded-lg shadow">
              <h2 className="text-lg font-semibold mb-2">Test Session</h2>
              <p className="text-sm text-gray-600">Session ID: {sessionId}</p>
            </div>

            {/* Message Input */}
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="font-semibold mb-3">Send Test Message</h3>
              <div className="flex gap-3">
                <input
                  type="text"
                  value={testMessage}
                  onChange={(e) => setTestMessage(e.target.value)}
                  placeholder="Type a test message..."
                  className="flex-1 border rounded px-3 py-2"
                  onKeyPress={(e) => e.key === 'Enter' && sendTestMessage('realtime')}
                />
                <button
                  onClick={() => sendTestMessage('current')}
                  disabled={!testMessage.trim()}
                  className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
                >
                  Send via Current Hook
                </button>
                <button
                  onClick={() => sendTestMessage('realtime')}
                  disabled={!testMessage.trim()}
                  className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
                >
                  Send via Pure Realtime
                </button>
              </div>
            </div>

            {/* Comparison Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Current Chat Hook */}
              <div className="bg-white rounded-lg shadow">
                <div className="bg-green-500 text-white p-4 rounded-t-lg">
                  <h3 className="font-semibold">⚡ Current Chat Hook</h3>
                  <div className="text-sm opacity-90">
                    Connected: {currentChat.connected ? '✅' : '❌'} |
                    Realtime: {currentChat.realtimeConnected ? '✅' : '❌'} |
                    Fallback: {currentChat.usePollingFallback ? '⚠️' : '✅'}
                  </div>
                </div>
                <div className="p-4">
                  <div className="h-64 overflow-y-auto border rounded p-3 bg-gray-50">
                    {currentChat.messages.map((msg, idx) => (
                      <div key={idx} className="mb-2 text-sm">
                        <span className="text-gray-500">{new Date(msg.created_at).toLocaleTimeString()}</span>
                        <div className="bg-white p-2 rounded shadow-sm">
                          {msg.content}
                        </div>
                      </div>
                    ))}
                    {currentChat.messages.length === 0 && (
                      <div className="text-gray-400 text-center">No messages yet</div>
                    )}
                  </div>
                  <div className="mt-3 text-xs text-gray-500">
                    Messages: {currentChat.messages.length} |
                    Loading: {currentChat.loading ? 'Yes' : 'No'} |
                    Error: {currentChat.error || 'None'}
                  </div>
                </div>
              </div>

              {/* Pure Realtime Chat */}
              <div className="bg-white rounded-lg shadow">
                <div className="bg-blue-500 text-white p-4 rounded-t-lg">
                  <h3 className="font-semibold">🔄 Pure Realtime Hook</h3>
                  <div className="text-sm opacity-90">
                    Connected: {realtimeChat.connected ? '✅' : '❌'} |
                    Realtime: {realtimeChat.realtimeConnected ? '✅' : '❌'} |
                    Fallback: {realtimeChat.usePollingFallback ? '⚠️' : '✅'}
                  </div>
                </div>
                <div className="p-4">
                  <div className="h-64 overflow-y-auto border rounded p-3 bg-gray-50">
                    {realtimeChat.messages.map((msg, idx) => (
                      <div key={idx} className="mb-2 text-sm">
                        <span className="text-gray-500">{new Date(msg.created_at).toLocaleTimeString()}</span>
                        <div className="bg-white p-2 rounded shadow-sm">
                          {msg.content}
                        </div>
                      </div>
                    ))}
                    {realtimeChat.messages.length === 0 && (
                      <div className="text-gray-400 text-center">No messages yet</div>
                    )}
                  </div>
                  <div className="mt-3 text-xs text-gray-500">
                    Messages: {realtimeChat.messages.length} |
                    Loading: {realtimeChat.loading ? 'Yes' : 'No'} |
                    Error: {realtimeChat.error || 'None'}
                  </div>
                </div>
              </div>
            </div>

            {/* Performance Stats */}
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="font-semibold mb-3">Performance Comparison</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div className="bg-green-50 p-3 rounded">
                  <div className="text-lg font-bold text-green-600">⚡</div>
                  <div className="text-sm">Realtime</div>
                  <div className="text-xs text-gray-500">Instant delivery</div>
                </div>
                <div className="bg-blue-50 p-3 rounded">
                  <div className="text-lg font-bold text-blue-600">🔄</div>
                  <div className="text-sm">Fallback</div>
                  <div className="text-xs text-gray-500">5s delay when needed</div>
                </div>
                <div className="bg-purple-50 p-3 rounded">
                  <div className="text-lg font-bold text-purple-600">📊</div>
                  <div className="text-sm">Efficiency</div>
                  <div className="text-xs text-gray-500">90% less queries</div>
                </div>
                <div className="bg-orange-50 p-3 rounded">
                  <div className="text-lg font-bold text-orange-600">🔋</div>
                  <div className="text-sm">Battery</div>
                  <div className="text-xs text-gray-500">Lower usage</div>
                </div>
              </div>
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">How to Test:</h4>
              <ol className="text-blue-700 text-sm space-y-1">
                <li>1. Send messages using both buttons to compare implementations</li>
                <li>2. Open this page in multiple tabs to test real-time sync</li>
                <li>3. Check the connection status indicators</li>
                <li>4. Notice the instant delivery and fallback behavior</li>
              </ol>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
