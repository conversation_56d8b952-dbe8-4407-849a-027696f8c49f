/**
 * Enhanced useChat Hook with Performance Monitoring and Optimization
 * Combines Supabase Realtime with intelligent fallback and monitoring
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { createClientSupabase } from '@/lib/supabase';
import { realtimeMonitor, generateTrackingId, extractMessageId } from '@/lib/realtime-monitor';

interface ChatSession {
  id: string;
  guest_id: string;
  guest_language: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface ChatMessage {
  id: string;
  content: string;
  sender_type: 'guest' | 'staff';
  sender_name: string;
  timestamp: string;
  is_translated?: boolean;
  show_translation?: boolean;
  tracking_id?: string;
}

interface UseChatEnhancedProps {
  sessionId: string;
  guestId: string;
  guestLanguage?: string;
  autoTranslate?: boolean;
}

export function useChatEnhanced({
  sessionId,
  guestId,
  guestLanguage = 'en',
  autoTranslate = true
}: UseChatEnhancedProps) {
  // State management
  const [session, setSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [connected, setConnected] = useState(false);
  const [realtimeConnected, setRealtimeConnected] = useState(false);
  const [usePollingFallback, setUsePollingFallback] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Refs for cleanup and tracking
  const supabaseRef = useRef<any>(null);
  const subscriptionsRef = useRef<any[]>([]);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);
  const lastMessageIdRef = useRef<string | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const realtimeRetryCountRef = useRef(0);
  const performanceCheckRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize Supabase client with monitoring
  useEffect(() => {
    try {
      realtimeMonitor.startConnectionMonitoring();
      supabaseRef.current = createClientSupabase();
      setConnected(true);
      realtimeMonitor.recordConnection('CONNECTED');
      console.log('✅ Enhanced useChat: Supabase client initialized');
    } catch (err) {
      console.error('❌ Enhanced useChat: Failed to initialize Supabase:', err);
      realtimeMonitor.recordError(err instanceof Error ? err.message : 'Unknown error', 'initialization');
      setError('Failed to connect to chat service');
      setConnected(false);
    }
  }, []);

  // Load session data
  const loadSession = useCallback(async () => {
    if (!sessionId || !supabaseRef.current) return;

    try {
      const response = await fetch(`/api/chat-sessions/${sessionId}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.session) {
          setSession(data.session);
          console.log('✅ Enhanced useChat: Session loaded:', data.session.id);
        }
      }
    } catch (err) {
      console.error('❌ Enhanced useChat: Failed to load session:', err);
      realtimeMonitor.recordError(err instanceof Error ? err.message : 'Unknown error', 'loadSession');
    }
  }, [sessionId]);

  // Load initial messages
  const loadInitialMessages = useCallback(async () => {
    if (!sessionId || !supabaseRef.current) return;

    try {
      const response = await fetch(`/api/messages?session_id=${sessionId}&limit=50`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.messages)) {
          const messagesWithTracking = data.messages.map((msg: any) => ({
            ...msg,
            tracking_id: extractMessageId(msg)
          }));
          setMessages(messagesWithTracking);
          console.log(`✅ Enhanced useChat: Loaded ${messagesWithTracking.length} initial messages`);
        }
      }
    } catch (err) {
      console.error('❌ Enhanced useChat: Failed to load messages:', err);
      realtimeMonitor.recordError(err instanceof Error ? err.message : 'Unknown error', 'loadInitialMessages');
    } finally {
      setLoading(false);
    }
  }, [sessionId]);

  // Cleanup subscriptions
  const cleanupSubscriptions = useCallback(() => {
    console.log('🧹 Enhanced useChat: Cleaning up realtime subscriptions');
    subscriptionsRef.current.forEach(subscription => {
      try {
        subscription.unsubscribe();
      } catch (err) {
        console.warn('Warning: Failed to unsubscribe:', err);
      }
    });
    subscriptionsRef.current = [];
    setRealtimeConnected(false);
  }, []);

  // Setup Realtime subscriptions with enhanced monitoring
  const setupRealtimeSubscriptions = useCallback(() => {
    if (!supabaseRef.current || !sessionId || !mountedRef.current) return;

    console.log('🔄 Enhanced useChat: Setting up realtime subscriptions for session:', sessionId);
    realtimeMonitor.startSubscriptionMonitoring();
    
    try {
      // Messages subscription with enhanced tracking
      const messagesChannel = supabaseRef.current
        .channel(`enhanced-messages:${sessionId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'tenant_chat_messages',
            filter: `chat_session_id=eq.${sessionId}`
          },
          (payload: any) => {
            const messageId = extractMessageId(payload.new);
            console.log('🔔 Enhanced useChat: New message received via realtime:', {
              id: messageId,
              sender_type: payload.new.sender_type,
              content: payload.new.content?.substring(0, 50),
              timestamp: payload.new.created_at
            });

            // Record message received for latency tracking
            realtimeMonitor.recordMessageReceived(messageId, true);

            if (mountedRef.current) {
              const newMessage: ChatMessage = {
                id: payload.new.id,
                content: payload.new.content,
                sender_type: payload.new.sender_type,
                sender_name: payload.new.sender_name || (payload.new.sender_type === 'guest' ? 'Guest' : 'Staff'),
                timestamp: payload.new.created_at,
                is_translated: payload.new.is_translated || false,
                show_translation: false,
                tracking_id: messageId
              };

              setMessages(prev => {
                const exists = prev.some(msg => msg.id === newMessage.id);
                if (exists) return prev;
                return [...prev, newMessage];
              });

              lastMessageIdRef.current = newMessage.id;
            }
          }
        )
        .subscribe((status: string) => {
          console.log('📡 Enhanced useChat: Messages subscription status:', status);
          realtimeMonitor.recordConnection(status);
          
          if (status === 'SUBSCRIBED') {
            setRealtimeConnected(true);
            setUsePollingFallback(false);
            realtimeRetryCountRef.current = 0;
            console.log('✅ Enhanced useChat: Realtime messages subscription active');
          } else if (status === 'SUBSCRIPTION_ERROR' || status === 'CLOSED') {
            console.warn('⚠️ Enhanced useChat: Realtime subscription failed, falling back to polling');
            realtimeMonitor.recordError(`Subscription ${status}`, 'realtime-subscription');
            setRealtimeConnected(false);
            setUsePollingFallback(true);
            realtimeRetryCountRef.current++;
          }
        });

      // Session status subscription
      const sessionChannel = supabaseRef.current
        .channel(`enhanced-session:${sessionId}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'tenant_chat_sessions',
            filter: `id=eq.${sessionId}`
          },
          (payload: any) => {
            console.log('🔄 Enhanced useChat: Session updated via realtime:', payload.new);
            if (mountedRef.current) {
              setSession(payload.new as ChatSession);
            }
          }
        )
        .subscribe((status: string) => {
          console.log('📡 Enhanced useChat: Session subscription status:', status);
        });

      // Store subscriptions for cleanup
      subscriptionsRef.current = [messagesChannel, sessionChannel];

    } catch (err) {
      console.error('❌ Enhanced useChat: Failed to setup realtime subscriptions:', err);
      realtimeMonitor.recordError(err instanceof Error ? err.message : 'Unknown error', 'setupRealtimeSubscriptions');
      setUsePollingFallback(true);
    }
  }, [sessionId]);

  // Enhanced polling fallback with adaptive intervals
  const startPollingFallback = useCallback(() => {
    if (!usePollingFallback || pollingIntervalRef.current) return;

    console.log('🔄 Enhanced useChat: Starting enhanced polling fallback');

    let currentInterval = 1000; // Start with 1 second (much faster!)
    let noActivityCount = 0;
    
    const poll = async () => {
      if (mountedRef.current && sessionId && !document.hidden) {
        try {
          const response = await fetch(`/api/messages?session_id=${sessionId}&limit=50`);
          if (response.ok) {
            const data = await response.json();
            if (data.success && Array.isArray(data.messages)) {
              const latestMessage = data.messages[data.messages.length - 1];
              const hasNewMessages = latestMessage && latestMessage.id !== lastMessageIdRef.current;
              
              if (hasNewMessages) {
                // Record polling message received
                const messageId = extractMessageId(latestMessage);
                realtimeMonitor.recordMessageReceived(messageId, false);

                setMessages(data.messages.map((msg: any) => ({
                  ...msg,
                  tracking_id: extractMessageId(msg)
                })));
                lastMessageIdRef.current = latestMessage.id;

                // Reset to fast polling when active
                currentInterval = 1000;
                noActivityCount = 0;
                console.log('📥 Enhanced useChat: New messages via polling - resetting to 1s interval');
              } else {
                // Slow down gradually when no activity
                noActivityCount++;
                if (noActivityCount > 2) {
                  currentInterval = Math.min(currentInterval * 1.3, 4000); // Max 4 seconds, faster growth
                  console.log(`🔄 Enhanced useChat: No activity, slowing to ${currentInterval}ms`);
                }
              }
            }
          }
        } catch (err) {
          console.error('Enhanced useChat: Polling error:', err);
          realtimeMonitor.recordError(err instanceof Error ? err.message : 'Unknown error', 'polling');
        }
      }
      
      // Schedule next poll with adaptive interval
      if (mountedRef.current && usePollingFallback) {
        pollingIntervalRef.current = setTimeout(poll, currentInterval);
      }
    };
    
    poll();
  }, [usePollingFallback, sessionId]);

  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearTimeout(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
      console.log('⏹️ Enhanced useChat: Polling stopped');
    }
  }, []);

  // Send message with tracking
  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!sessionId || !content.trim() || !mountedRef.current) {
      return false;
    }

    const trackingId = generateTrackingId();
    realtimeMonitor.recordMessageSent(trackingId, 'guest');

    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: sessionId,
          sender_type: 'guest',
          sender_name: 'Guest',
          content: content.trim(),
          tracking_id: trackingId
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          console.log('✅ Enhanced useChat: Message sent successfully:', trackingId);
          return true;
        }
      }
      
      throw new Error('Failed to send message');
    } catch (err) {
      console.error('❌ Enhanced useChat: Failed to send message:', err);
      realtimeMonitor.recordError(err instanceof Error ? err.message : 'Unknown error', 'sendMessage');
      return false;
    }
  }, [sessionId]);

  // Enhanced typing indicators
  const startTyping = useCallback(() => {
    if (!supabaseRef.current || !sessionId || !guestId) return;

    const typingChannel = supabaseRef.current.channel(`typing:${sessionId}`);
    typingChannel.send({
      type: 'broadcast',
      event: 'typing',
      payload: { user_id: guestId, user_type: 'guest' }
    });
  }, [sessionId, guestId]);

  const stopTyping = useCallback(() => {
    if (!supabaseRef.current || !sessionId || !guestId) return;

    const typingChannel = supabaseRef.current.channel(`typing:${sessionId}`);
    typingChannel.send({
      type: 'broadcast',
      event: 'stop_typing',
      payload: { user_id: guestId, user_type: 'guest' }
    });
  }, [sessionId, guestId]);

  // Performance monitoring
  useEffect(() => {
    // Check for stale messages every 30 seconds
    performanceCheckRef.current = setInterval(() => {
      realtimeMonitor.checkStaleMessages();
    }, 30000);

    return () => {
      if (performanceCheckRef.current) {
        clearInterval(performanceCheckRef.current);
      }
    };
  }, []);

  // Initialize hook
  useEffect(() => {
    mountedRef.current = true;

    if (sessionId) {
      loadSession();
    }

    return () => {
      mountedRef.current = false;
      stopPolling();
      cleanupSubscriptions();
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (performanceCheckRef.current) {
        clearInterval(performanceCheckRef.current);
      }
    };
  }, [sessionId, loadSession, stopPolling, cleanupSubscriptions]);

  // Setup realtime after session is loaded
  useEffect(() => {
    if (session && connected && supabaseRef.current) {
      loadInitialMessages();
      setupRealtimeSubscriptions();
    }

    return () => {
      cleanupSubscriptions();
      stopPolling();
    };
  }, [session, connected, loadInitialMessages, setupRealtimeSubscriptions, cleanupSubscriptions, stopPolling]);

  // Start polling fallback when needed
  useEffect(() => {
    if (usePollingFallback) {
      startPollingFallback();
    } else {
      stopPolling();
    }
  }, [usePollingFallback, startPollingFallback, stopPolling]);

  return {
    session,
    messages,
    loading,
    connected: connected && (realtimeConnected || usePollingFallback),
    sendMessage,
    isTyping,
    startTyping,
    stopTyping,
    error,
    realtimeConnected,
    usePollingFallback,
    // Enhanced monitoring functions
    getPerformanceMetrics: () => realtimeMonitor.getPerformanceSummary(),
    logPerformance: () => realtimeMonitor.logPerformanceSummary(),
  };
}
