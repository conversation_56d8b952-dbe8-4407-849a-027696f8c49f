@import '../styles/_variables';

.card {
  background-color: white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  height: 100%;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }
}

.qrSection {
  padding: $spacing-md;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 180px;
  position: relative;
}

.qrWrapper {
  position: relative;
  width: 150px;
  height: 150px;
  
  &:hover .overlay {
    opacity: 1;
  }
}

.qrImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
  border-radius: $border-radius-sm;
}

.downloadButton {
  background-color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: $primary-color;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: $primary-color;
    color: white;
  }
}

.content {
  padding: $spacing-md;
  flex: 1;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: $dark-gray;
  margin-bottom: $spacing-sm;
  display: block;
  text-decoration: none;
  
  &:hover {
    color: $primary-color;
  }
}

.description {
  font-size: 14px;
  color: $gray;
  margin-bottom: $spacing-sm;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.meta {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-xs $spacing-md;
  margin-top: $spacing-sm;
}

.metaItem {
  font-size: 12px;
  display: flex;
  flex-direction: column;
}

.metaLabel {
  color: $gray;
}

.metaValue {
  color: $dark-gray;
  font-weight: 500;
}

.status {
  display: inline-block;
  font-weight: 500;
  
  &.active {
    color: #00a651;
  }
  
  &.inactive {
    color: $gray;
  }
}

.actions {
  display: flex;
  padding: $spacing-md;
  border-top: 1px solid #eee;
  gap: $spacing-sm;
}

.viewButton, .editButton {
  flex: 1;
  padding: 8px 12px;
  border-radius: $border-radius-sm;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s;
}

.viewButton {
  background-color: #f0f0f0;
  color: $dark-gray;
  
  &:hover {
    background-color: #e0e0e0;
  }
}

.editButton {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 10%);
  }
}

.placeholder {
  width: 150px;
  height: 150px;
  background-color: #eee;
  border-radius: $border-radius-sm;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: $primary-color;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
