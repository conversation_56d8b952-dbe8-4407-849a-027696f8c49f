.container {
  padding: 16px 0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  @media (max-width: 640px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

.headerLeft {
  display: flex;
  align-items: center;
}

.backLink {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  text-decoration: none;
  margin-right: 16px;
  padding: 6px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f3f4f6;
    color: #111827;
  }
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  
  @media (min-width: 768px) {
    font-size: 1.75rem;
  }
}

.actions {
  display: flex;
  gap: 12px;
}

.viewToggle {
  display: flex;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.viewToggleButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: white;
  border: none;
  cursor: pointer;
  color: #6b7280;
  
  &:hover {
    background-color: #f9fafb;
    color: #111827;
  }
  
  &.active {
    background-color: #f3f4f6;
    color: #111827;
  }
  
  &:first-child {
    border-right: 1px solid #e5e7eb;
  }
}

.createButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  text-decoration: none;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #2563eb;
  }
  
  svg {
    stroke-width: 2;
  }
}

.filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  gap: 16px;
  flex-wrap: wrap;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.searchBox {
  position: relative;
  min-width: 280px;
  flex: 1;
  max-width: 400px;
}

.searchInput {
  width: 100%;
  padding: 10px 16px 10px 40px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  outline: none;
  
  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
}

.searchIcon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.filterGroup {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filterSelect {
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  background-color: white;
  outline: none;
  color: #4b5563;
  min-width: 160px;
  
  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
}

.loading,
.error,
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 0;
  text-align: center;
}

.loading {
  color: #6b7280;
  font-size: 0.875rem;
}

.error {
  color: #b91c1c;
  font-size: 0.875rem;
  
  p {
    margin-bottom: 16px;
  }
}

.retryButton {
  padding: 8px 16px;
  background-color: #f3f4f6;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  
  &:hover {
    background-color: #e5e7eb;
  }
}

.emptyState {
  color: #6b7280;
  
  svg {
    color: #d1d5db;
    margin-bottom: 16px;
  }
  
  h3 {
    font-size: 1.125rem;
    font-weight: 500;
    color: #111827;
    margin: 0 0 8px 0;
  }
  
  p {
    margin: 0 0 24px 0;
    font-size: 0.875rem;
  }
}

.createEmptyButton {
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  
  &:hover {
    background-color: #2563eb;
  }
}

.areasGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.areaCard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 16px;
  transition: all 0.2s;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
}

.areaCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.areaName {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.areaType {
  font-size: 0.75rem;
  padding: 4px 8px;
  background-color: #e0f2fe;
  color: #0284c7;
  border-radius: 9999px;
  font-weight: 500;
}

.areaImageContainer {
  margin: -16px -16px 16px -16px;
  height: 140px;
  overflow: hidden;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.areaImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.areaDetails {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.areaDetail {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
}

.detailLabel {
  color: #6b7280;
}

.detailValue {
  font-weight: 500;
  color: #111827;
}

.areaActions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.viewButton,
.editButton {
  flex: 1;
  padding: 8px 0;
  text-align: center;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s;
}

.viewButton {
  background-color: #e5e7eb;
  color: #4b5563;
  
  &:hover {
    background-color: #d1d5db;
    color: #111827;
  }
}

.editButton {
  background-color: #dbeafe;
  color: #3b82f6;
  
  &:hover {
    background-color: #bfdbfe;
    color: #1d4ed8;
  }
}

.tableContainer {
  overflow-x: auto;
  margin-top: 16px;
}

.tableView {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
  
  th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }
  
  th {
    font-weight: 500;
    color: #374151;
    background-color: #f9fafb;
  }
  
  tr:hover {
    background-color: #f9fafb;
  }
}

.tableActions {
  display: flex;
  gap: 8px;
}

.actionLink {
  padding: 4px 10px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s;
  
  &:first-child {
    background-color: #e0f2fe;
    color: #0284c7;
    
    &:hover {
      background-color: #bae6fd;
    }
  }
  
  &:last-child {
    background-color: #e0e7ff;
    color: #4f46e5;
    
    &:hover {
      background-color: #c7d2fe;
    }
  }
}