//D:\loaloa\apps\admin-portal\app\api\areas\[id]
import { createClient } from '../../../../lib/supabase/server';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    const { data, error } = await supabase
      .from('tenant_areas')
      .select('*')
      .eq('id', params.id)
      .single();
    
    if (error) {
      console.error('Error fetching area:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    if (!data) {
      return NextResponse.json({ error: 'Area not found' }, { status: 404 });
    }
    
    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    const body = await request.json();
    
    const { 
      name, 
      area_type, 
      floor, 
      location,
     // description,
      staff_count,
      opening_hours,
      closing_hours,
      image_url,
      is_active 
    } = body;
    
    const { data, error } = await supabase
      .from('tenant_areas')
      .update({ 
        name, 
        area_type, 
        floor, 
        location,
      //  description,
        staff_count,
        opening_hours,
        closing_hours,
        image_url,
        is_active
      })
      .eq('id', params.id)
      .select();
    
    if (error) {
      console.error('Error updating area:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ data: data[0] });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    const { error } = await supabase
      .from('tenant_areas')
      .delete()
      .eq('id', params.id);
    
    if (error) {
      console.error('Error deleting area:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
