import React, { useState } from 'react';
import { Form, Button } from '@loaloa/ui';
import styles from './RevokeLicenseModal.module.scss';

interface RevokeLicenseModalProps {
  licenseId: string;
  licenseKey: string;
  onClose: () => void;
  onSuccess: () => void;
}

const RevokeLicenseModal: React.FC<RevokeLicenseModalProps> = ({
  licenseId,
  licenseKey,
  onClose,
  onSuccess
}) => {
  // State
  const [reason, setReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Handle submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!reason) {
      setError('Please provide a reason for revoking this license');
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/licenses/${licenseId}/revoke`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to revoke license');
      }
      
      onSuccess();
    } catch (err: any) {
      setError(err.message || 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modal}>
        <div className={styles.modalHeader}>
          <h2>Revoke License</h2>
          <button className={styles.closeButton} onClick={onClose}>×</button>
        </div>
        
        {error && (
          <div className={styles.errorMessage}>{error}</div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            <div className={styles.warning}>
              <p>
                <strong>Warning:</strong> You are about to revoke license <code>{licenseKey}</code>. 
                This action will prevent the license from being used and cannot be easily undone.
              </p>
            </div>
            
            <Form.FormGroup label="Revocation Reason" htmlFor="reason">
              <Form.TextArea
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Please provide a reason for revoking this license"
                required
                rows={4}
              />
            </Form.FormGroup>
          </div>
          
          <div className={styles.modalFooter}>
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="danger"
              disabled={isLoading}
            >
              {isLoading ? 'Revoking...' : 'Revoke License'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RevokeLicenseModal;