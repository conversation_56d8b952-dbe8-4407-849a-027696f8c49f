import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';
import logger from '../utils/logger';
import { License, LicenseActivity, LicenseActivityType, LicenseClone, CloneStatus, LicenseActivation } from '../types';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export const db = {
  // License operations
  licenses: {
    async getById(id: string): Promise<License | null> {
      const { data, error } = await supabase
        .from('licenses')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        logger.error('Error getting license by ID', { error, id });
        return null;
      }
      
      return data as License;
    },
    
    async getByKey(licenseKey: string): Promise<License | null> {
      const { data, error } = await supabase
        .from('licenses')
        .select('*')
        .eq('license_key', licenseKey)
        .single();
      
      if (error) {
        logger.error('Error getting license by key', { error, licenseKey });
        return null;
      }
      
      return data as License;
    },
    
    async create(license: Omit<License, 'id'>): Promise<License | null> {
      const { data, error } = await supabase
        .from('licenses')
        .insert({
          ...license,
          id: uuidv4()
        })
        .select()
        .single();
      
      if (error) {
        logger.error('Error creating license', { error, license });
        return null;
      }
      
      return data as License;
    },
    
    async update(id: string, updates: Partial<License>): Promise<License | null> {
      const { data, error } = await supabase
        .from('licenses')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      if (error) {
        logger.error('Error updating license', { error, id, updates });
        return null;
      }
      
      return data as License;
    },
    
    async listAll(
      page: number = 1, 
      limit: number = 10, 
      status?: string,
      search?: string
    ): Promise<{ data: License[], count: number }> {
      let query = supabase
        .from('licenses')
        .select('*', { count: 'exact' });
      
      // Apply filters if provided
      if (status && status !== 'ALL') {
        if (status === 'ACTIVE') {
          query = query.eq('is_active', true);
        } else if (status === 'INACTIVE') {
          query = query.eq('is_active', false);
        }
      }
      
      if (search) {
        query = query.or(`license_key.ilike.%${search}%,customer_name.ilike.%${search}%,customer_email.ilike.%${search}%`);
      }
      
      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      
      const { data, error, count } = await query
        .order('issue_date', { ascending: false })
        .range(from, to);
      
      if (error) {
        logger.error('Error listing licenses', { error, page, limit, status });
        return { data: [], count: 0 };
      }
      
      return { 
        data: data as License[], 
        count: count || 0 
      };
    }
  },
  
  // License activity operations
  activities: {
    async create(activity: Omit<LicenseActivity, 'id' | 'timestamp'>): Promise<LicenseActivity | null> {
      const { data, error } = await supabase
        .from('license_activities')
        .insert({
          ...activity,
          id: uuidv4(),
          timestamp: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        logger.error('Error creating license activity', { error, activity });
        return null;
      }
      
      return data as LicenseActivity;
    },
    
    async listByLicenseId(
      licenseId: string,
      page: number = 1,
      limit: number = 20
    ): Promise<{ data: LicenseActivity[], count: number }> {
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      
      const { data, error, count } = await supabase
        .from('license_activities')
        .select('*', { count: 'exact' })
        .eq('license_id', licenseId)
        .order('timestamp', { ascending: false })
        .range(from, to);
      
      if (error) {
        logger.error('Error listing license activities', { error, licenseId });
        return { data: [], count: 0 };
      }
      
      return { 
        data: data as LicenseActivity[], 
        count: count || 0 
      };
    }
  },
  
  // License clone operations
  clones: {
    async create(clone: Omit<LicenseClone, 'id' | 'detection_time'>): Promise<LicenseClone | null> {
      const { data, error } = await supabase
        .from('license_clones')
        .insert({
          ...clone,
          id: uuidv4(),
          detection_time: new Date().toISOString(),
          status: CloneStatus.DETECTED
        })
        .select()
        .single();
      
      if (error) {
        logger.error('Error creating license clone record', { error, clone });
        return null;
      }
      
      return data as LicenseClone;
    },
    
    async update(id: string, status: CloneStatus): Promise<LicenseClone | null> {
      const { data, error } = await supabase
        .from('license_clones')
        .update({ status })
        .eq('id', id)
        .select()
        .single();
      
      if (error) {
        logger.error('Error updating license clone status', { error, id, status });
        return null;
      }
      
      return data as LicenseClone;
    },
    
    async listByLicenseId(
      licenseId: string,
      page: number = 1,
      limit: number = 10
    ): Promise<{ data: LicenseClone[], count: number }> {
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      
      const { data, error, count } = await supabase
        .from('license_clones')
        .select('*', { count: 'exact' })
        .eq('license_id', licenseId)
        .order('detection_time', { ascending: false })
        .range(from, to);
      
      if (error) {
        logger.error('Error listing license clones', { error, licenseId });
        return { data: [], count: 0 };
      }
      
      return { 
        data: data as LicenseClone[], 
        count: count || 0 
      };
    }
  },
  
  // License activation operations
  activations: {
    async create(activation: Omit<LicenseActivation, 'id' | 'expires_at' | 'is_used'>): Promise<LicenseActivation | null> {
      // Set expiration to 24 hours from now
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);
      
      const { data, error } = await supabase
        .from('license_activations')
        .insert({
          ...activation,
          id: uuidv4(),
          expires_at: expiresAt.toISOString(),
          is_used: false
        })
        .select()
        .single();
      
      if (error) {
        logger.error('Error creating license activation', { error, activation });
        return null;
      }
      
      return data as LicenseActivation;
    },
    
    async getById(id: string): Promise<LicenseActivation | null> {
      const { data, error } = await supabase
        .from('license_activations')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        logger.error('Error getting license activation by ID', { error, id });
        return null;
      }
      
      return data as LicenseActivation;
    },
    
    async markAsUsed(id: string): Promise<boolean> {
      const { error } = await supabase
        .from('license_activations')
        .update({ 
          is_used: true,
          used_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (error) {
        logger.error('Error marking license activation as used', { error, id });
        return false;
      }
      
      return true;
    }
  }
};