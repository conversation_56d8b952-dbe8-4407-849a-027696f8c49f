.container {
  padding: 1.5rem;
}

.pageHeader {
  margin-bottom: 1.5rem;
}

.pageTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0.5rem 0;
}

.backLink {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #6b7280;
  text-decoration: none;
  
  &:hover {
    color: #4b5563;
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
}

.formCard {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #374151;
}

.input, .select, .textarea {
  padding: 0.625rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: white;
  font-size: 0.875rem;
  color: #374151;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5);
  }
  
  &:disabled {
    background-color: #f3f4f6;
    cursor: not-allowed;
  }
}

.required {
  color: #ef4444;
  margin-left: 0.125rem;
}

.helpText {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.hoursContainer {
  display: flex;
  gap: 1rem;
}

.timeInput {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  
  label {
    font-size: 0.75rem;
    color: #6b7280;
  }
}

.timeField {
  width: 5rem;
  padding: 0.375rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
}

.daysContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.dayOption {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  
  label {
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
  }
  
  input[type="checkbox"] {
    cursor: pointer;
  }
}

.checkboxContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkboxLabel {
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.cancelButton {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background-color: #f3f4f6;
  }
}
