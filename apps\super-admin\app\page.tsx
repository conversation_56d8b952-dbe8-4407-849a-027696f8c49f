'use client';

import React, { useState, useEffect } from 'react';
import { DashboardLayout, Card, Table, Button } from '@loaloa/ui';
import { StatCard } from '@loaloa/ui/src/components/Stats/StatCard';
import Link from 'next/link';
import styles from './page.module.scss';

// Mock icons (replace with your actual icons)
const HomeIcon = () => (
  <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z"></path>
    <polyline points="9 22 9 12 15 12 15 22"></polyline>
  </svg>
);

const UsersIcon = () => (
  <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"></path>
    <circle cx="9" cy="7" r="4"></circle>
    <path d="M23 21v-2a4 4 0 00-3-3.87"></path>
    <path d="M16 3.13a4 4 0 010 7.75"></path>
  </svg>
);

const BuildingIcon = () => (
  <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
    <rect x="4" y="2" width="16" height="20" rx="2" ry="2"></rect>
    <path d="M9 22v-4h6v4"></path>
    <path d="M8 6h.01"></path>
    <path d="M16 6h.01"></path>
    <path d="M12 6h.01"></path>
    <path d="M12 10h.01"></path>
    <path d="M12 14h.01"></path>
    <path d="M16 10h.01"></path>
    <path d="M16 14h.01"></path>
    <path d="M8 10h.01"></path>
    <path d="M8 14h.01"></path>
  </svg>
);

const LicenseIcon = () => (
  <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
    <path d="M16 4h2a2 2 0 012 2v14a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2h2"></path>
    <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
    <path d="M10 14l2 2 4-4"></path>
  </svg>
);

const SettingsIcon = () => (
  <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
    <circle cx="12" cy="12" r="3"></circle>
    <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z"></path>
  </svg>
);

// Mock data
const recentTenants = [
  {
    id: '1',
    name: 'Hotel California',
    domain: 'hotel-california.loaloa.app',
    contact_email: '<EMAIL>',
    plan: 'premium',
    status: 'active',
    created_at: '2025-05-01T10:30:00Z',
  },
  {
    id: '2',
    name: 'Grand Resort & Spa',
    domain: 'grand-resort.loaloa.app',
    contact_email: '<EMAIL>',
    plan: 'enterprise',
    status: 'active',
    created_at: '2025-04-28T14:45:00Z',
  },
  {
    id: '3',
    name: 'Seaside Inn',
    domain: 'seaside-inn.loaloa.app', 
    contact_email: '<EMAIL>',
    plan: 'basic',
    status: 'pending',
    created_at: '2025-04-25T09:15:00Z',
  },
  {
    id: '4',
    name: 'Mountain Lodge',
    domain: 'mountain-lodge.loaloa.app',
    contact_email: '<EMAIL>',
    plan: 'basic',
    status: 'inactive',
    created_at: '2025-04-20T16:30:00Z',
  },
];

export default function SuperAdminDashboard() {
  const sidebarItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      href: '/',
      icon: <HomeIcon />,
      active: true,
    },
    {
      id: 'tenants',
      label: 'Tenants',
      href: '/tenants',
      icon: <BuildingIcon />,
    },
    {
      id: 'users',
      label: 'Users',
      href: '/users',
      icon: <UsersIcon />,
    },
    {
      id: 'licenses',
      label: 'Licenses',
      href: '/licenses',
      icon: <LicenseIcon />,
    },
    {
      id: 'settings',
      label: 'Settings',
      href: '/settings',
      icon: <SettingsIcon />,
    },
  ];

  const columns = [
    {
      header: 'Tenant',
      accessor: (tenant: any) => (
        <div className={styles.tenantCell}>
          <Link href={`/tenants/${tenant.id}`} className={styles.tenantName}>
            {tenant.name}
          </Link>
          <span className={styles.tenantDomain}>{tenant.domain}</span>
        </div>
      ),
      width: '30%',
    },
    {
      header: 'Contact',
      accessor: 'contact_email',
    },
    {
      header: 'Plan',
      accessor: (tenant: any) => (
        <span className={`${styles.planBadge} ${styles[tenant.plan]}`}>
          {tenant.plan}
        </span>
      ),
      width: '15%',
    },
    {
      header: 'Status',
      accessor: (tenant: any) => {
        const statusClass = `status-badge status-${tenant.status}`;
        return <span className={statusClass}>{tenant.status}</span>;
      },
      width: '15%',
    },
    {
      header: 'Created',
      accessor: (tenant: any) => new Date(tenant.created_at).toLocaleDateString(),
      width: '15%',
    },
  ];

  return (
    <DashboardLayout
      sidebarItems={sidebarItems}
      title="Super Admin"
      username="Admin User"
      breadcrumbs={[{ label: 'Dashboard', href: '/' }]}
    >
      <div className={styles.dashboard}>
        {/* KPI Stats */}
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <StatCard
              title="Total Tenants"
              value="15"
              variant="primary"
              change={{ value: 12, isPositive: true }}
              footer="2 new this month"
            />
          </div>
          <div className={styles.statCard}>
            <StatCard
              title="Active Users"
              value="2,543"
              variant="info"
              change={{ value: 8, isPositive: true }}
              footer="152 online now"
            />
          </div>
          <div className={styles.statCard}>
            <StatCard
              title="Monthly Revenue"
              value="$24,500"
              variant="success"
              change={{ value: 5, isPositive: true }}
              footer="$1,200 more than last month"
            />
          </div>
          <div className={styles.statCard}>
            <StatCard
              title="Active Licenses"
              value="12"
              variant="warning"
              change={{ value: 3, isPositive: false }}
              footer="1 expires next week"
            />
          </div>
        </div>

        {/* Recent Tenants */}
        <Card className={styles.recentTenantsCard}>
          <div className={styles.cardHeader}>
            <h2>Recent Tenants</h2>
            <Link href="/tenants">
              <Button size="small" variant="outline">View All</Button>
            </Link>
          </div>
          <Table
            columns={columns}
            data={recentTenants}
            keyExtractor={(item) => item.id}
          />
        </Card>

        {/* Quick Actions */}
        <div className={styles.quickActions}>
          <Card>
            <h2 className={styles.sectionTitle}>Quick Actions</h2>
            <div className={styles.actionButtons}>
              <Button variant="primary">Add New Tenant</Button>
              <Button variant="outline">Create License</Button>
              <Button variant="outline">Manage Users</Button>
              <Button variant="outline">View Reports</Button>
            </div>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}