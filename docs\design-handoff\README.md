# LoaLoa Design Handoff

## Tổng quan
Tài liệu này định nghĩa quy trình chuyển giao thiết kế từ designer sang developer trong dự án LoaLoa.

## Công cụ
- **Design**: Figma
- **Design Tokens**: Đượ<PERSON> định nghĩa trong packages/design-tokens
- **Documentation**: Markdown files trong docs/design-handoff

## Quy trình Handoff

### 1. Designer
- Thiết kế UI trong Figma theo design system đã thống nhất
- Đặt tên layers và components theo quy ước đặt tên (xem phần Quy ước đặt tên)
- Export màn hình hoặc component cụ thể
- Tạo file handoff và thêm vào thư mục docs/design-handoff

### 2. Developer
- Tham khảo file handoff và Figma design
- Sử dụng design tokens đã được định nghĩa
- Triển khai UI theo design specs
- Tạo pull request với component preview hoặc screenshot để review

## Quy ước đặt tên
- Components: `[component-name]-[variant]`
- Screens: `[feature]-[screen-name]`
- Colors: Sử dụng semantic naming từ design tokens
- Spacing: Tuân theo design tokens
