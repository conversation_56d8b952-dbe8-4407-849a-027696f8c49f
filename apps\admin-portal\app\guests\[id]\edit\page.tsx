'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Alert } from '@loaloa/ui';
import styles from '../../create/guestForm.module.scss';

interface Guest {
  id: string;
  full_name: string;
  email: string | null;
  phone: string | null;
  room_number: string | null;
  tenant_id: string;
}

export default function EditGuestPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  
  const [guestData, setGuestData] = useState<Guest>({
    id: '',
    full_name: '',
    email: '',
    phone: '',
    room_number: '',
    tenant_id: '',
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchGuestDetails = async () => {
      try {
        const response = await fetch(`/api/guests/${params.id}`);
        const result = await response.json();
        
        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch guest details');
        }
        
        setGuestData(result.data);
      } catch (err) {
        console.error('Error fetching guest details:', err);
        setError('Không thể tải thông tin khách hàng. Vui lòng thử lại sau.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchGuestDetails();
  }, [params.id]);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setGuestData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!guestData.full_name) {
      setError('Vui lòng nhập họ tên khách hàng');
      return;
    }
    
    try {
      setSaving(true);
      setError(null);
      
      const response = await fetch(`/api/guests/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          full_name: guestData.full_name,
          email: guestData.email,
          phone: guestData.phone,
          room_number: guestData.room_number,
        }),
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Không thể cập nhật thông tin khách hàng');
      }
      
      // Chuyển hướng đến trang chi tiết guest
      router.push(`/guests/${params.id}`);
    } catch (err) {
      console.error('Error updating guest:', err);
      setError(err instanceof Error ? err.message : 'Đã xảy ra lỗi khi cập nhật thông tin khách');
    } finally {
      setSaving(false);
    }
  };
  
  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loadingState}>
          <div className={styles.spinner}></div>
          <p>Đang tải thông tin khách hàng...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className={styles.container}>
      <div className={styles.pageHeader}>
        <div className={styles.titleSection}>
          <Link href={`/guests/${params.id}`} className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.33333 5.83331L4.16666 9.99998L8.33333 14.1666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Quay lại chi tiết khách
          </Link>
          <h1 className={styles.pageTitle}>Chỉnh sửa thông tin khách</h1>
        </div>
      </div>
      
      {error && (
        <Alert 
          variant="error" 
          title="Lỗi" 
          closable 
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}
      
      <div className={styles.formCard}>
        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="full_name" className={styles.label}>
              Họ và tên <span className={styles.required}>*</span>
            </label>
            <input
              type="text"
              id="full_name"
              name="full_name"
              value={guestData.full_name}
              onChange={handleChange}
              className={styles.input}
              placeholder="Nhập họ và tên của khách"
              required
            />
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="email" className={styles.label}>
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={guestData.email || ''}
              onChange={handleChange}
              className={styles.input}
              placeholder="Nhập địa chỉ email"
            />
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="phone" className={styles.label}>
              Số điện thoại
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={guestData.phone || ''}
              onChange={handleChange}
              className={styles.input}
              placeholder="Nhập số điện thoại"
            />
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="room_number" className={styles.label}>
              Số phòng
            </label>
            <input
              type="text"
              id="room_number"
              name="room_number"
              value={guestData.room_number || ''}
              onChange={handleChange}
              className={styles.input}
              placeholder="Nhập số phòng"
              disabled={guestData.room_number !== null}
            />
            {guestData.room_number && (
              <p className={styles.helpText}>
                Số phòng không thể thay đổi sau khi đã check-in. Để thay đổi phòng, 
                vui lòng check-out khách và check-in lại vào phòng mới.
              </p>
            )}
          </div>
          
          <div className={styles.formActions}>
            <Link href={`/guests/${params.id}`} className={styles.cancelButton}>
              Hủy
            </Link>
            <button 
              type="submit" 
              className={styles.submitButton}
              disabled={saving}
            >
              {saving ? 'Đang lưu...' : 'Lưu thay đổi'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
