import { Request, Response } from 'express';
import * as temporaryUserService from '../services/temporaryUserService';

export const createTemporaryUser = async (req: Request, res: Response) => {
  try {
    const { preferred_language, hotel_id, room_number, metadata } = req.body;
    
    const result = await temporaryUserService.createTemporaryUser(
      preferred_language,
      hotel_id,
      room_number,
      metadata
    );
    
    if (!result) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create temporary user'
      });
    }
    
    return res.status(201).json({
      success: true,
      data: {
        temporary_user: result.temporaryUser,
        qr_code_url: result.qrCodeUrl
      }
    });
  } catch (error) {
    console.error('Create temporary user controller error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const activateTemporaryUser = async (req: Request, res: Response) => {
  try {
    const { qr_token } = req.params;
    const { device_id } = req.body;
    
    if (!qr_token || !device_id) {
      return res.status(400).json({
        success: false,
        message: 'QR token and device ID are required'
      });
    }
    
    const activatedUser = await temporaryUserService.activateTemporaryUser(qr_token, device_id);
    
    if (!activatedUser) {
      return res.status(400).json({
        success: false,
        message: 'Failed to activate temporary user. QR code may be invalid or expired.'
      });
    }
    
    return res.json({
      success: true,
      data: activatedUser
    });
  } catch (error) {
    console.error('Activate temporary user controller error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getTemporaryUserByDevice = async (req: Request, res: Response) => {
  try {
    const { device_id } = req.params;
    
    if (!device_id) {
      return res.status(400).json({
        success: false,
        message: 'Device ID is required'
      });
    }
    
    const temporaryUser = await temporaryUserService.getTemporaryUserByDeviceId(device_id);
    
    if (!temporaryUser) {
      return res.status(404).json({
        success: false,
        message: 'Temporary user not found for this device'
      });
    }
    
    return res.json({
      success: true,
      data: temporaryUser
    });
  } catch (error) {
    console.error('Get temporary user controller error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const convertToPermamentUser = async (req: Request, res: Response) => {
  try {
    const { temporary_user_id } = req.params;
    const { email, password, full_name } = req.body;
    
    if (!temporary_user_id || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Temporary user ID, email, and password are required'
      });
    }
    
    const success = await temporaryUserService.convertToPermamentUser(
      temporary_user_id,
      { email, password, full_name }
    );
    
    if (!success) {
      return res.status(400).json({
        success: false,
        message: 'Failed to convert temporary user to permanent user'
      });
    }
    
    return res.json({
      success: true,
      message: 'Temporary user converted to permanent user successfully'
    });
  } catch (error) {
    console.error('Convert to permanent user controller error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
