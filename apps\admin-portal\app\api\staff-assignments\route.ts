import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Lấy danh sách phân công nhân viên
export async function GET(request: NextRequest) {
  try {
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }

    // Lấy các query parameters
    const searchParams = request.nextUrl.searchParams;
    const department = searchParams.get('department') || '';
    const user_id = searchParams.get('user_id') || '';
    const is_active = searchParams.get('is_active') === 'true' ? true : 
                     searchParams.get('is_active') === 'false' ? false : undefined;
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    const offset = (page - 1) * limit;

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Truy vấn danh sách phân công nhân viên
    let query = supabase
      .from('tenant_staff_assignments')
      .select(`
        *,
        tenant_users!inner (
          id,
          role,
          tenant_users_details (
            email,
            display_name,
            avatar_url
          )
        )
      `, { count: 'exact' })
      .eq('tenant_id', tenant_id)
      .order('priority', { ascending: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Áp dụng các bộ lọc
    if (department) query = query.eq('department', department);
    if (user_id) query = query.eq('user_id', user_id);
    if (is_active !== undefined) query = query.eq('is_active', is_active);

    // Thực hiện truy vấn
    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching staff assignments:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Tái cấu trúc dữ liệu để dễ sử dụng bên phía frontend
    const formattedData = data?.map(item => ({
      id: item.id,
      user_id: item.user_id,
      user: {
        id: item.tenant_users.id,
        role: item.tenant_users.role,
        email: item.tenant_users.tenant_users_details?.email,
        display_name: item.tenant_users.tenant_users_details?.display_name,
        avatar_url: item.tenant_users.tenant_users_details?.avatar_url,
      },
      department: item.department,
      assignment_type: item.assignment_type,
      resource_id: item.resource_id,
      working_hours: item.working_hours,
      priority: item.priority,
      is_active: item.is_active,
      created_at: item.created_at,
      updated_at: item.updated_at
    }));

    // Trả về kết quả
    return NextResponse.json({
      data: formattedData,
      meta: {
        total: count || 0,
        page,
        limit,
        pageCount: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error: any) {
    console.error('Error in GET staff assignments:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 });
  }
}

// POST: Tạo phân công nhân viên mới
export async function POST(request: NextRequest) {
  try {
    // Lấy dữ liệu từ request body
    const {
      user_id,
      department,
      assignment_type,
      resource_id,
      working_hours,
      priority,
      is_active
    } = await request.json();

    // Kiểm tra dữ liệu bắt buộc
    if (!user_id) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }
    if (!department) {
      return NextResponse.json({ error: 'Department is required' }, { status: 400 });
    }

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra user_id có tồn tại trong tenant không
    const { data: existingUser, error: userCheckError } = await supabase
      .from('tenant_users')
      .select('id')
      .eq('id', user_id)
      .eq('tenant_id', tenant_id)
      .single();

    if (userCheckError || !existingUser) {
      return NextResponse.json({ error: 'User not found in tenant' }, { status: 404 });
    }

    // Kiểm tra xem đã có phân công cho user và department này chưa
    const { data: existingAssignment, error: assignmentCheckError } = await supabase
      .from('tenant_staff_assignments')
      .select('id')
      .eq('tenant_id', tenant_id)
      .eq('user_id', user_id)
      .eq('department', department)
      .maybeSingle();

    // Nếu đã có, trả về lỗi
    if (existingAssignment) {
      return NextResponse.json({ 
        error: 'User already assigned to this department' 
      }, { status: 409 });
    }

    // Tạo phân công mới
    const newAssignment = {
      tenant_id,
      user_id,
      department,
      assignment_type: assignment_type || 'general',
      resource_id: resource_id || null,
      working_hours: working_hours || null,
      priority: priority !== undefined ? priority : 1,
      is_active: is_active !== undefined ? is_active : true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Lưu vào database
    const { data, error } = await supabase
      .from('tenant_staff_assignments')
      .insert(newAssignment)
      .select()
      .single();

    if (error) {
      console.error('Error creating staff assignment:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Lấy thông tin user để trả về
    const { data: userData, error: userError } = await supabase
      .from('tenant_users')
      .select(`
        id,
        role,
        tenant_users_details (
          email,
          display_name,
          avatar_url
        )
      `)
      .eq('id', user_id)
      .single();

    if (userError) {
      console.error('Error fetching user details:', userError);
    }

    // Trả về kết quả
    return NextResponse.json({
      data: {
        ...data,
        user: userData || undefined
      },
      message: 'Staff assignment created successfully'
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error in POST staff assignment:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 });
  }
}