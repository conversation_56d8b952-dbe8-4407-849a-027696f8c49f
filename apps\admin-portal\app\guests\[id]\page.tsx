'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Alert } from '@ui';
import styles from './guestDetail.module.scss';
import RoomSelectionModal from '../../components/modals/RoomSelectionModal';

interface Guest {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  room_number: string;
  check_in: string;
  check_out: string | null;
  is_active: boolean;
  tenant_id: string;
  tenant_rooms?: {
    room_type: string;
    floor: string;
  }
}

export default function GuestDetailPage({ params }: { params: { id: string } }) {
  const [guest, setGuest] = useState<Guest | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isCheckInModalOpen, setIsCheckInModalOpen] = useState(false);
  const [isReactivateModalOpen, setIsReactivateModalOpen] = useState(false);

  useEffect(() => {
    fetchGuestDetails();
  }, []);

  const fetchGuestDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/guests/${params.id}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch guest details');
      }

      setGuest(result.data);
    } catch (err) {
      console.error('Error fetching guest details:', err);
      setError('Không thể tải thông tin khách hàng. Vui lòng thử lại sau.');
    } finally {
      setLoading(false);
    }
  };

  // Format date to display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

 // Handle check-in
const handleCheckIn = () => {
  setIsCheckInModalOpen(true);
};

const handleCheckInConfirm = async (roomNumber: string) => {
  try {
    const response = await fetch(`/api/guests/${params.id}/checkin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ room_number: roomNumber })
    });
    
    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to check-in guest');
    }
    
    // Refresh guest details
    fetchGuestDetails();
    alert('Check-in successful!');
  } catch (err) {
    console.error('Error checking in guest:', err);
    alert('Could not check-in guest. Please try again.');
  }
};

// Handle reactivate (undo check-out)
const handleReactivate = () => {
  setIsReactivateModalOpen(true);
};

const handleReactivateConfirm = async (roomNumber: string) => {
  try {
    const response = await fetch(`/api/guests/${params.id}/reactivate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ room_number: roomNumber })
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to reactivate guest');
    }

    // Refresh guest details
    fetchGuestDetails();
    alert('Kích hoạt lại khách thành công!');
  } catch (err) {
    console.error('Error reactivating guest:', err);
    alert('Không thể kích hoạt lại khách. Vui lòng thử lại.');
  }
};

// Handle delete
const handleDelete = async () => {
  if (!confirm('Bạn có chắc chắn muốn xóa khách hàng này? Hành động này không thể hoàn tác.')) return;
  
  try {
    const response = await fetch(`/api/guests/${params.id}`, {
      method: 'DELETE',
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to delete guest');
    }

    // Chuyển hướng về trang danh sách khách
    router.push('/guests');
  } catch (err) {
    console.error('Error deleting guest:', err);
    alert('Không thể xóa khách. Vui lòng thử lại.');
  }
};
  // Handle check-out
  const handleCheckOut = async () => {
    if (!confirm('Bạn có chắc muốn check-out khách này?')) return;
    
    try {
      const response = await fetch(`/api/guests/${params.id}/checkout`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to check-out guest');
      }

      // Refresh guest details
      fetchGuestDetails();
      alert('Check-out thành công!');
    } catch (err) {
      console.error('Error checking out guest:', err);
      alert('Không thể check-out khách. Vui lòng thử lại.');
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Đang tải thông tin khách hàng...</p>
      </div>
    );
  }

  if (error || !guest) {
    return (
      <div className={styles.container}>
        <div className={styles.pageHeader}>
          <div className={styles.titleSection}>
            <Link href="/guests" className={styles.backLink}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15.8333 10H4.16666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M8.33333 5.83331L4.16666 9.99998L8.33333 14.1666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Quay lại danh sách khách
            </Link>
            <h1 className={styles.pageTitle}>Chi tiết khách</h1>
          </div>
        </div>
        
        <Alert 
          variant="error" 
          title="Lỗi" 
          closable={false}
        >
          {error || 'Không tìm thấy thông tin khách hàng'}
        </Alert>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.pageHeader}>
        <div className={styles.titleSection}>
          <Link href="/guests" className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.33333 5.83331L4.16666 9.99998L8.33333 14.1666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Quay lại danh sách khách
          </Link>
          <h1 className={styles.pageTitle}>{guest.full_name}</h1>
        </div>
        
        <div className={styles.actions}>
          <Link href={`/guests/${guest.id}/edit`} className={styles.primaryButton}>
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
              <path d="M10.5 6L12 7.5M2.25 15.75L5.4 15.12C5.64 15.07 5.85 14.95 6.02 14.78L15.75 5.05C16.08 4.72 16.08 4.18 15.75 3.85L14.15 2.25C13.82 1.92 13.28 1.92 12.95 2.25L3.22 11.98C3.05 12.15 2.93 12.36 2.88 12.6L2.25 15.75Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Chỉnh sửa
          </Link>
          
          {guest.is_active && !guest.room_number && (
            <button className={styles.secondaryButton} onClick={handleCheckIn}>
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path d="M9 1.5L11.3175 6.195L16.5 6.9525L12.75 10.605L13.635 15.765L9 13.3275L4.365 15.765L5.25 10.605L1.5 6.9525L6.6825 6.195L9 1.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Check-in
            </button>
          )}
          
          {guest.is_active && guest.room_number && (
            <button className={styles.warningButton} onClick={handleCheckOut}>
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path d="M6.75 15.75H3.75C3.35218 15.75 2.97064 15.592 2.68934 15.3107C2.40804 15.0294 2.25 14.6478 2.25 14.25V3.75C2.25 3.35218 2.40804 2.97064 2.68934 2.68934C2.97064 2.40804 3.35218 2.25 3.75 2.25H6.75M12.75 12.75L15.75 9.75M15.75 9.75L12.75 6.75M15.75 9.75H6.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Check-out
            </button>
          )}
        </div>
      </div>
      
      <div className={styles.guestDetailLayout}>
        <div className={styles.mainInfo}>
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <h2 className={styles.cardTitle}>Thông tin khách hàng</h2>
              <span className={`${styles.statusBadge} ${guest.is_active ? styles.active : styles.inactive}`}>
                {guest.is_active ? 'Đang lưu trú' : 'Đã check-out'}
              </span>
            </div>
            
            <div className={styles.cardBody}>
              <div className={styles.infoGrid}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Họ và tên:</span>
                  <span className={styles.infoValue}>{guest.full_name}</span>
                </div>
                
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Email:</span>
                  <span className={styles.infoValue}>{guest.email || '—'}</span>
                </div>
                
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Số điện thoại:</span>
                  <span className={styles.infoValue}>{guest.phone || '—'}</span>
                </div>
                
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Số phòng:</span>
                  <span className={styles.infoValue}>{guest.room_number || '—'}</span>
                </div>
                
                {guest.tenant_rooms && guest.room_number && (
                  <>
                    <div className={styles.infoItem}>
                      <span className={styles.infoLabel}>Loại phòng:</span>
                      <span className={styles.infoValue}>{guest.tenant_rooms.room_type || '—'}</span>
                    </div>
                    
                    <div className={styles.infoItem}>
                      <span className={styles.infoLabel}>Tầng:</span>
                      <span className={styles.infoValue}>{guest.tenant_rooms.floor || '—'}</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
          
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <h2 className={styles.cardTitle}>Thông tin lưu trú</h2>
            </div>
            
            <div className={styles.cardBody}>
              <div className={styles.infoGrid}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Thời gian check-in:</span>
                  <span className={styles.infoValue}>{formatDate(guest.check_in)}</span>
                </div>
                
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Thời gian check-out:</span>
                  <span className={styles.infoValue}>
                    {guest.check_out ? formatDate(guest.check_out) : guest.is_active ? 'Chưa check-out' : '—'}
                  </span>
                </div>
                
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>Trạng thái:</span>
                  <span className={styles.infoValue}>
                    {guest.is_active ? 'Đang lưu trú' : 'Đã check-out'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className={styles.sideInfo}>
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <h2 className={styles.cardTitle}>Hoạt động gần đây</h2>
            </div>
            
            <div className={styles.cardBody}>
              <div className={styles.activityList}>
                {guest.check_in && (
                  <div className={styles.activityItem}>
                    <div className={styles.activityIcon}>
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 1.33333C4.32 1.33333 1.33334 4.32 1.33334 8C1.33334 11.68 4.32 14.6667 8 14.6667C11.68 14.6667 14.6667 11.68 14.6667 8C14.6667 4.32 11.68 1.33333 8 1.33333ZM8 13.3333C5.06 13.3333 2.66667 10.94 2.66667 8C2.66667 5.06 5.06 2.66666 8 2.66666C10.94 2.66666 13.3333 5.06 13.3333 8C13.3333 10.94 10.94 13.3333 8 13.3333Z" fill="currentColor"/>
                        <path d="M8.33334 4.66666H7.33334V8.33333L10.5 10.3333L11 9.5L8.33334 7.83333V4.66666Z" fill="currentColor"/>
                      </svg>
                    </div>
                    <div className={styles.activityContent}>
                      <span className={styles.activityText}>Check-in vào hệ thống</span>
                      <span className={styles.activityTime}>{formatDate(guest.check_in)}</span>
                    </div>
                  </div>
                )}
                
                {guest.room_number && (
                  <div className={styles.activityItem}>
                    <div className={styles.activityIcon}>
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 0L10.3175 4.695L15.5 5.4525L11.75 9.105L12.635 14.265L8 11.8275L3.365 14.265L4.25 9.105L0.5 5.4525L5.6825 4.695L8 0Z" fill="currentColor"/>
                      </svg>
                    </div>
                    <div className={styles.activityContent}>
                      <span className={styles.activityText}>Đã nhận phòng {guest.room_number}</span>
                      <span className={styles.activityTime}>{formatDate(guest.check_in)}</span>
                    </div>
                  </div>
                )}
                
                {guest.check_out && (
                  <div className={styles.activityItem}>
                    <div className={styles.activityIcon}>
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M6 14H3.33333C2.97971 14 2.64057 13.8595 2.39052 13.6095C2.14048 13.3594 2 13.0203 2 12.6667V3.33333C2 2.97971 2.14048 2.64057 2.39052 2.39052C2.64057 2.14048 2.97971 2 3.33333 2H6M10.6667 11.3333L14 8M14 8L10.6667 4.66667M14 8H6" fill="currentColor"/>
                      </svg>
                    </div>
                    <div className={styles.activityContent}>
                      <span className={styles.activityText}>Đã trả phòng</span>
                      <span className={styles.activityTime}>{formatDate(guest.check_out)}</span>
                    </div>
                  </div>
                )}
                
                {!guest.check_in && !guest.room_number && !guest.check_out && (
                  <div className={styles.emptyState}>
                    <p>Chưa có hoạt động nào</p>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <h2 className={styles.cardTitle}>Liên kết nhanh</h2>
            </div>
            
            <div className={styles.cardBody}>
              <div className={styles.quickLinks}>
                <Link href={`/guests/${guest.id}/edit`} className={styles.quickLink}>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M9.33333 5.33333L10.6667 6.66667M2 14L4.8 13.44C5.01333 13.3933 5.2 13.2867 5.35333 13.1333L14 4.47333C14.2929 4.18044 14.2929 3.70711 14 3.41421L12.5858 2C12.2929 1.70711 11.8196 1.70711 11.5267 2L2.86667 10.6467C2.71333 10.8 2.60667 11 2.56 11.2L2 14Z" stroke="currentColor" strokeWidth="1.33" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  Chỉnh sửa thông tin
                </Link>
                
                {guest.is_active && !guest.room_number && (
                  <button onClick={handleCheckIn} className={styles.quickLink}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M8 1.33333L10.3175 5.52833L15.5 6.28583L11.75 9.76083L12.635 14.9292L8 12.47L3.365 14.9292L4.25 9.76083L0.5 6.28583L5.6825 5.52833L8 1.33333Z" stroke="currentColor" strokeWidth="1.33" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    Check-in
                  </button>
                )}
                
                {guest.is_active && guest.room_number && (
                  <button onClick={handleCheckOut} className={styles.quickLink}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M6 14H3.33333C2.97971 14 2.64057 13.8595 2.39052 13.6095C2.14048 13.3594 2 13.0203 2 12.6667V3.33333C2 2.97971 2.14048 2.64057 2.39052 2.39052C2.64057 2.14048 2.97971 2 3.33333 2H6M10.6667 11.3333L14 8M14 8L10.6667 4.66667M14 8H6" stroke="currentColor" strokeWidth="1.33" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    Check-out
                  </button>
                )}
                
                <Link href="/guests" className={styles.quickLink}>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M14 8H2M2 8L6 4M2 8L6 12" stroke="currentColor" strokeWidth="1.33" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  Quay lại danh sách
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
	   {/* Modals */}
<RoomSelectionModal 
  isOpen={isCheckInModalOpen} 
  onClose={() => setIsCheckInModalOpen(false)} 
  onConfirm={handleCheckInConfirm} 
  title="Select Room for Check-in"
  showUnavailableRooms={false}
/>

<RoomSelectionModal 
  isOpen={isReactivateModalOpen} 
  onClose={() => setIsReactivateModalOpen(false)} 
  onConfirm={handleReactivateConfirm} 
  title="Select Room to Reactivate Guest" 
  showUnavailableRooms={false}
/>
    </div>
  );
}
