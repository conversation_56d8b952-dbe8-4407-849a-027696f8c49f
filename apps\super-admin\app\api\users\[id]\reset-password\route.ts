import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '../../../../utils/supabase/server';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id;
    const supabase = await createClient();
    
    // Gửi email reset password
    const { error } = await supabase.auth.admin.generateLink({
      type: 'recovery',
      email: '', // Cần email để gửi link reset
      options: {
        userId
      }
    });

    if (error) {
      console.error('Error triggering password reset:', error);
      return NextResponse.json(
        { error: error.message || 'Failed to reset password' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true, message: 'Password reset link sent' });
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to reset password' },
      { status: 500 }
    );
  }
}
