import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../lib/supabase/admin';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// Tạo Supabase admin client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// POST: Xử lý khi quét QR code
export async function POST(request: NextRequest) {
  try {
    // Lấy dữ liệu từ request
    const requestData = await request.json();
    const { 
      qr_code_id,          // ID của mã QR được quét
      session_id,          // ID phiên của khách vãng lai (nếu có)
      guest_id,            // ID của khách lưu trú (nếu có)
      device_info,         // Thông tin thiết bị
      language,            // Ngôn ngữ của người dùng
      location,            // Vị trí người dùng
      metadata             // Dữ liệu bổ sung
    } = requestData;
    
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }
    
    // Tạo Supabase client
    const supabase = createSupabaseClient();
    
    // Lấy thông tin về QR code được quét
    const { data: qrCodeData, error: qrCodeError } = await supabase
      .from('tenant_qr_codes')
      .select(`
        *,
        tenant_qr_code_types (
          id, 
          name, 
          default_action,
          icon_url
        )
      `)
      .eq('id', qr_code_id)
      .eq('tenant_id', tenant_id)
      .single();
    
    if (qrCodeError) {
      console.error('Error fetching QR code:', qrCodeError);
      return NextResponse.json({ 
        error: 'QR code not found or access denied.' 
      }, { status: 404 });
    }
    
    // Kiểm tra QR code có hoạt động không
    if (!qrCodeData.is_active) {
      return NextResponse.json({ 
        error: 'This QR code is inactive.' 
      }, { status: 403 });
    }
    
    // Lấy thông tin request để ghi log
    const ip_address = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const user_agent = request.headers.get('user-agent') || 'unknown';
    
    // Ghi lại lượt quét QR code
    const { data: scanData, error: scanError } = await supabase
      .from('tenant_qr_code_scans')
      .insert({
        tenant_id,
        qr_code_id,
        guest_id,
        session_id,
        device_info,
        ip_address,
        user_agent,
        location,
        language,
        scan_time: new Date().toISOString(),
        action_taken: null, // Sẽ cập nhật sau khi xử lý
        metadata
      })
      .select()
      .single();
    
    if (scanError) {
      console.error('Error recording QR scan:', scanError);
      // Tiếp tục xử lý ngay cả khi không ghi được log
    }
    
    let guestData = null;
    let temporaryUser = null;
    
    // Kiểm tra và xử lý thông tin khách
    if (guest_id) {
      // Nếu có guest_id, đây là khách lưu trú
      const { data: guest, error: guestError } = await supabase
        .from('tenant_guests')
        .select('*')
        .eq('id', guest_id)
        .eq('tenant_id', tenant_id)
        .single();
      
      if (guestError) {
        console.error('Error fetching guest:', guestError);
      } else {
        guestData = guest;
      }
    } else if (session_id) {
      // Nếu có session_id, đây là khách vãng lai
      const { data: tempUser, error: tempUserError } = await supabase
        .from('temporary_users')
        .select('*')
        .eq('session_id', session_id)
        .eq('tenant_id', tenant_id)
        .maybeSingle();
      
      if (tempUserError) {
        console.error('Error fetching temporary user:', tempUserError);
      } else if (tempUser) {
        temporaryUser = tempUser;
      } else {
        // Tạo thông tin tạm thời cho khách vãng lai mới
        const { data: newTempUser, error: createTempUserError } = await supabase
          .from('temporary_users')
          .insert({
            tenant_id,
            session_id,
            language,
            device_fingerprint: device_info?.fingerprint || null,
            first_interaction_at: new Date().toISOString(),
            last_interaction_at: new Date().toISOString()
          })
          .select()
          .single();
        
        if (createTempUserError) {
          console.error('Error creating temporary user:', createTempUserError);
        } else {
          temporaryUser = newTempUser;
        }
      }
    } else {
      return NextResponse.json({ 
        error: 'Either guest_id or session_id must be provided' 
      }, { status: 400 });
    }
    
    // Xác định hành động dựa trên loại QR và cấu hình
    let action = qrCodeData.tenant_qr_code_types?.default_action || 'info';
    if (qrCodeData.custom_action && Object.keys(qrCodeData.custom_action).length > 0) {
      action = qrCodeData.custom_action.action || action;
    }
    
    // Cập nhật action đã thực hiện trong bản ghi quét
    if (scanData) {
      await supabase
        .from('tenant_qr_code_scans')
        .update({ action_taken: action })
        .eq('id', scanData.id);
    }
    
    // Xử lý theo hành động
    switch(action) {
      case 'chat': {
        // Bắt đầu hoặc tiếp tục phiên chat
        let chatSessionId = null;
        
        // Xác định bộ phận nhận tin nhắn
        const target_department = qrCodeData.target_department;
        
        // Kiểm tra xem có phiên chat nào đang mở với bộ phận này
        const { data: existingChats, error: chatCheckError } = await supabase
          .from('tenant_chat_sessions')
          .select('id')
          .eq('tenant_id', tenant_id)
          .eq(guest_id ? 'guest_id' : 'session_id', guest_id || session_id)
          .eq('status', 'active')
          .eq('department', target_department)
          .maybeSingle();
        
        if (chatCheckError) {
          console.error('Error checking existing chats:', chatCheckError);
        }
        
        if (existingChats) {
          // Tiếp tục phiên chat hiện có
          chatSessionId = existingChats.id;
        } else {
          // Áp dụng quy tắc định tuyến chat
          const { data: routingRules, error: routingError } = await supabase
            .from('tenant_chat_routing_rules')
            .select('*')
            .eq('tenant_id', tenant_id)
            .eq('rule_type', 'qr_code')
            .eq('is_active', true)
            .order('priority', { ascending: false });
          
          if (routingError) {
            console.error('Error fetching routing rules:', routingError);
          }
          
          // Mặc định sử dụng target_department từ QR code
          let targetDepartment = target_department || 'reception';
          let targetUserId = null;
          
          // Áp dụng quy tắc định tuyến nếu có
          if (routingRules && routingRules.length > 0) {
            // Tìm quy tắc phù hợp
            const matchedRule = routingRules.find(rule => {
              const condition = rule.rule_condition;
              
              if (condition.qr_type_id && condition.qr_type_id === qrCodeData.qr_type_id) {
                return true;
              }
              
              if (condition.qr_code_id && condition.qr_code_id === qr_code_id) {
                return true;
              }
              
              return false;
            });
            
            if (matchedRule) {
              targetDepartment = matchedRule.target_department || targetDepartment;
              targetUserId = matchedRule.target_user_id || null;
            }
          }
          
          // Tạo phiên chat mới
          const chatSession = {
            tenant_id,
            guest_id: guestData?.id || null,
            session_id: !guestData ? session_id : null,
            status: 'waiting',
            source_type: 'qr_code',
            source_id: qr_code_id,
            department: targetDepartment,
            source_qr_code_id: qr_code_id,
            guest_language: language || 'en',
            auto_translate: true,
            started_at: new Date().toISOString()
          };
          
          const { data: newChat, error: chatCreateError } = await supabase
            .from('tenant_chat_sessions')
            .insert(chatSession)
            .select()
            .single();
          
          if (chatCreateError) {
            console.error('Error creating chat session:', chatCreateError);
            return NextResponse.json({ 
              error: 'Failed to create chat session.' 
            }, { status: 500 });
          }
          
          chatSessionId = newChat.id;
          
          // Tìm nhân viên phù hợp để gán phiên chat
          if (!targetUserId) {
            // Tìm nhân viên đang làm việc thuộc bộ phận đích
            const { data: availableStaff, error: staffError } = await supabase
              .from('tenant_staff_assignments')
              .select(`
                user_id,
                priority,
                tenant_users!inner (
                  id,
                  is_active
                )
              `)
              .eq('tenant_id', tenant_id)
              .eq('department', targetDepartment)
              .eq('is_active', true)
              .eq('tenant_users.is_active', true)
              .order('priority', { ascending: false });
            
            if (staffError) {
              console.error('Error finding available staff:', staffError);
            } else if (availableStaff && availableStaff.length > 0) {
              // Chọn nhân viên có mức ưu tiên cao nhất
              targetUserId = availableStaff[0].user_id;
            }
          }
          
          // Gán phiên chat cho nhân viên nếu tìm được
          if (targetUserId) {
            const { error: assignError } = await supabase
              .from('tenant_chat_session_assignments')
              .insert({
                tenant_id,
                chat_session_id: newChat.id,
                assigned_user_id: targetUserId,
                assignment_status: 'assigned',
                assigned_at: new Date().toISOString()
              });
            
            if (assignError) {
              console.error('Error assigning chat session:', assignError);
            }
          }
        }
        
        // Trả về thông tin để điều hướng đến phiên chat
        return NextResponse.json({
          success: true,
          action: 'chat',
          chat_session_id: chatSessionId,
          guest_type: guestData ? 'registered' : 'temporary',
          qr_code: {
            id: qrCodeData.id,
            name: qrCodeData.name,
            type: qrCodeData.tenant_qr_code_types?.name || 'Unknown',
            icon_url: qrCodeData.tenant_qr_code_types?.icon_url
          }
        });
      }
      
      case 'info': {
        // Trả về thông tin về khu vực/phòng liên kết với QR code
        let resourceInfo = null;
        
        if (qrCodeData.room_id) {
          const { data: roomData, error: roomError } = await supabase
            .from('tenant_rooms')
            .select('*')
            .eq('id', qrCodeData.room_id)
            .single();
          
          if (!roomError) {
            resourceInfo = {
              type: 'room',
              data: roomData
            };
          }
        } else if (qrCodeData.area_id) {
          const { data: areaData, error: areaError } = await supabase
            .from('tenant_areas')
            .select('*')
            .eq('id', qrCodeData.area_id)
            .single();
          
          if (!areaError) {
            resourceInfo = {
              type: 'area',
              data: areaData
            };
          }
        }
        
        return NextResponse.json({
          success: true,
          action: 'info',
          resource: resourceInfo,
          qr_code: {
            id: qrCodeData.id,
            name: qrCodeData.name,
            type: qrCodeData.tenant_qr_code_types?.name || 'Unknown',
            icon_url: qrCodeData.tenant_qr_code_types?.icon_url
          },
          custom_content: qrCodeData.custom_action?.content
        });
      }
      
      case 'service': {
        // Xử lý yêu cầu dịch vụ
        const serviceType = qrCodeData.custom_action?.service_type || 'general';
        
        return NextResponse.json({
          success: true,
          action: 'service',
          service_type: serviceType,
          qr_code: {
            id: qrCodeData.id,
            name: qrCodeData.name,
            type: qrCodeData.tenant_qr_code_types?.name || 'Unknown'
          }
        });
      }
      
      case 'feedback': {
        // Xử lý phiếu đánh giá
        return NextResponse.json({
          success: true,
          action: 'feedback',
          feedback_template: qrCodeData.custom_action?.feedback_template,
          qr_code: {
            id: qrCodeData.id,
            name: qrCodeData.name,
            type: qrCodeData.tenant_qr_code_types?.name || 'Unknown'
          }
        });
      }
      
      default:
        return NextResponse.json({
          success: true,
          action: 'unknown',
          qr_code: {
            id: qrCodeData.id,
            name: qrCodeData.name
          }
        });
    }
  } catch (error: any) {
    console.error('Error processing QR code scan:', error);
    return NextResponse.json({ 
      error: 'Failed to process QR code scan', 
      details: error.message 
    }, { status: 500 });
  }
}
