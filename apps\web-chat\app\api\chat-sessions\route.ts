import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabase } from '@/lib/supabase'
import { chatRoutingService } from '@/lib/services/chatRouting'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      temporary_user_id, 
      guest_language = 'en', 
      source_type = 'qr_code', 
      source_qr_code_id, // This is the QR code VALUE, not ID
      reception_point_id // Optional manual override
    } = body

    console.log('📝 Creating chat session with:', {
      temporary_user_id,
      guest_language,
      source_type,
      source_qr_code_id,
      reception_point_id
    })

    if (!temporary_user_id) {
      return NextResponse.json(
        { error: 'temporary_user_id is required' },
        { status: 400 }
      )
    }

    const supabase = createServerSupabase()

    // Get temporary user to extract tenant_id
    const { data: tempUser, error: tempUserError } = await supabase
      .from('temporary_users')
      .select('hotel_id, metadata, qr_code_id')
      .eq('id', temporary_user_id)
      .single()

    if (tempUserError || !tempUser) {
      console.error('❌ Invalid temporary user:', tempUserError)
      return NextResponse.json(
        { error: 'Invalid temporary user' },
        { status: 404 }
      )
    }

    console.log('✅ Temporary user found:', {
      hotel_id: tempUser.hotel_id,
      qr_code_id: tempUser.qr_code_id
    })

    // Get the actual QR code data including UUID and routing info
    let qrCodeData = null
    let qrCodeUUID = null

    if (source_qr_code_id) {
      const { data: qrCode, error: qrError } = await supabase
        .from('tenant_qr_codes')
        .select(`
          id,
          code_value,
          tenant_id,
          target_type,
          room_number,
          target_id,
          reception_point_id,
          location,
          description
        `)
        .eq('tenant_id', tempUser.hotel_id)
        .eq('code_value', source_qr_code_id)
        .single()

      if (qrCode && !qrError) {
        qrCodeData = qrCode
        qrCodeUUID = qrCode.id
        console.log('✅ QR Code data found:', {
          id: qrCode.id,
          code_value: qrCode.code_value,
          target_type: qrCode.target_type,
          reception_point_id: qrCode.reception_point_id
        })
      } else {
        console.warn('⚠️ Could not find QR code data for value:', source_qr_code_id)
      }
    }

    // Step 1: Determine reception point using routing logic
    let finalReceptionPointId = reception_point_id // Manual override takes precedence
    let routingInfo = null

    if (!finalReceptionPointId && qrCodeData) {
      try {
        console.log('🎯 Starting chat routing process...')
        
        const routingContext = {
          qr_code: qrCodeData,
          guest_language,
          source_type,
          tenant_id: tempUser.hotel_id,
          current_time: new Date()
        }

        const routingResult = await chatRoutingService.routeNewChatSession(routingContext)
        
        if (routingResult.reception_point_id) {
          finalReceptionPointId = routingResult.reception_point_id
          routingInfo = {
            routing_rule_used: routingResult.routing_rule_used,
            assigned_user_id: routingResult.assigned_user_id,
            department: routingResult.department,
            priority: routingResult.priority
          }
          console.log('✅ Chat routing completed:', routingInfo)
        }
      } catch (routingError) {
        console.error('⚠️ Chat routing failed, proceeding without routing:', routingError)
      }
    }

    // Step 2: Create chat session with proper data
    const sessionData = {
      tenant_id: tempUser.hotel_id,
      guest_language,
      status: 'active',
      source_type,
      source_qr_code_id: qrCodeUUID, // Use UUID instead of string value
      reception_point_id: finalReceptionPointId || null,
      auto_translate: true,
      priority: routingInfo?.priority || 'normal'
    }

    console.log('📋 Session data to insert:', sessionData)

    const { data: session, error: sessionError } = await supabase
      .from('tenant_chat_sessions')
      .insert(sessionData)
      .select()
      .single()

    if (sessionError) {
      console.error('❌ Error creating chat session:', sessionError)
      return NextResponse.json(
        { error: 'Failed to create chat session: ' + sessionError.message },
        { status: 500 }
      )
    }

    console.log('✅ Chat session created successfully:', {
      id: session.id,
      reception_point_id: session.reception_point_id,
      priority: session.priority
    })

    // Step 3: If routing assigned a specific user, create assignment
    if (routingInfo?.assigned_user_id) {
      try {
        const assignmentData = {
          tenant_id: tempUser.hotel_id,
          chat_session_id: session.id,
          assigned_user_id: routingInfo.assigned_user_id,
          assignment_status: 'assigned',
          assigned_at: new Date().toISOString()
        }

        const { error: assignmentError } = await supabase
          .from('tenant_chat_session_assignments')
          .insert(assignmentData)

        if (assignmentError) {
          console.error('⚠️ Failed to create user assignment:', assignmentError)
        } else {
          console.log('✅ User assignment created for:', routingInfo.assigned_user_id)
        }
      } catch (assignmentError) {
        console.error('⚠️ Error creating user assignment:', assignmentError)
      }
    }

    // Step 4: Return session with routing info
    return NextResponse.json({
      success: true,
      session: {
        ...session,
        routing_info: routingInfo,
        qr_info: qrCodeData ? {
          code_value: qrCodeData.code_value,
          location: qrCodeData.location,
          room_number: qrCodeData.room_number,
          description: qrCodeData.description
        } : null
      }
    })

  } catch (error) {
    console.error('❌ Error in chat sessions API:', error)
    return NextResponse.json(
      { error: 'Internal server error: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const reception_point_id = searchParams.get('reception_point_id')
    const status = searchParams.get('status') || 'active'
    const tenant_id = searchParams.get('tenant_id')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    const supabase = createServerSupabase()

    let query = supabase
  .from('tenant_chat_sessions')
  .select(`
    *,
    tenant_qr_codes(
      code_value,
      location,
      room_number,
      description
    ),
    tenant_message_reception_points!tenant_chat_sessions_reception_point_id_fkey(
      name,
      code,
      description
    ),
    tenant_chat_session_assignments(
      assigned_user_id,
      assignment_status,
      assigned_at,
      tenant_users(
        tenant_users_details(
          display_name,
          email
        )
      )
    )
  `)
  .eq('status', status)
  .order('created_at', { ascending: false })
  .range(offset, offset + limit - 1)

    if (reception_point_id) {
      query = query.eq('reception_point_id', reception_point_id)
    }

    if (tenant_id) {
      query = query.eq('tenant_id', tenant_id)
    }

    const { data: sessions, error } = await query

    if (error) {
      console.error('❌ Error fetching chat sessions:', error)
      return NextResponse.json(
        { error: 'Failed to fetch chat sessions' },
        { status: 500 }
      )
    }

    // Format the response data
    const formattedSessions = sessions?.map(session => ({
      ...session,
      qr_info: session.tenant_qr_codes ? {
        code_value: session.tenant_qr_codes.code_value,
        location: session.tenant_qr_codes.location,
        room_number: session.tenant_qr_codes.room_number,
        description: session.tenant_qr_codes.description
      } : null,
      reception_point: session.tenant_message_reception_points ? {
        name: session.tenant_message_reception_points.name,
        code: session.tenant_message_reception_points.code,
        description: session.tenant_message_reception_points.description
      } : null,
      assignment: session.tenant_chat_session_assignments?.[0] ? {
        assigned_user_id: session.tenant_chat_session_assignments[0].assigned_user_id,
        assignment_status: session.tenant_chat_session_assignments[0].assignment_status,
        assigned_at: session.tenant_chat_session_assignments[0].assigned_at,
        staff_name: session.tenant_chat_session_assignments[0].tenant_users?.tenant_users_details?.display_name
      } : null
    })) || []

    return NextResponse.json({
      success: true,
      sessions: formattedSessions,
      total: formattedSessions.length
    })

  } catch (error) {
    console.error('❌ Error in chat sessions GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
