// FIXED: Staff Dashboard with working Supabase Realtime
// Original backed up to dashboard.backup.tsx

'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import ChatMessage from './components/ChatMessage';
import ChatInput from './components/ChatInput';
import styles from './dashboard.module.scss';
import NotificationCenter from './components/notifications/NotificationCenter';
import StaffStatus from './components/StaffStatus';
import ChatSearch from './components/ChatSearch';
import ChatStats from './components/ChatStats';
import QuickActions from './components/QuickActions';
import { createClientSupabase } from '../../lib/supabase';

interface User {
  id: string;
  email: string;
  display_name: string;
  role: string;
  tenant_id: string;
  department?: string;
  title?: string;
  avatar_url?: string;
  reception_points?: any[];
}

interface ChatSession {
  id: string;
  guest_name: string;
  room_number?: string;
  language: string;
  status: 'active' | 'pending' | 'waiting';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  last_message: string;
  last_message_time: string;
  unread_count: number;
  source: string;
  session_ids?: string[];
}

interface Message {
  id: string;
  content: string;
  original_content?: string;
  translated_content?: string;
  sender_type: 'guest' | 'staff';
  sender_name: string;
  timestamp: string;
  is_translated: boolean;
  original_language?: string;
  translated_language?: string;
  translation_confidence?: number;
  show_translation: boolean;
}

export default function StaffDashboard() {
  // Core state
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeChatSessions, setActiveChatSessions] = useState<ChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [onlineStatus, setOnlineStatus] = useState<'online' | 'busy' | 'away'>('online');
  const [autoTranslate, setAutoTranslate] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [realtimeStatus, setRealtimeStatus] = useState<string>('Not connected');
  const [filteredChatSessions, setFilteredChatSessions] = useState<ChatSession[]>([]);

  // Chat filters
  const [chatFilters, setChatFilters] = useState({
    status: 'all' as 'all' | 'active' | 'pending' | 'waiting',
    priority: 'all' as 'all' | 'low' | 'normal' | 'high' | 'urgent',
    language: 'all' as 'all' | 'en' | 'vi' | 'ko' | 'ja' | 'es' | 'fr' | 'de' | 'th' | 'id',
    timeRange: 'all' as 'all' | 'today' | 'week' | 'month',
    unreadOnly: false
  });

  // Refs
  const router = useRouter();
  const supabaseRef = useRef<any>(null);
  const channelRef = useRef<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Utility functions
  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Initialize Supabase (exactly like debugger)
  const initializeSupabase = useCallback(() => {
    try {
      supabaseRef.current = createClientSupabase();
      console.log('✅ Fixed Dashboard: Supabase client initialized');
      console.log(`📡 URL: ${supabaseRef.current.supabaseUrl}`);
      console.log(`🔑 Key: ${supabaseRef.current.supabaseKey.substring(0, 20)}...`);
    } catch (err) {
      console.error(`❌ Fixed Dashboard: Failed to initialize Supabase: ${err}`);
    }
  }, []);

  // Setup realtime subscription (exactly like debugger)
  const setupRealtimeSubscription = useCallback(() => {
    if (!supabaseRef.current || !user) {
      console.log('❌ Fixed Dashboard: Supabase not initialized or no user');
      return;
    }

    if (channelRef.current) {
      console.log('🔄 Fixed Dashboard: Unsubscribing existing channel...');
      channelRef.current.unsubscribe();
    }

    console.log('🔄 Fixed Dashboard: Setting up realtime subscription...');
    console.log(`🎯 Filter: tenant_id=eq.${user.tenant_id}`);

    // Use EXACT same logic as working debugger
    channelRef.current = supabaseRef.current
      .channel('fixed-staff-messages')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'tenant_chat_messages',
          filter: `tenant_id=eq.${user.tenant_id}`
        },
        (payload: any) => {
          console.log(`🔔 Fixed Dashboard: REALTIME MESSAGE RECEIVED!`);
          console.log(`📄 Message ID: ${payload.new.id}`);
          console.log(`👤 Sender: ${payload.new.sender_type} (${payload.new.sender_name})`);
          console.log(`💬 Content: ${payload.new.content?.substring(0, 50)}...`);
          console.log(`🏢 Tenant: ${payload.new.tenant_id}`);
          console.log(`⏰ Time: ${payload.new.created_at}`);

          // Update messages if for selected session
          if (selectedSession && payload.new.chat_session_id === selectedSession) {
            const newMessage: Message = {
              id: payload.new.id,
              content: payload.new.content,
              sender_type: payload.new.sender_type,
              sender_name: payload.new.sender_name || (payload.new.sender_type === 'guest' ? 'Guest' : 'Staff'),
              timestamp: payload.new.created_at,
              is_translated: payload.new.is_translated || false,
              show_translation: false
            };

            setMessages(prev => {
              const exists = prev.some(msg => msg.id === newMessage.id);
              if (exists) return prev;
              console.log('➕ Fixed Dashboard: Adding new message to chat:', newMessage.id);
              return [...prev, newMessage];
            });

            setTimeout(scrollToBottom, 100);
          }

          // Update session list
          refreshSessionList();
        }
      )
      .subscribe((status: string) => {
        setRealtimeStatus(status);
        console.log(`📡 Fixed Dashboard: Subscription status: ${status}`);
        
        if (status === 'SUBSCRIBED') {
          console.log('✅ Fixed Dashboard: REALTIME SUBSCRIPTION ACTIVE!');
        } else if (status === 'CHANNEL_ERROR') {
          console.log('❌ Fixed Dashboard: REALTIME SUBSCRIPTION FAILED!');
        } else if (status === 'CLOSED') {
          console.log('⚠️ Fixed Dashboard: REALTIME SUBSCRIPTION CLOSED!');
        }
      });
  }, [user, selectedSession]);

  // Session list refresh
  const refreshSessionList = useCallback(async () => {
    if (!user) return;

    try {
      const response = await fetch(`/api/chat-sessions?tenant_id=${user.tenant_id}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.sessions)) {
          const transformedSessions: ChatSession[] = data.sessions.map((session: any) => ({
            id: session.id,
            guest_name: session.qr_info?.room_number ?
              `Room ${session.qr_info.room_number} Guest` :
              'Guest User',
            room_number: session.qr_info?.room_number || undefined,
            language: session.guest_language?.toUpperCase() || 'EN',
            status: session.status as 'active' | 'pending' | 'waiting',
            priority: session.priority as 'low' | 'normal' | 'high' | 'urgent',
            last_message: 'Loading...',
            last_message_time: formatTimeAgo(session.updated_at),
            unread_count: 0,
            source: session.qr_info?.location || session.reception_point?.name || 'Direct'
          }));

          setActiveChatSessions(transformedSessions);
          console.log(`✅ Fixed Dashboard: Session list refreshed (${transformedSessions.length} sessions)`);
        }
      }
    } catch (error) {
      console.error('❌ Fixed Dashboard: Error refreshing session list:', error);
    }
  }, [user]);

  // Send message
  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!selectedSession || !content.trim()) return false;

    try {
      console.log(`📤 Fixed Dashboard: Sending message: "${content}"`);
      
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: selectedSession,
          sender_type: 'staff',
          sender_name: user?.display_name || 'Staff',
          content: content.trim()
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Fixed Dashboard: Message sent successfully: ${data.message?.id}`);
        return true;
      }
      
      throw new Error('Failed to send message');
    } catch (err) {
      console.error('❌ Fixed Dashboard: Failed to send message:', err);
      return false;
    }
  }, [selectedSession, user]);

  // Load messages for selected session
  const loadMessages = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/messages?session_id=${sessionId}&limit=50`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.messages)) {
          const transformedMessages: Message[] = data.messages.map((msg: any) => ({
            id: msg.id,
            content: msg.content,
            sender_type: msg.sender_type,
            sender_name: msg.sender_name || (msg.sender_type === 'guest' ? 'Guest' : 'Staff'),
            timestamp: msg.created_at,
            is_translated: msg.is_translated || false,
            show_translation: false
          }));
          setMessages(transformedMessages);
          setTimeout(scrollToBottom, 100);
        }
      }
    } catch (err) {
      console.error('❌ Fixed Dashboard: Failed to load messages:', err);
    }
  }, []);

  // Handle session selection
  const handleSessionSelect = useCallback((sessionId: string) => {
    setSelectedSession(sessionId);
    loadMessages(sessionId);
    
    // Mark session as read
    setActiveChatSessions(prev => prev.map(session => 
      session.id === sessionId ? { ...session, unread_count: 0 } : session
    ));
  }, [loadMessages]);
