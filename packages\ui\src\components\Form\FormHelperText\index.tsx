import React from 'react';
import styles from './FormHelperText.module.scss';

export interface FormHelperTextProps {
  children: React.ReactNode;
  className?: string;
  error?: boolean;
}

export const FormHelperText: React.FC<FormHelperTextProps> = ({ 
  children, 
  className,
  error = false 
}) => {
  return (
    <p className={`${styles.formHelperText} ${error ? styles.error : ''} ${className || ''}`}>
      {children}
    </p>
  );
};

export default FormHelperText;
