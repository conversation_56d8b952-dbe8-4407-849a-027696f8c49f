// server.js
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
// Sử dụng node-fetch phiên bản 2
const fetch = require('node-fetch');
global.fetch = fetch;

const app = express();
const port = process.env.PORT || 3005;

// Middleware
app.use(cors({
  origin: '*',  // Cho phép tất cả các origin trong môi trường dev
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'apikey']
}));
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Tạo thư mục public nếu chưa có
const fs = require('fs');
if (!fs.existsSync(path.join(__dirname, 'public'))) {
  fs.mkdirSync(path.join(__dirname, 'public'));
}

// Đảm bảo biến môi trường được thiết lập
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const serviceRoleKey = process.env.SERVICE_ROLE_KEY;

// Function test kết nối Supabase
async function testSupabaseConnection(key = supabaseKey) {
  console.log(`Testing Supabase connection with ${key === serviceRoleKey ? 'service role key' : 'anon key'}...`);
  try {
    const supabase = createClient(supabaseUrl, key);
    const { data, error } = await supabase.from('users').select('id').limit(1);
    
    if (error) throw error;
    console.log('Connection successful!', data);
    return { success: true, data };
  } catch (error) {
    console.error('Connection failed:', error.message);
    return { success: false, error: error.message };
  }
}

// Route kiểm tra kết nối
app.get('/api/test-connection', async (req, res) => {
  const result = await testSupabaseConnection();
  res.json(result);
});

// Route kiểm tra kết nối với service role key
app.get('/api/test-service-connection', async (req, res) => {
  const result = await testSupabaseConnection(serviceRoleKey);
  res.json(result);
});

// Route lấy danh sách users
app.get('/api/users', async (req, res) => {
  try {
    const supabase = createClient(supabaseUrl, serviceRoleKey);
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .limit(5);
      
    if (error) throw error;
    
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Route lấy danh sách rooms
app.get('/api/rooms', async (req, res) => {
  try {
    const supabase = createClient(supabaseUrl, serviceRoleKey);
    let data, error;
    
    // Thử bảng chat_rooms
    try {
      const response = await supabase
        .from('chat_rooms')
        .select('*')
        .limit(5);
        
      data = response.data;
      error = response.error;
      
      if (!error && data) {
        console.log('Successfully fetched from chat_rooms');
      }
    } catch (e) {
      console.log('Error fetching from chat_rooms, trying rooms table...');
    }
    
    // Nếu không có dữ liệu từ chat_rooms, thử bảng rooms
    if (!data || error) {
      const response = await supabase
        .from('rooms')
        .select('*')
        .limit(5);
        
      data = response.data;
      error = response.error;
      
      if (!error && data) {
        console.log('Successfully fetched from rooms');
      }
    }
    
    if (error) throw error;
    
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error fetching rooms:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Route kiểm tra health
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is running' });
});

// Trang HTML test
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'supabase-test.html'));
});

// Tạo file HTML test
fs.writeFileSync(
  path.join(__dirname, 'public', 'supabase-test.html'),
  `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Supabase Connection Test</title>
  <style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    .panel { background: white; padding: 15px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    h3 { margin-top: 0; }
    .success { color: green; }
    .error { color: red; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    button { background: #4CAF50; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; margin-right: 10px; }
    button:hover { background: #45a049; }
  </style>
</head>
<body>
  <h1>Supabase Connection Test</h1>
  
  <div class="panel">
    <h3>Test via Proxy Server</h3>
    <div>
      <button id="test-anon">Test with Anon Key</button>
      <button id="test-service">Test with Service Role Key</button>
    </div>
    <div id="result-proxy" style="margin-top: 10px;"></div>
  </div>
  
  <div class="panel">
    <h3>Fetch Data</h3>
    <div>
      <button id="fetch-users">Fetch Users</button>
      <button id="fetch-rooms">Fetch Rooms</button>
    </div>
    <div id="result-data" style="margin-top: 10px;"></div>
  </div>

  <script>
    // Test kết nối với anon key
    document.getElementById('test-anon').addEventListener('click', async () => {
      const resultElement = document.getElementById('result-proxy');
      resultElement.innerHTML = 'Testing connection...';
      
      try {
        const response = await fetch('/api/test-connection');
        const data = await response.json();
        
        if (data.success) {
          resultElement.innerHTML = '<div class="success">Connection successful with anon key!</div><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } else {
          resultElement.innerHTML = '<div class="error">Connection failed with anon key!</div><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }
      } catch (error) {
        resultElement.innerHTML = '<div class="error">Error: ' + error.message + '</div>';
      }
    });
    
    // Test kết nối với service role key
    document.getElementById('test-service').addEventListener('click', async () => {
      const resultElement = document.getElementById('result-proxy');
      resultElement.innerHTML = 'Testing connection with service role key...';
      
      try {
        const response = await fetch('/api/test-service-connection');
        const data = await response.json();
        
        if (data.success) {
          resultElement.innerHTML = '<div class="success">Connection successful with service role key!</div><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } else {
          resultElement.innerHTML = '<div class="error">Connection failed with service role key!</div><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }
      } catch (error) {
        resultElement.innerHTML = '<div class="error">Error: ' + error.message + '</div>';
      }
    });
    
    // Fetch users
    document.getElementById('fetch-users').addEventListener('click', async () => {
      const resultElement = document.getElementById('result-data');
      resultElement.innerHTML = 'Fetching users...';
      
      try {
        const response = await fetch('/api/users');
        const data = await response.json();
        
        if (data.success) {
          resultElement.innerHTML = '<div class="success">Successfully fetched users!</div><pre>' + JSON.stringify(data.data, null, 2) + '</pre>';
        } else {
          resultElement.innerHTML = '<div class="error">Failed to fetch users!</div><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }
      } catch (error) {
        resultElement.innerHTML = '<div class="error">Error: ' + error.message + '</div>';
      }
    });
    
    // Fetch rooms
    document.getElementById('fetch-rooms').addEventListener('click', async () => {
      const resultElement = document.getElementById('result-data');
      resultElement.innerHTML = 'Fetching rooms...';
      
      try {
        const response = await fetch('/api/rooms');
        const data = await response.json();
        
        if (data.success) {
          resultElement.innerHTML = '<div class="success">Successfully fetched rooms!</div><pre>' + JSON.stringify(data.data, null, 2) + '</pre>';
        } else {
          resultElement.innerHTML = '<div class="error">Failed to fetch rooms!</div><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }
      } catch (error) {
        resultElement.innerHTML = '<div class="error">Error: ' + error.message + '</div>';
      }
    });
  </script>
</body>
</html>
  `
);

// Khởi động server
app.listen(port, () => {
  console.log(`Supabase proxy server running at http://localhost:${port}`);
  
  // Test kết nối khi khởi động
  console.log('\n--- TESTING CONNECTION WHEN STARTING SERVER ---');
  testSupabaseConnection().then(() => {
    console.log('--- ANON KEY TEST COMPLETED ---\n');
    return testSupabaseConnection(serviceRoleKey);
  }).then(() => {
    console.log('--- SERVICE ROLE KEY TEST COMPLETED ---\n');
  }).catch(err => {
    console.error('Error during startup tests:', err);
  });
});