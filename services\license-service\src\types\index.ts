import { Request } from 'express';

export enum LicenseStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  REVOKED = 'REVOKED',
  NOT_ACTIVATED = 'NOT_ACTIVATED',
  EXPIRING_SOON = 'EXPIRING_SOON'
}

export enum LicenseActivityType {
  ACTIVATION = 'ACTIVATION',
  CHECK_IN = 'CHECK_IN',
  WARNING = 'WARNING',
  VIOLATION = 'VIOLATION',
  REVOCATION = 'REVOCATION'
}

export enum CloneStatus {
  DETECTED = 'DETECTED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  CONFIRMED = 'CONFIRMED',
  FALSE_ALARM = 'FALSE_ALARM',
  REVOKED = 'REVOKED'
}

export interface License {
  id: string;
  license_key: string;
  customer_name: string;
  customer_email?: string;
  issue_date: string;
  expiry_date: string;
  is_active: boolean;
  hardware_fingerprint?: string;
  activation_date?: string;
  last_check_in?: string;
  check_in_count: number;
  metadata?: Record<string, any>;
  revocation_reason?: string;
}

export interface LicenseActivity {
  id: string;
  license_id: string;
  activity_type: LicenseActivityType;
  hardware_fingerprint?: string;
  ip_address?: string;
  timestamp: string;
  details?: Record<string, any>;
}

export interface LicenseClone {
  id: string;
  license_id: string;
  detection_time: string;
  original_fingerprint: string;
  clone_fingerprint: string;
  status: CloneStatus;
}

export interface LicenseActivation {
  id: string;
  license_id: string;
  activation_token: string;
  activation_email: string;
  hardware_fingerprint: string;
  ip_address?: string;
  expires_at: string;
  is_used: boolean;
  used_at?: string;
}

export interface RequestWithUser extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
  };
}

export interface EmailVerificationPayload {
  activationId: string;
  token: string;
  licenseId: string;
  email: string;
}