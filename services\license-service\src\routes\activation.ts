import express from 'express';
import { licenseController } from '../controllers/license';

const router = express.Router();

/**
 * @route POST /license-check?action=activate
 * @desc Activate license with email verification
 * @access Public
 */
router.post('/license-check', (req, res) => {
  const action = req.query.action;
  
  // Handle activation request
  if (action === 'activate') {
    return licenseController.activate(req, res);
  }
  
  // Default is check-in
  return licenseController.checkIn(req, res);
});

/**
 * @route POST /license-check/verify
 * @desc Verify license activation through email
 * @access Public
 */
router.post('/license-check/verify', licenseController.verifyActivation);

export default router;