'use client';
import { useState, useEffect, FormEvent } from 'react';
import { Alert } from '@ui';
import Link from 'next/link';
import styles from './tenant.module.scss';

interface TenantSettings {
  name: string;
  domain: string;
  logoUrl: string;
  primaryColor: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  languages: string[];
  autoTranslation: boolean;
}

export default function TenantSettingsPage() {
  const [settings, setSettings] = useState<TenantSettings>({
    name: '',
    domain: '',
    logoUrl: '',
    primaryColor: '#FF4D00',
    contactEmail: '',
    contactPhone: '',
    address: '',
    languages: ['en', 'vi'],
    autoTranslation: true
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // Danh sách ngôn ngữ được hỗ trợ
  const availableLanguages = [
    { code: 'en', name: 'English' },
    { code: 'vi', name: 'Tiếng Việt' },
    { code: 'ja', name: '日本語' },
    { code: 'ko', name: '한국어' },
    { code: 'zh', name: '中文' },
    { code: 'fr', name: 'Français' },
    { code: 'de', name: 'Deutsch' },
    { code: 'es', name: 'Español' },
    { code: 'ru', name: 'Русский' },
    { code: 'ar', name: 'العربية' },
  ];

  useEffect(() => {
    // Giả lập tải cài đặt tenant từ API
    const fetchTenantSettings = async () => {
      try {
        // Trong thực tế, bạn sẽ gọi API
        // Đây là dữ liệu mẫu
        const mockSettings: TenantSettings = {
          name: 'Hotel Paradise',
          domain: 'hotel-paradise.loaloa.app',
          logoUrl: 'https://placekitten.com/200/200', // Logo mẫu
          primaryColor: '#FF4D00',
          contactEmail: '<EMAIL>',
          contactPhone: '+84 123 456 789',
          address: '123 Beach Road, Ocean City, Paradise Island',
          languages: ['en', 'vi', 'ja', 'zh'],
          autoTranslation: true
        };
        
        setTimeout(() => {
          setSettings(mockSettings);
          setLoading(false);
        }, 800);
        
      } catch (error) {
        console.error('Error fetching tenant settings:', error);
        setError('Không thể tải cài đặt tenant. Vui lòng thử lại sau.');
        setLoading(false);
      }
    };
    
    fetchTenantSettings();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSettings(prevSettings => ({
      ...prevSettings,
      [name]: value
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setSettings(prevSettings => ({
      ...prevSettings,
      [name]: checked
    }));
  };

  const handleLanguageChange = (languageCode: string) => {
    setSettings(prevSettings => {
      const currentLanguages = [...prevSettings.languages];
      
      if (currentLanguages.includes(languageCode)) {
        return {
          ...prevSettings,
          languages: currentLanguages.filter(code => code !== languageCode)
        };
      } else {
        return {
          ...prevSettings,
          languages: [...currentLanguages, languageCode]
        };
      }
    });
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    
    try {
      // Giả lập lưu cài đặt
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSaveSuccess(true);
      
      // Ẩn thông báo thành công sau 3 giây
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
      
    } catch (error) {
      console.error('Error saving tenant settings:', error);
      setError('Không thể lưu cài đặt. Vui lòng thử lại sau.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Đang tải cài đặt tenant...</p>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.pageHeader}>
        <div className={styles.titleSection}>
          <Link href="/settings" className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.33333 5.83331L4.16666 9.99998L8.33333 14.1666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Quay lại cài đặt
          </Link>
          <h1 className={styles.pageTitle}>Cài đặt Tenant</h1>
        </div>
        <button
          type="button"
          className={styles.primaryButton}
          onClick={() => window.location.reload()}
        >
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M13.65 2.35C12.2 0.9 10.21 0 8 0C3.58 0 0 3.58 0 8C0 12.42 3.58 16 8 16C11.73 16 14.84 13.45 15.73 10H13.65C12.83 12.33 10.61 14 8 14C4.69 14 2 11.31 2 8C2 4.69 4.69 2 8 2C9.66 2 11.14 2.69 12.22 3.78L9 7H16V0L13.65 2.35Z" fill="currentColor"/>
          </svg>
          Làm mới
        </button>
      </div>

      {error && (
        <Alert
          variant="error"
          title="Lỗi"
          closable
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {saveSuccess && (
        <Alert
          variant="success"
          title="Thành công"
          closable
          onClose={() => setSaveSuccess(false)}
        >
          Cài đặt đã được lưu thành công.
        </Alert>
      )}

      <div className={styles.settingsLayout}>
        <aside className={styles.settingsSidebar}>
          <nav className={styles.settingsNav}>
            <a href="#general" className={styles.settingsNavItem + ' ' + styles.active}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM10 18C5.58 18 2 14.42 2 10C2 5.58 5.58 2 10 2C14.42 2 18 5.58 18 10C18 14.42 14.42 18 10 18Z" fill="currentColor"/>
                <path d="M10 15C12.7614 15 15 12.7614 15 10C15 7.23858 12.7614 5 10 5C7.23858 5 5 7.23858 5 10C5 12.7614 7.23858 15 10 15Z" fill="currentColor"/>
              </svg>
              Thông tin chung
            </a>
            <a href="#appearance" className={styles.settingsNavItem}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                 <path d="M17.5 0H2.5C1.12 0 0 1.12 0 2.5V17.5C0 18.88 1.12 20 2.5 20H17.5C18.88 20 20 18.88 20 17.5V2.5C20 1.12 18.88 0 17.5 0ZM2.5 2H17.5C17.78 2 18 2.22 18 2.5V13H2V2.5C2 2.22 2.22 2 2.5 2ZM17.5 18H2.5C2.22 18 2 17.78 2 17.5V15H18V17.5C18 17.78 17.78 18 17.5 18Z" fill="currentColor"/>
              </svg>
              Giao diện
            </a>
            <a href="#languages" className={styles.settingsNavItem}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM16.5 6H13.91C13.65 4.75 13.19 3.55 12.54 2.44C14.44 3.07 15.96 4.35 16.5 6ZM10 2C10.94 3.16 11.62 4.58 11.92 6H8.08C8.38 4.58 9.06 3.16 10 2ZM2 10C2 9.34 2.08 8.7 2.24 8.1H5.07C4.97 8.72 4.92 9.36 4.92 10C4.92 10.64 4.97 11.28 5.07 11.9H2.24C2.08 11.3 2 10.66 2 10ZM3.5 14H6.09C6.35 15.25 6.81 16.45 7.46 17.56C5.56 16.93 4.04 15.66 3.5 14ZM3.5 6H6.09C6.35 4.75 6.81 3.55 7.46 2.44C5.56 3.07 4.04 4.34 3.5 6ZM10 18C9.06 16.84 8.38 15.42 8.08 14H11.92C11.62 15.42 10.94 16.84 10 18ZM12.4 12H7.6C7.48 11.34 7.42 10.68 7.42 10C7.42 9.32 7.48 8.65 7.6 8H12.4C12.52 8.65 12.58 9.32 12.58 10C12.58 10.68 12.52 11.34 12.4 12ZM12.54 17.56C13.19 16.45 13.65 15.25 13.91 14H16.5C15.96 15.65 14.44 16.93 12.54 17.56ZM14.93 11.9C15.03 11.28 15.08 10.64 15.08 10C15.08 9.36 15.03 8.72 14.93 8.1H17.76C17.92 8.7 18 9.34 18 10C18 10.66 17.92 11.3 17.76 11.9H14.93Z" fill="currentColor"/>
              </svg>
              Ngôn ngữ
            </a>
            <a href="#contact" className={styles.settingsNavItem}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M17 0H3C1.35 0 0 1.35 0 3V17C0 18.65 1.35 20 3 20H17C18.65 20 20 18.65 20 17V3C20 1.35 18.65 0 17 0ZM18 17C18 17.55 17.55 18 17 18H3C2.45 18 2 17.55 2 17V3C2 2.45 2.45 2 3 2H17C17.55 2 18 2.45 18 3V17Z" fill="currentColor"/>
                <path d="M10 6C8.9 6 8 6.9 8 8C8 9.1 8.9 10 10 10C11.1 10 12 9.1 12 8C12 6.9 11.1 6 10 6Z" fill="currentColor"/>
                <path d="M6 13.58C6 12.08 8.67 11.33 10 11.33C11.33 11.33 14 12.08 14 13.58V15H6V13.58Z" fill="currentColor"/>
              </svg>
              Liên hệ
            </a>
          </nav>
        </aside>

        <form onSubmit={handleSubmit} className={styles.settingsContent}>
          {/* Thông tin chung */}
          <section id="general" className={styles.settingsSection}>
            <h2 className={styles.sectionTitle}>Thông tin chung</h2>
            
            <div className={styles.formGroup}>
              <label htmlFor="name" className={styles.label}>Tên Tenant</label>
              <input
                type="text"
                id="name"
                name="name"
                value={settings.name}
                onChange={handleInputChange}
                className={styles.input}
                required
              />
              <p className={styles.fieldHelp}>Tên hiển thị của tenant trong hệ thống.</p>
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="domain" className={styles.label}>Tên miền</label>
              <div className={styles.inputGroup}>
                <input
                  type="text"
                  id="domain"
                  name="domain"
                  value={settings.domain.split('.')[0]}
                  onChange={(e) => {
                    const value = e.target.value.replace(/[^a-z0-9-]/g, '');
                    setSettings(prev => ({
                      ...prev,
                      domain: `${value}.loaloa.app`
                    }));
                  }}
                  className={styles.inputPrefix}
                  required
                />
                <span className={styles.inputSuffix}>.loaloa.app</span>
              </div>
              <p className={styles.fieldHelp}>Chỉ sử dụng ký tự chữ cái thường, số và dấu gạch ngang.</p>
            </div>
          </section>

          {/* Giao diện */}
          <section id="appearance" className={styles.settingsSection}>
            <h2 className={styles.sectionTitle}>Giao diện</h2>
            
            <div className={styles.formGroup}>
              <label htmlFor="logoUrl" className={styles.label}>Logo URL</label>
              <div className={styles.logoField}>
                <input
                  type="text"
                  id="logoUrl"
                  name="logoUrl"
                  value={settings.logoUrl}
                  onChange={handleInputChange}
                  className={styles.input}
                />
                {settings.logoUrl && (
                  <div className={styles.logoPreview}>
                    <img 
                      src={settings.logoUrl} 
                      alt="Logo Preview" 
                      className={styles.logoImage}
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'https://via.placeholder.com/100x100?text=Invalid+URL';
                      }}
                    />
                  </div>
                )}
              </div>
              <p className={styles.fieldHelp}>Đường dẫn đến logo của bạn. Kích thước khuyến nghị: 200x200px.</p>
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="primaryColor" className={styles.label}>Màu chủ đạo</label>
              <div className={styles.colorField}>
                <input
                  type="color"
                  id="primaryColor"
                  name="primaryColor"
                  value={settings.primaryColor}
                  onChange={handleInputChange}
                  className={styles.colorInput}
                />
                <input
                  type="text"
                  value={settings.primaryColor}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)) {
                      setSettings(prev => ({
                        ...prev,
                        primaryColor: value
                      }));
                    }
                  }}
                  className={styles.colorText}
                />
              </div>
              <p className={styles.fieldHelp}>Màu chủ đạo được sử dụng trong toàn bộ ứng dụng.</p>
            </div>
          </section>

          {/* Ngôn ngữ */}
          <section id="languages" className={styles.settingsSection}>
            <h2 className={styles.sectionTitle}>Cài đặt ngôn ngữ</h2>
            
            <div className={styles.formGroup}>
              <label className={styles.label}>Ngôn ngữ được hỗ trợ</label>
              <div className={styles.checkboxGrid}>
                {availableLanguages.map(lang => (
                  <label key={lang.code} className={styles.checkbox}>
                    <input
                      type="checkbox"
                      checked={settings.languages.includes(lang.code)}
                      onChange={() => handleLanguageChange(lang.code)}
                    />
                    <span>{lang.name}</span>
                  </label>
                ))}
              </div>
              <p className={styles.fieldHelp}>Chọn các ngôn ngữ bạn muốn hỗ trợ trong ứng dụng.</p>
            </div>
            
            <div className={styles.formGroup}>
              <div className={styles.switchField}>
                <label htmlFor="autoTranslation" className={styles.label}>Dịch tự động</label>
                <label className={styles.switch}>
                  <input
                    type="checkbox"
                    id="autoTranslation"
                    name="autoTranslation"
                    checked={settings.autoTranslation}
                    onChange={handleCheckboxChange}
                  />
                  <span className={styles.slider}></span>
                </label>
              </div>
              <p className={styles.fieldHelp}>Tự động dịch tin nhắn sang ngôn ngữ của người nhận.</p>
            </div>
          </section>

          {/* Thông tin liên hệ */}
          <section id="contact" className={styles.settingsSection}>
            <h2 className={styles.sectionTitle}>Thông tin liên hệ</h2>
            
            <div className={styles.formGroup}>
              <label htmlFor="contactEmail" className={styles.label}>Email liên hệ</label>
              <input
                type="email"
                id="contactEmail"
                name="contactEmail"
                value={settings.contactEmail}
                onChange={handleInputChange}
                className={styles.input}
              />
              <p className={styles.fieldHelp}>Email liên hệ chính được sử dụng cho thông báo hệ thống.</p>
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="contactPhone" className={styles.label}>Số điện thoại</label>
              <input
                type="text"
                id="contactPhone"
                name="contactPhone"
                value={settings.contactPhone}
                onChange={handleInputChange}
                className={styles.input}
              />
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="address" className={styles.label}>Địa chỉ</label>
              <textarea
                id="address"
                name="address"
                value={settings.address}
                onChange={handleInputChange}
                className={styles.textarea}
                rows={3}
              ></textarea>
            </div>
          </section>

          <div className={styles.formActions}>
            <button 
              type="button" 
              className={styles.secondaryButton}
              onClick={() => window.location.reload()}
            >
              Hủy
            </button>
            <button 
              type="submit" 
              className={styles.primaryButton}
              disabled={saving}
            >
              {saving ? 'Đang lưu...' : 'Lưu cài đặt'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}