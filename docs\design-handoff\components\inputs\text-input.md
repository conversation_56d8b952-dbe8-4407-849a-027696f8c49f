# Component Design Handoff: Text Input

## Overview
- **Component Name:** TextInput
- **Designer:** UI Team
- **Last Updated:** 2025-04-28
- **Status:** Ready for Handoff

## Visual Design

### Desktop/Mobile View
[Screenshot from Figma - text input in normal state]

### States
- **Default:** Light gray background (#F3F4F6), dark text, medium gray border
- **Focus:** White background, blue border (#3B82F6)
- **Filled:** Same as default but with content
- **Disabled:** Lighter gray, non-interactive
- **Error:** Same as default but with red border (#EF4444) and error message below

## Specifications

### Layout & Spacing
- **Padding:** spacing-3 (12px) vertical, spacing-4 (16px) horizontal
- **Height:** 48px (fixed)
- **Width:** 100% of container
- **Label Spacing:** spacing-2 (8px) above input

### Typography
- **Input Text:**
  - Font Family: fontFamily.base (Roboto)
  - Font Size: fontSize.md (16px)
  - Font Weight: fontWeight.regular (400)
  - Line Height: 1.5
- **Label Text:**
  - Font Size: fontSize.sm (14px)
  - Font Weight: fontWeight.medium (500)
- **Error Text:**
  - Font Size: fontSize.sm (14px)
  - Color: colors.feedback.error (#EF4444)

### Colors
- **Background:**
  - Default: colors.background.input (#F3F4F6)
  - Focus: White (#FFFFFF)
  - Disabled: colors.background.input (#F3F4F6), with opacity
- **Border:**
  - Default: colors.border.default (#D1D5DB)
  - Focus: colors.border.focus (#93C5FD)
  - Error: colors.feedback.error (#EF4444)

### Other Properties
- **Border Radius:** borderRadius.md (4px)
- **Border Width:** 1px
- **Transition:** 150ms ease for border and background

## Behavior
- **Interactions:** Click/tap focuses input
- **Placeholder:** Disappears when user starts typing
- **Overflow:** Text ellipsis if content exceeds width

## Accessibility
- **Keyboard Navigation:** Tab focuses input
- **Labels:** Always use visible labels or aria-label
- **Error Messaging:** Use aria-describedby to connect input with error message

## Implementation Notes
- Ensure proper HTML5 input types (text, email, number, etc.)
- Implement controlled component pattern in React
- Support for left/right icons or addons
- Proper form validation and error handling

## Assets
- None required beyond the design tokens