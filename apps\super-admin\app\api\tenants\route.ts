import { NextRequest, NextResponse } from 'next/server';
import { TenantService, TenantFilterOptions, PaginationOptions } from '../../services/TenantService';


export async function GET(request: NextRequest) {
  try {
    // Lấy query parameters
    const searchParams = request.nextUrl.searchParams;
    const dropdown = searchParams.get('dropdown') === 'true';
    
    if (dropdown) {
      // Trả về danh sách đơn giản cho dropdowns
      const tenants = await TenantService.getAllTenantsForDropdown();
      return NextResponse.json({ data: tenants });
    } else {
      // Xử lý pagination và filters cho danh sách đầy đủ
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '10');
      
      const status = searchParams.get('status') as 'active' | 'inactive' | 'all' || 'all';
      const search = searchParams.get('search') || undefined;
      
      const pagination: PaginationOptions = { page, limit };
      const filter: TenantFilterOptions = { status, search };
      
      const { data, count } = await TenantService.getTenants(pagination, filter);
      
      return NextResponse.json({
        data,
        meta: {
          total: count,
          page,
          limit,
          pageCount: Math.ceil(count / limit)
        }
      });
    }
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch tenants' },
      { status: 500 }
    );
  }
}
