FROM node:20-alpine AS base

# Cài đặt pnpm
RUN npm install -g pnpm

# Thiết lập thư mục làm việc
WORKDIR /app

# Bước build
FROM base AS builder

# Copy file cấu hình và dependencies
COPY package.json pnpm-lock.yaml* ./

# Cài đặt dependencies
RUN pnpm install

# Copy source code
COPY . .

# Build ứng dụng
RUN pnpm build

# Bước runtime
FROM base AS runner

# Thiết lập môi trường production
ENV NODE_ENV production

# Tạo người dùng non-root cho bảo mật
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy các file đã build từ builder
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Thiết lập quyền cho nextjs user
USER nextjs

# Expose port 3000
EXPOSE 3000

# Command để chạy ứng dụng
CMD ["node", "server.js"]
