{"name": "@loaloa/storybook", "version": "0.1.0", "private": true, "scripts": {"dev": "storybook dev -p 6006", "build": "storybook build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@loaloa/design-tokens": "workspace:*", "@loaloa/ui": "workspace:*", "react": "18.2.0", "react-dom": "18.2.0"}, "devDependencies": {"@storybook/addon-essentials": "^8.0.0", "@storybook/addon-interactions": "^8.0.0", "@storybook/addon-links": "^8.0.0", "@storybook/addon-onboarding": "^8.0.0", "@storybook/addon-webpack5-compiler-swc": "3.0.0", "@storybook/blocks": "^8.0.0", "@storybook/react": "^8.0.0", "@storybook/react-webpack5": "^8.0.0", "@storybook/test": "^8.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "storybook": "^8.0.0", "typescript": "^5.4.5", "webpack": "5"}}