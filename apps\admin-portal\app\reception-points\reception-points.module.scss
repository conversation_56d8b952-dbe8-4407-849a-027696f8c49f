.container {
  padding: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.description {
  color: #666;
  margin: 0.5rem 0 0 0;
}

.filtersContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  gap: 1rem;
}

.searchBar {
  flex: 1;
  min-width: 250px;
}

.statusFilter {
  min-width: 150px;
}

.select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.875rem;
}

.tableContainer {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
  }
  
  th {
    background-color: #f5f5f5;
    font-weight: 600;
    font-size: 0.875rem;
    color: #555;
  }
  
  tr:last-child td {
    border-bottom: none;
  }
  
  tr:hover {
    background-color: #f9f9f9;
  }
}

.nameCell {
  min-width: 250px;
}

.nameLink {
  color: #1976d2;
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    text-decoration: underline;
  }
}

.description {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.25rem;
}

.codeCell {
  font-family: monospace;
  font-size: 0.875rem;
}

.priorityCell {
  text-align: center;
}

.statusBadge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  
  &.active {
    background-color: #e6f7e6;
    color: #2e7d32;
  }
  
  &.inactive {
    background-color: #f5f5f5;
    color: #757575;
  }
}

.actionsCell {
  text-align: right;
  white-space: nowrap;
}

.actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.editButton, .deleteButton {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.editButton {
  background-color: #e3f2fd;
  color: #1976d2;
  text-decoration: none;
  
  &:hover {
    background-color: #bbdefb;
  }
}

.deleteButton {
  background-color: #ffebee;
  color: #d32f2f;
  border: none;
  
  &:hover {
    background-color: #ffcdd2;
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  flex-direction: column;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.emptyState {
  text-align: center;
  padding: 3rem 1rem;
  
  svg {
    color: #bdbdbd;
    margin-bottom: 1rem;
  }
  
  h3 {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
  }
  
  p {
    color: #757575;
    margin: 0 0 1.5rem 0;
  }
}

.createButton {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #1976d2;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 500;
  
  &:hover {
    background-color: #1565c0;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1.5rem;
  gap: 1rem;
}

.paginationButton {
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover:not(:disabled) {
    background-color: #e0e0e0;
  }
  
  &:disabled {
    color: #bdbdbd;
    cursor: not-allowed;
  }
}

.pageInfo {
  font-size: 0.875rem;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .filtersContainer {
    flex-direction: column;
    align-items: stretch;
  }
  
  .searchBar {
    width: 100%;
  }
  
  .statusFilter {
    width: 100%;
  }
  
  .actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .editButton, .deleteButton {
    padding: 0.5rem;
    text-align: center;
  }
}
