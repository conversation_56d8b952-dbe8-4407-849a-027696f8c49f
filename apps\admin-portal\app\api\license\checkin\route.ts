import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import os from 'os';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

function generateHardwareFingerprint() {
  const cpus = os.cpus();
  const network = os.networkInterfaces();
  const platform = os.platform();
  const release = os.release();
  const hostname = os.hostname();
  
  const systemInfo = JSON.stringify({
    cpuModel: cpus[0]?.model || '',
    cpuCount: cpus.length,
    network: Object.keys(network),
    platform,
    release,
    hostname
  });
  
  return crypto.createHash('sha256').update(systemInfo).digest('hex');
}

function getLicenseFromConfig() {
  try {
    const configPath = path.join(process.cwd(), 'license_config.json');
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8');
      return JSON.parse(configData);
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function POST() {
  try {
    // Đọc thông tin license từ cấu hình cục bộ
    const licenseConfig = getLicenseFromConfig();
    
    // Nếu không tìm thấy cấu hình
    if (!licenseConfig || !licenseConfig.licenseKey) {
      return NextResponse.json({ 
        success: false, 
        message: 'License chưa được kích hoạt.'
      }, { status: 400 });
    }
    
    // Tạo Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Kiểm tra license key có tồn tại không
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('license_key', licenseConfig.licenseKey)
      .single();
    
    if (error || !license) {
      return NextResponse.json({ 
        success: false, 
        message: 'License key không hợp lệ.'
      }, { status: 404 });
    }
    
    // Tạo hardware fingerprint
    const hardwareFingerprint = generateHardwareFingerprint();
    
    // Cập nhật thời gian check-in và tăng số lần check-in
    const now = new Date().toISOString();
    const { error: updateError } = await supabase
      .from('licenses')
      .update({
        last_check_in: now,
        check_in_count: (license.check_in_count || 0) + 1
      })
      .eq('id', license.id);
    
    if (updateError) {
      return NextResponse.json({ 
        success: false, 
        message: 'Không thể cập nhật thông tin check-in.'
      }, { status: 500 });
    }
    
    // Ghi log hoạt động
    await supabase.from('license_activities').insert({
      license_id: license.id,
      activity_type: 'CHECK_IN',
      hardware_fingerprint: hardwareFingerprint,
      ip_address: 'localhost', // Trong thực tế, bạn sẽ lấy IP thực
      details: { checkInTime: now }
    });
    
    return NextResponse.json({ 
      success: true, 
      message: 'Check-in thành công.',
      lastCheckIn: now,
      checkInCount: (license.check_in_count || 0) + 1
    });
    
  } catch (err) {
    console.error('Error checking in license:', err);
    return NextResponse.json({ 
      success: false, 
      message: 'Lỗi hệ thống khi check-in license.'
    }, { status: 500 });
  }
}
