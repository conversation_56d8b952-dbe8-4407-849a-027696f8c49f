.form {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 20px;
  
  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.formGroupFull {
  grid-column: 1 / -1;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroupCheckbox {
  grid-column: 1 / -1;
  margin-top: 8px;
}

.label {
  display: block;
  margin-bottom: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.required {
  color: #ef4444;
}

.input,
.select,
.textarea {
  padding: 10px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #374151;
  width: 100%;
  
  &:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
  }
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
  
  span {
    margin-left: 8px;
  }
}

.checkbox {
  width: 16px;
  height: 16px;
  accent-color: #0ea5e9;
}

.error {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 0.875rem;
}

.buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.submitButton,
.cancelButton {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submitButton {
  background-color: #0ea5e9;
  color: white;
  border: none;
  
  &:hover:not(:disabled) {
    background-color: #0284c7;
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.cancelButton {
  background-color: white;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  
  &:hover:not(:disabled) {
    background-color: #f9fafb;
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}
