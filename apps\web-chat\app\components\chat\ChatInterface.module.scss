.chatInterface {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  border-radius: 0.5rem;
  overflow: hidden;
}

.errorBanner {
  background: #fef2f2;
  border-left: 4px solid #f87171;
  padding: 1rem;
  animation: slideDown 0.3s ease-out;
}

.messagesArea {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: linear-gradient(to bottom, #f8fafc, #ffffff);
  scroll-behavior: smooth;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    
    &:hover {
      background: #94a3b8;
    }
  }
}

.welcomeMessage {
  text-align: center;
  padding: 2rem 0;
  animation: fadeIn 0.6s ease-out;
  
  .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: bounce 2s infinite;
  }
  
  .title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }
  
  .subtitle {
    color: #6b7280;
    margin-bottom: 1rem;
  }
  
  .badges {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
  }
  
  .badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    
    &.connected {
      background: #dcfce7;
      color: #166534;
    }
    
    &.language {
      background: #dbeafe;
      color: #1e40af;
    }
    
    &.translation {
      background: #e5e7eb;
      color: #7c2d12;
    }
  }
}

.messagesList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.messageContainer {
  display: flex;
  animation: slideUp 0.3s ease-out;
  
  &.guest {
    justify-content: flex-end;
  }
  
  &.staff {
    justify-content: flex-start;
  }
}

.messageBubble {
  max-width: 20rem;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  position: relative;
  word-wrap: break-word;
  
  @media (min-width: 1024px) {
    max-width: 24rem;
  }
  
  &.guest {
    background: linear-gradient(135deg, #f97316, #ea580c);
    color: white;
    border-bottom-right-radius: 0.25rem;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: -8px;
      width: 0;
      height: 0;
      border: 8px solid transparent;
      border-top-color: #ea580c;
      border-left-color: #ea580c;
    }
  }
  
  &.staff {
    background: #f3f4f6;
    color: #111827;
    border-bottom-left-radius: 0.25rem;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -8px;
      width: 0;
      height: 0;
      border: 8px solid transparent;
      border-top-color: #f3f4f6;
      border-right-color: #f3f4f6;
    }
  }
}

.messageHeader {
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
  opacity: 0.8;
}

.messageContent {
  font-size: 0.875rem;
  line-height: 1.4;
}

.originalContent {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-style: italic;
  opacity: 0.7;
}

.translationInfo {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  opacity: 0.7;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.typingIndicator {
  display: flex;
  justify-content: flex-start;
  animation: slideUp 0.3s ease-out;
  
  .bubble {
    background: #f3f4f6;
    color: #111827;
    max-width: 20rem;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    border-bottom-left-radius: 0.25rem;
    
    .dots {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      
      .dot {
        width: 0.5rem;
        height: 0.5rem;
        background: #9ca3af;
        border-radius: 50%;
        
        &:nth-child(1) {
          animation: typingDot 1.4s infinite;
        }
        
        &:nth-child(2) {
          animation: typingDot 1.4s infinite 0.2s;
        }
        
        &:nth-child(3) {
          animation: typingDot 1.4s infinite 0.4s;
        }
      }
    }
    
    .text {
      font-size: 0.75rem;
      color: #6b7280;
      margin-left: 0.5rem;
    }
  }
}

.inputArea {
  border-top: 1px solid #e5e7eb;
  padding: 1rem;
  background: #ffffff;
}

.inputContainer {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
}

.messageInput {
  flex: 1;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  resize: none;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #f97316;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
  }
  
  &:disabled {
    background: #f9fafb;
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  &::placeholder {
    color: #9ca3af;
  }
}

.sendButton {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(249, 115, 22, 0.2);
  
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #ea580c, #dc2626);
    box-shadow: 0 4px 8px rgba(249, 115, 22, 0.3);
    transform: translateY(-1px);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(249, 115, 22, 0.2);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
  
  .spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.statusBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.statusLeft {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.connectionStatus {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  
  .indicator {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    
    &.connected {
      background: #10b981;
    }
    
    &.disconnected {
      background: #ef4444;
    }
  }
  
  &.connected {
    color: #059669;
  }
  
  &.disconnected {
    color: #dc2626;
  }
}

// Animations
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-30px);
  }
  70% {
    transform: translateY(-15px);
  }
  90% {
    transform: translateY(-4px);
  }
}

@keyframes typingDot {
  0%, 60%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive design
@media (max-width: 640px) {
  .messageBubble {
    max-width: 16rem;
  }
  
  .inputContainer {
    gap: 0.5rem;
  }
  
  .sendButton {
    padding: 0.75rem;
    
    .text {
      display: none;
    }
  }
  
  .statusBar {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
}