<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LoaLoa Chat Tester v2</title>
  <!-- T<PERSON><PERSON><PERSON> thư viện Supabase từ CDN -->
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <style>
    /* CSS giữ nguyên từ file gốc */
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
    .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
    .panel { background: white; padding: 15px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    .panel-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; border-bottom: 1px solid #eee; padding-bottom: 5px; }
    h3 { margin: 0; color: #333; }
    .status { font-weight: bold; padding: 4px 8px; border-radius: 4px; font-size: 14px; }
    .pending { background-color: #ffeaa7; color: #d35400; }
    .success { background-color: #c4e6ca; color: #2d6a4f; }
    .error { background-color: #ffcccc; color: #cc0000; }
    input, textarea, select { width: 100%; padding: 8px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
    button { background-color: #4CAF50; color: white; border: none; padding: 8px 12px; cursor: pointer; border-radius: 4px; }
    button:hover { background-color: #45a049; }
    .tab-panel { display: flex; margin-bottom: 10px; }
    .tab { padding: 8px 12px; cursor: pointer; background: #eee; margin-right: 5px; border-radius: 4px 4px 0 0; }
    .tab.active { background: #fff; border-bottom: 2px solid #4CAF50; }
    .tab-content { background: white; padding: 15px; border-radius: 0 0 8px 8px; }
    table { width: 100%; border-collapse: collapse; }
    table, th, td { border: 1px solid #ddd; }
    th, td { padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .chat-container { display: flex; gap: 20px; margin-top: 10px; }
    .chat-user { flex: 1; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
    .chat-header { background: #f2f2f2; padding: 10px; border-bottom: 1px solid #ddd; }
    .messages { height: 300px; overflow-y: auto; padding: 10px; }
    .message { margin-bottom: 10px; padding: 8px; border-radius: 4px; max-width: 70%; }
    .sent { background-color: #e3f2fd; margin-left: auto; text-align: right; }
    .received { background-color: #f1f1f1; }
    .input-container { display: flex; border-top: 1px solid #ddd; }
    .input-container input { margin: 5px; border-radius: 4px; padding: 8px; flex: 1; }
    .input-container button { margin: 5px; }
    .debug-console { height: 200px; overflow-y: auto; background: #272822; color: #f8f8f2; font-family: monospace; padding: 10px; border-radius: 4px; }
    .debug-message { margin-bottom: 5px; padding: 2px 0; border-bottom: 1px solid #444; }
    /* Thêm style cho ghi chú JWT Token */
    .note { background-color: #ffeaa7; padding: 10px; border-radius: 4px; margin-bottom: 10px; font-size: 14px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>LoaLoa Chat Tester v2</h1>
    
    <!-- Panel 1: Connect to Supabase -->
    <div class="panel">
      <div class="panel-header">
        <h3>Connect to Supabase</h3>
        <span class="status pending" id="supabase-status">Pending</span>
      </div>
      <div>
        <input type="text" id="supabase-url" placeholder="Supabase URL" value="https://uvfosdvduemcktgayllz.supabase.co">
        <input type="password" id="supabase-key" placeholder="Supabase Key" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2Zm9zZHZkdWVtY2t0Z2F5bGx6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2ODQ5MjQ0MjksImV4cCI6MjAwMDUwMDQyOX0.vZn916S2nYhXsVO6ublrZ_mV9FxKlrJbKD9mgmrkSUE">
        <button id="connect-supabase">Connect to Supabase</button>
      </div>
    </div>
    
    <!-- Panel 2: Generate JWT Token -->
    <div class="panel">
      <div class="panel-header">
        <h3>Generate JWT Token</h3>
        <span class="status pending" id="jwt-status">Pending</span>
      </div>
      <div class="note">
        <strong>Need a JWT Token?</strong> Run this command:<br>
        <code>cd D:\loaloa\services\chat</code><br>
        <code>npx ts-node src\tests\generate-token.ts</code>
      </div>
      <textarea id="jwt-token" placeholder="JWT Token" rows="3"></textarea>
      <button id="validate-token">Validate Token</button>
    </div>
    
    <!-- Panel 3: Database Explorer -->
    <div class="panel">
      <div class="panel-header">
        <h3>Database Explorer</h3>
        <span class="status pending" id="db-status">Pending</span>
      </div>
      <div class="tab-panel">
        <div class="tab active" data-tab="users">Users</div>
        <div class="tab" data-tab="rooms">Chat Rooms</div>
        <div class="tab" data-tab="participants">Participants</div>
        <div class="tab" data-tab="messages">Messages</div>
      </div>
      <div class="tab-content">
        <!-- Users Table -->
        <div id="users-tab" class="tab-pane active">
          <h4>Users</h4>
          <table id="users-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Email</th>
                <th>Created At</th>
              </tr>
            </thead>
            <tbody>
              <tr><td colspan="3">No data loaded yet</td></tr>
            </tbody>
          </table>
        </div>
        
        <!-- Chat Rooms Table -->
        <div id="rooms-tab" class="tab-pane" style="display:none;">
          <h4>Chat Rooms</h4>
          <table id="rooms-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Type</th>
                <th>Created At</th>
              </tr>
            </thead>
            <tbody>
              <tr><td colspan="4">No data loaded yet</td></tr>
            </tbody>
          </table>
        </div>
        
        <!-- Participants Table -->
        <div id="participants-tab" class="tab-pane" style="display:none;">
          <h4>Chat Participants</h4>
          <table id="participants-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Room ID</th>
                <th>User ID</th>
                <th>Role</th>
                <th>Joined At</th>
              </tr>
            </thead>
            <tbody>
              <tr><td colspan="5">No data loaded yet</td></tr>
            </tbody>
          </table>
        </div>
        
        <!-- Messages Table -->
        <div id="messages-tab" class="tab-pane" style="display:none;">
          <h4>Chat Messages</h4>
          <table id="messages-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Room ID</th>
                <th>Sender ID</th>
                <th>Content</th>
                <th>Language</th>
                <th>Sent At</th>
              </tr>
            </thead>
            <tbody>
              <tr><td colspan="6">No data loaded yet</td></tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- Panel 4: User-Room Management -->
    <div class="panel">
      <div class="panel-header">
        <h3>User-Room Management</h3>
        <span class="status pending" id="manage-status">Pending</span>
      </div>
      <div>
        <select id="select-user">
          <option value="">Select User</option>
        </select>
        <select id="select-room">
          <option value="">Select Room</option>
        </select>
        <button id="check-participation">Check</button>
        <div id="participation-status" style="margin-top: 10px;">User is active in room</div>
      </div>
    </div>
    
    <!-- Panel 5: WebSocket Connection -->
    <div class="panel">
      <div class="panel-header">
        <h3>WebSocket Connection</h3>
        <span class="status pending" id="ws-status">Pending</span>
      </div>
      <div>
        <input type="text" id="ws-url" placeholder="WebSocket Server URL" value="http://localhost:3002">
        <input type="text" id="room-id" placeholder="Room to Join" value="c636ea40-1d87-4982-a6a3-86fa0805e258">
        <button id="connect-ws">Connect to WebSocket</button>
        <div class="checkbox-container">
          <input type="checkbox" id="show-debug" checked>
          <label for="show-debug">Show Debug Console</label>
        </div>
        <div id="debug-console" class="debug-console">
          <!-- Debug messages will be added here -->
        </div>
      </div>
    </div>
    
    <!-- Panel 6: Test Chat Interface -->
    <div class="panel">
      <div class="panel-header">
        <h3>Test Chat Interface</h3>
        <span class="status pending" id="chat-status">Pending</span>
      </div>
      <div class="chat-container">
        <!-- User 1 Chat -->
        <div class="chat-user">
          <div class="chat-header">
            <h5>User 1</h5>
            <div><strong>Vietnamese</strong></div>
            <div id="user1-status">Disconnected</div>
          </div>
          <div id="user1-messages" class="messages">
            <!-- Messages will be added here -->
          </div>
          <div class="input-container">
            <input type="text" id="user1-input" placeholder="Type a message...">
            <button id="user1-send">Send</button>
          </div>
        </div>
        
        <!-- User 2 Chat -->
        <div class="chat-user">
          <div class="chat-header">
            <h5>User 2</h5>
            <div><strong>English</strong></div>
            <div id="user2-status">Disconnected</div>
          </div>
          <div id="user2-messages" class="messages">
            <!-- Messages will be added here -->
          </div>
          <div class="input-container">
            <input type="text" id="user2-input" placeholder="Type a message...">
            <button id="user2-send">Send</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Kiểm tra xem thư viện Supabase đã được nạp chưa
    if (typeof supabase === 'undefined') {
      console.error('Supabase library not loaded correctly');
    }
    
    // Biến toàn cục
    let supabaseClient = null;
    let jwtToken = '';
    let userId = '';
    let socket1 = null;
    let socket2 = null;
    let currentRoomId = '';
    
    // Đối tượng để theo dõi trạng thái của các bảng điều khiển
    const panels = {
      supabase: document.getElementById('supabase-status'),
      jwt: document.getElementById('jwt-status'),
      db: document.getElementById('db-status'),
      manage: document.getElementById('manage-status'),
      ws: document.getElementById('ws-status'),
      chat: document.getElementById('chat-status')
    };
    
    // Hàm cập nhật trạng thái panel
    function updateStatus(panel, status, message = '') {
      if (panels[panel]) {
        panels[panel].className = `status ${status}`;
        panels[panel].textContent = status.charAt(0).toUpperCase() + status.slice(1);
        
        if (message && status === 'error') {
          console.error(message);
          addDebugMessage(`${message}`);
        }
      }
    }
    
    // Hàm thêm tin nhắn debug
    function addDebugMessage(message) {
      const debugConsole = document.getElementById('debug-console');
      if (debugConsole) {
        const timestamp = new Date().toLocaleTimeString();
        const msgElement = document.createElement('div');
        msgElement.className = 'debug-message';
        msgElement.textContent = `[${timestamp}] ${message}`;
        debugConsole.appendChild(msgElement);
        debugConsole.scrollTop = debugConsole.scrollHeight;
      }
    }
    
    // Kết nối đến Supabase
    document.getElementById('connect-supabase').addEventListener('click', async () => {
      try {
        const supabaseUrl = document.getElementById('supabase-url').value;
        const supabaseKey = document.getElementById('supabase-key').value;
        
        if (!supabaseUrl || !supabaseKey) {
          updateStatus('supabase', 'error', 'Supabase URL and key are required');
          return;
        }
        
        // Kiểm tra xem thư viện Supabase có sẵn sàng chưa
        if (typeof supabase !== 'undefined' && typeof supabase.createClient !== 'undefined') {
          // Sử dụng createClient từ namespace toàn cục
          supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
          
          // Kiểm tra kết nối bằng cách thực hiện một truy vấn đơn giản
          const { data, error } = await supabaseClient.from('users').select('count(*)');
          
          if (error) {
            updateStatus('supabase', 'error', `Failed to connect to Supabase: ${error.message}`);
          } else {
            updateStatus('supabase', 'success');
            addDebugMessage('Successfully connected to Supabase');
            loadData();
          }
        } else {
          updateStatus('supabase', 'error', 'Supabase library not loaded correctly (createClient not found)');
        }
      } catch (err) {
        updateStatus('supabase', 'error', `Failed to connect to Supabase: ${err.message}`);
      }
    });
    
    // Xác thực JWT Token
    document.getElementById('validate-token').addEventListener('click', () => {
      const token = document.getElementById('jwt-token').value.trim();
      
      if (!token) {
        updateStatus('jwt', 'error', 'Please enter a JWT token');
        return;
      }
      
      try {
        // Decode JWT token để lấy payload (không cần private key)
        const base64Url = token.split('.')[1];
        if (!base64Url) {
          updateStatus('jwt', 'error', 'Invalid JWT token format');
          addDebugMessage('Invalid JWT token format');
          return;
        }
        
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        
        const payload = JSON.parse(jsonPayload);
        
        // Kiểm tra xem token đã hết hạn chưa
        const expiryDate = new Date(payload.exp * 1000);
        if (expiryDate < new Date()) {
          updateStatus('jwt', 'error', 'Invalid JWT token: Token has expired');
          addDebugMessage('Invalid JWT token: Token has expired');
          return;
        }
        
        // Lưu token và userId
        jwtToken = token;
        userId = payload.userId;
        
        updateStatus('jwt', 'success');
        addDebugMessage(`Valid JWT token for user: ${userId}`);
      } catch (err) {
        updateStatus('jwt', 'error', `Invalid JWT token: ${err.message}`);
        addDebugMessage(`Invalid JWT token: ${err.message}`);
      }
    });
    
    // Hàm tải dữ liệu từ Supabase
    async function loadData() {
      if (!supabaseClient) {
        addDebugMessage('Failed to load users: Please connect to Supabase first');
        return;
      }
      
      try {
        // Tải Users
        const { data: users, error: usersError } = await supabaseClient
          .from('users')
          .select('*');
          
        if (usersError) {
          updateStatus('db', 'error', `Failed to load users: ${usersError.message}`);
        } else {
          updateStatus('db', 'success');
          displayUsers(users);
          populateUserDropdown(users);
        }
        
        // Tải Chat Rooms
        const { data: rooms, error: roomsError } = await supabaseClient
          .from('chat_rooms')
          .select('*');
          
        if (roomsError) {
          updateStatus('db', 'error', `Failed to load rooms: ${roomsError.message}`);
        } else {
          displayRooms(rooms);
          populateRoomDropdown(rooms);
        }
        
        // Tải Chat Participants
        const { data: participants, error: participantsError } = await supabaseClient
          .from('chat_participants')
          .select('*');
          
        if (participantsError) {
          updateStatus('db', 'error', `Failed to load participants: ${participantsError.message}`);
        } else {
          displayParticipants(participants);
        }
        
        // Tải Chat Messages
        const { data: messages, error: messagesError } = await supabaseClient
          .from('chat_messages')
          .select('*');
          
        if (messagesError) {
          updateStatus('db', 'error', `Failed to load messages: ${messagesError.message}`);
        } else {
          displayMessages(messages);
        }
        
      } catch (err) {
        updateStatus('db', 'error', `Failed to load data: ${err.message}`);
      }
    }
    
    // Hàm hiển thị dữ liệu User
    function displayUsers(users) {
      const table = document.getElementById('users-table');
      const tbody = table.getElementsByTagName('tbody')[0];
      tbody.innerHTML = '';
      
      if (users && users.length > 0) {
        users.forEach(user => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${user.id || ''}</td>
            <td>${user.email || ''}</td>
            <td>${new Date(user.created_at).toLocaleString() || ''}</td>
          `;
          tbody.appendChild(row);
        });
      } else {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="3">No users found</td>`;
        tbody.appendChild(row);
      }
    }
    
    // Hàm hiển thị dữ liệu Chat Rooms
    function displayRooms(rooms) {
      const table = document.getElementById('rooms-table');
      const tbody = table.getElementsByTagName('tbody')[0];
      tbody.innerHTML = '';
      
      if (rooms && rooms.length > 0) {
        rooms.forEach(room => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${room.id || ''}</td>
            <td>${room.name || ''}</td>
            <td>${room.room_type || ''}</td>
            <td>${new Date(room.created_at).toLocaleString() || ''}</td>
          `;
          tbody.appendChild(row);
        });
      } else {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="4">No rooms found</td>`;
        tbody.appendChild(row);
      }
    }
    
    // Hàm hiển thị dữ liệu Chat Participants
    function displayParticipants(participants) {
      const table = document.getElementById('participants-table');
      const tbody = table.getElementsByTagName('tbody')[0];
      tbody.innerHTML = '';
      
      if (participants && participants.length > 0) {
        participants.forEach(participant => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${participant.id || ''}</td>
            <td>${participant.chat_room_id || ''}</td>
            <td>${participant.user_id || participant.temporary_user_id || ''}</td>
            <td>${participant.participant_role || ''}</td>
            <td>${new Date(participant.joined_at).toLocaleString() || ''}</td>
          `;
          tbody.appendChild(row);
        });
      } else {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="5">No participants found</td>`;
        tbody.appendChild(row);
      }
    }
    
    // Hàm hiển thị dữ liệu Chat Messages
    function displayMessages(messages) {
      const table = document.getElementById('messages-table');
      const tbody = table.getElementsByTagName('tbody')[0];
      tbody.innerHTML = '';
      
      if (messages && messages.length > 0) {
        messages.forEach(message => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${message.id || ''}</td>
            <td>${message.chat_room_id || ''}</td>
            <td>${message.sender_id || message.temporary_sender_id || ''}</td>
            <td>${message.content || ''}</td>
            <td>${message.original_language || ''}</td>
            <td>${new Date(message.sent_at).toLocaleString() || ''}</td>
          `;
          tbody.appendChild(row);
        });
      } else {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="6">No messages found</td>`;
        tbody.appendChild(row);
      }
    }
    
    // Điền dữ liệu vào dropdown Users
    function populateUserDropdown(users) {
      const select = document.getElementById('select-user');
      select.innerHTML = '<option value="">Select User</option>';
      
      if (users && users.length > 0) {
        users.forEach(user => {
          const option = document.createElement('option');
          option.value = user.id;
          option.textContent = user.email || user.id;
          select.appendChild(option);
        });
      }
    }
    
    // Điền dữ liệu vào dropdown Rooms
    function populateRoomDropdown(rooms) {
      const select = document.getElementById('select-room');
      select.innerHTML = '<option value="">Select Room</option>';
      
      if (rooms && rooms.length > 0) {
        rooms.forEach(room => {
          const option = document.createElement('option');
          option.value = room.id;
          option.textContent = room.name || room.id;
          select.appendChild(option);
        });
      }
    }
    
    // Chuyển đổi giữa các tab
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        // Ẩn tất cả các tab-pane
        document.querySelectorAll('.tab-pane').forEach(pane => {
          pane.style.display = 'none';
        });
        
        // Xóa lớp active từ tất cả các tab
        document.querySelectorAll('.tab').forEach(t => {
          t.classList.remove('active');
        });
        
        // Hiển thị tab-pane được chọn
        const tabName = tab.getAttribute('data-tab');
        document.getElementById(`${tabName}-tab`).style.display = 'block';
        
        // Thêm lớp active cho tab được chọn
        tab.classList.add('active');
      });
    });
    
    // Kiểm tra sự tham gia của người dùng trong phòng
    document.getElementById('check-participation').addEventListener('click', async () => {
      const selectedUserId = document.getElementById('select-user').value;
      const selectedRoomId = document.getElementById('select-room').value;
      
      if (!supabaseClient) {
        updateStatus('manage', 'error', 'Please connect to Supabase first');
        return;
      }
      
      if (!selectedUserId || !selectedRoomId) {
        updateStatus('manage', 'error', 'Please select both user and room');
        document.getElementById('participation-status').textContent = 'Please select both user and room';
        return;
      }
      
      try {
        const { data, error } = await supabaseClient
          .from('chat_participants')
          .select('*')
          .eq('user_id', selectedUserId)
          .eq('chat_room_id', selectedRoomId)
          .eq('is_active', true);
          
        if (error) {
          updateStatus('manage', 'error', `Failed to check participation: ${error.message}`);
          return;
        }
        
        updateStatus('manage', 'success');
        
        if (data && data.length > 0) {
          document.getElementById('participation-status').textContent = 'User is active in this room';
        } else {
          document.getElementById('participation-status').textContent = 'User is NOT active in this room';
        }
      } catch (err) {
        updateStatus('manage', 'error', `Failed to check participation: ${err.message}`);
      }
    });
    
    // Kết nối đến WebSocket server
    document.getElementById('connect-ws').addEventListener('click', () => {
      const wsUrl = document.getElementById('ws-url').value;
      const roomId = document.getElementById('room-id').value;
      
      if (!wsUrl) {
        updateStatus('ws', 'error', 'WebSocket URL is required');
        return;
      }
      
      if (!roomId) {
        updateStatus('ws', 'error', 'Room ID is required');
        return;
      }
      
      if (!jwtToken) {
        addDebugMessage('Failed to connect WebSocket: Please enter JWT token');
        updateStatus('ws', 'error', 'JWT Token is required');
        return;
      }
      
      currentRoomId = roomId;
      connectWebSockets(wsUrl, roomId);
    });
    
    // Hàm kết nối WebSocket cho cả hai người dùng
    function connectWebSockets(serverUrl, roomId) {
      try {
        addDebugMessage(`Attempting to connect to WebSocket server: ${serverUrl}`);
		 // Kết nối WebSocket cho User 1 (Tiếng Việt)
        socket1 = io(serverUrl, {
          auth: { token: jwtToken }
        });
        
        // Kết nối WebSocket cho User 2 (Tiếng Anh)
        // User 2 sẽ sử dụng cùng token nhưng ngôn ngữ khác
        socket2 = io(serverUrl, {
          auth: { token: jwtToken }
        });
        
        addDebugMessage('WebSocket connection initiated');
        
        // Xử lý kết nối và các sự kiện cho User 1
        socket1.on('connect', () => {
          addDebugMessage('User 1 connected to WebSocket server');
          document.getElementById('user1-status').textContent = 'Connected';
          
          // Thiết lập thông tin người dùng
          socket1.emit('setup', {
            userId,
            preferredLanguage: 'vi',
            deviceId: `test-device-user1-${Date.now()}`
          }, (response) => {
            if (response.success) {
              addDebugMessage(`User 1 setup success: ${JSON.stringify(response.data)}`);
              
              // Tham gia phòng chat
              socket1.emit('join_room', roomId, (joinResponse) => {
                if (joinResponse.success) {
                  addDebugMessage(`User 1 joined room ${roomId}`);
                  addDebugMessage('User 1 ready to chat');
                  updateStatus('chat', 'success');
                } else {
                  addDebugMessage(`User 1 failed to join room: ${joinResponse.error}`);
                  updateStatus('chat', 'error', `User 1 failed to join room: ${joinResponse.error}`);
                }
              });
            } else {
              addDebugMessage(`User 1 setup failed: ${response.error}`);
              updateStatus('ws', 'error', `User 1 setup failed: ${response.error}`);
            }
          });
        });
        
        // Xử lý kết nối và các sự kiện cho User 2
        socket2.on('connect', () => {
          addDebugMessage('User 2 connected to WebSocket server');
          document.getElementById('user2-status').textContent = 'Connected';
          
          // Thiết lập thông tin người dùng
          socket2.emit('setup', {
            userId: 'a9813ae-9a46-4dc9-9fa3-6f04062f7e50', // ID khác cho User 2
            preferredLanguage: 'en',
            deviceId: `test-device-user2-${Date.now()}`
          }, (response) => {
            if (response.success) {
              addDebugMessage(`User 2 setup success: ${JSON.stringify(response.data)}`);
              
              // Tham gia phòng chat
              socket2.emit('join_room', roomId, (joinResponse) => {
                if (joinResponse.success) {
                  addDebugMessage(`User 2 joined room ${roomId}`);
                  addDebugMessage('User 2 ready to chat');
                } else {
                  addDebugMessage(`User 2 failed to join room: ${joinResponse.error}`);
                }
              });
            } else {
              addDebugMessage(`User 2 setup failed: ${response.error}`);
            }
          });
        });
        
        // Xử lý sự kiện nhận tin nhắn cho User 1
        socket1.on('message_received', (data) => {
          addDebugMessage(`User 1 received message: ${JSON.stringify(data.message)}`);
          displayMessage('user1-messages', data.message, false);
        });
        
        // Xử lý sự kiện nhận tin nhắn cho User 2
        socket2.on('message_received', (data) => {
          addDebugMessage(`User 2 received message: ${JSON.stringify(data.message)}`);
          displayMessage('user2-messages', data.message, false);
        });
        
        // Xử lý sự kiện nhận bản dịch
        socket1.on('translation_received', (data) => {
          addDebugMessage(`User 1 received translation: ${JSON.stringify(data)}`);
        });
        
        socket2.on('translation_received', (data) => {
          addDebugMessage(`User 2 received translation: ${JSON.stringify(data)}`);
        });
        
        // Xử lý các sự kiện lỗi và ngắt kết nối
        socket1.on('disconnect', () => {
          addDebugMessage('User 1 disconnected from server');
          document.getElementById('user1-status').textContent = 'Disconnected';
        });
        
        socket2.on('disconnect', () => {
          addDebugMessage('User 2 disconnected from server');
          document.getElementById('user2-status').textContent = 'Disconnected';
        });
        
        socket1.on('error', (error) => {
          addDebugMessage(`User 1 error: ${JSON.stringify(error)}`);
        });
        
        socket2.on('error', (error) => {
          addDebugMessage(`User 2 error: ${JSON.stringify(error)}`);
        });
        
        updateStatus('ws', 'success');
      } catch (err) {
        addDebugMessage(`Failed to connect to WebSocket: ${err.message}`);
        updateStatus('ws', 'error', `Failed to connect to WebSocket: ${err.message}`);
      }
    }
    
    // Gửi tin nhắn từ User 1
    document.getElementById('user1-send').addEventListener('click', () => {
      sendMessageUser1();
    });
    
    document.getElementById('user1-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessageUser1();
      }
    });
    
    // Gửi tin nhắn từ User 2
    document.getElementById('user2-send').addEventListener('click', () => {
      sendMessageUser2();
    });
    
    document.getElementById('user2-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessageUser2();
      }
    });
    
    // Hàm gửi tin nhắn từ User 1
    function sendMessageUser1() {
      const input = document.getElementById('user1-input');
      const content = input.value.trim();
      
      if (!content || !socket1 || !currentRoomId) {
        return;
      }
      
      addDebugMessage(`User 1 sending message: ${content}`);
      
      // Đầu tiên thử gửi thông qua socket
      socket1.emit('send_message', {
        roomId: currentRoomId,
        content,
        originalLanguage: 'vi'
      }, (response) => {
        if (response.success) {
          addDebugMessage('User 1 sent message successfully');
          displayMessage('user1-messages', response.data.message, true);
          input.value = '';
        } else {
          addDebugMessage(`User 1 failed to send message: ${response.error}`);
          
          // Nếu việc gửi qua socket thất bại, thử lưu trực tiếp vào cơ sở dữ liệu
          saveMessageDirectlyToDb(userId, null, currentRoomId, content, 'vi');
        }
      });
    }
    
    // Hàm gửi tin nhắn từ User 2
    function sendMessageUser2() {
      const input = document.getElementById('user2-input');
      const content = input.value.trim();
      
      if (!content || !socket2 || !currentRoomId) {
        return;
      }
      
      addDebugMessage(`User 2 sending message: ${content}`);
      
      // Đầu tiên thử gửi thông qua socket
      socket2.emit('send_message', {
        roomId: currentRoomId,
        content,
        originalLanguage: 'en'
      }, (response) => {
        if (response.success) {
          addDebugMessage('User 2 sent message successfully');
          displayMessage('user2-messages', response.data.message, true);
          input.value = '';
        } else {
          addDebugMessage(`User 2 failed to send message: ${response.error}`);
          
          // Nếu việc gửi qua socket thất bại, thử lưu trực tiếp vào cơ sở dữ liệu
          saveMessageDirectlyToDb('a9813ae-9a46-4dc9-9fa3-6f04062f7e50', null, currentRoomId, content, 'en');
        }
      });
    }
    
    // Hàm lưu tin nhắn trực tiếp vào cơ sở dữ liệu
    async function saveMessageDirectlyToDb(userId, temporaryUserId, roomId, content, originalLanguage) {
      if (!supabaseClient) {
        addDebugMessage('Error sending message directly to database: Supabase not connected');
        return;
      }
      
      try {
        const { data, error } = await supabaseClient
          .from('chat_messages')
          .insert([{
            chat_room_id: roomId,
            sender_id: userId,
            temporary_sender_id: temporaryUserId,
            content,
            original_language: originalLanguage,
            sent_at: new Date().toISOString()
          }])
          .select();
          
        if (error) {
          addDebugMessage(`Failed to save message directly: ${error.message}`);
        } else {
          addDebugMessage('Message saved directly to database');
          const sender = userId === document.getElementById('user1-input') ? 'user1' : 'user2';
          displayMessage(`${sender}-messages`, data[0], true);
          document.getElementById(`${sender}-input`).value = '';
        }
      } catch (err) {
        addDebugMessage(`Error saving message directly: ${err.message}`);
      }
    }
    
    // Hàm hiển thị tin nhắn trong giao diện chat
    function displayMessage(containerId, message, isSent) {
      const messagesContainer = document.getElementById(containerId);
      const messageEl = document.createElement('div');
      messageEl.className = `message ${isSent ? 'sent' : 'received'}`;
      
      // Nội dung tin nhắn
      const contentEl = document.createElement('div');
      contentEl.textContent = message.content;
      messageEl.appendChild(contentEl);
      
      // Thông tin về thời gian và ngôn ngữ
      const infoEl = document.createElement('div');
      infoEl.style.fontSize = '0.8em';
      infoEl.style.color = '#666';
      infoEl.textContent = `[${message.original_language}] ${new Date(message.sent_at).toLocaleTimeString()}`;
      messageEl.appendChild(infoEl);
      
      messagesContainer.appendChild(messageEl);
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Khởi tạo giao diện
    document.addEventListener('DOMContentLoaded', () => {
      // Hiển thị/ẩn console debug
      document.getElementById('show-debug').addEventListener('change', (e) => {
        document.getElementById('debug-console').style.display = e.target.checked ? 'block' : 'none';
      });
    });
  </script>
</body>
</html>