import { colors } from '../primitives/colors';

export const studioTheme = {
  colors: {
    // Brand colors
    primary: colors.orange['500'],
    secondary: colors.gray['50'],
    accent: colors.blue['600'], // Cerulean
    
    // Text colors
    text: {
      primary: colors.white,
      secondary: colors.gray['400'],
      disabled: colors.gray['600'],
      inverse: colors.dark['975']
    },
    
    // Background colors
    background: {
      primary: colors.dark['800'], // Gunmetal
      secondary: colors.dark['850'], // Charcoal
      tertiary: colors.blue['600'], // Cerulean
      inverse: colors.white
    },
    
    // Border colors
    border: {
      primary: colors.dark['850'],
      secondary: colors.blue['600'],
      focus: colors.orange['500']
    },
    
    // Status colors
    status: {
      success: colors.success.light,
      info: colors.info.light,
      warning: colors.warning.light,
      error: colors.error.light
    },
    
    // Specific components
    button: {
      primary: {
        background: colors.orange['500'],
        text: colors.white,
        hover: '#E64500',
        active: '#CC3D00'
      },
      secondary: {
        background: colors.blue['600'],
        text: colors.white,
        hover: '#366F7D',
        active: '#2F6271'
      },
      accent: {
        background: colors.gray['50'],
        text: colors.dark['975'],
        hover: '#DEDEDE',
        active: '#D1D1D1'
      },
      outline: {
        border: colors.gray['50'],
        text: colors.gray['50'],
        hover: 'rgba(235, 235, 235, 0.1)',
        active: 'rgba(235, 235, 235, 0.2)'
      }
    },
    
    // Card colors
    card: {
      background: colors.dark['850'],
      border: colors.dark['800'],
      headerBackground: colors.blue['600']
    },
    
    // Form elements
    input: {
      background: colors.dark['850'],
      border: colors.dark['800'],
      text: colors.white,
      placeholder: colors.gray['600'],
      focus: {
        border: colors.orange['500']
      },
      error: {
        border: colors.error.light,
        text: colors.error.light
      }
    }
  }
};
