@import '../styles/_variables';

.container {
  width: 100%;
  max-width: 1200px;
  padding: 24px;
  margin: 0 auto;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
}

.pageTitle {
  font-size: 24px;
  font-weight: 600;
  color: $black;
  margin: 0;
}

.addButton {
  display: flex;
  align-items: center;
  background-color: $primary-color;
  color: white;
  padding: 8px 16px;
  border-radius: $border-radius-md;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: darken($primary-color, 10%);
  }

  svg {
    margin-right: 8px;
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: $primary-color;
  animation: spin 1s linear infinite;
  margin-bottom: $spacing-md;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.filterBar {
  background-color: white;
  border-radius: $border-radius-md;
  padding: $spacing-md;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-sm;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
  }
}

.searchBox {
  position: relative;
  flex: 1;
  
  input {
    width: 100%;
    padding: 10px 10px 10px 40px;
    border: 1px solid #ddd;
    border-radius: $border-radius-sm;
    font-size: 14px;
    outline: none;
    
    &:focus {
      border-color: $primary-color;
    }
  }
  
  svg {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
  }
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-md;
  align-items: center;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  label {
    font-size: 12px;
    color: $gray;
  }
  
  input, select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: $border-radius-sm;
    min-width: 120px;
    outline: none;
    
    &:focus {
      border-color: $primary-color;
    }
  }
}

.filterButton {
  background-color: $primary-color;
  color: white;
  border: none;
  border-radius: $border-radius-sm;
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 37px;
  margin-top: auto;
  
  &:hover {
    background-color: darken($primary-color, 10%);
  }
}

.guestList {
  background-color: white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  overflow: hidden;
  margin-bottom: $spacing-xl;
}

.guestTable {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: $spacing-md;
    text-align: left;
    border-bottom: 1px solid #eee;
  }
  
  th {
    font-weight: 600;
    color: $dark-gray;
    background-color: #f9f9f9;
  }
  
  tr:last-child td {
    border-bottom: none;
  }
  
  .inactiveRow {
    background-color: #f9f9f9;
    color: $gray;
  }
}

.guestInfo {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.guestAvatar {
  width: 40px;
  height: 40px;
  background-color: $primary-color;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.guestFullName {
  font-weight: 500;
  margin: 0;
  color: $dark-gray;
}

.guestEmail, .guestPhone {
  margin: 2px 0 0;
  font-size: 12px;
  color: $gray;
}

.guestName {
  text-decoration: none;
  color: inherit;
  &:hover .guestFullName {
    color: $primary-color;
    text-decoration: underline;
  }
}

.status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: $border-radius-sm;
  font-size: 12px;
  font-weight: 500;
  
  &.active {
    background-color: #e6f7ed;
    color: #00a651;
  }
  
  &.inactive {
    background-color: #f5f5f5;
    color: $gray;
  }
}

.actions {
  display: flex;
  gap: 8px;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: none;
  border: 1px solid #ddd;
  cursor: pointer;
  color: $gray;
  transition: all 0.2s;
  text-decoration: none;
  
  &:hover {
    border-color: $primary-color;
    color: $primary-color;
    background-color: #f9f9f9;
  }
}

.noResults {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  color: $gray;
  text-align: center;
  
  svg {
    margin-bottom: $spacing-md;
    opacity: 0.5;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: $spacing-md;
}

.paginationButton {
  display: flex;
  align-items: center;
  background: none;
  border: 1px solid #ddd;
  padding: 6px 12px;
  border-radius: $border-radius-sm;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  color: $dark-gray;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &:not(:disabled):hover {
    border-color: $primary-color;
    color: $primary-color;
  }
  
  svg {
    margin-right: 4px;
    
    &:last-child {
      margin-right: 0;
      margin-left: 4px;
    }
  }
}

.paginationPages {
  display: flex;
  gap: 4px;
}

.paginationPage {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: $border-radius-sm;
  border: none;
  background: none;
  cursor: pointer;
  color: $dark-gray;
  font-size: 14px;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &.active {
    background-color: $primary-color;
    color: white;
  }
}
.deleteButton {
  &:hover {
    color: #d63c00;
    border-color: #d63c00;
  }
}