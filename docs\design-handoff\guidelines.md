# Guidelines cho Design Handoff

## Naming Convention

### Components
- Button: `button-[variant]-[size]` (ví dụ: button-primary-md)
- Input: `input-[variant]-[state]` (ví dụ: input-outlined-focus)
- Card: `card-[variant]` (ví dụ: card-highlight)
- Icon: `icon-[name]-[size]` (ví dụ: icon-home-24)

### Layers
- Container: `container-[name]`
- Text: `text-[type]` (ví dụ: text-heading, text-body)
- Group: `group-[name]`

## Spacing
- Tuân thủ spacing scale: 4px, 8px, 12px, 16px, 20px, 24px, 32px, 40px, 48px, 64px
- Padding containers nên là bội số của 8px
- Khoảng cách giữa các element nên là bội số của 4px

## Colors
- Sử dụng color tokens từ design system, không sử dụng hard-coded colors
- Đối với text, sử dụng text.primary, text.secondary, text.disabled
- Đối với background, sử dụng background.primary, background.secondary
- Đ<PERSON>i với borders, sử dụng border.primary, border.secondary

## Typography
- Headings: h1, h2, h3, h4, h5
- Body: body-regular, body-medium, body-small
- UI Labels: button-label, caption-label, small-label

## Metadata
- Đặt tên cho màn hình: `[feature]-[screen-name]`
- Thêm description cho component để mô tả behavior
- Đánh dấu các states khác nhau: default, hover, active, disabled, focus
- Chỉ rõ độ responsive cho các component và layout
