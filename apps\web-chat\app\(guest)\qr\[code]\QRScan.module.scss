// QR <PERSON>an Page Styles
$primary-color: #f97316;
$primary-dark: #ea580c;
$success-color: #10b981;
$error-color: #ef4444;
$text-color: #111827;
$text-light: #6b7280;
$text-muted: #9ca3af;
$background: #ffffff;
$surface: #f8fafc;
$border: #e5e7eb;
$radius: 0.75rem;
$radius-lg: 1rem;
$shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
$transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: linear-gradient(135deg, $surface 0%, $background 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

// Loading Card
.loadingCard {
  background: $background;
  border-radius: $radius-lg;
  padding: 3rem 2rem;
  box-shadow: $shadow-lg;
  text-align: center;
  max-width: 400px;
  width: 100%;
  border: 1px solid $border;
  
  .spinner {
    margin: 0 auto 2rem;
    width: 60px;
    height: 60px;
    position: relative;
  }
  
  .spinnerRing {
    width: 100%;
    height: 100%;
    border: 4px solid rgba(249, 115, 22, 0.2);
    border-top: 4px solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .title {
    font-size: 1.5rem;
    font-weight: 700;
    color: $text-color;
    margin-bottom: 0.5rem;
  }
  
  .subtitle {
    color: $text-light;
    margin-bottom: 2rem;
    font-size: 0.9rem;
  }
  
  .qrCode {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem;
    background: $surface;
    border-radius: $radius;
    border: 1px solid $border;
    
    .qrIcon {
      font-size: 1.5rem;
    }
    
    .codeValue {
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 0.875rem;
      color: $text-color;
      background: $background;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      border: 1px solid $border;
    }
  }
}

// Success Card
.successCard {
  background: $background;
  border-radius: $radius-lg;
  padding: 3rem 2rem;
  box-shadow: $shadow-lg;
  text-align: center;
  max-width: 400px;
  width: 100%;
  border: 1px solid $border;
  
  .successIcon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    animation: bounce 0.6s ease-in-out;
  }
  
  .successTitle {
    font-size: 1.5rem;
    font-weight: 700;
    color: $success-color;
    margin-bottom: 2rem;
  }
  
  .qrInfo {
    text-align: left;
    margin-bottom: 2rem;
    
    .infoItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid $border;
      
      &:last-child {
        border-bottom: none;
      }
      
      .infoLabel {
        font-weight: 600;
        color: $text-light;
        font-size: 0.875rem;
      }
      
      .infoValue {
        font-weight: 600;
        color: $text-color;
        font-size: 0.875rem;
      }
    }
  }
  
  .redirectMessage {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    
    .loadingDots {
      display: flex;
      gap: 0.25rem;
      
      span {
        width: 0.5rem;
        height: 0.5rem;
        background: $primary-color;
        border-radius: 50%;
        animation: loadingDot 1.4s infinite;
        
        &:nth-child(1) { animation-delay: 0s; }
        &:nth-child(2) { animation-delay: 0.2s; }
        &:nth-child(3) { animation-delay: 0.4s; }
      }
    }
    
    p {
      color: $text-light;
      font-size: 0.9rem;
      margin: 0;
    }
  }
}

// Error Card
.errorCard {
  background: $background;
  border-radius: $radius-lg;
  padding: 3rem 2rem;
  box-shadow: $shadow-lg;
  text-align: center;
  max-width: 400px;
  width: 100%;
  border: 1px solid $border;
  
  .errorIcon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    animation: shake 0.5s ease-in-out;
  }
  
  .errorTitle {
    font-size: 1.5rem;
    font-weight: 700;
    color: $error-color;
    margin-bottom: 1rem;
  }
  
  .errorMessage {
    color: $text-light;
    margin-bottom: 2rem;
    font-size: 0.9rem;
    line-height: 1.5;
  }
  
  .errorActions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }
}

// Buttons
.button {
  padding: 0.75rem 1.5rem;
  border-radius: $radius;
  font-weight: 600;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  transition: $transition;
  min-width: 120px;
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.buttonPrimary {
  background: linear-gradient(135deg, $primary-color, $primary-dark);
  color: white;
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
  
  &:hover {
    box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4);
  }
}

.buttonSecondary {
  background: $surface;
  color: $text-color;
  border: 1px solid $border;
  
  &:hover {
    background: $background;
    box-shadow: $shadow;
  }
}

// Animations
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -20px, 0);
  }
  70% {
    transform: translate3d(0, -10px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

@keyframes loadingDot {
  0%, 60%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.3);
  }
}

// Responsive Design
@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }
  
  .loadingCard,
  .successCard,
  .errorCard {
    padding: 2rem 1.5rem;
    margin: 0 0.5rem;
  }
  
  .errorActions {
    flex-direction: column;
    
    .button {
      width: 100%;
    }
  }
}
