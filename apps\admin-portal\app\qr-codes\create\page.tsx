'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import styles from './create-qrcode.module.scss';

import DashboardLayout from '../../dashboard-layout';
import QrCodeForm from '../../components/qr-codes/QrCodeForm';

export default function CreateQrCodePage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [newQrCode, setNewQrCode] = useState<any>(null);
  
  // Handle form submission
  const handleSubmit = async (formData: any) => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      const response = await fetch('/api/qr-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create QR code');
      }
      
      const data = await response.json();
      setNewQrCode(data.data);
      setSuccess(true);
    } catch (err: any) {
      console.error('Error creating QR code:', err);
      setError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleCreateAnother = () => {
    setSuccess(false);
    setNewQrCode(null);
  };

  return (
    <DashboardLayout>
      <div className={styles.container}>
        {!success ? (
          <>
            <div className={styles.header}>
              <Link href="/qr-codes" className={styles.backButton}>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M10.6667 2.66667L5.33333 8.00001L10.6667 13.3333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Back to QR Codes
              </Link>
              <h1 className={styles.title}>Create New QR Code</h1>
            </div>
            
            {error && (
              <div className={styles.error}>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <circle cx="8" cy="8" r="7" stroke="currentColor" strokeWidth="1.5" />
                  <path d="M8 5V8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                  <path d="M8 11L8 11.01" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                </svg>
                <span>{error}</span>
              </div>
            )}
            
            <QrCodeForm 
              onSubmit={handleSubmit}
              isSubmitting={isSubmitting}
            />
          </>
        ) : (
          <div className={styles.successContainer}>
            <div className={styles.successContent}>
              <div className={styles.successIcon}>
                <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                  <circle cx="24" cy="24" r="24" fill="#DCFCE7" />
                  <path d="M32 19L22 29L17 24" stroke="#16A34A" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
              <h2 className={styles.successTitle}>QR Code Created Successfully!</h2>
              <p className={styles.successMessage}>
                Your QR code has been created and is now ready to use.
              </p>
              
              {newQrCode && (
                <div className={styles.qrCodePreview}>
                  <img 
                    src={`/api/qr-codes/${newQrCode.id}/image?size=200`}
                    alt="Generated QR Code"
                    width={200}
                    height={200}
                  />
                  <div className={styles.qrCodeDetails}>
                    <div className={styles.qrCodeName}>{newQrCode.name}</div>
                    {newQrCode.description && (
                      <div className={styles.qrCodeDescription}>{newQrCode.description}</div>
                    )}
                    <div className={styles.qrCodeDownload}>
                      <a 
                        href={`/api/qr-codes/${newQrCode.id}/download`}
                        download={`qrcode-${newQrCode?.name?.toLowerCase().replace(/\s+/g, '-') || 'qrcode'}.png`}
                        className={styles.downloadButton}
                      >
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path d="M14 10V12.6667C14 13.0203 13.8595 13.3594 13.6095 13.6095C13.3594 13.8595 13.0203 14 12.6667 14H3.33333C2.97971 14 2.64057 13.8595 2.39052 13.6095C2.14048 13.3594 2 13.0203 2 12.6667V10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M4.66667 6.66667L8 10L11.3333 6.66667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M8 10V2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        Download QR Code
                      </a>
                    </div>
                  </div>
                </div>
              )}
              
              <div className={styles.successActions}>
                <button 
                  className={styles.viewButton}
                  onClick={() => router.push(`/qr-codes/${newQrCode?.id}`)}
                >
                  View QR Code
                </button>
                <button 
                  className={styles.createButton}
                  onClick={handleCreateAnother}
                >
                  Create Another
                </button>
                <button 
                  className={styles.backToListButton}
                  onClick={() => router.push('/qr-codes')}
                >
                  Back to List
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
