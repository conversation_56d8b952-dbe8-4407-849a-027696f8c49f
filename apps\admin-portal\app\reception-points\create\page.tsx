'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '../../dashboard-layout';
import styles from './create-point.module.scss';
import { Alert } from '@ui';
import ReceptionPointForm from '../../components/reception-points/ReceptionPointForm';

export default function CreateReceptionPointPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (formData: any) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/reception-points', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to create reception point');
      }
      
      // Navigate to the reception points list on success
      router.push('/reception-points');
    } catch (err: any) {
      console.error('Error creating reception point:', err);
      setError(err.message || 'An error occurred while creating the reception point');
      // Don't set loading to false here, the form component will handle it
      throw err; // Re-throw to let the form component know about the error
    }
  };
  
  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Link href="/reception-points" className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" >
              <path d="M15.8333 10H4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M10 15.8333L4.16667 10L10 4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to Reception Points
          </Link>
          <h1 className={styles.title}>Create Reception Point</h1>
        </div>
        
        {error && (
          <Alert variant="error" title="Error" closable onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        <div className={styles.formContainer}>
          <ReceptionPointForm
            onSubmit={handleSubmit}
            isSubmitting={loading}
          />
        </div>
      </div>
    </DashboardLayout>
  );
}
