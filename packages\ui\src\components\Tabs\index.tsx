import React, { createContext, useContext, useState, ReactNode } from 'react';
import styles from './Tabs.module.scss';

interface TabsContextType {
  activeTab: number;
  setActiveTab: (index: number) => void;
}

const TabsContext = createContext<TabsContextType | undefined>(undefined);

export const useTabs = () => {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('Tabs compound components must be used within a Tabs component');
  }
  return context;
};

interface TabsProps {
  children: ReactNode;
  defaultIndex?: number;
  className?: string;
}

export const Tabs: React.FC<TabsProps> = ({ 
  children, 
  defaultIndex = 0,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState(defaultIndex);
  
  return (
    <TabsContext.Provider value={{ activeTab, setActiveTab }}>
      <div className={`${styles.tabs} ${className}`}>
        {children}
      </div>
    </TabsContext.Provider>
  );
};

interface TabListProps {
  children: ReactNode;
  className?: string;
}

export const TabList: React.FC<TabListProps> = ({ children, className = '' }) => {
  return (
    <div className={`${styles.tabList} ${className}`}>
      {children}
    </div>
  );
};

interface TabProps {
  children: ReactNode;
  index?: number;
  className?: string;
}

export const Tab: React.FC<TabProps> = ({ 
  children, 
  index,
  className = ''
}) => {
  const { activeTab, setActiveTab } = useTabs();
  
  // If index is not provided, try to determine it from the parent
  const handleClick = () => {
    if (typeof index === 'number') {
      setActiveTab(index);
    }
  };
  
  const isActive = typeof index === 'number' && activeTab === index;
  
  return (
    <button 
      className={`${styles.tab} ${isActive ? styles.active : ''} ${className}`} 
      onClick={handleClick}
      role="tab"
      aria-selected={isActive}
    >
      {children}
    </button>
  );
};

interface TabPanelsProps {
  children: ReactNode;
  className?: string;
}

export const TabPanels: React.FC<TabPanelsProps> = ({ children, className = '' }) => {
  return (
    <div className={`${styles.tabPanels} ${className}`}>
      {children}
    </div>
  );
};

interface TabPanelProps {
  children: ReactNode;
  index?: number;
  className?: string;
}

export const TabPanel: React.FC<TabPanelProps> = ({ 
  children, 
  index,
  className = ''
}) => {
  const { activeTab } = useTabs();
  
  if (typeof index === 'number' && activeTab !== index) {
    return null;
  }
  
  return (
    <div 
      className={`${styles.tabPanel} ${className}`}
      role="tabpanel"
    >
      {children}
    </div>
  );
};
