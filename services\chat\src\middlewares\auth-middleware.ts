export const authenticate = (req: Request, res: Response, next: NextFunction) => {
  try {
    // TEMP: Thê<PERSON> thông tin người dùng giả để kiểm tra
    // @ts-ignore
    req.user = {
      userId: '973c8e99-9b06-437a-9ab5-e4bdf2aa4b53',
      email: '<EMAIL>',
      roles: ['admin', 'staff']
    };
    
    next();
    
    // Đoạn code bên dưới sẽ được sử dụng sau khi đã kiểm tra xong
    /*
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      const temporaryUserId = req.body.temporary_user_id || req.query.temporary_user_id;
      
      if (temporaryUserId) {
        // @ts-ignore
        req.temporaryUser = { id: temporaryUserId };
        return next();
      }
      
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }
    
    const token = authHeader.split(' ')[1];
    const decoded = verifyToken(token);
    
    if (!decoded) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }
    
    // @ts-ignore
    req.user = decoded;
    
    next();
    */
  } catch (error) {
    console.error('Authentication middleware error:', error);
    // TEMP: Cho phép truy cập ngay cả khi có lỗi
    next();
  }
};
