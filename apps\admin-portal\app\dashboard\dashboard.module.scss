@import '../styles/_variables.scss';

.dashboardContainer {
  padding: $spacing-md;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
}

.dashboardTitle {
  font-size: 24px;
  font-weight: 600;
  color: $black;
  margin: 0;
}

.refreshButton {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  cursor: pointer;
  color: $primary-color;
  font-size: 14px;
  transition: all 0.2s ease;
  
  &:hover {
    opacity: 0.8;
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: $primary-color;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: $spacing-md;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.errorContainer {
  margin: $spacing-xl auto;
  max-width: 500px;
}

.actionButtons {
  display: flex;
  justify-content: center;
  gap: $spacing-md;
  margin-top: $spacing-lg;
}

.primaryButton {
  display: inline-block;
  padding: 10px 20px;
  background-color: $primary-color;
  color: $white;
  border-radius: $border-radius-md;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  text-transform: uppercase;
  
  &:hover {
    opacity: 0.9;
  }
}

.secondaryButton {
  display: inline-block;
  padding: 10px 20px;
  background-color: $secondary-color;
  color: $black;
  border-radius: $border-radius-md;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  text-transform: uppercase;
  
  &:hover {
    opacity: 0.9;
  }
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
}

.statsGridHalf {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;
  margin-bottom: $spacing-lg;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.statCard {
  background-color: $white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  overflow: hidden;
}

.statCardHeader {
  padding: $spacing-md;
  border-bottom: 1px solid $secondary-color;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
}

.statCardBody {
  padding: $spacing-md;
}

.statItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: $spacing-sm;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.statLabel {
  font-weight: 500;
  color: $gray;
}

.statValue {
  font-weight: 500;
  
  &.active {
    color: #10b981;
  }
  
  &.inactive {
    color: #ef4444;
  }
  
  &.good {
    color: #10b981;
  }
  
  &.warning {
    color: #f59e0b;
  }
  
  &.danger {
    color: #ef4444;
  }
}

.statusItem {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-sm;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.statusIndicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: $spacing-sm;
  
  &.operational {
    background-color: #10b981;
  }
  
  &.degraded {
    background-color: #f59e0b;
  }
  
  &.down {
    background-color: #ef4444;
  }
}

.statusLabel {
  font-weight: 500;
  color: $gray;
  margin-right: $spacing-sm;
}

.statusValue {
  font-weight: 500;
}

.actionGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;
  padding: $spacing-md;
}

.actionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  background-color: $secondary-color;
  color: $black;
  border-radius: $border-radius-md;
  text-decoration: none;
  text-align: center;
  transition: all 0.2s ease;
  
  svg {
    margin-bottom: $spacing-sm;
  }
  
  &:hover {
    background-color: darken($secondary-color, 5%);
    transform: translateY(-2px);
  }
}

.alertAction {
  margin-top: $spacing-md;
}

.alertButton {
  display: inline-block;
  padding: 8px 16px;
  background-color: $white;
  color: $primary-color;
  border: 1px solid $primary-color;
  border-radius: $border-radius-sm;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: $primary-color;
    color: $white;
  }
}
.errorState {
  color: #dc2626;
  text-align: center;
  padding: 10px;
  
  .retryButton {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    
    &:hover {
      background-color: #e5e7eb;
    }
  }
}

.emptyState {
  color: #6b7280;
  text-align: center;
  padding: 10px;
}