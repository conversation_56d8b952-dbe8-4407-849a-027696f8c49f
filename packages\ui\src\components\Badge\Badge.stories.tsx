import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Badge } from './index';

const meta = {
  title: 'UI/Badge',
  component: Badge,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'accent', 'outline'],
      description: 'Badge variant',
    },
    size: {
      control: 'select',
      options: ['small', 'medium'],
      description: 'Size of the badge',
    },
    rounded: {
      control: 'boolean',
      description: 'Make the badge rounded',
    },
    label: {
      control: 'text',
      description: 'Badge text',
    },
    icon: {
      control: { disable: true },
      description: 'Optional icon to display before label',
    },
  },
} satisfies Meta<typeof Badge>;

export default meta;
type Story = StoryObj<typeof meta>;

// Simple icon component for demo
const CircleIcon = () => (
  <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="4" cy="4" r="4" fill="currentColor" />
  </svg>
);

export const Primary: Story = {
  args: {
    label: 'Primary',
    variant: 'primary',
  },
};

export const Secondary: Story = {
  args: {
    label: 'Secondary',
    variant: 'secondary',
  },
};

export const Accent: Story = {
  args: {
    label: 'Accent',
    variant: 'accent',
  },
};

export const Outline: Story = {
  args: {
    label: 'Outline',
    variant: 'outline',
  },
};

export const Small: Story = {
  args: {
    label: 'Small',
    size: 'small',
  },
};

export const Rounded: Story = {
  args: {
    label: 'Rounded',
    rounded: true,
  },
};

export const WithIcon: Story = {
  args: {
    label: 'With Icon',
    icon: <CircleIcon />,
  },
};

export const BadgeShowcase: Story = {
  name: 'All Badges',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
        <Badge label="Primary" variant="primary" />
        <Badge label="Secondary" variant="secondary" />
        <Badge label="Accent" variant="accent" />
        <Badge label="Outline" variant="outline" />
      </div>
      
      <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
        <Badge label="Small Primary" variant="primary" size="small" />
        <Badge label="Small Secondary" variant="secondary" size="small" />
        <Badge label="Small Accent" variant="accent" size="small" />
        <Badge label="Small Outline" variant="outline" size="small" />
      </div>
      
      <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
        <Badge label="Rounded" variant="primary" rounded />
        <Badge label="Rounded" variant="secondary" rounded />
        <Badge label="Rounded" variant="accent" rounded />
        <Badge label="Rounded" variant="outline" rounded />
      </div>
      
      <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
        <Badge label="With Icon" variant="primary" icon={<CircleIcon />} />
        <Badge label="With Icon" variant="secondary" icon={<CircleIcon />} />
        <Badge label="With Icon" variant="accent" icon={<CircleIcon />} />
        <Badge label="With Icon" variant="outline" icon={<CircleIcon />} />
      </div>
      
      <div style={{ padding: '16px', backgroundColor: '#161616' }}>
        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
          <Badge label="On Dark" variant="primary" />
          <Badge label="On Dark" variant="secondary" />
          <Badge label="On Dark" variant="accent" />
          <Badge label="On Dark" variant="outline" />
        </div>
      </div>
    </div>
  ),
};

export const StatusBadges: Story = {
  name: 'Status Badges',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <Badge 
        label="Active" 
        variant="primary" 
        rounded 
        icon={<CircleIcon />} 
      />
      <Badge 
        label="Pending" 
        variant="accent" 
        rounded 
        icon={<CircleIcon />} 
        style={{ backgroundColor: '#f59e0b' }} 
      />
      <Badge 
        label="Completed" 
        variant="accent" 
        rounded 
        icon={<CircleIcon />} 
        style={{ backgroundColor: '#10b981' }} 
      />
      <Badge 
        label="Cancelled" 
        variant="secondary" 
        rounded 
        icon={<CircleIcon />} 
        style={{ color: '#ef4444' }} 
      />
    </div>
  ),
};

export const NotificationBadges: Story = {
  name: 'Notification Badges',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <div style={{ position: 'relative', width: 'fit-content' }}>
        <div style={{ 
          padding: '8px', 
          backgroundColor: '#f5f5f5', 
          borderRadius: '4px',
          width: '32px',
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <span>🔔</span>
        </div>
        <Badge 
          label="3" 
          variant="primary" 
          size="small" 
          rounded 
          style={{ 
            position: 'absolute', 
            top: '-5px', 
            right: '-5px',
            minWidth: '16px',
            height: '16px'
          }} 
        />
      </div>
      
      <div style={{ position: 'relative', width: 'fit-content' }}>
        <div style={{ 
          padding: '8px', 
          backgroundColor: '#f5f5f5', 
          borderRadius: '4px',
          width: '32px',
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <span>✉️</span>
        </div>
        <Badge 
          label="New" 
          variant="accent" 
          size="small" 
          style={{ 
            position: 'absolute', 
            top: '-5px', 
            right: '-20px',
          }} 
        />
      </div>
    </div>
  ),
};
