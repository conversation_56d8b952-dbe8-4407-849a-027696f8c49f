.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 24px;
}

.errorContent {
  max-width: 600px;
  text-align: center;
  
  h2 {
    font-size: 1.5rem;
    margin-bottom: 12px;
    color: var(--color-text);
  }
  
  p {
    color: var(--color-text-secondary);
    margin-bottom: 24px;
  }
}

.errorDetails {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 24px;
  text-align: left;
  font-family: monospace;
}

.errorMessage {
  color: #d32f2f;
  word-break: break-word;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}
