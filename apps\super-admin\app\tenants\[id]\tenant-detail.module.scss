.tenantDetail {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tenantInfo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logoContainer {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background-secondary);
  border: 1px solid var(--color-border);
}

.logo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.tenantName {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--color-text);
}

.tenantMeta {
  display: flex;
  gap: 8px;
}

.domainBadge {
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: var(--color-background-secondary);
  color: var(--color-text-secondary);
}

.planBadge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  
  &.free {
    background-color: var(--color-background-secondary);
    color: var(--color-text-secondary);
  }
  
  &.basic {
    background-color: #E3F2FD;
    color: #1565C0;
  }
  
  &.premium {
    background-color: #FFF8E1;
    color: #F57F17;
  }
  
  &.enterprise {
    background-color: #E8F5E9;
    color: #2E7D32;
  }
}

.headerActions {
  display: flex;
  gap: 8px;
}

.formActions {
  display: flex;
  gap: 8px;
}

.tabContainer {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
}

.tabHeader {
  display: flex;
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-background);
}

.tabButton {
  padding: 12px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-secondary);
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  
  &:hover {
    color: var(--color-text);
    background-color: var(--color-background-hover);
  }
  
  &.active {
    color: var(--color-primary);
    border-bottom-color: var(--color-primary);
  }
}

.tabContent {
  background-color: var(--color-background);
  padding: 24px 0;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--color-text);
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 24px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.fullWidth {
  grid-column: span 2;
  
  @media (max-width: 768px) {
    grid-column: span 1;
  }
}

.colorField {
  display: flex;
  align-items: center;
  gap: 8px;
}

.colorPreview {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid var(--color-border);
}

.metaInfo {
  display: flex;
  gap: 24px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.metaItem {
  display: flex;
  gap: 4px;
}

.metaLabel {
  font-weight: 500;
}

.licenseDetails {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.licenseSection {
  h3 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 12px 0;
    color: var(--color-text);
  }
}

.licenseInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.licenseItem {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  
  .licenseLabel {
    font-weight: 500;
    color: var(--color-text-secondary);
  }
  
  .licenseValue {
    color: var(--color-text);
    
    &.free {
      color: var(--color-text-secondary);
    }
    
    &.basic {
      color: #1565C0;
    }
    
    &.premium {
      color: #F57F17;
    }
    
    &.enterprise {
      color: #2E7D32;
    }
  }
}

.featureList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.featureItem {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  
  .featureName {
    font-weight: 500;
    color: var(--color-text-secondary);
  }
  
  .featureValue {
    color: var(--color-text);
  }
}

.licenseCta {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border);
}

.dangerZone {
  margin-top: 32px;
  padding: 16px;
  border: 1px dashed var(--color-error);
  border-radius: 8px;
  background-color: rgba(211, 47, 47, 0.05);
}

.dangerTitle {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-error);
  margin: 0 0 12px 0;
}

.dangerActions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dangerAction {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid rgba(211, 47, 47, 0.2);
  
  h4 {
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0 0 4px 0;
  }
  
  p {
    font-size: 0.75rem;
    color: var(--color-text-secondary);
    margin: 0;
    max-width: 400px;
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.notFound {
  text-align: center;
  padding: 64px 16px;
  
  h2 {
    font-size: 1.5rem;
    margin-bottom: 16px;
  }
  
  p {
    color: var(--color-text-secondary);
    margin-bottom: 24px;
  }
}

.actions {
  display: flex;
  gap: 8px;
}
