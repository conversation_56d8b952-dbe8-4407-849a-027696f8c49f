'use client';
import { useState, useEffect } from 'react';
import styles from './RoomSelectionModal.module.scss';

interface Room {
  id: string;
  room_number: string;
  room_type: string;
  floor: string;
  status: string;
}

interface RoomSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (roomNumber: string) => void;
  title: string;
  showUnavailableRooms?: boolean;
}

export default function RoomSelectionModal({
  isOpen,
  onClose,
  onConfirm,
  title = 'Select Room',
  showUnavailableRooms = false
}: RoomSelectionModalProps) {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedRoom, setSelectedRoom] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [filterFloor, setFilterFloor] = useState<string>('');
  const [filterType, setFilterType] = useState<string>('');

  useEffect(() => {
    if (isOpen) {
      fetchRooms();
    }
  }, [isOpen]);

  const fetchRooms = async () => {
    setLoading(true);
    setError(null);
    try {
      // Only fetch available rooms by default
      const statusFilter = !showUnavailableRooms ? 'available' : '';
      
      const response = await fetch(
        `/api/rooms?status=${statusFilter}&limit=100`
      );
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch rooms');
      }
      
      setRooms(result.data || []);
    } catch (err) {
      console.error('Error fetching rooms:', err);
      setError('Could not load rooms. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = () => {
    if (!selectedRoom) {
      setError('Please select a room first');
      return;
    }
    onConfirm(selectedRoom);
    onClose();
  };

  // Filter rooms based on selected filters
  const filteredRooms = rooms.filter(room => {
    if (filterFloor && room.floor !== filterFloor) return false;
    if (filterType && room.room_type !== filterType) return false;
    return true;
  });

  // Get unique floor and room type values for filters
  const floors = [...new Set(rooms.map(room => room.floor))];
  const roomTypes = [...new Set(rooms.map(room => room.room_type))];

  if (!isOpen) return null;

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modal}>
        <div className={styles.modalHeader}>
          <h2>{title}</h2>
          <button className={styles.closeButton} onClick={onClose}>×</button>
        </div>
        
        <div className={styles.modalContent}>
          {error && <div className={styles.error}>{error}</div>}
          
          <div className={styles.filters}>
            <div className={styles.filterGroup}>
              <label>Floor:</label>
              <select 
                value={filterFloor} 
                onChange={(e) => setFilterFloor(e.target.value)}
              >
                <option value="">All Floors</option>
                {floors.map(floor => (
                  <option key={floor} value={floor}>Floor {floor}</option>
                ))}
              </select>
            </div>
            
            <div className={styles.filterGroup}>
              <label>Room Type:</label>
              <select 
                value={filterType} 
                onChange={(e) => setFilterType(e.target.value)}
              >
                <option value="">All Types</option>
                {roomTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
          </div>
          
          {loading ? (
            <div className={styles.loading}>Loading rooms...</div>
          ) : filteredRooms.length === 0 ? (
            <div className={styles.noRooms}>
              No rooms available matching the selected filters
            </div>
          ) : (
            <div className={styles.roomList}>
              {filteredRooms.map((room) => (
                <div 
                  key={room.id} 
                  className={`
                    ${styles.roomItem} 
                    ${selectedRoom === room.room_number ? styles.selected : ''}
                    ${room.status !== 'available' ? styles.unavailable : ''}
                  `}
                  onClick={() => room.status === 'available' || showUnavailableRooms ? setSelectedRoom(room.room_number) : null}
                >
                  <div className={styles.roomNumber}>Room {room.room_number}</div>
                  <div className={styles.roomInfo}>
                    <span className={styles.roomType}>{room.room_type}</span>
                    <span className={styles.roomFloor}>Floor {room.floor}</span>
                  </div>
                  {room.status !== 'available' && (
                    <span className={styles.roomStatus}>{room.status}</span>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
        
        <div className={styles.modalActions}>
          <button 
            className={styles.cancelButton} 
            onClick={onClose}
          >
            Cancel
          </button>
          <button 
            className={styles.confirmButton} 
            onClick={handleConfirm}
            disabled={!selectedRoom || loading}
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
}
