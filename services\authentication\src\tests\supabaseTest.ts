import supabase from '../utils/supabase';

const testConnection = async () => {
  console.log('Testing Supabase connection...');
  
  try {
    // Thử truy vấn đơn gi<PERSON>n
    const { data, error, count } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });
    
    if (error) {
      console.error('Supabase connection error:', error);
    } else {
      console.log('✅ Supabase connection successful!');
      console.log('Count of users:', count);
    }
    
    // Check if tables exist
    const tables = ['users', 'roles', 'permissions', 'role_permissions', 'user_roles', 'temporary_users'];
    console.log('\nChecking if tables exist:');
    
    for (const table of tables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('*', { head: true });
        
        if (error) {
          console.error(`❌ Table '${table}' error:`, error.message);
        } else {
          console.log(`✅ Table '${table}' exists and is accessible`);
        }
      } catch (tableError) {
        console.error(`❌ Error checking table '${table}':`, tableError);
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

testConnection()
  .then(() => console.log('Test completed'))
  .catch(error => console.error('Test failed:', error));
