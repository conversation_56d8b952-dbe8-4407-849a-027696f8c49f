[{"table_name": "tenant_message_attachments", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO"}, {"table_name": "tenant_message_attachments", "column_name": "message_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_message_attachments", "column_name": "file_url", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_message_attachments", "column_name": "file_name", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_message_attachments", "column_name": "file_type", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_message_attachments", "column_name": "file_size", "data_type": "integer", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_message_attachments", "column_name": "mime_type", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_message_attachments", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_message_attachments", "column_name": "uploaded_by", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_message_attachments", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_translation_cache", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO"}, {"table_name": "tenant_translation_cache", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_translation_cache", "column_name": "source_text", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_translation_cache", "column_name": "source_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_translation_cache", "column_name": "target_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_translation_cache", "column_name": "translated_text", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_translation_cache", "column_name": "provider", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_translation_cache", "column_name": "confidence", "data_type": "numeric", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_translation_cache", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_translation_cache", "column_name": "used_count", "data_type": "integer", "character_maximum_length": null, "column_default": "1", "is_nullable": "YES"}, {"table_name": "tenant_translation_cache", "column_name": "last_used_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_translation_cache", "column_name": "source_text_hash", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO"}, {"table_name": "tenant_translation_settings", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_translation_settings", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "guest_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "session_id", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "default_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_translation_settings", "column_name": "auto_translate", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "target_languages", "data_type": "ARRAY", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "display_mode", "data_type": "character varying", "character_maximum_length": 20, "column_default": "'parallel'::character varying", "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "preferred_provider", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "provider", "data_type": "character varying", "character_maximum_length": 50, "column_default": "'google'::character varying", "is_nullable": "NO"}, {"table_name": "tenant_translation_settings", "column_name": "api_key_encrypted", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "supported_languages", "data_type": "jsonb", "character_maximum_length": null, "column_default": "'[]'::jsonb", "is_nullable": "NO"}, {"table_name": "tenant_translation_settings", "column_name": "default_guest_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": "'en'::character varying", "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "default_staff_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": "'en'::character varying", "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "auto_detect_language", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES"}, {"table_name": "tenant_translation_settings", "column_name": "translation_enabled", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES"}, {"table_name": "tenant_typing_status", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO"}, {"table_name": "tenant_typing_status", "column_name": "session_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_typing_status", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_typing_status", "column_name": "user_type", "data_type": "character varying", "character_maximum_length": 20, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_typing_status", "column_name": "is_typing", "data_type": "boolean", "character_maximum_length": null, "column_default": "false", "is_nullable": "NO"}, {"table_name": "tenant_typing_status", "column_name": "last_typing_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_typing_status", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_typing_status", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_typing_status", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_users", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO"}, {"table_name": "tenant_users", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_users", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_users", "column_name": "is_primary_tenant", "data_type": "boolean", "character_maximum_length": null, "column_default": "false", "is_nullable": "YES"}, {"table_name": "tenant_users", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES"}, {"table_name": "tenant_users", "column_name": "joined_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_users", "column_name": "last_login_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_users", "column_name": "expiry_date", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_users", "column_name": "permissions", "data_type": "jsonb", "character_maximum_length": null, "column_default": "'{}'::jsonb", "is_nullable": "YES"}, {"table_name": "tenant_users", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_users", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_users", "column_name": "metadata", "data_type": "jsonb", "character_maximum_length": null, "column_default": "'{}'::jsonb", "is_nullable": "YES"}, {"table_name": "tenant_users", "column_name": "role", "data_type": "USER-DEFINED", "character_maximum_length": null, "column_default": "'user'::tenant_user_role", "is_nullable": "NO"}, {"table_name": "tenant_users_details", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO"}, {"table_name": "tenant_users_details", "column_name": "tenant_user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_users_details", "column_name": "email", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_users_details", "column_name": "display_name", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_users_details", "column_name": "avatar_url", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_users_details", "column_name": "phone", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_users_details", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_users_details", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_users_details", "column_name": "preferred_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_users_details", "column_name": "department", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_users_details", "column_name": "title", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO"}, {"table_name": "tenant_voice_messages", "column_name": "message_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_voice_messages", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_voice_messages", "column_name": "voice_file_url", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_voice_messages", "column_name": "voice_file_size", "data_type": "integer", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "duration_seconds", "data_type": "integer", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "mime_type", "data_type": "character varying", "character_maximum_length": 100, "column_default": "'audio/webm'::character varying", "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "transcription", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "transcription_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "transcription_confidence", "data_type": "numeric", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "is_transcribed", "data_type": "boolean", "character_maximum_length": null, "column_default": "false", "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "transcription_provider", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "uploaded_by", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO"}, {"table_name": "tenant_voice_messages", "column_name": "processing_status", "data_type": "character varying", "character_maximum_length": 20, "column_default": "'pending'::character varying", "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "error_message", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}, {"table_name": "tenant_voice_messages", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES"}]