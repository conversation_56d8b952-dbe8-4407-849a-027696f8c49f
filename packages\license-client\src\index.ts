// Export original types
export * from './types';

// Export web chat types
export * from './web-chat-types';

// Export original services
export { LicenseActivationService } from './activation';
export { LicenseValidationService } from './validation';
export { LicenseCheckInService } from './checkin';

// Export web chat license service
export * from './web-chat-license';

// Export utilities
export { generateHardwareFingerprint } from './fingerprint';
export { calculateDaysRemaining, formatDate, isValidLicenseKeyFormat } from './utils';

/**
 * Main class for license client (original)
 */
export class LicenseClient {
  private apiUrl: string;
  private activationService: import('./activation').LicenseActivationService;
  private validationService: import('./validation').LicenseValidationService;
  private checkInService: import('./checkin').LicenseCheckInService;
  private checkInInterval?: NodeJS.Timeout;
  
  /**
   * Constructor
   * @param apiUrl The URL of the license service API
   */
  constructor(apiUrl: string) {
    this.apiUrl = apiUrl;
    this.activationService = new (require('./activation').LicenseActivationService)(apiUrl);
    this.validationService = new (require('./validation').LicenseValidationService)(apiUrl);
    this.checkInService = new (require('./checkin').LicenseCheckInService)(apiUrl);
  }
  
  /**
   * Activate a license
   * @param licenseKey The license key
   * @param customerName The customer name
   * @param customerEmail The customer email
   * @returns Promise with activation response
   */
  async activateLicense(
    licenseKey: string,
    customerName: string,
    customerEmail: string
  ) {
    return this.activationService.activateLicense(licenseKey, customerName, customerEmail);
  }
  
  /**
   * Verify email activation
   * @param activationId The activation ID
   * @param token The verification token
   * @returns Promise with activation response
   */
  async verifyEmailActivation(activationId: string, token: string) {
    return this.activationService.verifyEmailActivation(activationId, token);
  }
  
  /**
   * Validate a license
   * @param licenseKey The license key
   * @returns Promise with validation response
   */
  async validateLicense(licenseKey: string) {
    return this.validationService.validateLicense(licenseKey);
  }
  
  /**
   * Perform a license check-in
   * @param licenseKey The license key
   * @returns Promise with check-in response
   */
  async performCheckIn(licenseKey: string) {
    return this.checkInService.performCheckIn(licenseKey);
  }
  
  /**
   * Start scheduled check-ins
   * @param licenseKey The license key
   * @param intervalHours How often to check in (in hours)
   * @param onSuccess Callback for successful check-ins
   * @param onError Callback for failed check-ins
   */
  startScheduledCheckIns(
    licenseKey: string,
    intervalHours: number = 24,
    onSuccess?: (response: import('./types').LicenseCheckInResponse) => void,
    onError?: (error: any) => void
  ) {
    // Clear any existing check-in interval
    this.stopScheduledCheckIns();
    
    // Start new check-in interval
    this.checkInInterval = this.checkInService.scheduleCheckIns(
      licenseKey,
      intervalHours,
      onSuccess,
      onError
    );
  }
  
  /**
   * Stop scheduled check-ins
   */
  stopScheduledCheckIns() {
    if (this.checkInInterval) {
      clearInterval(this.checkInInterval);
      this.checkInInterval = undefined;
    }
  }
}

// Default export
export default LicenseClient;
