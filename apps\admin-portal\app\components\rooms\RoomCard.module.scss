.roomCard {
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 16px;
  background-color: #fff;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  &.available {
    border-left: 4px solid #10b981;
  }
  
  &.occupied {
    border-left: 4px solid #f59e0b;
  }
  
  &.maintenance {
    border-left: 4px solid #ef4444;
  }
  
  &.cleaning {
    border-left: 4px solid #3b82f6;
  }
}

.roomHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.roomInfo {
  display: flex;
  flex-direction: column;
}

.roomNumber {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  margin-bottom: 2px;
}

.roomType {
  font-size: 0.85rem;
  color: #666;
}

.statusBadge {
  font-size: 0.75rem;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  
  &.available {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
  }
  
  &.occupied {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
  }
  
  &.maintenance {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }
  
  &.cleaning {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }
}

.roomDetails {
  margin-top: 8px;
}

.detailItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 0.85rem;
}

.label {
  color: #666;
}

.value {
  font-weight: 500;
}

.guestInfo {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px dashed #eee;
}

.roomActions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
  opacity: 0;
  transition: opacity 0.2s ease;
  
  &.visible {
    opacity: 1;
  }
}

.actionButton {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: #f3f4f6;
  color: #374151;
  border: none;
  cursor: pointer;
  text-decoration: none;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #e5e7eb;
  }
}
