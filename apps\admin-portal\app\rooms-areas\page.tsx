'use client';
import { useState } from 'react';
import Link from 'next/link';
import DashboardLayout from '../dashboard-layout';
import RoomCard from '../components/rooms/RoomCard';
import AreaCard from '../components/areas/AreaCard';
import styles from './rooms-areas.module.scss';

export default function RoomsAreasPage() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [roomStats, setRoomStats] = useState({
    total: 0,
    available: 0,
    occupied: 0,
    maintenance: 0
  });
  const [areaStats, setAreaStats] = useState({
    total: 0,
    active: 0,
    inactive: 0
  });
  const [recentRooms, setRecentRooms] = useState([]);
  const [recentAreas, setRecentAreas] = useState([]);

  // Fetch stats and recent items on component mount
  useState(() => {
    // Fetch room stats
    fetch('/api/rooms/stats')
      .then(res => res.json())
      .then(data => {
        if (data.success) {
          setRoomStats(data.stats);
        }
      })
      .catch(err => console.error('Error fetching room stats:', err));

    // Fetch area stats
    fetch('/api/areas/stats')
      .then(res => res.json())
      .then(data => {
        if (data.success) {
          setAreaStats(data.stats);
        }
      })
      .catch(err => console.error('Error fetching area stats:', err));

    // Fetch recent rooms
    fetch('/api/rooms?limit=4')
      .then(res => res.json())
      .then(data => {
        setRecentRooms(data.data || []);
      })
      .catch(err => console.error('Error fetching recent rooms:', err));

    // Fetch recent areas
    fetch('/api/areas?limit=4')
      .then(res => res.json())
      .then(data => {
        setRecentAreas(data.data || []);
      })
      .catch(err => console.error('Error fetching recent areas:', err));
  }, []);

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Rooms & Areas Management</h1>
          <div className={styles.actions}>
            <div className={styles.actionGroup}>
              <Link href="/rooms-areas/rooms/create" className={styles.createButton}>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M8 3.33334V12.6667"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M3.33334 8H12.6667"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Add Room
              </Link>
              <Link href="/rooms-areas/areas/create" className={styles.createButton}>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M8 3.33334V12.6667"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M3.33334 8H12.6667"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Add Area
              </Link>
            </div>
            <div className={styles.actionGroup}>
              <button className={styles.secondaryButton}>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M2 12L8 14L14 12M2 8L8 10L14 8M8 2L2 4L8 6L14 4L8 2Z"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Import
              </button>
              <button className={styles.secondaryButton}>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M2 10V12C2 12.5304 2.21071 13.0391 2.58579 13.4142C2.96086 13.7893 3.46957 14 4 14H12C12.5304 14 13.0391 13.7893 13.4142 13.4142C13.7893 13.0391 14 12.5304 14 12V10M8 10V2M8 10L5 7M8 10L11 7"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Export
              </button>
            </div>
          </div>
        </div>

        <div className={styles.tabs}>
          <button
            className={`${styles.tabButton} ${activeTab === 'dashboard' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('dashboard')}
          >
            Dashboard
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'rooms' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('rooms')}
          >
            Hotel Rooms
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'areas' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('areas')}
          >
            Common Areas
          </button>
        </div>

        <div className={styles.content}>
          {activeTab === 'dashboard' && (
            <div className={styles.dashboard}>
              {/* Quick Stats */}
              <div className={styles.statsSection}>
                <h2 className={styles.sectionTitle}>Room & Area Statistics</h2>
                <div className={styles.statsGrid}>
                  <div className={styles.statCard}>
                    <div className={styles.statIcon} style={{ backgroundColor: '#ebf5ff' }}>
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#0284c7"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9z"></path>
                        <polyline points="9 22 9 12 15 12 15 22"></polyline>
                      </svg>
                    </div>
                    <div className={styles.statInfo}>
                      <span className={styles.statValue}>{roomStats.total}</span>
                      <span className={styles.statLabel}>Total Rooms</span>
                    </div>
                  </div>

                  <div className={styles.statCard}>
                    <div className={styles.statIcon} style={{ backgroundColor: '#f0fdf4' }}>
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#10b981"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                    </div>
                    <div className={styles.statInfo}>
                      <span className={styles.statValue}>{roomStats.available}</span>
                      <span className={styles.statLabel}>Available Rooms</span>
                    </div>
                  </div>

                  <div className={styles.statCard}>
                    <div className={styles.statIcon} style={{ backgroundColor: '#fff7ed' }}>
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#f59e0b"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                      </svg>
                    </div>
                    <div className={styles.statInfo}>
                      <span className={styles.statValue}>{roomStats.occupied}</span>
                      <span className={styles.statLabel}>Occupied Rooms</span>
                    </div>
                  </div>

                  <div className={styles.statCard}>
                    <div className={styles.statIcon} style={{ backgroundColor: '#f1f5f9' }}>
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#64748b"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                      </svg>
                    </div>
                    <div className={styles.statInfo}>
                      <span className={styles.statValue}>{areaStats.total}</span>
                      <span className={styles.statLabel}>Total Areas</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Rooms */}
              <div className={styles.recentSection}>
                <div className={styles.sectionHeader}>
                  <h2 className={styles.sectionTitle}>Recent Rooms</h2>
                  <Link href="/rooms-areas/rooms" className={styles.viewAllLink}>
                    View All
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path
                        d="M6 12L10 8L6 4"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </Link>
                </div>
                <div className={styles.roomsRow}>
                  {recentRooms.length > 0 ? (
                    recentRooms.map((room) => (
                      <div key={room.id} className={styles.roomCardSmall}>
                        <RoomCard
                          id={room.id}
                          roomNumber={room.room_number}
                          roomType={room.room_type}
                          roomCategory={room.room_category || room.room_type}
                          floor={room.floor}
                          status={room.status || 'available'}
                          guest={room.tenant_guests?.[0]}
                        />
                      </div>
                    ))
                  ) : (
                    <div className={styles.emptyState}>
                      <p>
                        No rooms have been created yet.{' '}
                        <Link href="/rooms-areas/rooms/create">Create a new room</Link>
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Recent Areas */}
              <div className={styles.recentSection}>
                <div className={styles.sectionHeader}>
                  <h2 className={styles.sectionTitle}>Common Areas</h2>
                  <Link href="/rooms-areas/areas" className={styles.viewAllLink}>
                    View All
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path
                        d="M6 12L10 8L6 4"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </Link>
                </div>
                <div className={styles.areasRow}>
                  {recentAreas.length > 0 ? (
                    recentAreas.map((area) => (
                      <div key={area.id} className={styles.areaCardSmall}>
                        <AreaCard
                          id={area.id}
                          name={area.name}
                          areaType={area.area_type}
                          floor={area.floor}
                          staffCount={area.staff_count || 0}
                          openingHours={area.opening_hours || ''}
                          closingHours={area.closing_hours || ''}
                          isActive={area.is_active !== false}
                        />
                      </div>
                    ))
                  ) : (
                    <div className={styles.emptyState}>
                      <p>
                        No areas have been created yet.{' '}
                        <Link href="/rooms-areas/areas/create">Create a new area</Link>
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'rooms' && (
            <div className={styles.redirectSection}>
              <Link href="/rooms-areas/rooms" className={styles.redirectButton}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M5 12H19M19 12L13 6M19 12L13 18"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Go to Room Management
              </Link>
            </div>
          )}

          {activeTab === 'areas' && (
            <div className={styles.redirectSection}>
              <Link href="/rooms-areas/areas" className={styles.redirectButton}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M5 12H19M19 12L13 6M19 12L13 18"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Go to Area Management
              </Link>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
