{"name": "@loaloa/license-service", "version": "0.1.0", "private": true, "main": "dist/index.js", "license": "MIT", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "test": "jest"}, "dependencies": {"@supabase/supabase-js": "^2.39.6", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.9", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.11.5", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "jest": "^29.7.0", "nodemon": "^3.0.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}