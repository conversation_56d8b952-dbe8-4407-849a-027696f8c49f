.container {
  padding: 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  .backButton {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #64748b;
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s;
    
    &:hover {
      color: #0f172a;
    }
  }
  
  .title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
  }
}

.content {
  display: flex;
  gap: 2rem;
  
  .sidebar {
    flex: 0 0 220px;
    
    .sideNav {
      list-style: none;
      padding: 0;
      margin: 0;
      background-color: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 0.5rem;
      overflow: hidden;
      
      .sideNavItem {
        a {
          display: block;
          padding: 0.75rem 1rem;
          color: #64748b;
          text-decoration: none;
          border-bottom: 1px solid #e2e8f0;
          transition: all 0.2s;
          
          &:hover {
            background-color: #f1f5f9;
            color: #0f172a;
          }
          
          &.active {
            background-color: #e2e8f0;
            color: #0f172a;
            font-weight: 500;
          }
        }
        
        &:last-child a {
          border-bottom: none;
        }
      }
    }
  }
  
  .mainContent {
    flex: 1;
    min-width: 0;
  }
}

.form {
  margin-top: 1.5rem;
}

.formGroup {
  margin-bottom: 1.5rem;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #1e293b;
    font-size: 0.9375rem;
  }
  
  input, select {
    width: 100%;
    padding: 0.625rem;
    border: 1px solid #cbd5e1;
    border-radius: 0.375rem;
    font-size: 0.9375rem;
    transition: border-color 0.2s;
    
    &:focus {
      outline: none;
      border-color: #0ea5e9;
      box-shadow: 0 0 0 1px rgba(14, 165, 233, 0.2);
    }
  }
  
  .disabledInput {
    background-color: #f8fafc;
    color: #94a3b8;
  }
  
  .helpText {
    margin-top: 0.375rem;
    font-size: 0.8125rem;
    color: #64748b;
  }
}

.required {
  color: #ef4444;
  margin-left: 2px;
}

.checkboxGroup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #0ea5e9;
  }
  
  label {
    margin-bottom: 0;
    cursor: pointer;
  }
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 2rem;
}

.buttonContainer {
  margin-top: 1.5rem;
}

.receptionPointsSection {
  margin-top: 1rem;
  
  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    
    .sectionTitle {
      font-size: 1rem;
      font-weight: 600;
      margin: 0;
      color: #1e293b;
    }
  }
}
