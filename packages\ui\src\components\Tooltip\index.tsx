import React, { useState, useRef, useEffect, ReactNode } from 'react';

export type TooltipPosition = 'top' | 'right' | 'bottom' | 'left';

export interface TooltipProps {
  /**
   * Tooltip content
   */
  content: ReactNode;
  /**
   * Children element that will trigger the tooltip
   */
  children: ReactNode;
  /**
   * Tooltip position
   */
  position?: TooltipPosition;
  /**
   * Tooltip background variant
   */
  variant?: 'light' | 'dark' | 'studio';
  /**
   * Additional CSS properties for tooltip container
   */
  style?: React.CSSProperties;
  /**
   * Optional CSS class name
   */
  className?: string;
  /**
   * Delay before showing tooltip (ms)
   */
  delay?: number;
  /**
   * Whether to show an arrow
   */
  arrow?: boolean;
  /**
   * Maximum width of the tooltip
   */
  maxWidth?: number | string;
}

export const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = 'top',
  variant = 'dark',
  style,
  className,
  delay = 300,
  arrow = true,
  maxWidth = 200,
  ...props
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<number | null>(null);

  // Theme-based colors
  const themeColors = {
    light: {
      background: '#FFFFFF',
      text: '#010103',
      border: '#EBEBEB',
      shadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    },
    dark: {
      background: '#161616',
      text: '#EBEBEB',
      border: '#1E1E1E',
      shadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
    },
    studio: {
      background: '#16262E',
      text: '#EBEBEB',
      border: '#2E4756',
      shadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
    },
  };

  // Calculate the position of the tooltip
  const calculatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const scrollY = window.scrollY;
    const scrollX = window.scrollX;

    let top = 0;
    let left = 0;

    switch (position) {
      case 'top':
        top = triggerRect.top + scrollY - tooltipRect.height - 8;
        left = triggerRect.left + scrollX + (triggerRect.width / 2) - (tooltipRect.width / 2);
        break;
      case 'right':
        top = triggerRect.top + scrollY + (triggerRect.height / 2) - (tooltipRect.height / 2);
        left = triggerRect.right + scrollX + 8;
        break;
      case 'bottom':
        top = triggerRect.bottom + scrollY + 8;
        left = triggerRect.left + scrollX + (triggerRect.width / 2) - (tooltipRect.width / 2);
        break;
      case 'left':
        top = triggerRect.top + scrollY + (triggerRect.height / 2) - (tooltipRect.height / 2);
        left = triggerRect.left + scrollX - tooltipRect.width - 8;
        break;
    }

    // Adjust if tooltip is out of viewport
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // Horizontal adjustment
    if (left < 10) left = 10;
    if (left + tooltipRect.width > viewportWidth - 10) {
      left = viewportWidth - tooltipRect.width - 10;
    }
    
    // Vertical adjustment
    if (top < 10) top = 10;
    if (top + tooltipRect.height > viewportHeight + scrollY - 10) {
      top = viewportHeight + scrollY - tooltipRect.height - 10;
    }

    setTooltipPosition({ top, left });
  };

  // Handle mouse enter
  const handleMouseEnter = () => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = window.setTimeout(() => {
      setIsVisible(true);
      // Wait for next frame to calculate position after tooltip is visible
      requestAnimationFrame(calculatePosition);
    }, delay);
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = window.setTimeout(() => {
      setIsVisible(false);
    }, 100);
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, []);

  // Recalculate position on window resize
  useEffect(() => {
    if (!isVisible) return;

    const handleResize = () => calculatePosition();
    window.addEventListener('resize', handleResize);
    
    // Initial position calculation
    calculatePosition();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isVisible]);

  // Trigger container style
  const triggerContainerStyle: React.CSSProperties = {
    display: 'inline-block',
    position: 'relative',
  };

  // Tooltip container style
  const tooltipContainerStyle: React.CSSProperties = {
    position: 'absolute',
    top: tooltipPosition.top,
    left: tooltipPosition.left,
    zIndex: 1050,
    opacity: isVisible ? 1 : 0,
    visibility: isVisible ? 'visible' : 'hidden',
    transition: 'opacity 0.2s ease, visibility 0.2s ease',
    pointerEvents: 'none',
    ...style,
  };

  // Tooltip content style
  const tooltipContentStyle: React.CSSProperties = {
    backgroundColor: themeColors[variant].background,
    color: themeColors[variant].text,
    border: `1px solid ${themeColors[variant].border}`,
    boxShadow: themeColors[variant].shadow,
    padding: '8px 12px',
    borderRadius: '4px',
    fontSize: '12px',
    maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,
    whiteSpace: 'normal',
    wordBreak: 'break-word',
    position: 'relative',
  };

  // Arrow style
  const getArrowStyle = (): React.CSSProperties => {
    const baseArrowStyle: React.CSSProperties = {
      position: 'absolute',
      width: '8px',
      height: '8px',
      backgroundColor: themeColors[variant].background,
      border: `1px solid ${themeColors[variant].border}`,
      transform: 'rotate(45deg)',
    };

    switch (position) {
      case 'top':
        return {
          ...baseArrowStyle,
          bottom: '-5px',
          left: 'calc(50% - 4px)',
          borderTop: 'none',
          borderLeft: 'none',
        };
      case 'right':
        return {
          ...baseArrowStyle,
          left: '-5px',
          top: 'calc(50% - 4px)',
          borderRight: 'none',
          borderBottom: 'none',
        };
      case 'bottom':
        return {
          ...baseArrowStyle,
          top: '-5px',
          left: 'calc(50% - 4px)',
          borderBottom: 'none',
          borderRight: 'none',
        };
      case 'left':
        return {
          ...baseArrowStyle,
          right: '-5px',
          top: 'calc(50% - 4px)',
          borderLeft: 'none',
          borderTop: 'none',
        };
      default:
        return baseArrowStyle;
    }
  };

  return (
    <div
      ref={triggerRef}
      style={triggerContainerStyle}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleMouseEnter}
      onBlur={handleMouseLeave}
    >
      {children}

      <div
        ref={tooltipRef}
        style={tooltipContainerStyle}
        className={className}
        role="tooltip"
        {...props}
      >
        <div style={tooltipContentStyle}>
          {content}
          {arrow && <div style={getArrowStyle()} />}
        </div>
      </div>
    </div>
  );
};

export default Tooltip;
