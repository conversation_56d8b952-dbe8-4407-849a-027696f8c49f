import { spacing } from '../../primitives/spacing';
import { borders } from '../../primitives/borders';
import { typography } from '../../primitives/typography';

export const chatInputTokens = {
  // Container
  container: {
    padding: spacing[2],
    borderRadius: borders.radii.lg,
    minHeight: '56px',
    maxHeight: '120px'
  },
  
  // Input
  input: {
    fontSize: typography.fontSizes.base,
    lineHeight: typography.lineHeights.normal,
    padding: `${spacing[2]} ${spacing[2]}`,
  },
  
  // Button spacing
  buttonSpacing: spacing[2],
  
  // Attachment button
  attachButton: {
    size: '36px'
  }
};
