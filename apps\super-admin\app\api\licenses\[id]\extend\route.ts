import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '../../../../../utils/supabase/server';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const body = await request.json();
    const { days } = body;
    
    if (!days || days <= 0) {
      return NextResponse.json(
        { error: 'Valid number of days is required' },
        { status: 400 }
      );
    }
    
    const supabase = await createClient();
    
    // Kiểm tra license có tồn tại không
    const { data: license, error: fetchError } = await supabase
      .from('licenses')
      .select('*')
      .eq('id', id)
      .single();
      
    if (fetchError || !license) {
      return NextResponse.json(
        { error: 'License not found' },
        { status: 404 }
      );
    }
    
    // Tính toán ngày hết hạn mới
    const currentExpiryDate = new Date(license.expiry_date);
    const newExpiryDate = new Date(currentExpiryDate);
    newExpiryDate.setDate(newExpiryDate.getDate() + days);
    
    // Cập nhật license với ngày hết hạn mới
    const { data: updatedLicense, error } = await supabase
      .from('licenses')
      .update({
        expiry_date: newExpiryDate.toISOString()
      })
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      console.error('Error extending license:', error);
      return NextResponse.json(
        { error: 'Failed to extend license' },
        { status: 500 }
      );
    }
    
    // Ghi log hoạt động
    await supabase
      .from('license_activities')
      .insert({
        license_id: id,
        activity_type: 'ACTIVATION',
        details: { message: `License extended by ${days} days` }
      });
      
    return NextResponse.json({
      data: updatedLicense,
      message: `License extended by ${days} days`
    });
    
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while extending the license' },
      { status: 500 }
    );
  }
}
