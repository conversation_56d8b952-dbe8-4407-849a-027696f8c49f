.container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
  color: var(--color-text);
}

.actions {
  display: flex;
  gap: 8px;
}

.errorMessage {
  padding: 12px;
  background-color: #FFEBEE;
  color: #C62828;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #C62828;
}

.form {
  width: 100%;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.formCard {
  padding: 16px;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--color-text);
}

.formGroup {
  margin-bottom: 16px;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 6px;
  color: var(--color-text);
}

.required {
  color: #D32F2F;
}

.input, .select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 0.875rem;
  
  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(255, 77, 0, 0.1);
  }
}

.inputError {
  border-color: #D32F2F;
}

.errorText {
  color: #D32F2F;
  font-size: 0.75rem;
  margin-top: 4px;
}

.helperText {
  font-size: 0.75rem;
  margin-top: 4px;
  color: var(--color-text-secondary);
}

.checkboxGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox {
  width: 16px;
  height: 16px;
}

.checkboxLabel {
  font-size: 0.875rem;
  color: var(--color-text);
}

.tenantSelector {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.selectorGroup {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.selectorRow {
  display: flex;
  gap: 8px;
}

.roleSelect {
  width: 120px;
  flex-shrink: 0;
}

.tenantList {
  margin-top: 16px;
}

.emptyState {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  text-align: center;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

.tenantItems {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tenantItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.tenantInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tenantName {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text);
}

.tenantRole {
  font-size: 0.75rem;
  padding: 2px 6px;
  background-color: #e0e0e0;
  border-radius: 4px;
  color: var(--color-text-secondary);
}

.removeButton {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #757575;
  cursor: pointer;
  padding: 0 4px;
  
  &:hover {
    color: #D32F2F;
  }
}

.noteBox {
  margin-top: 24px;
  padding: 12px;
  background-color: #FFF8E1;
  border-radius: 4px;
  border-left: 4px solid #FFC107;
}

.noteTitle {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--color-text);
}

.noteBox p {
  font-size: 0.875rem;
  margin: 0;
  color: var(--color-text);
}

.loadingText {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  margin-top: 4px;
}