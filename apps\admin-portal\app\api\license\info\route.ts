import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '../../../../lib/supabase/server';
import { cookies } from 'next/headers';
import fs from 'fs';
import path from 'path';
import { getLicenseConfig } from '../../../../lib/utils/license';

export async function GET(request: NextRequest) {
  try {
    // Lấy thông tin từ file cấu hình
    const licenseConfig = getLicenseConfig();
    
    if (!licenseConfig || !licenseConfig.licenseKey) {
      return NextResponse.json({ 
        error: 'License config not found' 
      }, { status: 404 });
    }
    
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Kiểm tra license key có hợp lệ không
    const { data: license, error: licenseError } = await supabase
      .from('licenses')
      .select('*')
      .eq('license_key', licenseConfig.licenseKey)
      .single();
    
    if (licenseError || !license) {
      return NextResponse.json({ 
        error: 'License not found',
        licenseKey: licenseConfig.licenseKey,
        customerName: licenseConfig.customerName,
        email: licenseConfig.email,
        tenant_id: licenseConfig.tenant_id
      });
    }
    
    // Lấy thông tin tenant
    const tenant_id = license.tenant_id || licenseConfig.tenant_id;
    let tenant_name = license.customer_name || licenseConfig.customerName;
    
    if (tenant_id) {
      const { data: tenant, error: tenantError } = await supabase
        .from('tenants')
        .select('name, contact_email, max_rooms, max_qr_codes')
        .eq('id', tenant_id)
        .single();
      
      if (!tenantError && tenant) {
        tenant_name = tenant.name;
        
        // Trả về thông tin đầy đủ
        return NextResponse.json({
          licenseKey: license.license_key,
          customerName: tenant_name,
          email: tenant.contact_email || licenseConfig.email,
          tenant_id: tenant_id,
          issueDate: license.issue_date,
          expiryDate: license.expiry_date,
          isActive: license.is_active,
          lastCheckIn: license.last_check_in,
          checkInCount: license.check_in_count,
          maxRooms: tenant.max_rooms,
          maxQrCodes: tenant.max_qr_codes
        });
      }
    }
    
    // Fallback nếu không tìm thấy tenant
    return NextResponse.json({
      licenseKey: license.license_key,
      customerName: tenant_name,
      email: licenseConfig.email,
      tenant_id: tenant_id,
      issueDate: license.issue_date,
      expiryDate: license.expiry_date,
      isActive: license.is_active,
      lastCheckIn: license.last_check_in,
      checkInCount: license.check_in_count
    });
  } catch (error) {
    console.error('Error fetching license info:', error);
    return NextResponse.json({
      error: 'Failed to fetch license information',
    }, { status: 500 });
  }
}
