-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE temporary_users ENABLE ROW LEVEL SECURITY;

-- Create a custom claim to store user's role
CREATE OR REPLACE FUNCTION public.get_user_roles(user_id UUID)
RETURNS SETOF roles AS $$
  SELECT r.*
  FROM roles r
  JOIN user_roles ur ON r.id = ur.role_id
  WHERE ur.user_id = get_user_roles.user_id;

$$ LANGUAGE sql SECURITY DEFINER;

-- Define policies
-- Users table policies
CREATE POLICY "Users can view their own data" 
  ON users FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own data" 
  ON users FOR UPDATE 
  USING (auth.uid() = id);

-- <PERSON><PERSON> can view all users
CREATE POLICY "<PERSON><PERSON> can view all users" 
  ON users FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid() AND r.name = 'admin'
  ));

-- Admin can edit all users
CREATE POLICY "Admins can update all users" 
  ON users FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid() AND r.name = 'admin'
  ));

-- Temporary users policies
CREATE POLICY "Temporary users accessible by QR code" 
  ON temporary_users FOR SELECT 
  USING (qr_code_id IS NOT NULL);
