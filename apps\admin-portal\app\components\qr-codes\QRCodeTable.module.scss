.container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.header {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.5rem;
  
  h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #6b7280;
    margin-bottom: 1rem;
  }
  
  .actions {
    display: flex;
    justify-content: flex-end;
  }
}

.tableContainer {
  overflow-x: auto;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-top: 1rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 0.75rem 1rem;
    text-align: left;
  }
  
  th {
    background-color: #f9fafb;
    font-weight: 500;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
  }
  
  td {
    border-bottom: 1px solid #e5e7eb;
  }
  
  tbody tr:last-child td {
    border-bottom: none;
  }
}

.loading, .noData {
  text-align: center;
  padding: 2rem 0;
  color: #6b7280;
}