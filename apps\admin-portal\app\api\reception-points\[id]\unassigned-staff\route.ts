import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const receptionPointId = params.id;
    
    // Lấy tenant_id từ file config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json(
        { error: 'Tenant ID not found. Please activate your license.' },
        { status: 400 }
      );
    }
    
    // Tạo Supabase client
    const supabase = createAdminClient(cookies());
    
    // Kiểm tra reception point có tồn tại không
    const { data: receptionPoint, error: rptError } = await supabase
      .from('tenant_message_reception_points')
      .select('id')
      .eq('id', receptionPointId)
      .eq('tenant_id', tenant_id)
      .single();
      
    if (rptError) {
      if (rptError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Reception point not found' }, { status: 404 });
      }
      return NextResponse.json(
        { error: 'Error checking reception point' },
        { status: 500 }
      );
    }
    
    // Lấy tất cả các user thuộc tenant
    const { data: allUsers, error: allUsersError } = await supabase
      .from('tenant_users')
      .select(`
        id,
        user_id,
        role,
        tenant_users_details (
          email,
          display_name,
          avatar_url
        )
      `)
      .eq('tenant_id', tenant_id)
      .in('role', ['user', 'manager']);
      
    if (allUsersError) {
      console.error('Error fetching all users:', allUsersError);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }
    
    // Lấy các user đã được gán cho reception point này
    const { data: assignedUsers, error: assignedError } = await supabase
      .from('tenant_user_reception_points')
      .select('tenant_user_id')
      .eq('reception_point_id', receptionPointId);
      
    if (assignedError) {
      console.error('Error fetching assigned users:', assignedError);
      return NextResponse.json(
        { error: 'Failed to fetch assigned users' },
        { status: 500 }
      );
    }
    
    // Tạo set các ID user đã được gán
    const assignedUserIdSet = new Set(
      assignedUsers.map(item => item.tenant_user_id)
    );
    
    // Lọc ra các user chưa được gán
    const unassignedUsers = allUsers
      .filter(user => !assignedUserIdSet.has(user.id) && ['user', 'manager'].includes(user.role))
      .map(user => ({
        id: user.id,
        user_id: user.user_id,
        display_name: user.tenant_users_details?.display_name || 'Unknown',
        email: user.tenant_users_details?.email || 'No email',
        avatar_url: user.tenant_users_details?.avatar_url || null
      }));
    
    return NextResponse.json({ data: unassignedUsers });
  } catch (error: any) {
    console.error('Error in GET unassigned staff:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
