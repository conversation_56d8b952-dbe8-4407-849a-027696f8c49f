/* <PERSON><PERSON><PERSON> nhật CSS hiện tại và thêm các style cho Reception Points */
.container {
  padding: 1.5rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
  margin-top: 0;
}

.subtitle {
  color: #6b7280;
  margin: 0;
}

.detailsCard {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.profileSection {
  padding: 1.5rem;
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}

.avatarContainer {
  margin-right: 1.5rem;
}

.avatar {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  object-fit: cover;
}

.avatarPlaceholder {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  background-color: #60a5fa;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 600;
}

.profileInfo {
  flex: 1;
}

.name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 0.75rem;
  color: #111827;
}

.infoItem {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  
  .label {
    font-weight: 500;
    color: #6b7280;
    width: 7rem;
  }
}

.roleTag {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: #e0f2fe;
  color: #0369a1;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.statusTag {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  
  &.active {
    background-color: #d1fae5;
    color: #047857;
  }
  
  &.inactive {
    background-color: #fecaca;
    color: #b91c1c;
  }
}

.detailsSection {
  padding: 1.5rem;
}

.sectionTitle {
  font-size: 1rem;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 1rem;
  color: #111827;
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.detailItem {
  display: flex;
  flex-direction: column;
  
  .label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 0.25rem;
  }
}

.content {
  margin-top: 1.5rem;
}

.receptionPointsSection {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.buttonContainer {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-start;
}
