 <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LoaLoa Chat Tester v3 (Fix)</title>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <style>
    /* Giữ nguyên các style từ file gốc */
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
    .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
    .panel { background: white; padding: 15px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    .panel-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; border-bottom: 1px solid #eee; padding-bottom: 5px; }
    h3 { margin: 0; color: #333; }
    .status { font-weight: bold; padding: 4px 8px; border-radius: 4px; font-size: 14px; }
    .pending { background-color: #ffeaa7; color: #d35400; }
    .success { background-color: #c4e6ca; color: #2d6a4f; }
    .error { background-color: #ffcccc; color: #cc0000; }
    input, textarea, select { width: 100%; padding: 8px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
    button { background-color: #4CAF50; color: white; border: none; padding: 8px 12px; cursor: pointer; border-radius: 4px; }
    button:hover { background-color: #45a049; }
    .tab-panel { display: flex; margin-bottom: 10px; }
    .tab { padding: 8px 12px; cursor: pointer; background: #eee; margin-right: 5px; border-radius: 4px 4px 0 0; }
    .tab.active { background: #fff; border-bottom: 2px solid #4CAF50; }
    .tab-content { background: white; padding: 15px; border-radius: 0 0 8px 8px; }
    table { width: 100%; border-collapse: collapse; }
    table, th, td { border: 1px solid #ddd; }
    th, td { padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .chat-container { display: flex; gap: 20px; margin-top: 10px; }
    .chat-user { flex: 1; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
    .chat-header { background: #f2f2f2; padding: 10px; border-bottom: 1px solid #ddd; }
    .messages { height: 300px; overflow-y: auto; padding: 10px; }
    .message { margin-bottom: 10px; padding: 8px; border-radius: 4px; max-width: 70%; }
    .sent { background-color: #e3f2fd; margin-left: auto; text-align: right; }
    .received { background-color: #f1f1f1; }
    .input-container { display: flex; border-top: 1px solid #ddd; }
    .input-container input { margin: 5px; border-radius: 4px; padding: 8px; flex: 1; }
    .input-container button { margin: 5px; }
    .debug-console { height: 200px; overflow-y: auto; background: #272822; color: #f8f8f2; font-family: monospace; padding: 10px; border-radius: 4px; }
    .debug-message { margin-bottom: 5px; padding: 2px 0; border-bottom: 1px solid #444; }
    .note { background-color: #ffeaa7; padding: 10px; border-radius: 4px; margin-bottom: 10px; font-size: 14px; }
    .action-buttons { display: flex; gap: 10px; margin-top: 10px; }
    .user-info { margin-top: 10px; padding: 10px; background-color: #f9f9f9; border-radius: 4px; }
    .user-info p { margin: 5px 0; }
    .mocking-option { margin-bottom: 10px; padding: 10px; background-color: #e3f2fd; border-radius: 4px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>LoaLoa Chat Tester v3 (Fix)</h1>
    
    <!-- Panel 1: Connect to Supabase -->
    <div class="panel">
      <div class="panel-header">
        <h3>Connect to Supabase</h3>
        <span class="status pending" id="supabase-status">Pending</span>
      </div>
      <div class="mocking-option">
        <input type="checkbox" id="mock-supabase" checked>
        <label for="mock-supabase"><strong>Use Mock Mode</strong> (solves "Failed to fetch" errors)</label>
        <p style="margin: 5px 0 0 0; font-size: 14px;">Mock mode simulates a Supabase connection without actual fetch requests. Recommended for local testing.</p>
      </div>
      <div>
        <input type="text" id="supabase-url" placeholder="Supabase URL" value="https://uvfosdvduemcktgayllz.supabase.co">
        <input type="password" id="supabase-key" placeholder="Supabase Key" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2Zm9zZHZkdWVtY2t0Z2F5bGx6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2ODQ5MjQ0MjksImV4cCI6MjAwMDUwMDQyOX0.vZn916S2nYhXsVO6ublrZ_mV9FxKlrJbKD9mgmrkSUE">
        <button id="connect-supabase">Connect to Supabase</button>
        <div id="supabase-connection-info" class="user-info" style="display: none;">
          <p id="supabase-version"></p>
        </div>
      </div>
    </div>
    
    <!-- Panel 2: Generate JWT Token -->
    <div class="panel">
      <div class="panel-header">
        <h3>Generate JWT Token</h3>
        <span class="status pending" id="jwt-status">Pending</span>
      </div>
      <div class="note">
        <strong>Need a JWT Token?</strong> Run this command:<br>
        <code>cd D:\loaloa\services\chat</code><br>
        <code>npx ts-node src\tests\generate-token.ts</code>
      </div>
      <textarea id="jwt-token" placeholder="JWT Token" rows="3"></textarea>
      <button id="validate-token">Validate Token</button>
      <div id="token-info" class="user-info" style="display: none;">
        <p><strong>User ID:</strong> <span id="token-user-id"></span></p>
        <p><strong>Email:</strong> <span id="token-email"></span></p>
        <p><strong>Expiry:</strong> <span id="token-expiry"></span></p>
      </div>
    </div>
    
    <!-- Panel 3: Database Explorer -->
    <div class="panel">
      <div class="panel-header">
        <h3>Database Explorer</h3>
        <span class="status pending" id="db-status">Pending</span>
      </div>
      <div class="tab-panel">
        <div class="tab active" data-tab="users">Users</div>
        <div class="tab" data-tab="rooms">Chat Rooms</div>
        <div class="tab" data-tab="participants">Participants</div>
        <div class="tab" data-tab="messages">Messages</div>
      </div>
      <div class="action-buttons">
        <button id="load-users">Load Users</button>
        <button id="load-rooms">Load Chat Rooms</button>
        <button id="load-participants">Load Participants</button>
        <button id="load-messages">Load Messages</button>
      </div>
      <div class="tab-content">
        <!-- Users Table -->
        <div id="users-tab" class="tab-pane active">
          <h4>Users</h4>
          <table id="users-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Email</th>
                <th>Created At</th>
              </tr>
            </thead>
            <tbody>
              <tr><td colspan="3">No data loaded yet</td></tr>
            </tbody>
          </table>
        </div>
        
        <!-- Chat Rooms Table -->
        <div id="rooms-tab" class="tab-pane" style="display:none;">
          <h4>Chat Rooms</h4>
          <table id="rooms-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Type</th>
                <th>Created At</th>
              </tr>
            </thead>
            <tbody>
              <tr><td colspan="4">No data loaded yet</td></tr>
            </tbody>
          </table>
        </div>
        
        <!-- Participants Table -->
        <div id="participants-tab" class="tab-pane" style="display:none;">
          <h4>Chat Participants</h4>
          <table id="participants-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Room ID</th>
                <th>User ID</th>
                <th>Role</th>
                <th>Joined At</th>
              </tr>
            </thead>
            <tbody>
              <tr><td colspan="5">No data loaded yet</td></tr>
            </tbody>
          </table>
        </div>
        
        <!-- Messages Table -->
        <div id="messages-tab" class="tab-pane" style="display:none;">
          <h4>Chat Messages</h4>
          <table id="messages-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Room ID</th>
                <th>Sender ID</th>
                <th>Content</th>
                <th>Language</th>
                <th>Sent At</th>
              </tr>
            </thead>
            <tbody>
              <tr><td colspan="6">No data loaded yet</td></tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- Panel 4: User-Room Management -->
    <div class="panel">
      <div class="panel-header">
        <h3>User-Room Management</h3>
        <span class="status pending" id="manage-status">Pending</span>
      </div>
      <div>
        <select id="select-user">
          <option value="">Select User</option>
        </select>
        <select id="select-room">
          <option value="">Select Room</option>
        </select>
        <button id="check-participation">Check</button>
        <div id="participation-status" style="margin-top: 10px;">User is active in room</div>
      </div>
    </div>
    
    <!-- Panel 5: WebSocket Connection -->
    <div class="panel">
      <div class="panel-header">
        <h3>WebSocket Connection</h3>
        <span class="status pending" id="ws-status">Pending</span>
      </div>
      <div>
        <input type="text" id="ws-url" placeholder="WebSocket Server URL" value="http://localhost:3002">
        <input type="text" id="room-id" placeholder="Room to Join" value="c636ea40-1d87-4982-a6a3-86fa0805e258">
        <button id="connect-ws">Connect to WebSocket</button>
        <div class="checkbox-container">
          <input type="checkbox" id="show-debug" checked>
          <label for="show-debug">Show Debug Console</label>
        </div>
        <div id="debug-console" class="debug-console">
          <!-- Debug messages will be added here -->
        </div>
      </div>
    </div>
    
    <!-- Panel 6: Test Chat Interface -->
    <div class="panel">
      <div class="panel-header">
        <h3>Test Chat Interface</h3>
        <span class="status pending" id="chat-status">Pending</span>
      </div>
      <div class="chat-container">
        <!-- User 1 Chat -->
        <div class="chat-user">
          <div class="chat-header">
            <h5>User 1</h5>
            <input type="text" id="user1-id" placeholder="User ID" value="973c8e99-9b06-437a-9ab5-e4bdf2aa4b53">
            <p id="user1-email"></p>
            <div><strong>Vietnamese</strong></div>
            <div id="user1-status">Disconnected</div>
          </div>
          <div id="user1-messages" class="messages">
            <!-- Messages will be added here -->
          </div>
          <div class="input-container">
            <input type="text" id="user1-input" placeholder="Type a message...">
            <button id="user1-send">Send</button>
          </div>
        </div>
        
        <!-- User 2 Chat -->
        <div class="chat-user">
          <div class="chat-header">
            <h5>User 2</h5>
            <input type="text" id="user2-id" placeholder="User ID" value="a9813ae-9a46-4dc9-9fa3-6f04062f7e50">
            <p id="user2-email"></p>
            <div><strong>English</strong></div>
            <div id="user2-status">Disconnected</div>
          </div>
          <div id="user2-messages" class="messages">
            <!-- Messages will be added here -->
          </div>
          <div class="input-container">
            <input type="text" id="user2-input" placeholder="Type a message...">
            <button id="user2-send">Send</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // ====== MOCK DATA ======
    const mockData = {
      users: [
        { id: '973c8e99-9b06-437a-9ab5-e4bdf2aa4b53', email: '<EMAIL>', created_at: '2025-01-01T00:00:00.000Z' },
        { id: 'a9813ae-9a46-4dc9-9fa3-6f04062f7e50', email: '<EMAIL>', created_at: '2025-01-02T00:00:00.000Z' },
        { id: 'b9813ae-9a46-4dc9-9fa3-6f04062f7e51', email: '<EMAIL>', created_at: '2025-01-03T00:00:00.000Z' }
      ],
      rooms: [
        { id: 'c636ea40-1d87-4982-a6a3-86fa0805e258', name: 'General Chat', room_type: 'general', created_at: '2025-01-01T00:00:00.000Z' },
        { id: 'd636ea40-1d87-4982-a6a3-86fa0805e259', name: 'Support', room_type: 'support', created_at: '2025-01-02T00:00:00.000Z' }
      ],
      participants: [
        { id: '7a1d73c5-aa9f-4d00-bc3f-acc2ff6e5974', chat_room_id: 'c636ea40-1d87-4982-a6a3-86fa0805e258', user_id: '973c8e99-9b06-437a-9ab5-e4bdf2aa4b53', participant_role: 'member', is_active: true, joined_at: '2025-05-05T09:23:17.861Z' },
        { id: '45846ff7-9f05-4e36-ae56-6ef61561f3dc', chat_room_id: 'c636ea40-1d87-4982-a6a3-86fa0805e258', user_id: 'a9813ae-9a46-4dc9-9fa3-6f04062f7e50', participant_role: 'member', is_active: true, joined_at: '2025-05-05T09:32:28.100Z' }
      ],
      messages: []
    };
    
    // Biến toàn cục
    let supabaseClient = null;
    let mockMode = true;
    let jwtToken = '';
    let userId = '';
    let socket1 = null;
    let socket2 = null;
    let currentRoomId = '';
    let user1Email = '';
    let user2Email = '';
    
    // Đối tượng để theo dõi trạng thái của các bảng điều khiển
    const panels = {
      supabase: document.getElementById('supabase-status'),
      jwt: document.getElementById('jwt-status'),
      db: document.getElementById('db-status'),
      manage: document.getElementById('manage-status'),
      ws: document.getElementById('ws-status'),
      chat: document.getElementById('chat-status')
    };
    
    // Mock Supabase Client
    class MockSupabaseClient {
      constructor() {
        this.mockData = JSON.parse(JSON.stringify(mockData)); // Deep clone
      }
      
      from(table) {
        return new MockQueryBuilder(this.mockData[table] || []);
      }
      
      rpc(func) {
        if (func === 'version') {
          return Promise.resolve({ data: 'Mock 1.0.0', error: null });
        }
        return Promise.resolve({ data: null, error: null });
      }
    }
    
    class MockQueryBuilder {
      constructor(data) {
        this._data = data;
        this._filters = [];
        this._limit = null;
        this._orderField = null;
        this._orderAsc = true;
      }
      
      select(columns) {
        return this;
      }
      
      eq(field, value) {
        this._filters.push(item => item[field] === value);
        return this;
      }
      
      limit(num) {
        this._limit = num;
        return this;
      }
      
      order(field, { ascending = true } = {}) {
        this._orderField = field;
        this._orderAsc = ascending;
        return this;
      }
      
      single() {
        let result = [...this._data];
        
        // Apply filters
        for (const filter of this._filters) {
          result = result.filter(filter);
        }
        
        // Apply ordering
        if (this._orderField) {
          result.sort((a, b) => {
            if (a[this._orderField] < b[this._orderField]) return this._orderAsc ? -1 : 1;
            if (a[this._orderField] > b[this._orderField]) return this._orderAsc ? 1 : -1;
            return 0;
          });
        }
        
        return Promise.resolve({
          data: result.length > 0 ? result[0] : null,
          error: null
        });
      }
      
      then(callback) {
        let result = [...this._data];
        
        // Apply filters
        for (const filter of this._filters) {
          result = result.filter(filter);
        }
        
        // Apply ordering
        if (this._orderField) {
          result.sort((a, b) => {
            if (a[this._orderField] < b[this._orderField]) return this._orderAsc ? -1 : 1;
            if (a[this._orderField] > b[this._orderField]) return this._orderAsc ? 1 : -1;
            return 0;
          });
        }
        
        // Apply limit
        if (this._limit !== null) {
          result = result.slice(0, this._limit);
        }
        
        return Promise.resolve({
          data: result,
          error: null
        }).then(callback);
      }
      
      insert(records) {
        const newRecords = records.map(record => {
          // Generate UUID for id if not provided
          const newRecord = { ...record };
          if (!newRecord.id) {
            newRecord.id = generateUUID();
          }
          return newRecord;
        });
        
        // Push new records to mock data
        this._data.push(...newRecords);
        
        return {
          select: () => ({
            then: (callback) => Promise.resolve({
              data: newRecords,
              error: null
            }).then(callback)
          }),
          single: () => ({
            then: (callback) => Promise.resolve({
              data: newRecords.length > 0 ? newRecords[0] : null,
              error: null
            }).then(callback)
          }),
          then: (callback) => Promise.resolve({
            data: newRecords,
            error: null
          }).then(callback)
        };
      }
    }
    
    // Helper function to generate UUID
    function generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }
    
    // Hàm cập nhật trạng thái panel
    function updateStatus(panel, status, message = '') {
      if (panels[panel]) {
        panels[panel].className = `status ${status}`;
        panels[panel].textContent = status.charAt(0).toUpperCase() + status.slice(1);
        
        if (message && status === 'error') {
          console.error(message);
          addDebugMessage(`${message}`);
        }
      }
    }
    
    // Hàm thêm tin nhắn debug
    function addDebugMessage(message) {
      const debugConsole = document.getElementById('debug-console');
      if (debugConsole) {
        const timestamp = new Date().toLocaleTimeString();
        const msgElement = document.createElement('div');
        msgElement.className = 'debug-message';
        msgElement.textContent = `[${timestamp}] ${message}`;
        debugConsole.appendChild(msgElement);
        debugConsole.scrollTop = debugConsole.scrollHeight;
      }
    }
    
    // Kết nối đến Supabase
    document.getElementById('connect-supabase').addEventListener('click', async () => {
      try {
        // Kiểm tra xem có đang ở chế độ mock hay không
        mockMode = document.getElementById('mock-supabase').checked;
        
        const supabaseUrl = document.getElementById('supabase-url').value;
        const supabaseKey = document.getElementById('supabase-key').value;
        
        if (!supabaseUrl || !supabaseKey) {
          updateStatus('supabase', 'error', 'Supabase URL and key are required');
          return;
        }
        
        if (mockMode) {
          addDebugMessage('Using Mock Supabase Mode (no actual API calls)');
          // Sử dụng mock client
          supabaseClient = new MockSupabaseClient();
          
          document.getElementById('supabase-connection-info').style.display = 'block';
          document.getElementById('supabase-version').innerHTML = `
            <strong>Connected to Mock Supabase</strong><br>
            <span style="color: #666;">Using mock mode to bypass browser security restrictions</span><br>
            <span style="color: #666;">All data is simulated locally</span>
          `;
          
          updateStatus('supabase', 'success');
          addDebugMessage('Successfully connected to Mock Supabase');
        } else {
          // Sử dụng real client nếu không ở chế độ mock
          addDebugMessage(`Attempting to connect to real Supabase at ${supabaseUrl}`);
          
          // Tải thư viện Supabase từ CDN
          await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.min.js';
            script.onload = resolve;
            script.onerror = () => reject(new Error('Failed to load Supabase script'));
            document.head.appendChild(script);
          });
          
          if (!window.supabase) {
            throw new Error('Supabase library not available after loading');
          }
          
          // Tạo client với service_role key để bypass RLS
          supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
          
          // Kiểm tra kết nối bằng cách thực hiện một truy vấn đơn giản
          const { data, error } = await supabaseClient.from('users').select('count(*)');
          
          if (error) {
            throw new Error(error.message);
          }
          
          document.getElementById('supabase-connection-info').style.display = 'block';
          document.getElementById('supabase-version').textContent = `Connected to Supabase successfully!`;
          
          updateStatus('supabase', 'success');
          addDebugMessage('Successfully connected to real Supabase');
        }
        
        // Tải dữ liệu ban đầu
        await loadUsers();
        await loadRooms();
        await loadParticipants();
        await loadMessages();
      } catch (err) {
        updateStatus('supabase', 'error', `Failed to connect to Supabase: ${err.message}`);
        addDebugMessage(`Error connecting to Supabase: ${err.message}`);
        
        // Hiển thị gợi ý sử dụng Mock Mode
        document.getElementById('supabase-connection-info').style.display = 'block';
        document.getElementById('supabase-version').innerHTML = `
          <strong>Error connecting to Supabase:</strong> ${err.message}<br>
          <span style="color: #666;">Try enabling "Mock Mode" checkbox above to use simulated data</span>
        `;
      }
    });
    
    // Xác thực JWT Token
    document.getElementById('validate-token').addEventListener('click', () => {
      const token = document.getElementById('jwt-token').value.trim();
      
      if (!token) {
        updateStatus('jwt', 'error', 'Please enter a JWT token');
        return;
      }
      
      try {
        // Decode JWT token để lấy payload (không cần private key)
        const base64Url = token.split('.')[1];
        if (!base64Url) {
          updateStatus('jwt', 'error', 'Invalid JWT token format');
          addDebugMessage('Invalid JWT token format');
          return;
        }
        
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        
        const payload = JSON.parse(jsonPayload);
        
        // Kiểm tra xem token đã hết hạn chưa
        const currentTime = Math.floor(Date.now() / 1000);
        const expiryDate = new Date(payload.exp * 1000);
 if (payload.exp < currentTime) {
          updateStatus('jwt', 'error', 'Invalid JWT token: Token has expired');
          addDebugMessage('Invalid JWT token: Token has expired');
          return;
        }
        
        // Lưu token và userId
        jwtToken = token;
        userId = payload.userId;
        
        // Hiển thị thông tin từ token
        document.getElementById('token-info').style.display = 'block';
        document.getElementById('token-user-id').textContent = userId;
        document.getElementById('token-email').textContent = payload.email || 'Not provided';
        document.getElementById('token-expiry').textContent = expiryDate.toLocaleString();
        
        // Tự động điền User ID vào User 1
        document.getElementById('user1-id').value = userId;
        if (payload.email) {
          document.getElementById('user1-email').textContent = payload.email;
          user1Email = payload.email;
        }
        
        updateStatus('jwt', 'success');
        addDebugMessage(`Valid JWT token for user: ${userId}`);
      } catch (err) {
        updateStatus('jwt', 'error', `Invalid JWT token: ${err.message}`);
        addDebugMessage(`Invalid JWT token: ${err.message}`);
      }
    });
    
    // Hàm tải Users
    async function loadUsers() {
      if (!supabaseClient) {
        addDebugMessage('Failed to load users: Please connect to Supabase first');
        updateStatus('db', 'error', 'Please connect to Supabase first');
        return null;
      }
      
      try {
        addDebugMessage('Loading users from database...');
        const { data: users, error: usersError } = await supabaseClient
          .from('users')
          .select('*');
          
        if (usersError) {
          updateStatus('db', 'error', `Failed to load users: ${usersError.message}`);
          addDebugMessage(`Failed to load users: ${usersError.message}`);
          return null;
        } else {
          updateStatus('db', 'success');
          addDebugMessage(`Loaded ${users.length} users from database`);
          displayUsers(users);
          populateUserDropdown(users);
          
          // Store emails for chat interface
          const userMap = {};
          users.forEach(user => {
            userMap[user.id] = user.email || 'No Email';
          });
          
          // Display emails for user1 and user2 if IDs are filled
          const user1Id = document.getElementById('user1-id').value;
          if (user1Id && userMap[user1Id]) {
            document.getElementById('user1-email').textContent = userMap[user1Id];
            user1Email = userMap[user1Id];
          }
          
          const user2Id = document.getElementById('user2-id').value;
          if (user2Id && userMap[user2Id]) {
            document.getElementById('user2-email').textContent = userMap[user2Id];
            user2Email = userMap[user2Id];
          }
          
          return users;
        }
      } catch (err) {
        updateStatus('db', 'error', `Failed to load users: ${err.message}`);
        addDebugMessage(`Error loading users: ${err.message}`);
        return null;
      }
    }
    
    // Hàm tải Chat Rooms
    async function loadRooms() {
      if (!supabaseClient) {
        addDebugMessage('Failed to load rooms: Please connect to Supabase first');
        updateStatus('db', 'error', 'Please connect to Supabase first');
        return null;
      }
      
      try {
        addDebugMessage('Loading chat rooms from database...');
        const { data: rooms, error: roomsError } = await supabaseClient
          .from('rooms')
          .select('*');
          
        if (roomsError) {
          addDebugMessage(`Failed to load rooms: ${roomsError.message}`);
          addDebugMessage('Trying alternative table name...');
          
          // Try alternative table name
          const { data: altRooms, error: altRoomsError } = await supabaseClient
            .from('chat_rooms')
            .select('*');
            
          if (altRoomsError) {
            updateStatus('db', 'error', `Failed to load rooms: ${altRoomsError.message}`);
            addDebugMessage(`Failed to load rooms from alternative table: ${altRoomsError.message}`);
            return null;
          }
          
          updateStatus('db', 'success');
          addDebugMessage(`Loaded ${altRooms.length} chat rooms from database (alternative table)`);
          displayRooms(altRooms);
          populateRoomDropdown(altRooms);
          return altRooms;
        } else {
          updateStatus('db', 'success');
          addDebugMessage(`Loaded ${rooms.length} chat rooms from database`);
          displayRooms(rooms);
          populateRoomDropdown(rooms);
          return rooms;
        }
      } catch (err) {
        updateStatus('db', 'error', `Failed to load chat rooms: ${err.message}`);
        addDebugMessage(`Error loading chat rooms: ${err.message}`);
        return null;
      }
    }
    
    // Hàm tải Chat Participants
    async function loadParticipants() {
      if (!supabaseClient) {
        addDebugMessage('Failed to load participants: Please connect to Supabase first');
        updateStatus('db', 'error', 'Please connect to Supabase first');
        return null;
      }
      
      try {
        addDebugMessage('Loading chat participants from database...');
        const { data: participants, error: participantsError } = await supabaseClient
          .from('chat_participants')
          .select('*');
          
        if (participantsError) {
          updateStatus('db', 'error', `Failed to load participants: ${participantsError.message}`);
          addDebugMessage(`Failed to load participants: ${participantsError.message}`);
          return null;
        } else {
          updateStatus('db', 'success');
          addDebugMessage(`Loaded ${participants.length} participants from database`);
          displayParticipants(participants);
          return participants;
        }
      } catch (err) {
        updateStatus('db', 'error', `Failed to load participants: ${err.message}`);
        addDebugMessage(`Error loading participants: ${err.message}`);
        return null;
      }
    }
    
    // Hàm tải Chat Messages
    async function loadMessages() {
      if (!supabaseClient) {
        addDebugMessage('Failed to load messages: Please connect to Supabase first');
        updateStatus('db', 'error', 'Please connect to Supabase first');
        return null;
      }
      
      try {
        addDebugMessage('Loading chat messages from database...');
        const { data: messages, error: messagesError } = await supabaseClient
          .from('chat_messages')
          .select('*')
          .limit(20) // Giới hạn số lượng tin nhắn tải về
          .order('sent_at', { ascending: false });
          
        if (messagesError) {
          updateStatus('db', 'error', `Failed to load messages: ${messagesError.message}`);
          addDebugMessage(`Failed to load messages: ${messagesError.message}`);
          return null;
        } else {
          updateStatus('db', 'success');
          addDebugMessage(`Loaded ${messages.length} messages from database`);
          displayMessages(messages);
          
          // Thêm tin nhắn vào dữ liệu mock
          if (mockMode) {
            mockData.messages = messages;
          }
          
          return messages;
        }
      } catch (err) {
        updateStatus('db', 'error', `Failed to load messages: ${err.message}`);
        addDebugMessage(`Error loading messages: ${err.message}`);
        return null;
      }
    }
    
    // User ID and email display logic for User 1
    document.getElementById('user1-id').addEventListener('change', () => {
      const userId = document.getElementById('user1-id').value;
      fetchUserEmail(userId, 'user1-email');
    });
    
    // User ID and email display logic for User 2
    document.getElementById('user2-id').addEventListener('change', () => {
      const userId = document.getElementById('user2-id').value;
      fetchUserEmail(userId, 'user2-email');
    });
    
    // Hàm lấy email của user theo ID
    async function fetchUserEmail(userId, elementId) {
      if (!supabaseClient || !userId) {
        document.getElementById(elementId).textContent = 'Connect to Supabase first';
        return;
      }
      
      try {
        const { data, error } = await supabaseClient
          .from('users')
          .select('email')
          .eq('id', userId)
          .single();
          
        if (error) {
          document.getElementById(elementId).textContent = 'User not found';
          return;
        }
        
        if (data && data.email) {
          document.getElementById(elementId).textContent = data.email;
          if (elementId === 'user1-email') user1Email = data.email;
          if (elementId === 'user2-email') user2Email = data.email;
        } else {
          document.getElementById(elementId).textContent = 'No email available';
        }
      } catch (err) {
        document.getElementById(elementId).textContent = 'Error fetching user data';
        console.error('Error fetching user email:', err);
      }
    }
    
    // Hàm hiển thị dữ liệu User
    function displayUsers(users) {
      const table = document.getElementById('users-table');
      const tbody = table.getElementsByTagName('tbody')[0];
      tbody.innerHTML = '';
      
      if (users && users.length > 0) {
        users.forEach(user => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${user.id || ''}</td>
            <td>${user.email || ''}</td>
            <td>${user.created_at ? new Date(user.created_at).toLocaleString() : ''}</td>
          `;
          tbody.appendChild(row);
        });
      } else {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="3">No users found</td>`;
        tbody.appendChild(row);
      }
    }
    
    // Hàm hiển thị dữ liệu Chat Rooms
    function displayRooms(rooms) {
      const table = document.getElementById('rooms-table');
      const tbody = table.getElementsByTagName('tbody')[0];
      tbody.innerHTML = '';
      
      if (rooms && rooms.length > 0) {
        rooms.forEach(room => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${room.id || ''}</td>
            <td>${room.name || ''}</td>
            <td>${room.room_type || ''}</td>
            <td>${room.created_at ? new Date(room.created_at).toLocaleString() : ''}</td>
          `;
          tbody.appendChild(row);
        });
      } else {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="4">No rooms found</td>`;
        tbody.appendChild(row);
      }
    }
    
    // Hàm hiển thị dữ liệu Chat Participants
    function displayParticipants(participants) {
      const table = document.getElementById('participants-table');
      const tbody = table.getElementsByTagName('tbody')[0];
      tbody.innerHTML = '';
      
      if (participants && participants.length > 0) {
        participants.forEach(participant => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${participant.id || ''}</td>
            <td>${participant.chat_room_id || ''}</td>
            <td>${participant.user_id || participant.temporary_user_id || ''}</td>
            <td>${participant.participant_role || ''}</td>
            <td>${participant.joined_at ? new Date(participant.joined_at).toLocaleString() : ''}</td>
          `;
          tbody.appendChild(row);
        });
      } else {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="5">No participants found</td>`;
        tbody.appendChild(row);
      }
    }
    
    // Hàm hiển thị dữ liệu Chat Messages
    function displayMessages(messages) {
      const table = document.getElementById('messages-table');
      const tbody = table.getElementsByTagName('tbody')[0];
      tbody.innerHTML = '';
      
      if (messages && messages.length > 0) {
        messages.forEach(message => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${message.id || ''}</td>
            <td>${message.chat_room_id || ''}</td>
            <td>${message.sender_id || message.temporary_sender_id || ''}</td>
            <td>${message.content || ''}</td>
            <td>${message.original_language || ''}</td>
            <td>${message.sent_at ? new Date(message.sent_at).toLocaleString() : ''}</td>
          `;
          tbody.appendChild(row);
        });
      } else {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="6">No messages found</td>`;
        tbody.appendChild(row);
      }
    }
    
    // Điền dữ liệu vào dropdown Users
    function populateUserDropdown(users) {
      const select = document.getElementById('select-user');
      select.innerHTML = '<option value="">Select User</option>';
      
      if (users && users.length > 0) {
        users.forEach(user => {
          const option = document.createElement('option');
          option.value = user.id;
          option.textContent = user.email || user.id;
          select.appendChild(option);
        });
      }
    }
    
    // Điền dữ liệu vào dropdown Rooms
    function populateRoomDropdown(rooms) {
      const select = document.getElementById('select-room');
      select.innerHTML = '<option value="">Select Room</option>';
      
      if (rooms && rooms.length > 0) {
        rooms.forEach(room => {
          const option = document.createElement('option');
          option.value = room.id;
          option.textContent = room.name || room.id;
          select.appendChild(option);
        });
      }
    }
    
    // Chuyển đổi giữa các tab
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        // Ẩn tất cả các tab-pane
        document.querySelectorAll('.tab-pane').forEach(pane => {
          pane.style.display = 'none';
        });
        
        // Xóa lớp active từ tất cả các tab
        document.querySelectorAll('.tab').forEach(t => {
          t.classList.remove('active');
        });
        
        // Hiển thị tab-pane được chọn
        const tabName = tab.getAttribute('data-tab');
        document.getElementById(`${tabName}-tab`).style.display = 'block';
        
        // Thêm lớp active cho tab được chọn
        tab.classList.add('active');
      });
    });
    
    // Kiểm tra sự tham gia của người dùng trong phòng
    document.getElementById('check-participation').addEventListener('click', async () => {
      const selectedUserId = document.getElementById('select-user').value;
      const selectedRoomId = document.getElementById('select-room').value;
      
      if (!supabaseClient) {
        updateStatus('manage', 'error', 'Please connect to Supabase first');
        return;
      }
      
      if (!selectedUserId || !selectedRoomId) {
        updateStatus('manage', 'error', 'Please select both user and room');
        document.getElementById('participation-status').textContent = 'Please select both user and room';
        return;
      }
      
      try {
        addDebugMessage(`Checking if user ${selectedUserId} is in room ${selectedRoomId}...`);
        const { data, error } = await supabaseClient
          .from('chat_participants')
          .select('*')
          .eq('user_id', selectedUserId)
          .eq('chat_room_id', selectedRoomId)
          .eq('is_active', true);
          
        if (error) {
          updateStatus('manage', 'error', `Failed to check participation: ${error.message}`);
          addDebugMessage(`Failed to check participation: ${error.message}`);
          return;
        }
        
        updateStatus('manage', 'success');
        
        if (data && data.length > 0) {
          document.getElementById('participation-status').textContent = 'User is active in this room';
          addDebugMessage('User is active in this room');
        } else {
          document.getElementById('participation-status').textContent = 'User is NOT active in this room';
          addDebugMessage('User is NOT active in this room');
        }
      } catch (err) {
        updateStatus('manage', 'error', `Failed to check participation: ${err.message}`);
        addDebugMessage(`Error checking participation: ${err.message}`);
      }
    });
    
    // Kết nối đến WebSocket server
    document.getElementById('connect-ws').addEventListener('click', () => {
      const wsUrl = document.getElementById('ws-url').value;
      const roomId = document.getElementById('room-id').value;
      const user1Id = document.getElementById('user1-id').value;
      const user2Id = document.getElementById('user2-id').value;
      
      if (!wsUrl) {
        updateStatus('ws', 'error', 'WebSocket URL is required');
        addDebugMessage('WebSocket URL is required');
        return;
      }
      
      if (!roomId) {
        updateStatus('ws', 'error', 'Room ID is required');
        addDebugMessage('Room ID is required');
        return;
      }
      
      if (!jwtToken && !mockMode) {
        addDebugMessage('Warning: No JWT token provided. This may cause authentication issues.');
      }
      
      if (!user1Id) {
        addDebugMessage('User 1 ID is required');
        return;
      }
      
      if (!user2Id) {
        addDebugMessage('User 2 ID is required');
        return;
      }
      
      currentRoomId = roomId;
      connectWebSockets(wsUrl, roomId, user1Id, user2Id);
    });
    
    // Hàm kết nối WebSocket cho cả hai người dùng
    function connectWebSockets(serverUrl, roomId, user1Id, user2Id) {
      try {
        addDebugMessage(`Attempting to connect to WebSocket server: ${serverUrl}`);
        
        // Kết nối WebSocket cho User 1 (Tiếng Việt)
        socket1 = io(serverUrl, {
          auth: { token: jwtToken }
        });
        
        // Kết nối WebSocket cho User 2 (Tiếng Anh)
        socket2 = io(serverUrl, {
          auth: { token: jwtToken }
        });
        
        addDebugMessage('WebSocket connection initiated');
        
        // Xử lý kết nối và các sự kiện cho User 1
        socket1.on('connect', () => {
          addDebugMessage('User 1 connected to WebSocket server');
          document.getElementById('user1-status').textContent = 'Connected';
          
          // Thiết lập thông tin người dùng
          socket1.emit('setup', {
            userId: user1Id,
            preferredLanguage: 'vi',
            deviceId: `test-device-user1-${Date.now()}`
          }, (response) => {
            if (response.success) {
              addDebugMessage(`User 1 setup success: ${JSON.stringify(response.data)}`);
              
              // Tham gia phòng chat
              socket1.emit('join_room', roomId, (joinResponse) => {
                if (joinResponse.success) {
                  addDebugMessage(`User 1 joined room ${roomId}`);
                  addDebugMessage('User 1 ready to chat');
                  updateStatus('chat', 'success');
                } else {
                  addDebugMessage(`User 1 failed to join room: ${joinResponse.error}`);
                  updateStatus('chat', 'error', `User 1 failed to join room: ${joinResponse.error}`);
                }
              });
            } else {
              addDebugMessage(`User 1 setup failed: ${response.error}`);
              updateStatus('ws', 'error', `User 1 setup failed: ${response.error}`);
            }
          });
        });
        
        // Xử lý kết nối và các sự kiện cho User 2
        socket2.on('connect', () => {
          addDebugMessage('User 2 connected to WebSocket server');
          document.getElementById('user2-status').textContent = 'Connected';
          
          // Thiết lập thông tin người dùng
          socket2.emit('setup', {
            userId: user2Id, 
            preferredLanguage: 'en',
            deviceId: `test-device-user2-${Date.now()}`
          }, (response) => {
            if (response.success) {
              addDebugMessage(`User 2 setup success: ${JSON.stringify(response.data)}`);
              
              // Tham gia phòng chat
              socket2.emit('join_room', roomId, (joinResponse) => {
                if (joinResponse.success) {
                  addDebugMessage(`User 2 joined room ${roomId}`);
                  addDebugMessage('User 2 ready to chat');
                } else {
                  addDebugMessage(`User 2 failed to join room: ${joinResponse.error}`);
                }
              });
            } else {
              addDebugMessage(`User 2 setup failed: ${response.error}`);
            }
          });
        });
        
        // Xử lý sự kiện nhận tin nhắn cho User 1
        socket1.on('message_received', (data) => {
          addDebugMessage(`User 1 received message: ${JSON.stringify(data.message)}`);
          displayMessage('user1-messages', data.message, false);
        });
        
        // Xử lý sự kiện nhận tin nhắn cho User 2
        socket2.on('message_received', (data) => {
          addDebugMessage(`User 2 received message: ${JSON.stringify(data.message)}`);
          displayMessage('user2-messages', data.message, false);
        });
        
        // Xử lý sự kiện nhận bản dịch
        socket1.on('translation_received', (data) => {
          addDebugMessage(`User 1 received translation: ${JSON.stringify(data)}`);
          addTranslation('user1-messages', data);
        });
        
        socket2.on('translation_received', (data) => {
          addDebugMessage(`User 2 received translation: ${JSON.stringify(data)}`);
          addTranslation('user2-messages', data);
        });
        
        // Xử lý các sự kiện lỗi và ngắt kết nối
        socket1.on('disconnect', () => {
          addDebugMessage('User 1 disconnected from server');
          document.getElementById('user1-status').textContent = 'Disconnected';
        });
        
        socket2.on('disconnect', () => {
          addDebugMessage('User 2 disconnected from server');
          document.getElementById('user2-status').textContent = 'Disconnected';
        });
        
        socket1.on('error', (error) => {
          addDebugMessage(`User 1 error: ${JSON.stringify(error)}`);
        });
        
        socket2.on('error', (error) => {
          addDebugMessage(`User 2 error: ${JSON.stringify(error)}`);
        });
        
        updateStatus('ws', 'success');
      } catch (err) {
        addDebugMessage(`Failed to connect to WebSocket: ${err.message}`);
        updateStatus('ws', 'error', `Failed to connect to WebSocket: ${err.message}`);
      }
    }
    
    // Gửi tin nhắn từ User 1
    document.getElementById('user1-send').addEventListener('click', () => {
      sendMessageUser1();
    });
    
    document.getElementById('user1-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessageUser1();
      }
    });
    
    // Gửi tin nhắn từ User 2
    document.getElementById('user2-send').addEventListener('click', () => {
      sendMessageUser2();
    });
    
    document.getElementById('user2-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessageUser2();
      }
    });
    
    // Hàm gửi tin nhắn từ User 1
    function sendMessageUser1() {
      const input = document.getElementById('user1-input');
      const content = input.value.trim();
      const userId = document.getElementById('user1-id').value;
      
      if (!content || (!socket1 && !mockMode) || !currentRoomId) {
        return;
      }
      
      addDebugMessage(`User 1 sending message: ${content}`);
      
      // Nếu ở chế độ mock hoặc không có kết nối socket, lưu tin nhắn trực tiếp
      if (mockMode || !socket1.connected) {
        saveMessageDirectlyToDb(userId, null, currentRoomId, content, 'vi', 'user1');
        return;
      }
      
      // Đầu tiên thử gửi thông qua socket
      socket1.emit('send_message', {
        roomId: currentRoomId,
        content,
        originalLanguage: 'vi'
      }, async (response) => {
        if (response && response.success) {
          addDebugMessage('User 1 sent message successfully');
          displayMessage('user1-messages', response.data.message, true);
          input.value = '';
        } else {
          addDebugMessage(`User 1 failed to send message: ${response ? response.error : 'No response'}`);
          
          // Nếu việc gửi qua socket thất bại, thử lưu trực tiếp vào cơ sở dữ liệu
          saveMessageDirectlyToDb(userId, null, currentRoomId, content, 'vi', 'user1');
        }
      });
    }
    
    // Hàm gửi tin nhắn từ User 2
    function sendMessageUser2() {
      const input = document.getElementById('user2-input');
      const content = input.value.trim();
      const userId = document.getElementById('user2-id').value;
      
      if (!content || (!socket2 && !mockMode) || !currentRoomId) {
        return;
      }
      
      addDebugMessage(`User 2 sending message: ${content}`);
      
      // Nếu ở chế độ mock hoặc không có kết nối socket, lưu tin nhắn trực tiếp
      if (mockMode || !socket2.connected) {
        saveMessageDirectlyToDb(userId, null, currentRoomId, content, 'en', 'user2');
        return;
      }
      
      // Đầu tiên thử gửi thông qua socket
      socket2.emit('send_message', {
        roomId: currentRoomId,
        content,
        originalLanguage: 'en'
      }, async (response) => {
        if (response && response.success) {
          addDebugMessage('User 2 sent message successfully');
          displayMessage('user2-messages', response.data.message, true);
          input.value = '';
        } else {
          addDebugMessage(`User 2 failed to send message: ${response ? response.error : 'No response'}`);
          
          // Nếu việc gửi qua socket thất bại, thử lưu trực tiếp vào cơ sở dữ liệu
          saveMessageDirectlyToDb(userId, null, currentRoomId, content, 'en', 'user2');
        }
      });
    }
    
    // Hàm lưu tin nhắn trực tiếp vào cơ sở dữ liệu hoặc mock
    async function saveMessageDirectlyToDb(userId, temporaryUserId, roomId, content, originalLanguage, sender) {
      try {
        if (!supabaseClient) {
          addDebugMessage('Error sending message directly to database: Supabase not connected');
          return;
        }
        
        const messageData = {
          chat_room_id: roomId,
          sender_id: userId,
          temporary_sender_id: temporaryUserId,
          content: content,
          original_language: originalLanguage,
          sent_at: new Date().toISOString(),
          is_translated: false
        };
        
        // Nếu đang ở chế độ mock
        if (mockMode) {
          // Thêm ID ngay lập tức
          messageData.id = generateUUID();
          
          // Thêm vào dữ liệu mock
          mockData.messages.push(messageData);
          
          addDebugMessage(`${sender === 'user1' ? 'User 1' : 'User 2'} message saved to mock database`);
          
          // Hiển thị tin nhắn
          displayMessage(`${sender}-messages`, messageData, true);
          document.getElementById(`${sender}-input`).value = '';
          
          // Giả lập nhận tin nhắn ở phía bên kia
          const otherSender = sender === 'user1' ? 'user2' : 'user1';
          setTimeout(() => {
            displayMessage(`${otherSender}-messages`, messageData, false);
          }, 300);
          
          // Cập nhật bảng messages
          await loadMessages();
          return;
        }
        
        // Sử dụng Supabase thật
        const { data, error } = await supabaseClient
          .from('chat_messages')
          .insert([messageData])
          .select();
        
        if (error) {
          addDebugMessage(`Failed to save message directly to database: ${error.message}`);
        } else {
          addDebugMessage(`${sender === 'user1' ? 'User 1' : 'User 2'} message saved directly to database`);
          displayMessage(`${sender}-messages`, data[0], true);
          document.getElementById(`${sender}-input`).value = '';
          
          // Reload messages để cập nhật bảng
          await loadMessages();
        }
      } catch (err) {
        addDebugMessage(`Error saving message directly to database: ${err.message}`);
      }
    }
 // Hàm hiển thị tin nhắn trong giao diện chat
    function displayMessage(containerId, message, isSent) {
      const messagesContainer = document.getElementById(containerId);
      const messageEl = document.createElement('div');
      messageEl.className = `message ${isSent ? 'sent' : 'received'}`;
      messageEl.dataset.messageId = message.id;
      
      // Nội dung tin nhắn
      const contentEl = document.createElement('div');
      contentEl.textContent = message.content;
      messageEl.appendChild(contentEl);
      
      // Thông tin về thời gian và ngôn ngữ
      const infoEl = document.createElement('div');
      infoEl.style.fontSize = '0.8em';
      infoEl.style.color = '#666';
      infoEl.textContent = `[${message.original_language}] ${new Date(message.sent_at).toLocaleTimeString()}`;
      messageEl.appendChild(infoEl);
      
      messagesContainer.appendChild(messageEl);
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Hàm thêm bản dịch vào tin nhắn
    function addTranslation(containerId, data) {
      const { messageId, language, translatedContent } = data;
      const messagesContainer = document.getElementById(containerId);
      const messageEl = messagesContainer.querySelector(`[data-message-id="${messageId}"]`);
      
      if (messageEl) {
        // Kiểm tra xem đã có bản dịch cho ngôn ngữ này chưa
        const existingTranslation = messageEl.querySelector(`[data-language="${language}"]`);
        if (existingTranslation) {
          existingTranslation.textContent = `[${language}] ${translatedContent}`;
        } else {
          const translationEl = document.createElement('div');
          translationEl.className = 'translation';
          translationEl.dataset.language = language;
          translationEl.textContent = `[${language}] ${translatedContent}`;
          translationEl.style.fontStyle = 'italic';
          translationEl.style.color = '#666';
          messageEl.appendChild(translationEl);
        }
      }
    }
    
    // Khởi tạo giao diện
    document.addEventListener('DOMContentLoaded', () => {
      // Hiển thị/ẩn console debug
      document.getElementById('show-debug').addEventListener('change', (e) => {
        document.getElementById('debug-console').style.display = e.target.checked ? 'block' : 'none';
      });
      
      // Thiết lập option giá trị mặc định
      document.getElementById('mock-supabase').checked = true;
      mockMode = true;
      
      // Tự động điền User ID cho User 2
      if (!document.getElementById('user2-id').value) {
        document.getElementById('user2-id').value = 'a9813ae-9a46-4dc9-9fa3-6f04062f7e50';
      }
      
      // Thêm event listener cho các nút load
      document.getElementById('load-users').addEventListener('click', loadUsers);
      document.getElementById('load-rooms').addEventListener('click', loadRooms);
      document.getElementById('load-participants').addEventListener('click', loadParticipants);
      document.getElementById('load-messages').addEventListener('click', loadMessages);
      
      // Thêm event listener cho checkbox mockMode
      document.getElementById('mock-supabase').addEventListener('change', (e) => {
        mockMode = e.target.checked;
        if (mockMode) {
          addDebugMessage('Switched to Mock Mode');
        } else {
          addDebugMessage('Switched to Real Connection Mode');
        }
      });
    });
  </script>
</body>
</html>