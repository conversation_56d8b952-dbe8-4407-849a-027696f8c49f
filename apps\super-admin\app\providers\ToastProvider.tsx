'use client';
import React from 'react';
import { Toaster } from 'react-hot-toast';

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <>
      {children}
      <Toaster 
        position="top-right" 
        toastOptions={{
          style: {
            background: '#333',
            color: '#fff',
            fontSize: '14px',
            padding: '16px 20px',
          },
          success: {
            style: {
              background: '#22C55E',
            },
          },
          error: {
            style: {
              background: '#EF4444',
            },
          }
        }}
      />
    </>
  );
};
