import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '../../../../../lib/supabase/admin';
import { cookies } from 'next/headers';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Trả về hình ảnh QR code
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const qrCodeId = params.id;
    
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return new Response('Tenant ID not found', { status: 400 });
    }
    
    // Lấy tham số kích thước từ query
    const searchParams = request.nextUrl.searchParams;
    const size = parseInt(searchParams.get('size') || '200');
    const margin = parseInt(searchParams.get('margin') || '4');
    
    // Tạo Supabase client
    const supabase = createAdminClient(cookies());
    
    // Lấy thông tin QR code
    const { data: qrCode, error } = await supabase
      .from('tenant_qr_codes')
      .select('*')
      .eq('id', qrCodeId)
      .eq('tenant_id', tenant_id)
      .single();
    
    if (error) {
      console.error('Error fetching QR code:', error);
      return new Response('QR code not found', { status: 404 });
    }
    
    // Tạo URL hoặc dữ liệu để mã hóa thành QR code
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://app.loaloa.com';
    const qrData = `loaloa:qr:${qrCode.id}`;
    
    // Tạo QR code
    const qrImage = await QRCode.toBuffer(qrData, {
      width: size,
      margin: margin,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'H'
    });
    
    // Trả về hình ảnh QR code
    return new Response(qrImage, {
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'public, max-age=86400'
      }
    });
    
  } catch (error) {
    console.error('Error generating QR code image:', error);
    return new Response('Error generating QR code image', { status: 500 });
  }
}
