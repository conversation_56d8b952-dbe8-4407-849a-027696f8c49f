// Thay thế toàn bộ useChat hook:
'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { createClientSupabase } from '../lib/supabase';

export interface ChatMessage {
  id: string;
  content: string;
  sender_type: 'guest' | 'staff';
  sender_name: string;
  created_at: string;
  is_translated: boolean;
  original_content?: string;
  translated_content?: string;
  original_language?: string;
  translated_language?: string;
  translation_confidence?: number;
  show_translation?: boolean;
}

export interface ChatSession {
  id: string;
  tenant_id: string;
  guest_id?: string;
  status: string;
  guest_language: string;
  staff_language?: string;
  auto_translate: boolean;
  priority: string;
  source_type?: string;
  source_qr_code_id?: string;
  created_at: string;
  updated_at: string;
}

interface UseChatProps {
  sessionId: string;
  guestId: string;
  autoTranslate: boolean;
  guestLanguage: string;
}

export function useChat({ sessionId, guestId, autoTranslate, guestLanguage }: UseChatProps) {
  const router = useRouter();
  const [session, setSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [realtimeConnected, setRealtimeConnected] = useState(false);
  const [usePollingFallback, setUsePollingFallback] = useState(false);

  // Refs for cleanup and tracking
  const supabaseRef = useRef<any>(null);
  const subscriptionsRef = useRef<any[]>([]);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);
  const lastMessageIdRef = useRef<string | null>(null);
  const lastCheckTimeRef = useRef<number>(Date.now());
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const realtimeRetryCountRef = useRef(0);

  // Initialize Supabase client
  useEffect(() => {
    try {
      supabaseRef.current = createClientSupabase();
      setConnected(true);
      console.log('✅ Supabase client initialized for realtime');
    } catch (err) {
      console.error('❌ Failed to initialize Supabase:', err);
      setError('Failed to connect to chat service');
      setConnected(false);
    }
  }, []);

  // Cleanup subscriptions
  const cleanupSubscriptions = useCallback(() => {
    console.log('🧹 Cleaning up realtime subscriptions');
    subscriptionsRef.current.forEach(subscription => {
      try {
        subscription.unsubscribe();
      } catch (err) {
        console.warn('Warning: Failed to unsubscribe:', err);
      }
    });
    subscriptionsRef.current = [];
    setRealtimeConnected(false);
  }, []);

  // Load session data
  const loadSession = useCallback(async () => {
    if (!sessionId || !mountedRef.current) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/chat-sessions/${sessionId}`);

      if (!response.ok) {
        throw new Error(`Failed to load session: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.session) {
        setSession(data.session);
        setError(null);
        console.log('✅ Session loaded:', data.session.id);
      } else {
        throw new Error(data.error || 'Invalid session data');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load session';
      setError(errorMessage);
      console.error('❌ Session load error:', err);
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, [sessionId]);

  // Load initial messages
  const loadInitialMessages = useCallback(async () => {
    if (!sessionId || !mountedRef.current) return;

    try {
      console.log('📨 Loading initial messages for session:', sessionId);

      const response = await fetch(`/api/messages?session_id=${sessionId}&limit=50`);

      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.messages)) {
          setMessages(data.messages);
          if (data.messages.length > 0) {
            lastMessageIdRef.current = data.messages[data.messages.length - 1].id;
          }
          console.log('✅ Initial messages loaded:', data.messages.length);
        }
      }
    } catch (err) {
      console.error('❌ Failed to load initial messages:', err);
    }
  }, [sessionId]);

  // Setup Realtime subscriptions
  const setupRealtimeSubscriptions = useCallback(() => {
    if (!supabaseRef.current || !sessionId || !mountedRef.current) return;

    console.log('🔄 Setting up realtime subscriptions for session:', sessionId);

    try {
      // Messages subscription
      const messagesChannel = supabaseRef.current
        .channel(`messages:${sessionId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'tenant_chat_messages',
            filter: `chat_session_id=eq.${sessionId}`
          },
          (payload: any) => {
            console.log('🔔 New message received via realtime:', payload.new);
            const newMessage = payload.new as ChatMessage;

            if (mountedRef.current) {
              setMessages(prev => {
                // Avoid duplicates
                const exists = prev.some(msg => msg.id === newMessage.id);
                if (exists) return prev;

                return [...prev, newMessage];
              });

              lastMessageIdRef.current = newMessage.id;

              // Show notification for staff messages
              if (newMessage.sender_type === 'staff' && document.hidden) {
                if (Notification.permission === 'granted') {
                  new Notification('New message from staff', {
                    body: newMessage.content.substring(0, 100),
                    icon: '/favicon.ico'
                  });
                }
              }
            }
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'tenant_chat_messages',
            filter: `chat_session_id=eq.${sessionId}`
          },
          (payload: any) => {
            console.log('🔄 Message updated via realtime:', payload.new);
            const updatedMessage = payload.new as ChatMessage;

            if (mountedRef.current) {
              setMessages(prev =>
                prev.map(msg => msg.id === updatedMessage.id ? updatedMessage : msg)
              );
            }
          }
        )
        .subscribe((status: string) => {
          console.log('📡 Messages subscription status:', status);

          if (status === 'SUBSCRIBED') {
            setRealtimeConnected(true);
            setUsePollingFallback(false);
            realtimeRetryCountRef.current = 0;
            console.log('✅ Realtime messages subscription active');
          } else if (status === 'SUBSCRIPTION_ERROR' || status === 'CLOSED') {
            console.warn('⚠️ Realtime subscription failed, falling back to polling');
            setRealtimeConnected(false);
            setUsePollingFallback(true);
            realtimeRetryCountRef.current++;
          }
        });

      // Session status subscription
      const sessionChannel = supabaseRef.current
        .channel(`session:${sessionId}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'tenant_chat_sessions',
            filter: `id=eq.${sessionId}`
          },
          (payload: any) => {
            console.log('🔄 Session updated via realtime:', payload.new);
            if (mountedRef.current) {
              setSession(payload.new as ChatSession);
            }
          }
        )
        .subscribe((status: string) => {
          console.log('📡 Session subscription status:', status);
        });

      // Store subscriptions for cleanup
      subscriptionsRef.current = [messagesChannel, sessionChannel];

    } catch (err) {
      console.error('❌ Failed to setup realtime subscriptions:', err);
      setUsePollingFallback(true);
    }
  }, [sessionId]);

  // Send message with realtime support
  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!sessionId || !content.trim() || !mountedRef.current) {
      return false;
    }

    try {
      console.log('📤 Sending guest message:', content.substring(0, 50));

      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: sessionId,
          content: content.trim(),
          sender_type: 'guest',
          sender_name: 'Guest',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.error || `Failed to send message: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.message) {
        console.log('✅ Guest message sent successfully');

        // If realtime is not connected, add message immediately for instant feedback
        if (!realtimeConnected) {
          setMessages(prev => {
            const exists = prev.some(msg => msg.id === data.message.id);
            if (exists) return prev;
            return [...prev, data.message];
          });
          lastMessageIdRef.current = data.message.id;
        }
        // If realtime is connected, the message will be added via subscription

        return true;
      } else {
        throw new Error(data.error || 'Invalid response format');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessage);
      console.error('❌ Send message error:', err);
      return false;
    }
  }, [sessionId, realtimeConnected]);

  // Fallback polling for when realtime fails
  const startPollingFallback = useCallback(() => {
    if (!usePollingFallback || pollingIntervalRef.current) return;

    console.log('🔄 Starting polling fallback');

    pollingIntervalRef.current = setInterval(async () => {
      if (mountedRef.current && sessionId && !document.hidden) {
        try {
          const response = await fetch(`/api/messages?session_id=${sessionId}&limit=50`);
          if (response.ok) {
            const data = await response.json();
            if (data.success && Array.isArray(data.messages)) {
              setMessages(data.messages);
            }
          }
        } catch (err) {
          console.error('Polling error:', err);
        }
      }
    }, 5000);
  }, [usePollingFallback, sessionId]);

  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
      console.log('⏹️ Polling stopped');
    }
  }, []);

  // Enhanced typing indicators with realtime broadcast
  const startTyping = useCallback(() => {
    if (!supabaseRef.current || !sessionId || !guestId) return;

    const typingChannel = supabaseRef.current.channel(`typing:${sessionId}`);
    typingChannel.send({
      type: 'broadcast',
      event: 'typing',
      payload: { user_id: guestId, user_type: 'guest' }
    });
  }, [sessionId, guestId]);

  const stopTyping = useCallback(() => {
    if (!supabaseRef.current || !sessionId || !guestId) return;

    const typingChannel = supabaseRef.current.channel(`typing:${sessionId}`);
    typingChannel.send({
      type: 'broadcast',
      event: 'stop_typing',
      payload: { user_id: guestId, user_type: 'guest' }
    });
  }, [sessionId, guestId]);

  // Auto-translate setting
  const setAutoTranslate = useCallback((enabled: boolean) => {
    console.log('Auto-translate setting:', enabled);
  }, []);

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && sessionId && mountedRef.current) {
        // Page became visible
        if (!realtimeConnected && !usePollingFallback) {
          // Try to reconnect realtime
          setupRealtimeSubscriptions();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [sessionId, realtimeConnected, usePollingFallback, setupRealtimeSubscriptions]);

  // Initialize hook
  useEffect(() => {
    mountedRef.current = true;

    if (sessionId) {
      loadSession();
    }

    // Request notification permission
    if (Notification.permission === 'default') {
      Notification.requestPermission();
    }

    return () => {
      mountedRef.current = false;
      stopPolling();
      cleanupSubscriptions();
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [sessionId, loadSession, stopPolling, cleanupSubscriptions]);

  // Setup realtime after session is loaded
  useEffect(() => {
    if (session && connected && supabaseRef.current) {
      loadInitialMessages();
      setupRealtimeSubscriptions();
    }

    return () => {
      cleanupSubscriptions();
      stopPolling();
    };
  }, [session, connected, loadInitialMessages, setupRealtimeSubscriptions, cleanupSubscriptions, stopPolling]);

  // Start polling fallback when needed
  useEffect(() => {
    if (usePollingFallback) {
      startPollingFallback();
    } else {
      stopPolling();
    }
  }, [usePollingFallback, startPollingFallback, stopPolling]);

  return {
    session,
    messages,
    loading,
    connected: connected && (realtimeConnected || usePollingFallback),
    sendMessage,
    isTyping,
    startTyping,
    stopTyping,
    setAutoTranslate,
    error,
    realtimeConnected,
    usePollingFallback,
  };
}

export default useChat;
