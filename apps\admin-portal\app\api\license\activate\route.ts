import { NextResponse } from 'next/server'; 
import { createClient } from '@supabase/supabase-js';
import os from 'os';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

// Tạo hardware fingerprint từ thông tin hệ thống 
function generateHardwareFingerprint() { 
  const cpus = os.cpus(); 
  const network = os.networkInterfaces(); 
  const platform = os.platform(); 
  const release = os.release(); 
  const hostname = os.hostname();

  // Kết hợp các thông tin này để tạo fingerprint 
  const systemInfo = JSON.stringify({ 
    cpuModel: cpus[0]?.model || '', 
    cpuCount: cpus.length, 
    network: Object.keys(network), 
    platform, 
    release, 
    hostname 
  });

  // Hash thông tin để tạo fingerprint 
  return crypto.createHash('sha256').update(systemInfo).digest('hex'); 
}

export async function POST(request: Request) { 
  try { 
    console.log('License activation API called');

    // L<PERSON>y thông tin từ request 
    const body = await request.json();
    const { licenseKey, customerName, email } = body;
    console.log('Received activation request:', { licenseKey, customerName, email });

    // Kiểm tra đầu vào 
    if (!licenseKey || !customerName || !email) {
      return NextResponse.json({ 
        success: false, 
        message: 'Thiếu thông tin cần thiết' 
      }, { status: 400 });
    }

    // Tạo hardware fingerprint 
    const hardwareFingerprint = generateHardwareFingerprint();
    console.log('Generated hardware fingerprint:', hardwareFingerprint);

    // Kết nối đến Supabase 
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://iwzwbrbmojvvvfstbqow.supabase.co',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0ja-GYxJVa8NlwjESiPDxVVZQhH91MUzScsXRxw3mok'
    );

    // Kiểm tra license key trong database - CHỈNH SỬA TRUY VẤN
    const { data: license, error: licenseError } = await supabase
      .from('licenses')
      .select('*')
      .eq('license_key', licenseKey)
      .single();

    if (licenseError || !license) {
      console.error('License not found:', licenseError);
      return NextResponse.json({ 
        success: false, 
        message: 'License key không hợp lệ hoặc không tồn tại' 
      }, { status: 400 });
    }

    console.log('License found:', license);

    // Lấy thông tin tenant nếu có tenant_id
    let tenantData = null;
    if (license.tenant_id) {
      const { data: tenant, error: tenantError } = await supabase
        .from('tenants')
        .select('*')
        .eq('id', license.tenant_id)
        .single();
        
      if (!tenantError && tenant) {
        tenantData = tenant;
        console.log('Tenant found:', tenant);
      } else {
        console.warn('Tenant not found:', tenantError);
      }
    }

    // Kiểm tra trạng thái active 
    if (!license.is_active) {
      return NextResponse.json({ 
        success: false, 
        message: 'License đã bị vô hiệu hóa' 
      }, { status: 400 });
    }

    // Kiểm tra thời hạn 
    const now = new Date();
    const expiryDate = license.expiry_date ? new Date(license.expiry_date) : null;

    if (expiryDate && now > expiryDate) {
      return NextResponse.json({ 
        success: false, 
        message: `License đã hết hạn vào ${expiryDate.toLocaleDateString()}` 
      }, { status: 400 });
    }

    // Cập nhật hardware fingerprint nếu chưa có 
    if (!license.hardware_fingerprint) {
      await supabase
        .from('licenses')
        .update({ hardware_fingerprint: hardwareFingerprint })
        .eq('id', license.id);
    }
    
    // Lấy tenant_id từ license
    const tenant_id = license.tenant_id;
    
    // Nếu không có tenant_id, thông báo lỗi
    if (!tenant_id) {
      return NextResponse.json({
        success: false,
        message: 'License không được gán cho tenant nào'
      }, { status: 400 });
    }
    
    // Cập nhật lần cuối check-in và đếm số lần 
    await supabase
      .from('licenses')
      .update({ 
        last_check_in: now.toISOString(),
        check_in_count: (license.check_in_count || 0) + 1,
        activation_date: license.activation_date || now.toISOString()
      })
      .eq('id', license.id);

    // Lưu thông tin vào file config 
    const configPath = path.resolve('./license_config.json');
    console.log('Saving license config to:', configPath);

    // Tạo đối tượng config với đầy đủ thông tin bao gồm tenant_id
    const configData = {
      licenseKey,
      customerName: license.customer_name || customerName,
      email,
      tenant_id: tenant_id
    };

    try {
      fs.writeFileSync(configPath, JSON.stringify(configData, null, 2));
      console.log('License config file created/updated successfully');
    } catch (fileError) {
      console.error('Error creating/updating license config file:', fileError);
      return NextResponse.json({ 
        success: false, 
        message: 'Không thể lưu thông tin license' 
      }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      message: 'License đã được kích hoạt thành công',
      license_info: {
        licenseKey,
        customerName: license.customer_name || (tenantData ? tenantData.name : customerName),
        email,
        tenant_id: tenant_id,
        expiryDate: license.expiry_date
      }
    });
  } 
  catch (error) { 
    console.error('Error during license activation:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'Đã xảy ra lỗi trong quá trình kích hoạt license' 
    }, { status: 500 });
  } 
}
