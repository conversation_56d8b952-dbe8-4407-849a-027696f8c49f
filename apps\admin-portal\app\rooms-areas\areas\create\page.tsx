'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import DashboardLayout from '../../../dashboard-layout';
import AreaForm from '../../../components/areas/AreaForm';
import styles from './create-area.module.scss';

export default function CreateAreaPage() {
  const router = useRouter();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [createdArea, setCreatedArea] = useState<any>(null);
  
  const handleSubmit = async (formData: any) => {
    try {
      setSubmitError(null);
      const response = await fetch('/api/areas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create area');
      }
      
      // Xử lý kết quả thành công
      const data = await response.json();
      setCreatedArea(data.data);
      setIsSuccess(true);
      
    } catch (error: any) {
      setSubmitError(error.message);
      throw error;
    }
  };
  
  const handleCreateAnother = () => {
    setIsSuccess(false);
    setCreatedArea(null);
  };
  
  return (
    <DashboardLayout>
      <div className={styles.container}>
        {!isSuccess ? (
          <>
            <div className={styles.header}>
              <Link href="/rooms-areas/areas" className={styles.backButton}>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M10.6667 2.66667L5.33333 8.00001L10.6667 13.3333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Quay lại
              </Link>
              <h1 className={styles.title}>Tạo khu vực mới</h1>
            </div>
            
            {submitError && (
              <div className={styles.error}>
                <p>Lỗi: {submitError}</p>
              </div>
            )}
            
            <div className={styles.formContainer}>
              <AreaForm onSubmit={handleSubmit} />
            </div>
          </>
        ) : (
          <div className={styles.successContainer}>
            <div className={styles.successCard}>
              <div className={styles.successIcon}>
                <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="24" cy="24" r="24" fill="#10B981" fillOpacity="0.1"/>
                  <path d="M32 20L22 30L18 26" stroke="#10B981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <h2 className={styles.successTitle}>Tạo khu vực thành công!</h2>
              <p className={styles.successDesc}>
                Khu vực {createdArea?.name} đã được tạo thành công.
              </p>
              <div className={styles.successActions}>
                <button 
                  className={styles.primaryButton}
                  onClick={() => router.push('/rooms-areas/areas')}
                >
                  Quay về danh sách khu vực
                </button>
                <button 
                  className={styles.secondaryButton}
                  onClick={handleCreateAnother}
                >
                  Tạo khu vực mới
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}