import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '../../../../../lib/supabase/admin';
import { cookies } from 'next/headers';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { createCanvas, loadImage } from 'canvas';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Tạo và tải xuống QR code với logo và tên
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const qrCodeId = params.id;
    
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return new Response('Tenant ID not found', { status: 400 });
    }
    
    // Lấy tham số từ query
    const searchParams = request.nextUrl.searchParams;
    const size = parseInt(searchParams.get('size') || '600');
    const margin = parseInt(searchParams.get('margin') || '20');
    const includeLogo = searchParams.get('logo') !== 'false';
    
    // Tạo Supabase client
    const supabase = createAdminClient(cookies());
    
    // Lấy thông tin QR code và tenant
    const { data: qrCode, error } = await supabase
      .from('tenant_qr_codes')
      .select(`
        *,
        tenant_qr_code_types (*),
        tenant_rooms (room_number),
        tenant_areas (name)
      `)
      .eq('id', qrCodeId)
      .eq('tenant_id', tenant_id)
      .single();
    
    if (error) {
      console.error('Error fetching QR code:', error);
      return new Response('QR code not found', { status: 404 });
    }
    
    // Lấy thông tin tenant
    const { data: tenant } = await supabase
      .from('tenants')
      .select('name, logo_url')
      .eq('id', tenant_id)
      .single();
    
    // Tạo dữ liệu QR code
    const qrData = `loaloa:qr:${qrCode.id}`;
    
    // Tạo canvas với kích thước phù hợp (bao gồm cả phần tiêu đề và logo)
    const canvasSize = size + margin * 2;
    const canvas = createCanvas(canvasSize, canvasSize + 100); // Thêm 100px cho phần tiêu đề
    const ctx = canvas.getContext('2d');
    
    // Vẽ nền trắng
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, canvasSize, canvasSize + 100);
    
    // Tạo QR code
    const qrBuffer = await QRCode.toBuffer(qrData, {
      width: size,
      margin: 0,  // Không cần margin vì chúng ta sẽ tự xử lý
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'H'
    });
    
    // Tải QR code lên canvas
    const qrImage = await loadImage(qrBuffer);
    ctx.drawImage(qrImage, margin, margin, size, size);
    
    // Thêm logo nếu cần
    if (includeLogo && tenant?.logo_url) {
      try {
        // Tải logo
        const logo = await loadImage(tenant.logo_url);
        
        // Tính toán kích thước và vị trí logo (1/4 kích thước QR code)
        const logoSize = size / 4;
        const logoX = margin + (size - logoSize) / 2;
        const logoY = margin + (size - logoSize) / 2;
        
        // Vẽ nền trắng cho logo
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(logoX, logoY, logoSize, logoSize);
        
        // Vẽ logo
        ctx.drawImage(logo, logoX, logoY, logoSize, logoSize);
      } catch (logoError) {
        console.error('Error loading logo:', logoError);
        // Tiếp tục mà không có logo
      }
    }
    
    // Thêm tên QR code
    ctx.fillStyle = '#111827';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(qrCode.name, canvasSize / 2, size + margin * 2 + 30);
    
    // Thêm thông tin vị trí (phòng hoặc khu vực)
    let locationText = '';
    if (qrCode.tenant_rooms) {
      locationText = `Room ${qrCode.tenant_rooms.room_number}`;
    } else if (qrCode.tenant_areas) {
      locationText = `Area: ${qrCode.tenant_areas.name}`;
    }
    
    if (locationText) {
      ctx.font = '14px Arial';
      ctx.fillStyle = '#4b5563';
      ctx.fillText(locationText, canvasSize / 2, size + margin * 2 + 55);
    }
    
    // Thêm tên tenant ở dưới cùng
    if (tenant?.name) {
      ctx.font = '12px Arial';
      ctx.fillStyle = '#6b7280';
      ctx.fillText(tenant.name, canvasSize / 2, size + margin * 2 + 80);
    }
    
    // Chuyển canvas thành buffer PNG
    const buffer = canvas.toBuffer('image/png');
    
    // Tạo tên file để tải xuống
    const fileName = `qrcode-${qrCode.name.toLowerCase().replace(/\s+/g, '-')}.png`;
    
    // Trả về hình ảnh QR code để tải xuống
    return new Response(buffer, {
      headers: {
        'Content-Type': 'image/png',
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Cache-Control': 'no-cache'
      }
    });
    
  } catch (error) {
    console.error('Error generating downloadable QR code:', error);
    return new Response('Error generating QR code for download', { status: 500 });
  }
}
