'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '../../dashboard-layout';
import styles from './create-assignment.module.scss';
import { <PERSON><PERSON>, Button } from '@ui';

export default function CreateStaffAssignmentPage() {
  const router = useRouter();
  
  // Form state
  const [formData, setFormData] = useState({
    user_id: '',
    department: '',
    assignment_type: 'general',
    priority: 1,
    working_hours: {
      start_hour: 9,
      end_hour: 17,
      days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
    },
    is_active: true
  });
  
  // Users list for select dropdown
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  
  // Fetch users on component mount
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await fetch('/api/users?limit=100');
        const result = await response.json();
        setUsers(result.data || []);
      } catch (err) {
        console.error('Error fetching users:', err);
      }
    };
    
    fetchUsers();
  }, []);
  
  // Available departments
  const departments = [
    { value: 'reception', label: 'Reception' },
    { value: 'housekeeping', label: 'Housekeeping' },
    { value: 'restaurant', label: 'Restaurant' },
    { value: 'spa', label: 'Spa' },
    { value: 'concierge', label: 'Concierge' },
    { value: 'maintenance', label: 'Maintenance' }
  ];
  
  // Available assignment types
  const assignmentTypes = [
    { value: 'general', label: 'General' },
    { value: 'specialized', label: 'Specialized' },
    { value: 'supervisor', label: 'Supervisor' }
  ];
  
  // Handle input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // Handle working hours changes
  const handleWorkingHoursChange = (e) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      working_hours: {
        ...prev.working_hours,
        [name]: name === 'days' ? value : parseInt(value, 10)
      }
    }));
  };
  
  // Handle working days changes
  const handleDayToggle = (day) => {
    setFormData(prev => {
      const currentDays = prev.working_hours.days || [];
      const newDays = currentDays.includes(day)
        ? currentDays.filter(d => d !== day)
        : [...currentDays, day];
        
      return {
        ...prev,
        working_hours: {
          ...prev.working_hours,
          days: newDays
        }
      };
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.user_id) {
      setError('Please select a staff member');
      return;
    }
    if (!formData.department) {
      setError('Please select a department');
      return;
    }
    
    setError(null);
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/staff-assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to create staff assignment');
      }
      
      setSuccess(true);
      // Redirect after short delay
      setTimeout(() => {
        router.push('/staff-assignments');
      }, 2000);
      
    } catch (err) {
      console.error('Error creating assignment:', err);
      setError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.pageHeader}>
          <Link href="/staff-assignments" className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.33333 5.83331L4.16666 9.99998L8.33333 14.1666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to Staff Assignments
          </Link>
          <h1 className={styles.pageTitle}>Create Staff Assignment</h1>
        </div>
        
        {error && (
          <Alert 
            variant="error" 
            title="Error" 
            closable 
            onClose={() => setError(null)}
          >
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert 
            variant="success" 
            title="Success" 
            closable={false}
          >
            Staff assignment created successfully. Redirecting...
          </Alert>
        )}
        
        <div className={styles.formCard}>
          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.formGroup}>
              <label htmlFor="user_id" className={styles.label}>
                Staff Member <span className={styles.required}>*</span>
              </label>
              <select 
                id="user_id" 
                name="user_id" 
                value={formData.user_id} 
                onChange={handleChange}
                className={styles.select}
                required
                disabled={isSubmitting}
              >
                <option value="">-- Select Staff Member --</option>
                {users.map(user => (
                  <option key={user.id} value={user.id}>
                    {user.display_name} ({user.email})
                  </option>
                ))}
              </select>
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="department" className={styles.label}>
                Department <span className={styles.required}>*</span>
              </label>
              <select 
                id="department" 
                name="department" 
                value={formData.department} 
                onChange={handleChange}
                className={styles.select}
                required
                disabled={isSubmitting}
              >
                <option value="">-- Select Department --</option>
                {departments.map(dept => (
                  <option key={dept.value} value={dept.value}>
                    {dept.label}
                  </option>
                ))}
              </select>
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="assignment_type" className={styles.label}>
                Assignment Type
              </label>
              <select 
                id="assignment_type" 
                name="assignment_type" 
                value={formData.assignment_type} 
                onChange={handleChange}
                className={styles.select}
                disabled={isSubmitting}
              >
                {assignmentTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="priority" className={styles.label}>
                Priority
              </label>
              <input 
                type="number" 
                id="priority" 
                name="priority" 
                value={formData.priority} 
                onChange={handleChange}
                className={styles.input}
                min="1"
                max="10"
                disabled={isSubmitting}
              />
              <div className={styles.helpText}>
                Higher priority staff members will be assigned chats first (1-10)
              </div>
            </div>
            
            <div className={styles.formGroup}>
              <label className={styles.label}>Working Hours</label>
              <div className={styles.hoursContainer}>
                <div className={styles.timeInput}>
                  <label htmlFor="start_hour">Start</label>
                  <input 
                    type="number" 
                    id="start_hour" 
                    name="start_hour" 
                    value={formData.working_hours.start_hour} 
                    onChange={handleWorkingHoursChange}
                    className={styles.timeField}
                    min="0"
                    max="23"
                    disabled={isSubmitting}
                  />
                </div>
                <div className={styles.timeInput}>
                  <label htmlFor="end_hour">End</label>
                  <input 
                    type="number" 
                    id="end_hour" 
                    name="end_hour" 
                    value={formData.working_hours.end_hour} 
                    onChange={handleWorkingHoursChange}
                    className={styles.timeField}
                    min="0"
                    max="24"
                    disabled={isSubmitting}
                  />
                </div>
              </div>
            </div>
            
            <div className={styles.formGroup}>
              <label className={styles.label}>Working Days</label>
              <div className={styles.daysContainer}>
                {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map(day => (
                  <div key={day} className={styles.dayOption}>
                    <input 
                      type="checkbox" 
                      id={`day-${day}`} 
                      checked={formData.working_hours.days.includes(day)}
                      onChange={() => handleDayToggle(day)}
                      disabled={isSubmitting}
                    />
                    <label htmlFor={`day-${day}`}>
                      {day.charAt(0).toUpperCase() + day.slice(1)}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            
            <div className={styles.formGroup}>
              <div className={styles.checkboxContainer}>
                <input 
                  type="checkbox" 
                  id="is_active" 
                  name="is_active" 
                  checked={formData.is_active} 
                  onChange={handleChange}
                  disabled={isSubmitting}
                />
                <label htmlFor="is_active" className={styles.checkboxLabel}>
                  Active
                </label>
              </div>
            </div>
            
            <div className={styles.formActions}>
              <Link 
                href="/staff-assignments" 
                className={styles.cancelButton}
                tabIndex={isSubmitting ? -1 : undefined}
              >
                Cancel
              </Link>
              <Button 
                type="submit" 
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                Create Assignment
              </Button>
            </div>
          </form>
        </div>
      </div>
    </DashboardLayout>
  );
}
