.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 4px;
  font-family: sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  text-transform: uppercase;
}

.primary {
  background-color: #FF4D00;
  color: #FFFFFF;
}

.secondary {
  background-color: #EBEBEB;
  color: #010103;
}

.accent {
  background-color: #104EC7;
  color: #FFFFFF;
}

.outline {
  background-color: transparent;
  color: #FF4D00;
  border: 1px solid #FF4D00;
}

.small {
  padding: 6px 12px;
  font-size: 12px;
}

.medium {
  padding: 8px 16px;
  font-size: 14px;
}

.large {
  padding: 10px 20px;
  font-size: 16px;
}
