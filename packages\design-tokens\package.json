{"name": "@loaloa/design-tokens", "version": "0.1.0", "private": true, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "scripts": {"build": "npm run build:tokens && npm run build:css && tsc -p tsconfig.build.json && tsup src/index.ts --format cjs,esm --no-dts", "build:tokens": "node scripts/build-tokens.js", "build:css": "node scripts/build-css.js", "dev": "tsc -p tsconfig.build.json --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "devDependencies": {"tsup": "^8.0.0", "typescript": "^5.4.5", "rimraf": "^5.0.5", "@types/node": "^20.0.0"}}