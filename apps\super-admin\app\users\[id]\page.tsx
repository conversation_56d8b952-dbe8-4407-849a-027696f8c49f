'use client';
import React from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { DashboardLayout } from '@loaloa/ui/src/components/Layout/DashboardLayout';
import { HomeIcon, BuildingIcon, UsersIcon, LicenseIcon, SettingsIcon } from '@loaloa/ui/src/components/Icons/icons';
import { Button, Card, Table } from '@loaloa/ui';
import styles from './page.module.scss';
import Link from 'next/link';
import { useUser } from '../../../hooks/useUser';
import { useUserActions } from '../../../hooks/useUserActions';
import { UserCardSkeleton } from '../../../components/SkeletonLoader';
import ErrorBoundary from '../../../components/ErrorBoundary';

export default function UserDetails() {
  const params = useParams();
  const router = useRouter();
  const userId = params.id as string;
  
  // Fetch dữ liệu user
  const { user, isLoading, isError, mutate } = useUser(userId);
  
  // User actions
  const { deactivateUser, activateUser, isLoading: isActionLoading, error: actionError } = useUserActions();
  
  // Xử lý thay đổi trạng thái user
  const handleStatusChange = async () => {
    if (!user) return;
    
    if (user.is_active) {
      // Nếu đang active, thì deactivate
      const success = await deactivateUser(userId);
      if (success) {
        mutate(); // Refresh data
      }
    } else {
      // Nếu đang inactive, thì activate
      const success = await activateUser(userId);
      if (success) {
        mutate(); // Refresh data
      }
    }
  };

  const sidebarItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      href: '/',
      icon: <HomeIcon />,
    },
    {
      id: 'tenants',
      label: 'Tenants',
      href: '/tenants',
      icon: <BuildingIcon />,
    },
    {
      id: 'users',
      label: 'Users',
      href: '/users',
      icon: <UsersIcon />,
      active: true,
    },
    {
      id: 'licenses',
      label: 'Licenses',
      href: '/licenses',
      icon: <LicenseIcon />,
    },
    {
      id: 'settings',
      label: 'Settings',
      href: '/settings',
      icon: <SettingsIcon />,
    },
  ];

  const activityColumns = [
    {
      header: 'Action',
      accessor: 'action',
      width: '20%',
    },
    {
      header: 'Timestamp',
      accessor: (log) => new Date(log.timestamp).toLocaleString(),
      width: '25%',
    },
    {
      header: 'IP Address',
      accessor: 'ip',
      width: '15%',
    },
    {
      header: 'Details',
      accessor: 'details',
      width: '40%',
    },
  ];

  const tenantColumns = [
    {
      header: 'Tenant',
      accessor: (tenant) => (
        <Link href={`/tenants/${tenant.id}`} className={styles.tenantLink}>
          {tenant.name}
        </Link>
      ),
      width: '40%',
    },
    {
      header: 'Role',
      accessor: 'role',
      width: '15%',
    },
    {
      header: 'Joined',
      accessor: (tenant) => tenant.joined_at ? new Date(tenant.joined_at).toLocaleDateString() : 'N/A',
      width: '20%',
    },
    {
      header: 'Last Activity',
      accessor: (tenant) => tenant.last_activity ? new Date(tenant.last_activity).toLocaleDateString() : 'N/A',
      width: '25%',
    },
  ];

  if (isLoading) {
    return (
      <DashboardLayout
        sidebarItems={sidebarItems}
        title="Loading User..."
        username="Admin User"
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Users', href: '/users' },
          { label: 'Loading...' }
        ]}
      >
        <div className={styles.loadingContainer}>
        <UserCardSkeleton />
      </div>
      </DashboardLayout>
    );
  }

  if (isError || !user) {
    return (
      <DashboardLayout
        sidebarItems={sidebarItems}
        title="User Not Found"
        username="Admin User"
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Users', href: '/users' },
          { label: 'Not Found' }
        ]}
      >
        <div className={styles.errorContainer}>
          <h2>User not found</h2>
          <p>The user may have been deleted or you don't have permission to view it.</p>
          {isError && <p className={styles.errorMessage}>
            Error: {(isError as any).info || 'Unknown error'}
          </p>}
          <Button variant="primary" label="Back to Users" onClick={() => router.push('/users')} />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      sidebarItems={sidebarItems}
      title={`User: ${user.full_name}`}
      username="Admin User"
      breadcrumbs={[
        { label: 'Dashboard', href: '/' },
        { label: 'Users', href: '/users' },
        { label: user.full_name }
      ]}
    >
	<ErrorBoundary>
      <div className={styles.container}>
        {/* Hiển thị lỗi action nếu có */}
        {actionError && (
          <div className={styles.errorMessage}>
            {actionError}
          </div>
        )}
        
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <img 
              src={user.profile_image || 'https://via.placeholder.com/80?text=User'} 
              alt={user.full_name} 
              className={styles.profileImage} 
            />
            <div className={styles.userInfo}>
              <h1 className={styles.userName}>{user.full_name}</h1>
              <p className={styles.userEmail}>{user.email}</p>
              <span className={`${styles.roleBadge} ${styles[user.role]}`}>
                {user.role.replace('_', ' ')}
              </span>
            </div>
          </div>
          <div className={styles.actions}>
            <Button variant="outline" label="Edit User" onClick={() => router.push(`/users/${userId}/edit`)} />
            {user.is_active ? 
              <Button 
                variant="danger" 
                label="Deactivate" 
                onClick={handleStatusChange}
                disabled={isActionLoading}
              /> : 
              <Button 
                variant="success" 
                label="Activate" 
                onClick={handleStatusChange}
                disabled={isActionLoading}
              />
            }
            <Button variant="outline" label="Reset Password" />
          </div>
        </div>

        <div className={styles.section}>
          <h2 className={styles.sectionTitle}>User Information</h2>
          <div className={styles.grid}>
            <Card className={styles.infoCard}>
              <div className={styles.infoGrid}>
                <div>
                  <div className={styles.infoLabel}>User ID</div>
                  <div className={styles.infoValue}>{user.id}</div>
                </div>
                <div>
                  <div className={styles.infoLabel}>Status</div>
                  <div className={styles.infoValue}>{user.is_active ? 'Active' : 'Inactive'}</div>
                </div>
                <div>
                  <div className={styles.infoLabel}>Email</div>
                  <div className={styles.infoValue}>{user.email}</div>
                </div>
                <div>
                  <div className={styles.infoLabel}>Phone</div>
                  <div className={styles.infoValue}>{user.phone || 'Not provided'}</div>
                </div>
                <div>
                  <div className={styles.infoLabel}>Created</div>
                  <div className={styles.infoValue}>
                    {new Date(user.created_at).toLocaleDateString()}
                  </div>
                </div>
                <div>
                  <div className={styles.infoLabel}>Last Login</div>
                  <div className={styles.infoValue}>
                    {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never logged in'}
                  </div>
                </div>
              </div>
            </Card>
            <Card className={styles.infoCard}>
              <h3 className={styles.cardTitle}>Role & Permissions</h3>
              <div className={styles.roleInfo}>
                <div className={styles.infoLabel}>System Role</div>
                <div className={`${styles.roleValue} ${styles[user.role]}`}>
                  {user.role.replace('_', ' ')}
                </div>
                
                <div className={styles.infoLabel}>Default Permissions</div>
                <div className={styles.permissionsList}>
                  {/* Quyền mặc định dựa trên role */}
                  {user.role === 'super_admin' && (
                    <>
                      <span className={styles.permissionItem}>Full system access</span>
                      <span className={styles.permissionItem}>User management</span>
                      <span className={styles.permissionItem}>Tenant management</span>
                    </>
                  )}
                  {user.role === 'admin' && (
                    <>
                      <span className={styles.permissionItem}>Limited system access</span>
                      <span className={styles.permissionItem}>User management (own tenants)</span>
                    </>
                  )}
                  {user.role === 'user' && (
                    <>
                      <span className={styles.permissionItem}>Basic access</span>
                    </>
                  )}
                </div>
              </div>
            </Card>
          </div>
        </div>

        <div className={styles.section}>
          <h2 className={styles.sectionTitle}>Tenant Associations</h2>
          <Card>
            <Table
              columns={tenantColumns}
              data={user.tenants || []}
              isLoading={false}
              emptyMessage="User is not associated with any tenants"
            />
          </Card>
        </div>

        {user.activity_log && user.activity_log.length > 0 && (
          <div className={styles.section}>
            <h2 className={styles.sectionTitle}>Recent Activity</h2>
            <Card>
              <Table
                columns={activityColumns}
                data={user.activity_log}
                isLoading={false}
                pagination={{
                  currentPage: 1,
                  totalPages: 1,
                  onPageChange: () => {}
                }}
              />
            </Card>
          </div>
        )}
      </div>
     </ErrorBoundary>
    </DashboardLayout>
  );
}
