-- Add tenant_id to qr_codes table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'qr_codes' 
    AND column_name = 'tenant_id'
  ) THEN
    ALTER TABLE public.qr_codes 
    ADD COLUMN tenant_id UUID REFERENCES public.tenants(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Add index to tenant_id column
CREATE INDEX IF NOT EXISTS idx_qr_codes_tenant_id ON public.qr_codes(tenant_id);

-- Update existing triggers to include tenant_id
CREATE OR REPLACE FUNCTION create_user_qr_code()
RETURNS TRIGGER AS $$
DECLARE
  tenant_id_val UUID;
BEGIN
  -- Get tenant_id from tenant_users table
  SELECT tenant_id INTO tenant_id_val
  FROM public.tenant_users
  WHERE user_id = NEW.id AND is_primary_tenant = true
  LIMIT 1;

  -- Create QR code for user profile
  INSERT INTO public.qr_codes (
    qr_type,
    title,
    description,
    owner_id,
    organization_id,
    reference_id,
    tenant_id  -- Add tenant_id here
  )
  VALUES (
    'user_profile',
    'User Profile: ' || COALESCE(NEW.full_name, 'User'),
    'QR Code for accessing user profile',
    NEW.id,
    (SELECT organization_id FROM public.users WHERE id = NEW.id),
    NEW.id,
    tenant_id_val  -- Set tenant_id
  );
  
  RETURN NEW;
END;

$$ LANGUAGE plpgsql;

-- Apply RLS policies for QR code multi-tenant isolation
DROP POLICY IF EXISTS qr_codes_tenant_isolation ON public.qr_codes;
CREATE POLICY qr_codes_tenant_isolation ON public.qr_codes
  USING (
    tenant_id = (SELECT get_current_tenant_id())
    OR
    tenant_id IS NULL -- For backward compatibility with existing QR codes
  );

-- Create function to ensure tenant_id is set when creating QR codes
CREATE OR REPLACE FUNCTION set_tenant_id_for_qr_code()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.tenant_id IS NULL THEN
        NEW.tenant_id := get_current_tenant_id();
    END IF;
    RETURN NEW;
END;

$$ LANGUAGE plpgsql;

-- Create trigger to automatically set tenant_id 
DROP TRIGGER IF EXISTS set_tenant_id_for_qr_code_trigger ON public.qr_codes;
CREATE TRIGGER set_tenant_id_for_qr_code_trigger
BEFORE INSERT ON public.qr_codes
FOR EACH ROW
EXECUTE FUNCTION set_tenant_id_for_qr_code();

-- Update existing QR codes to link with tenants based on organization
-- This is just an example, actual logic would depend on your data model
UPDATE public.qr_codes qc
SET tenant_id = (
  SELECT t.id 
  FROM public.tenants t 
  JOIN public.organizations o ON t.id = o.tenant_id 
  WHERE o.id = qc.organization_id
)
WHERE qc.tenant_id IS NULL AND qc.organization_id IS NOT NULL;
