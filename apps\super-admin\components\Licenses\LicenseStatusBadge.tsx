import React from 'react';
import styles from './LicenseStatusBadge.module.scss';

type LicenseStatus = 'ACTIVE' | 'EXPIRED' | 'REVOKED' | 'NOT_ACTIVATED' | 'EXPIRING_SOON';

interface LicenseStatusBadgeProps {
  status: LicenseStatus;
}

const LicenseStatusBadge: React.FC<LicenseStatusBadgeProps> = ({ status }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'ACTIVE':
        return { label: 'Active', className: styles.active };
      case 'EXPIRED':
        return { label: 'Expired', className: styles.expired };
      case 'REVOKED':
        return { label: 'Revoked', className: styles.revoked };
      case 'NOT_ACTIVATED':
        return { label: 'Not Activated', className: styles.notActivated };
      case 'EXPIRING_SOON':
        return { label: 'Expiring Soon', className: styles.expiringSoon };
      default:
        return { label: status, className: '' };
    }
  };

  const { label, className } = getStatusConfig();

  return <span className={`${styles.badge} ${className}`}>{label}</span>;
};

export default LicenseStatusBadge;