.tenantsContainer {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.titleSection {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.pageTitle {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
  color: var(--color-text);
}

.pageDescription {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  margin: 0;
}

.tenantTableCard {
  width: 100%;
  overflow: hidden;
}

.tenantCell {
  display: flex;
  flex-direction: column;
}

.tenantName {
  font-weight: 500;
  color: var(--color-text);
  text-decoration: none;
  
  &:hover {
    color: var(--color-primary);
    text-decoration: underline;
  }
}

.tenantDomain {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.contactCell {
  font-size: 0.875rem;
  
  .phoneNumber {
    font-size: 0.75rem;
    color: var(--color-text-secondary);
  }
}

.planBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  
  &.free {
    background-color: var(--color-background-secondary);
    color: var(--color-text-secondary);
  }
  
  &.basic {
    background-color: #E3F2FD;
    color: #1565C0;
  }
  
  &.premium {
    background-color: #FFF8E1;
    color: #F57F17;
  }
  
  &.enterprise {
    background-color: #E8F5E9;
    color: #2E7D32;
  }
}

.actions {
  display: flex;
  gap: 8px;
}
