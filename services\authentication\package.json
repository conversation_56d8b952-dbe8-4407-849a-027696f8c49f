{"name": "@loaloa/authentication-service", "version": "0.1.0", "description": "Authentication microservice for LoaLoa app", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "lint": "eslint . --ext .ts", "test": "jest"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "axios": "^1.9.0", "bcrypt": "^5.1.1", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "qrcode": "^1.5.4", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.3", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.26.0", "jest": "^29.7.0", "ts-jest": "^29.3.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}