@import '../styles/_variables';

.limitContainer {
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  background-color: #f5f7fa;
  border: 1px solid #e5e7eb;
}

.limitInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.limitLabel {
  font-weight: 500;
  color: #4b5563;
}

.limitValues {
  color: #1f2937;
  
  strong {
    font-weight: 600;
  }
}

.unlimited {
  font-style: italic;
  color: #6b7280;
  font-size: 0.875rem;
}

.progressContainer {
  height: 0.5rem;
  background-color: #e5e7eb;
  border-radius: 0.25rem;
  overflow: hidden;
}

.progressBar {
  height: 100%;
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.good {
  background-color: #10b981;
}

.warning {
  background-color: #f59e0b;
}

.danger {
  background-color: #ef4444;
}

.detailInfo {
  display: flex;
  justify-content: center; // Căn giữa nội dung
  margin-top: 0.5rem;
  font-size: 0.875rem;
}

.remaining {
  color: #4b5563;
  
  strong {
    font-weight: 600;
    color: #111827;
  }
}

.alertMessage {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 0.5rem 0.75rem;
  background-color: #fee2e2; // Nền màu hồng nhạt
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
}

.alertContent {
  color: #b91c1c; // Chữ màu đỏ đậm để tương phản với nền hồng nhạt
  font-size: 0.875rem;
  text-align: center; // Đảm bảo text được căn giữa
  
  strong {
    font-weight: 600;
    color: #991b1b; // Màu đỏ đậm hơn cho phần chữ đậm
  }
}

.limitContainer {
  margin: 1rem 0;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  text-align: center; // Căn giữa tất cả nội dung
}

.limitInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.progressContainer {
  height: 0.75rem;
  background-color: #e5e7eb;
  border-radius: 0.375rem;
  overflow: hidden;
  margin-bottom: 0.5rem; // Thêm khoảng cách dưới thanh tiến trình
}

.progressBar {
  height: 100%;
  border-radius: 0.375rem;
  transition: width 0.3s ease;
}

.good {
  background-color: #10b981;
}

.warning {
  background-color: #f59e0b;
}

.danger {
  background-color: #ef4444;
}
