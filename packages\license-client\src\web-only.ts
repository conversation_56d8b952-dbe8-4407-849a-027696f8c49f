import * as fs from 'fs';
import * as path from 'path';
import { createClient } from '@supabase/supabase-js';

interface LicenseConfig {
  licenseKey: string;
  customerName: string;
  email: string;
  tenant_id: string;
}

/**
 * Simple license client for web applications
 * Validates license and returns tenant ID
 */
class SimpleLicenseClient {
  private static instance: SimpleLicenseClient;
  private cachedConfig: LicenseConfig | null = null;

  private constructor() {}

  public static getInstance(): SimpleLicenseClient {
    if (!SimpleLicenseClient.instance) {
      SimpleLicenseClient.instance = new SimpleLicenseClient();
    }
    return SimpleLicenseClient.instance;
  }

  /**
   * Get license configuration from file system
   */
  public getLicenseConfig(): LicenseConfig | null {
    if (this.cachedConfig) {
      return this.cachedConfig;
    }

    // Try multiple paths for license config
    const possiblePaths = [
      path.resolve(process.cwd(), 'license_config.json'), // App root
      path.resolve(process.cwd(), '../../license_config.json'), // Monorepo root
      path.resolve(__dirname, '../../../license_config.json'), // Relative to package
      path.resolve(process.cwd(), '../license_config.json'), // Parent directory
    ];

    for (const configPath of possiblePaths) {
      try {
        if (fs.existsSync(configPath)) {
          const fileContent = fs.readFileSync(configPath, 'utf8');
          if (fileContent.trim()) {
            const config = JSON.parse(fileContent) as LicenseConfig;
            // Validate required fields
            if (config.licenseKey && config.tenant_id) {
              this.cachedConfig = config;
              console.log(`License config loaded from: ${configPath}`);
              return config;
            }
          }
        }
      } catch (error) {
        console.warn(`Failed to read license config from ${configPath}:`, error);
        continue;
      }
    }

    console.error('No valid license config found in any location');
    return null;
  }

  /**
   * Get tenant ID for web applications
   */
  public async getCurrentTenantIdForWeb(): Promise<string | null> {
    try {
      const config = this.getLicenseConfig();
      if (!config) {
        console.error('License config not found');
        return null;
      }

      // For now, just return tenant_id from config
      // In production, you might want to validate with Supabase
      console.log(`Using tenant ID: ${config.tenant_id}`);
      return config.tenant_id;
    } catch (error) {
      console.error('Error getting tenant ID:', error);
      return null;
    }
  }

  /**
   * Validate license with Supabase (optional)
   */
  public async validateLicense(): Promise<boolean> {
    try {
      const config = this.getLicenseConfig();
      if (!config) {
        return false;
      }

      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

      if (!supabaseUrl || !supabaseKey) {
        console.warn('Supabase credentials missing, skipping license validation');
        return true; // Allow in development
      }

      const supabase = createClient(supabaseUrl, supabaseKey);

      const { data: license, error } = await supabase
        .from('licenses')
        .select('*')
        .eq('license_key', config.licenseKey)
        .single();

      if (error || !license) {
        console.error('License validation failed:', error);
        return false;
      }

      return license.is_active && license.tenant_id === config.tenant_id;
    } catch (error) {
      console.error('License validation error:', error);
      return false;
    }
  }
}

// Export singleton instance and utility functions
const licenseClient = SimpleLicenseClient.getInstance();

export const getCurrentTenantIdForWeb = () => licenseClient.getCurrentTenantIdForWeb();
export const getLicenseConfig = () => licenseClient.getLicenseConfig();
export const validateLicense = () => licenseClient.validateLicense();
