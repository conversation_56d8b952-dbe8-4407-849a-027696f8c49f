import express from 'express';
import { licenseController } from '../controllers/license';
import { authenticate, authorize } from '../middleware/auth';

const router = express.Router();

/**
 * @route GET /licenses
 * @desc Get all licenses with pagination
 * @access Private (admin only)
 */
router.get('/', authenticate, authorize(['admin', 'super_admin']), licenseController.getAll);

/**
 * @route POST /licenses
 * @desc Create a new license
 * @access Private (admin only)
 */
router.post('/', authenticate, authorize(['admin', 'super_admin']), licenseController.create);

/**
 * @route GET /licenses/:id
 * @desc Get a license by ID
 * @access Private (admin only)
 */
router.get('/:id', authenticate, authorize(['admin', 'super_admin']), licenseController.getById);

/**
 * @route PUT /licenses/:id
 * @desc Update a license
 * @access Private (admin only)
 */
router.put('/:id', authenticate, authorize(['admin', 'super_admin']), licenseController.update);

/**
 * @route POST /licenses/:id/revoke
 * @desc Revoke a license
 * @access Private (admin only)
 */
router.post('/:id/revoke', authenticate, authorize(['admin', 'super_admin']), licenseController.revoke);

/**
 * @route POST /licenses/:id/extend
 * @desc Extend a license
 * @access Private (admin only)
 */
router.post('/:id/extend', authenticate, authorize(['admin', 'super_admin']), licenseController.extend);

/**
 * @route GET /licenses/:id/activities
 * @desc Get license activities
 * @access Private (admin only)
 */
router.get('/:id/activities', authenticate, authorize(['admin', 'super_admin']), licenseController.activities);

/**
 * @route GET /licenses/:id/clones
 * @desc Get license clones
 * @access Private (admin only)
 */
router.get('/:id/clones', authenticate, authorize(['admin', 'super_admin']), licenseController.clones);

/**
 * @route PUT /licenses/:id/clones/:cloneId
 * @desc Update clone status
 * @access Private (admin only)
 */
router.put('/:id/clones/:cloneId', authenticate, authorize(['admin', 'super_admin']), licenseController.updateCloneStatus);

export default router;