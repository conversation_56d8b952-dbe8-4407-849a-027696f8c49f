'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '../../../dashboard-layout';
import styles from './area-detail.module.scss';
import { <PERSON><PERSON>, But<PERSON> } from '@ui';
import DeleteConfirmModal from '../../../components/modals/DeleteConfirmModal';
// Thêm import
import ReceptionPointInfo from '../../../components/rooms/ReceptionPointInfo';

export default function AreaDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const areaId = params.id;
  const [area, setArea] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  useEffect(() => {
    const fetchArea = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`/api/areas/${areaId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Area not found');
          }
          throw new Error('Failed to fetch area details');
        }
        
        const data = await response.json();
        setArea(data.data);
      } catch (err) {
        console.error('Error fetching area:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (areaId) {
      fetchArea();
    }
  }, [areaId]);

  const handleDelete = async () => {
    try {
      setDeleteError(null);
      const response = await fetch(`/api/areas/${areaId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete area');
      }

      router.push('/rooms-areas/areas');
    } catch (err) {
      console.error('Error deleting area:', err);
      setDeleteError(err instanceof Error ? err.message : 'Failed to delete area');
    }
  };

  // Format area type for display
  const formatAreaType = (areaType: string) => {
    return areaType
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading area details...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className={styles.error}>
          <Alert variant="error" title="Error" closable={false}>
            {error}
          </Alert>
          <Button
            variant="secondary"
            onClick={() => router.push('/rooms-areas/areas')}
          >
            Back to Areas
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  if (!area) return null;

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Link href="/rooms-areas/areas" className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M10 15.8333L4.16667 10L10 4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to Areas
          </Link>
          <div className={styles.actions}>
            <Link href={`/rooms-areas/areas/${areaId}/edit`} className={styles.editButton}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M11.3333 2.00001C11.5085 1.82494 11.7163 1.68605 11.9451 1.59129C12.1739 1.49653 12.4187 1.44775 12.6667 1.44775C12.9146 1.44775 13.1595 1.49653 13.3883 1.59129C13.6171 1.68605 13.8248 1.82494 14 2.00001C14.1751 2.17508 14.314 2.38283 14.4088 2.61162C14.5035 2.84041 14.5523 3.08536 14.5523 3.33334C14.5523 3.58132 14.5035 3.82627 14.4088 4.05506C14.314 4.28385 14.1751 4.4916 14 4.66667L5 13.6667L1.33334 14.6667L2.33334 11L11.3333 2.00001Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Edit Area
            </Link>
            <button onClick={() => setShowDeleteModal(true)} className={styles.deleteButton}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M2 4H3.33333H14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M5.33334 4.00001V2.66667C5.33334 2.31305 5.47381 1.97391 5.7239 1.72386C5.97399 1.47381 6.31313 1.33334 6.66667 1.33334H9.33334C9.68688 1.33334 10.026 1.47381 10.2761 1.72386C10.5262 1.97391 10.6667 2.31305 10.6667 2.66667V4.00001" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12.6667 4V13.3333C12.6667 13.687 12.5262 14.0261 12.2761 14.2761C12.026 14.5262 11.6869 14.6667 11.3333 14.6667H4.66667C4.31305 14.6667 3.9739 14.5262 3.72386 14.2761C3.47381 14.0261 3.33334 13.687 3.33334 13.3333V4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Delete Area
            </button>
          </div>
        </div>

        <div className={styles.areaCard}>
          <div className={styles.areaHeader}>
            <h1 className={styles.areaName}>{area.name}</h1>
            <span className={`${styles.statusBadge} ${area.is_active ? styles.active : styles.inactive}`}>
              {area.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
          
          <div className={styles.detailsContainer}>
            <div className={styles.section}>
              <h3 className={styles.sectionTitle}>Area Information</h3>
              <div className={styles.detailsGrid}>
                <div className={styles.detailItem}>
                  <span className={styles.detailLabel}>Area Type</span>
                  <span className={styles.detailValue}>{formatAreaType(area.area_type)}</span>
                </div>
                
                <div className={styles.detailItem}>
                  <span className={styles.detailLabel}>Floor</span>
                  <span className={styles.detailValue}>{area.floor}</span>
                </div>
                
                <div className={styles.detailItem}>
                  <span className={styles.detailLabel}>Location</span>
                  <span className={styles.detailValue}>{area.location || 'Not specified'}</span>
                </div>
                
                <div className={styles.detailItem}>
                  <span className={styles.detailLabel}>Staff Count</span>
                  <span className={styles.detailValue}>{area.staff_count || 0}</span>
                </div>
              </div>
            </div>

            {area.opening_hours && area.closing_hours && (
              <div className={styles.section}>
                <h3 className={styles.sectionTitle}>Operating Hours</h3>
                <div className={styles.detailsGrid}>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Opening</span>
                    <span className={styles.detailValue}>{area.opening_hours}</span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Closing</span>
                    <span className={styles.detailValue}>{area.closing_hours}</span>
                  </div>
                </div>
              </div>
            )}

            {area.description && (
              <div className={styles.section}>
                <h3 className={styles.sectionTitle}>Description</h3>
                <p className={styles.description}>{area.description}</p>
              </div>
            )}
            
            {/* Thêm section hiển thị Reception Point */}
            <div className={styles.section}>
              <h3 className={styles.sectionTitle}>Message Reception Point</h3>
              <ReceptionPointInfo receptionPointId={area.reception_point_id} />
            </div>
          </div>
        </div>
      </div>

      <DeleteConfirmModal
        isOpen={showDeleteModal}
        title="Delete Area"
        message={`Are you sure you want to delete "${area.name}"? This action cannot be undone.`}
        onConfirm={handleDelete}
        onCancel={() => {
          setShowDeleteModal(false);
          setDeleteError(null);
        }}
        error={deleteError}
      />
    </DashboardLayout>
  );
}