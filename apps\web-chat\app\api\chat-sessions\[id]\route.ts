import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';

// GET /api/chat-sessions/[id] - Get specific chat session
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionId = params.id;
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      );
    }

    console.log('🔍 Getting chat session:', sessionId);

    const supabase = createServerSupabase();

    // Get chat session with related data - fix the relationship ambiguity
    const { data: session, error: sessionError } = await supabase
      .from('tenant_chat_sessions')
      .select(`
        *,
        tenant_qr_codes(
          code_value,
          location,
          room_number,
          description
        ),
        tenant_message_reception_points!tenant_chat_sessions_reception_point_id_fkey(
          name,
          code,
          description
        )
      `)
      .eq('id', sessionId)
      .single();

    if (sessionError) {
      console.error('❌ Session query error:', sessionError);
      
      if (sessionError.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: 'Chat session not found' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(
        { success: false, error: 'Failed to fetch session' },
        { status: 500 }
      );
    }

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Chat session not found' },
        { status: 404 }
      );
    }

    console.log('✅ Chat session found:', {
      id: session.id,
      status: session.status,
      guest_language: session.guest_language,
      reception_point_id: session.reception_point_id,
      qr_location: session.tenant_qr_codes?.location
    });

    // Format response data
    const formattedSession = {
      id: session.id,
      tenant_id: session.tenant_id,
      guest_id: session.guest_id,
      status: session.status,
      guest_language: session.guest_language,
      staff_language: session.staff_language,
      auto_translate: session.auto_translate,
      priority: session.priority,
      source_type: session.source_type,
      source_qr_code_id: session.source_qr_code_id,
      reception_point_id: session.reception_point_id,
      created_at: session.created_at,
      updated_at: session.updated_at,
      ended_at: session.ended_at,
      // Additional info from related tables
      qr_info: session.tenant_qr_codes ? {
        code_value: session.tenant_qr_codes.code_value,
        location: session.tenant_qr_codes.location,
        room_number: session.tenant_qr_codes.room_number,
        description: session.tenant_qr_codes.description
      } : null,
      reception_point: session.tenant_message_reception_points ? {
        name: session.tenant_message_reception_points.name,
        code: session.tenant_message_reception_points.code,
        description: session.tenant_message_reception_points.description
      } : null
    };

    return NextResponse.json({
      success: true,
      session: formattedSession
    });

  } catch (error) {
    console.error('❌ Get session error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/chat-sessions/[id] - Update chat session (Enhanced with check-in/out logic)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionId = params.id;
    const body = await request.json();

    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      );
    }

    console.log('🔄 Updating chat session:', sessionId, body);

    const supabase = createServerSupabase();

    const {
      action,
      status,
      staff_user_id,
      checkout_reason,
      notes,
      checkout_type = 'manual' // manual, auto, scheduled
    } = body;

    // Get current session to check room info
    const { data: currentSession, error: sessionError } = await supabase
      .from('tenant_chat_sessions')
      .select(`
        *,
        tenant_qr_codes(room_number, target_type, location)
      `)
      .eq('id', sessionId)
      .single();

    if (sessionError || !currentSession) {
      return NextResponse.json(
        { success: false, error: 'Session not found' },
        { status: 404 }
      );
    }

    let updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Handle specific actions
    if (action === 'checkout') {
      // Check-out logic
      updateData = {
        ...updateData,
        status: 'completed',
        ended_at: new Date().toISOString(),
        checkout_reason: checkout_reason || 'manual_checkout',
        checkout_by_staff_id: staff_user_id,
        checkout_type: checkout_type,
        notes: notes
      };

      // For room sessions, mark as checked out
      if (currentSession.tenant_qr_codes?.target_type === 'room') {
        updateData.room_checkout_at = new Date().toISOString();
      }

      console.log('🏨 Processing checkout for session:', sessionId);

    } else if (action === 'assign_staff') {
      // Staff assignment
      if (!staff_user_id) {
        return NextResponse.json(
          { success: false, error: 'staff_user_id is required for assignment' },
          { status: 400 }
        );
      }

      // Create or update assignment
      const assignmentData = {
        tenant_id: currentSession.tenant_id,
        chat_session_id: sessionId,
        assigned_user_id: staff_user_id,
        assignment_status: 'assigned',
        assigned_at: new Date().toISOString()
      };

      await supabase
        .from('tenant_chat_session_assignments')
        .upsert(assignmentData, {
          onConflict: 'chat_session_id'
        });

      console.log('👤 Staff assigned to session:', sessionId, 'Staff:', staff_user_id);

    } else {
      // Regular update - validate allowed fields
      const allowedFields = [
        'status',
        'staff_language',
        'auto_translate',
        'priority',
        'reception_point_id',
        'ended_at'
      ];

      Object.keys(body).forEach(key => {
        if (allowedFields.includes(key)) {
          updateData[key] = body[key];
        }
      });
    }

    const { data: updatedSession, error: updateError } = await supabase
      .from('tenant_chat_sessions')
      .update(updateData)
      .eq('id', sessionId)
      .select()
      .single();

    if (updateError) {
      console.error('❌ Session update error:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to update session' },
        { status: 500 }
      );
    }

    console.log('✅ Chat session updated:', updatedSession.id);

    return NextResponse.json({
      success: true,
      session: updatedSession,
      action_performed: action || 'update'
    });

  } catch (error) {
    console.error('❌ Update session error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/chat-sessions/[id] - End/Delete chat session
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionId = params.id;
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      );
    }

    console.log('🗑️ Ending chat session:', sessionId);

    const supabase = createServerSupabase();

    // Update session status to ended instead of deleting
    const { data: endedSession, error: endError } = await supabase
      .from('tenant_chat_sessions')
      .update({
        status: 'ended',
        ended_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', sessionId)
      .select()
      .single();

    if (endError) {
      console.error('❌ Session end error:', endError);
      return NextResponse.json(
        { success: false, error: 'Failed to end session' },
        { status: 500 }
      );
    }

    console.log('✅ Chat session ended:', endedSession.id);

    return NextResponse.json({
      success: true,
      message: 'Chat session ended successfully',
      session: endedSession
    });

  } catch (error) {
    console.error('❌ End session error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
