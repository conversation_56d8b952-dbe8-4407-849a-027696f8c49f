{"name": "web-chat", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3003", "build": "next build", "start": "next start -p 3003", "lint": "next lint", "clean": "rm -rf .next .turbo node_modules"}, "dependencies": {"@loaloa/design-tokens": "workspace:*", "@loaloa/license-client": "workspace:*", "@loaloa/ui": "workspace:*", "@supabase/supabase-js": "^2.38.4", "bcryptjs": "^3.0.2", "next": "14.1.3", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/node": "^20.11.5", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "autoprefixer": "^10.4.21", "eslint": "^8.46.0", "eslint-config-next": "14.1.3", "postcss": "^8.5.3", "sass": "^1.87.0", "tailwindcss": "^3.4.0", "typescript": "^5.4.5"}}