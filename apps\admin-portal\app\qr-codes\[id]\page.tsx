'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import styles from './qr-code-detail.module.scss';
import DashboardLayout from '../../dashboard-layout';
import { Button, Alert } from '@ui';
import DeleteConfirmModal from '../../components/modals/DeleteConfirmModal';

export default function QrCodeDetail({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { id } = params;

  const [qrCode, setQrCode] = useState<any>(null);
  const [scanHistory, setScanHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  useEffect(() => {
    const fetchQrCode = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/qr-codes/${id}`);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch QR code details');
        }
        const data = await response.json();
        setQrCode(data.data);
        setScanHistory(data.scan_history || []);
      } catch (err) {
        console.error('Error fetching QR code:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchQrCode();
  }, [id]);

  const handleDelete = async () => {
    try {
      setDeleteError(null);
      const response = await fetch(`/api/qr-codes/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete QR code');
      }

      router.push('/qr-codes');
    } catch (err) {
      console.error('Error deleting QR code:', err);
      setDeleteError(err instanceof Error ? err.message : 'Unknown error occurred');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading QR code details...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <Alert
          variant="error"
          title="Error"
          closable
          onClose={() => router.push('/qr-codes')}
        >
          {error}
        </Alert>
        <div className={styles.errorActions}>
          <Button 
            variant="primary" 
            label="Back to QR Codes"
            onClick={() => router.push('/qr-codes')}
          >
            Back to QR Codes
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  if (!qrCode) {
    return (
      <DashboardLayout>
        <div className={styles.notFound}>
          <h2>QR Code Not Found</h2>
          <p>The requested QR code could not be found.</p>
          <Link href="/qr-codes">
            <Button variant="primary" label="Back to QR Codes">
              Back to QR Codes
            </Button>
          </Link>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <div>
            <Link href="/qr-codes" className={styles.backButton}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M10.6667 2.66667L5.33333 8.00001L10.6667 13.3333"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              Back to QR Codes
            </Link>
            <h1 className={styles.title}>{qrCode.name || 'QR Code Details'}</h1>
          </div>
          <div className={styles.actions}>
            <Link href={`/qr-codes/${id}/edit`}>
              <Button variant="secondary" label="Edit QR Code">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M11.3333 2.33333L13.6667 4.66667L5.33333 13H3V10.6667L11.3333 2.33333Z"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Edit QR Code
              </Button>
            </Link>
            <Button
              variant="danger"
              label="Delete"
              onClick={() => setShowDeleteModal(true)}
            >
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M2 4H3.33333H14"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M5.33334 4V2.66667C5.33334 2.31305 5.47382 1.97391 5.72387 1.72386C5.97392 1.47381 6.31305 1.33334 6.66667 1.33334H9.33334C9.68696 1.33334 10.0261 1.47381 10.2761 1.72386C10.5262 1.97391 10.6667 2.31305 10.6667 2.66667V4"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M12.6667 4V13.3333C12.6667 13.687 12.5262 14.0261 12.2762 14.2761C12.0261 14.5262 11.687 14.6667 11.3333 14.6667H4.66667C4.31305 14.6667 3.97391 14.5262 3.72386 14.2761C3.47381 14.0261 3.33334 13.687 3.33334 13.3333V4"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M6.66666 7.33334V11.3333"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M9.33334 7.33334V11.3333"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              Delete
            </Button>
          </div>
        </div>

        <div className={styles.mainContent}>
          <div className={styles.qrDetails}>
            <div className={styles.section}>
              <h2 className={styles.sectionTitle}>Basic Information</h2>
              <div className={styles.detailsGrid}>
                <div className={styles.detailItem}>
                  <span className={styles.label}>Status</span>
                  <span
                    className={`${styles.statusBadge} ${
                      qrCode.status === 'active' ? styles.active : styles.inactive
                    }`}
                  >
                    {qrCode.status === 'active' ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className={styles.detailItem}>
                  <span className={styles.label}>QR Code Value</span>
                  <span className={styles.value}>{qrCode.code_value}</span>
                </div>
                <div className={styles.detailItem}>
                  <span className={styles.label}>Created</span>
                  <span className={styles.value}>
                    {qrCode.created_at ? formatDate(qrCode.created_at) : 'N/A'}
                  </span>
                </div>
                <div className={styles.detailItem}>
                  <span className={styles.label}>Last Updated</span>
                  <span className={styles.value}>
                    {qrCode.updated_at ? formatDate(qrCode.updated_at) : 'N/A'}
                  </span>
                </div>
                <div className={styles.detailItem}>
                  <span className={styles.label}>Last Scanned</span>
                  <span className={styles.value}>
                    {qrCode.last_used ? formatDate(qrCode.last_used) : 'Never'}
                  </span>
                </div>
                <div className={styles.detailItem}>
                  <span className={styles.label}>Total Scans</span>
                  <span className={styles.value}>{qrCode.scan_count || 0}</span>
                </div>
              </div>
            </div>

            <div className={styles.section}>
              <h2 className={styles.sectionTitle}>Location & Association</h2>
              <div className={styles.detailsGrid}>
                <div className={styles.detailItem}>
                  <span className={styles.label}>Physical Location</span>
                  <span className={styles.value}>{qrCode.location || 'Not specified'}</span>
                </div>
                {qrCode.room_number && (
                  <div className={styles.detailItem}>
                    <span className={styles.label}>Associated Room</span>
                    <span className={styles.value}>Room {qrCode.room_number}</span>
                  </div>
                )}
                {qrCode.target_type && qrCode.target_id && (
                  <div className={styles.detailItem}>
                    <span className={styles.label}>Target Type</span>
                    <span className={styles.value}>
                      {qrCode.target_type.charAt(0).toUpperCase() + qrCode.target_type.slice(1)}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className={styles.section}>
              <h2 className={styles.sectionTitle}>Message Routing</h2>
              <div className={styles.detailsGrid}>
                {qrCode.reception_point ? (
                  <div className={styles.detailItem}>
                    <span className={styles.label}>Reception Point</span>
                    <span className={styles.value}>
                      <span className={styles.receptionPointBadge}>{qrCode.reception_point.name}</span>
                    </span>
                  </div>
                ) : (
                  <div className={styles.detailItem}>
                    <span className={styles.label}>Reception Point</span>
                    <span className={styles.value}>No specific reception point</span>
                  </div>
                )}
                
                {qrCode.target_department && (
                  <div className={styles.detailItem}>
                    <span className={styles.label}>Department</span>
                    <span className={styles.value}>
                      <span className={styles.departmentBadge}>
                        {qrCode.target_department.charAt(0).toUpperCase() + qrCode.target_department.slice(1)}
                      </span>
                    </span>
                  </div>
                )}
              </div>
            </div>

            {qrCode.description && (
              <div className={styles.section}>
                <h2 className={styles.sectionTitle}>Description</h2>
                <div className={styles.descriptionText}>{qrCode.description}</div>
              </div>
            )}
          </div>

          <div className={styles.qrPreview}>
            <div className={styles.qrImageContainer}>
              <img
                src={`/api/qr-codes/${id}/image?size=200`}
                alt="QR Code"
                className={styles.qrImage}
              />
              <a
                href={`/api/qr-codes/${id}/download`}
                download={`qrcode-${qrCode.name ? qrCode.name.toLowerCase().replace(/\s+/g, '-') : id}.png`}
                className={styles.downloadButton}
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M14 10V12.6667C14 13.0203 13.8595 13.3594 13.6095 13.6095C13.3594 13.8595 13.0203 14 12.6667 14H3.33333C2.97971 14 2.64057 13.8595 2.39052 13.6095C2.14048 13.3594 2 13.0203 2 12.6667V10"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M4.66667 6.66667L8 10L11.3333 6.66667"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M8 10V2"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Download QR Code
              </a>
            </div>

            {scanHistory.length > 0 && (
              <div className={styles.scanHistoryContainer}>
                <h3 className={styles.scanHistoryTitle}>Recent Scans</h3>
                <ul className={styles.scanHistoryList}>
                  {scanHistory.map((scan, index) => (
                    <li key={index} className={styles.scanHistoryItem}>
                      <svg width="14" height="14" viewBox="0 0 16 16" fill="none">
                        <path
                          d="M8 4V8L10.6667 9.33333"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <circle
                          cx="8"
                          cy="8"
                          r="6.67"
                          stroke="currentColor"
                          strokeWidth="1.5"
                        />
                      </svg>
                      {formatDate(scan.created_at)}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>

      <DeleteConfirmModal
        isOpen={showDeleteModal}
        title="Delete QR Code"
        message={`Are you sure you want to delete this QR code? This action cannot be undone.`}
        onConfirm={handleDelete}
        onCancel={() => {
          setShowDeleteModal(false);
          setDeleteError(null);
        }}
        error={deleteError}
      />
    </DashboardLayout>
  );
}
