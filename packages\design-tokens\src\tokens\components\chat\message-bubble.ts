import { spacing } from '../../primitives/spacing';
import { borders } from '../../primitives/borders';
import { typography } from '../../primitives/typography';
import { shadows } from '../../primitives/shadows';

export const messageBubbleTokens = {
  // Padding
  padding: {
    sm: spacing[2],
    md: spacing[3],
    lg: spacing[4]
  },
  
  // Border radius
  borderRadius: {
    sender: `${borders.radii.lg} ${borders.radii.lg} ${borders.radii.xs} ${borders.radii.lg}`,
    receiver: `${borders.radii.lg} ${borders.radii.lg} ${borders.radii.lg} ${borders.radii.xs}`
  },
  
  // Typography
  typography: {
    fontSize: typography.fontSizes.base,
    lineHeight: typography.lineHeights.normal
  },
  
  // Max width
  maxWidth: '70%',
  
  // Time display
  timeDisplay: {
    fontSize: typography.fontSizes.xs,
    marginTop: spacing[1]
  },
  
  // Shadow
  shadow: shadows.sm
};
