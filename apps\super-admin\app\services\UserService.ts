import { createClient } from '../../utils/supabase/server';

// <PERSON><PERSON><PERSON> ngh<PERSON>a các kiểu dữ liệu
export interface UserTenant {
  id: string;
  name: string;
  role: string;
  joined_at?: string;
  last_activity?: string;
}

export interface User {
  id: string;
  email: string;
  full_name: string;
  role: string;
  phone?: string;
  created_at: string;
  last_login?: string;
  is_active: boolean;
  profile_image?: string;
  tenants?: UserTenant[];
}

export interface UserFilterOptions {
  status?: 'active' | 'inactive' | 'all';
  role?: string; 
  tenant_id?: string;
  search?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
}

export class UserService {
  /**
   * L<PERSON>y danh sách users với phân trang và lọc
   */
  static async getUsers(
  pagination: PaginationOptions = { page: 1, limit: 10 },
  filter: UserFilterOptions = {}
): Promise<{ data: User[]; count: number }> {
  try {
    const supabase = await createClient();
    const { page, limit } = pagination;
    const offset = (page - 1) * limit;

    // Truy vấn đúng cách - tách thành các bước riêng biệt
    let query = supabase
      .from('admin_profiles')
      .select('*', { count: 'exact' });

    // Áp dụng các bộ lọc
    if (filter.status && filter.status !== 'all') {
      query = query.eq('is_active', filter.status === 'active');
    }

    if (filter.role) {
      query = query.eq('role', filter.role);
    }

    // Thêm phân trang
    query = query.range(offset, offset + limit - 1)
                .order('created_at', { ascending: false });

    const { data: profilesData, error: profilesError, count } = await query;

    if (profilesError) {
      console.error('Error fetching admin profiles:', profilesError);
      throw new Error(`Failed to fetch users: ${profilesError.message}`);
    }

    // Lấy danh sách user IDs
    const userIds = profilesData.map(profile => profile.id);

    // Lấy thông tin email từ auth.users (thông qua API hoặc view)
    // Lưu ý: Bạn có thể cần tạo một view để truy cập dữ liệu này
    const { data: authData, error: authError } = await supabase
      .from('admin_profiles') 
      .select('id, email')
      .in('id', userIds);

    if (authError) {
      console.error('Error fetching user emails:', authError);
    }

    // Lấy thông tin tenant_users
    const { data: tenantData, error: tenantError } = await supabase
      .from('tenant_users')
      .select('user_id, tenant_id, role, joined_at, last_login_at, tenants:tenant_id(name)')
      .in('user_id', userIds);

    if (tenantError) {
      console.error('Error fetching tenant relations:', tenantError);
    }

    // Kết hợp dữ liệu
    const formattedUsers = profilesData.map(profile => {
      // Tìm email tương ứng
      const authUser = authData?.find(auth => auth.id === profile.id) || { email: '<EMAIL>' };
      
      // Tìm các tenant liên quan
      const userTenants = tenantData?.filter(t => t.user_id === profile.id) || [];
      
      return {
        id: profile.id,
        email: authUser.email,
        full_name: profile.full_name,
        role: profile.role,
        phone: profile.phone,
        is_active: profile.is_active,
        profile_image: profile.profile_image,
        created_at: profile.created_at,
        last_login: profile.last_login,
        tenants: userTenants.map(t => ({
          id: t.tenant_id,
          name: t.tenants?.name || 'Unknown Tenant',
          role: t.role,
          joined_at: t.joined_at,
          last_activity: t.last_login_at
        }))
      };
    });

    return {
      data: formattedUsers,
      count: count || 0
    };
  } catch (error) {
    console.error('Error in getUsers:', error);
    throw error;
  }
}

  /**
   * Lấy thông tin chi tiết của một user
   */
  static async getUserById(id: string): Promise<User | null> {
    try {
      const supabase = await createClient();
      
      // Lấy thông tin cơ bản của user
      const { data: userData, error: userError } = await supabase
        .from('admin_profiles')
        .select('*')
        .eq('id', id)
        .single();

      if (userError || !userData) {
        console.error('Error fetching user:', userError);
        return null;
      }

      // Lấy tenant của user
      const { data: tenantData, error: tenantError } = await supabase
        .from('tenant_users')
        .select('*, tenants:tenant_id(*)')
        .eq('user_id', id);

      if (tenantError) {
        console.error('Error fetching user tenants:', tenantError);
      }

      // Xử lý dữ liệu tenants nếu có
      const tenants: UserTenant[] = (tenantData || []).map(item => ({
        id: item.tenant_id,
        name: item.tenants?.name || 'Unknown Tenant',
        role: item.role,
        joined_at: item.joined_at,
        last_activity: item.last_login_at
      }));

      // Lấy lịch sử hoạt động
      const { data: activityData, error: activityError } = await supabase
        .from('user_activity_log')
        .select('*')
        .eq('user_id', id)
        .order('timestamp', { ascending: false })
        .limit(10);

      if (activityError) {
        console.error('Error fetching user activity:', activityError);
      }

      // Kết hợp dữ liệu
      return {
        ...userData,
        tenants,
        activity_log: activityData || []
      };
    } catch (error) {
      console.error('Error in getUserById:', error);
      throw error;
    }
  }

  /**
   * Tạo mới một user
   */
  static async createUser(userData: Partial<User>): Promise<User | null> {
    try {
      const supabase = await createClient();
      
      // Step 1: Tạo user auth
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email!,
        email_confirm: true,
        password: Math.random().toString(36).slice(-8), // Generate random password
        user_metadata: {
          full_name: userData.full_name
        }
      });

      if (authError || !authData.user) {
        console.error('Error creating auth user:', authError);
        throw new Error(`Failed to create user: ${authError?.message}`);
      }

      // Step 2: Thêm thông tin user vào bảng admin_profiles
      const { data: newUser, error: userError } = await supabase
        .from('admin_profiles')
        .insert({
          id: authData.user.id,
          email: userData.email,
          full_name: userData.full_name,
          role: userData.role || 'user',
          phone: userData.phone,
          is_active: userData.is_active !== undefined ? userData.is_active : true,
          profile_image: userData.profile_image
        })
        .select()
        .single();

      if (userError) {
        console.error('Error creating user profile:', userError);
        // Clean up the auth user if profile creation fails
        await supabase.auth.admin.deleteUser(authData.user.id);
        throw new Error(`Failed to create user profile: ${userError.message}`);
      }

      // Step 3: Liên kết user với tenant nếu có
      if (userData.tenants && userData.tenants.length > 0) {
        const tenantUserData = userData.tenants.map(tenant => ({
          user_id: authData.user.id,
          tenant_id: tenant.id,
          role: tenant.role,
          is_active: true,
          joined_at: new Date().toISOString()
        }));

        const { error: tenantError } = await supabase
          .from('tenant_users')
          .insert(tenantUserData);

        if (tenantError) {
          console.error('Error linking user to tenants:', tenantError);
        }
      }

      return newUser;
    } catch (error) {
      console.error('Error in createUser:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin của user
   */
  static async updateUser(id: string, userData: Partial<User>): Promise<User | null> {
    try {
      const supabase = await createClient();
      
      // Cập nhật thông tin cơ bản
      const { data: updatedUser, error: userError } = await supabase
        .from('admin_profiles')
        .update({
          full_name: userData.full_name,
          role: userData.role,
          phone: userData.phone,
          is_active: userData.is_active,
          profile_image: userData.profile_image
        })
        .eq('id', id)
        .select()
        .single();

      if (userError) {
        console.error('Error updating user:', userError);
        throw new Error(`Failed to update user: ${userError.message}`);
      }

      // Xử lý cập nhật tenant relationships nếu có
      if (userData.tenants) {
        // Lấy danh sách tenant hiện tại
        const { data: currentTenants } = await supabase
          .from('tenant_users')
          .select('tenant_id, role')
          .eq('user_id', id);
          
        // Logic cập nhật tenant relationships ở đây
        // (Trong thực tế sẽ phức tạp hơn với việc thêm/xóa/cập nhật)
        
        // Đây là một ví dụ đơn giản để minh họa
        if (currentTenants && currentTenants.length > 0) {
          // Xóa các tenant cũ
          await supabase
            .from('tenant_users')
            .delete()
            .eq('user_id', id);
        }
        
        // Thêm các tenant mới
        const tenantUserData = userData.tenants.map(tenant => ({
          user_id: id,
          tenant_id: tenant.id,
          role: tenant.role,
          is_active: true,
          joined_at: new Date().toISOString()
        }));
        
        await supabase
          .from('tenant_users')
          .insert(tenantUserData);
      }

      // Trả về user đã được cập nhật với các tenants mới
      return await this.getUserById(id);
    } catch (error) {
      console.error('Error in updateUser:', error);
      throw error;
    }
  }

  /**
   * Vô hiệu hóa một user
   */
  static async deactivateUser(id: string): Promise<boolean> {
    try {
      const supabase = await createClient();
      
      const { error } = await supabase
        .from('admin_profiles')
        .update({ is_active: false })
        .eq('id', id);

      if (error) {
        console.error('Error deactivating user:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deactivateUser:', error);
      throw error;
    }
  }

  /**
   * Kích hoạt một user
   */
  static async activateUser(id: string): Promise<boolean> {
    try {
      const supabase = await createClient();
      
      const { error } = await supabase
        .from('admin_profiles')
        .update({ is_active: true })
        .eq('id', id);

      if (error) {
        console.error('Error activating user:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in activateUser:', error);
      throw error;
    }
  }

  /**
   * Xử lý và định dạng dữ liệu người dùng
   */
  private static async formatUsers(userData: any[]): Promise<User[]> {
    // Group users data theo id
    const usersMap = new Map();
    
    for (const record of userData) {
      const userId = record.id;
      
      if (!usersMap.has(userId)) {
        usersMap.set(userId, {
          id: userId,
          email: record.email,
          full_name: record.full_name,
          role: record.role,
          phone: record.phone,
          created_at: record.created_at,
          last_login: record.last_login,
          is_active: record.is_active,
          profile_image: record.profile_image,
          tenants: []
        });
      }
      
      // Thêm thông tin tenant
      if (record.tenant_users) {
        const tenantId = record.tenant_users.tenant_id;
        
        // Lấy thông tin tên tenant từ bảng tenants
        const supabase = await createClient();
        const { data: tenantData } = await supabase
          .from('tenants')
          .select('name')
          .eq('id', tenantId)
          .single();
          
        if (tenantData) {
          usersMap.get(userId).tenants.push({
            id: tenantId,
            name: tenantData.name,
            role: record.tenant_users.role,
            joined_at: record.tenant_users.joined_at,
            last_activity: record.tenant_users.last_login_at
          });
        }
      }
    }
    
    // Convert Map thành array
    return Array.from(usersMap.values());
  }
}
