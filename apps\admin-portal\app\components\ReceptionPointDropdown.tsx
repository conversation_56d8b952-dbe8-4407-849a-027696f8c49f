'use client';
import { useState, useEffect } from 'react';
import styles from './ReceptionPointDropdown.module.scss';

interface ReceptionPointDropdownProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  required?: boolean;
  disabled?: boolean;
}

export default function ReceptionPointDropdown({
  value,
  onChange,
  error,
  required = false,
  disabled = false
}: ReceptionPointDropdownProps) {
  const [loading, setLoading] = useState(true);
  const [points, setPoints] = useState<any[]>([]);
  
  useEffect(() => {
    const fetchReceptionPoints = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/reception-points?limit=100');
        
        if (!response.ok) {
          throw new Error('Failed to fetch reception points');
        }
        
        const data = await response.json();
        setPoints(data.data || []);
      } catch (error) {
        console.error('Error fetching reception points:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchReceptionPoints();
  }, []);
  
  return (
    <div className={styles.container}>
      {loading ? (
        <select 
          disabled 
          className={styles.dropdown}
        >
          <option>Loading reception points...</option>
        </select>
      ) : (
        <select
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          className={`${styles.dropdown} ${error ? styles.error : ''}`}
        >
          <option value="">Select Reception Point{required ? '*' : ''}</option>
          {points.map((point) => (
            <option key={point.id} value={point.id}>
              {point.name} - {point.code}
            </option>
          ))}
        </select>
      )}
      {error && <div className={styles.errorMessage}>{error}</div>}
    </div>
  );
}
