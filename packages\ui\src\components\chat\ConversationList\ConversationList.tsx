import React from 'react';
import styles from './ConversationList.module.scss';
import ConversationItem, { ConversationItemProps } from '../ConversationItem';
import { SearchBar } from '../../../components/SearchBar';

export interface ConversationListProps {
  /**
   * Conversations to display
   */
  conversations: Omit<ConversationItemProps, 'onClick'>[];
  /**
   * ID of the active conversation
   */
  activeConversationId?: string;
  /**
   * Function called when a conversation is clicked
   */
  onConversationSelect: (id: string) => void;
  /**
   * Function called when search is performed
   */
  onSearch?: (query: string) => void;
  /**
   * Additional CSS class
   */
  className?: string;
}

export const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  activeConversationId,
  onConversationSelect,
  onSearch,
  className = '',
}) => {
  return (
    <div className={`${styles.container} ${className}`}>
      <div className={styles.header}>
                <h2 className={styles.title}>Conversations</h2>
      </div>
      
      {onSearch && (
        <div className={styles.searchContainer}>
          <SearchBar
            placeholder="Search conversations..."
            onSearch={onSearch}
            size="sm"
          />
        </div>
      )}
      
      <div className={styles.list}>
        {conversations.length > 0 ? (
          conversations.map((conversation) => (
            <ConversationItem
              key={conversation.id}
              {...conversation}
              isActive={conversation.id === activeConversationId}
              onClick={() => onConversationSelect(conversation.id)}
            />
          ))
        ) : (
          <div className={styles.emptyState}>
            <p>No conversations found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationList;
