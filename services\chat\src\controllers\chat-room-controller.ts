import { Request, Response } from 'express';
import * as chatRoomService from '../services/chat-room-service';

/**
 * <PERSON><PERSON><PERSON> danh sách phòng chat cho user hiện tại
 */
export const getUserRooms = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const userId = req.user?.userId;
    const temporaryUserId = req.query.temporary_user_id as string;
    
    if (!userId && !temporaryUserId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    const rooms = await chatRoomService.getRoomsForUser(userId, temporaryUserId);
    
    return res.json({
      success: true,
      data: rooms
    });
  } catch (error) {
    console.error('Error getting user rooms:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * L<PERSON>y thông tin phòng chat theo ID
 */
export const getRoomById = async (req: Request, res: Response) => {
  try {
    const roomId = req.params.id;
    
    const room = await chatRoomService.getRoomById(roomId);
    
    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Room not found'
      });
    }
    
    return res.json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error('Error getting room by ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Tạo phòng chat mới
 */
export const createRoom = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    const { name, description, room_type, hotel_id, room_number, metadata } = req.body;
    
    if (!name || !room_type) {
      return res.status(400).json({
        success: false,
        message: 'Name and room_type are required'
      });
    }
    
    const room = await chatRoomService.createRoom(
      name,
      description,
      room_type,
      userId,
      hotel_id,
      room_number,
      metadata
    );
    
    if (!room) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create room'
      });
    }
    
    // Tự động thêm người tạo vào phòng với quyền admin
    await chatRoomService.addParticipant(room.id, userId, undefined, 'admin');
    
    return res.status(201).json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error('Error creating room:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Cập nhật thông tin phòng chat
 */
export const updateRoom = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    const roomId = req.params.id;
    const { name, description, is_active, metadata } = req.body;
    
    const updates = {
      name,
      description,
      is_active,
      metadata
    };
    
    const room = await chatRoomService.updateRoom(roomId, updates);
    
    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Room not found or update failed'
      });
    }
    
    return res.json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error('Error updating room:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Lấy danh sách người tham gia trong phòng chat
 */
export const getRoomParticipants = async (req: Request, res: Response) => {
  try {
    const roomId = req.params.id;
    
    const participants = await chatRoomService.getRoomParticipants(roomId);
    
    return res.json({
      success: true,
      data: participants
    });
  } catch (error) {
    console.error('Error getting room participants:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Thêm người tham gia vào phòng chat
 */
export const addParticipant = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const currentUserId = req.user?.userId;
    
    if (!currentUserId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    const roomId = req.params.id;
    const { user_id, temporary_user_id, role, display_name, preferred_language } = req.body;
    
    if (!user_id && !temporary_user_id) {
      return res.status(400).json({
        success: false,
        message: 'Either user_id or temporary_user_id is required'
      });
    }
    
    // TODO: Kiểm tra quyền thêm người tham gia (admin của phòng hoặc staff)
    
    const participant = await chatRoomService.addParticipant(
      roomId,
      user_id,
      temporary_user_id,
      role || 'member',
      display_name,
      preferred_language
    );
    
    if (!participant) {
      return res.status(400).json({
        success: false,
        message: 'Failed to add participant'
      });
    }
    
    return res.status(201).json({
      success: true,
      data: participant
    });
  } catch (error) {
    console.error('Error adding participant:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Xóa người tham gia khỏi phòng chat
 */
export const removeParticipant = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const currentUserId = req.user?.userId;
    
    if (!currentUserId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    const roomId = req.params.id;
    const { user_id, temporary_user_id } = req.body;
    
    if (!user_id && !temporary_user_id) {
      return res.status(400).json({
        success: false,
        message: 'Either user_id or temporary_user_id is required'
      });
    }
    
    // TODO: Kiểm tra quyền xóa người tham gia (admin của phòng hoặc staff)
    
    const success = await chatRoomService.removeParticipant(
      roomId,
      user_id,
      temporary_user_id
    );
    
    if (!success) {
      return res.status(400).json({
        success: false,
        message: 'Failed to remove participant'
      });
    }
    
    return res.json({
      success: true,
      message: 'Participant removed successfully'
    });
  } catch (error) {
    console.error('Error removing participant:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
