'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import styles from './qr-scan.module.css';

// Đ<PERSON>nh nghĩa Props và component trang
interface QrPageProps {
  params: { code: string };
}

export default function QrPage({ params }: QrPageProps) {
  const { code } = params;
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [qrAction, setQrAction] = useState<string | null>(null);
  const [qrData, setQrData] = useState<any>(null);
  
  useEffect(() => {
    const processQrCode = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Lấy cookie session_id
        const getCookie = (name: string) => {
          const cookies = document.cookie.split(';');
          for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith(name + '=')) {
              return cookie.substring(name.length + 1);
            }
          }
          return null;
        };
        
        const sessionId = getCookie('session_id');
        
        // Thêm dữ liệu thiết bị
        const deviceInfo = {
          screenSize: {
            width: window.screen.width,
            height: window.screen.height
          },
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          fingerprint: generateFingerprint()
        };
        
        // Gọi API để xử lý quét QR code
        const response = await fetch('/api/qr-codes/scan', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            qr_code_id: code,
            session_id: sessionId,
            device_info: deviceInfo,
            language: navigator.language,
            location: null
          })
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to process QR code');
        }
        
        const result = await response.json();
        setQrAction(result.action);
        setQrData(result);
        
        // Xử lý hành động tương ứng
        switch(result.action) {
          case 'chat':
            // Chuyển hướng đến trang chat
            setTimeout(() => {
              router.push(`/chat/${result.chat_session_id}`);
            }, 500);
            break;
            
          case 'info':
            // Hiển thị thông tin
            // (Giữ nguyên trang hiện tại để hiển thị thông tin)
            break;
            
          case 'service':
            // Chuyển hướng đến trang dịch vụ
            setTimeout(() => {
              router.push(`/service/${result.service_type}`);
            }, 500);
            break;
            
          case 'feedback':
            // Chuyển hướng đến trang đánh giá
            setTimeout(() => {
              router.push(`/feedback?template=${result.feedback_template}`);
            }, 500);
            break;
            
          default:
            // Xử lý trường hợp không xác định
            setError('Unknown QR code action');
        }
        
      } catch (err: any) {
        setError(err.message || 'Error processing QR code');
        console.error('Error processing QR code:', err);
      } finally {
        setLoading(false);
      }
    };
    
    processQrCode();
  }, [code, router]);
  
  // Hàm tạo device fingerprint đơn giản
  const generateFingerprint = () => {
    const browserInfo = navigator.userAgent + navigator.language + 
      navigator.platform + window.screen.width + window.screen.height;
    
    // Đây chỉ là giả lập fingerprint đơn giản
    // Trong thực tế, nên sử dụng thư viện fingerprint như FingerprintJS
    return btoa(browserInfo).substring(0, 32);
  };
  
  // Hiển thị kết quả quét QR
  return (
    <div className={styles.container}>
      {loading && (
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>Processing QR code...</p>
        </div>
      )}
      
      {error && (
        <div className={styles.errorContainer}>
          <div className={styles.errorIcon}>!</div>
          <h2>Error</h2>
          <p>{error}</p>
          <button 
            className={styles.button}
            onClick={() => router.push('/')}
          >
            Back to Home
          </button>
        </div>
      )}
      
      {!loading && !error && qrAction === 'info' && qrData && (
        <div className={styles.infoContainer}>
          <div className={styles.qrHeader}>
            <img 
              src={qrData.qr_code.icon_url || '/icons/info.svg'} 
              alt={qrData.qr_code.type} 
              className={styles.qrIcon} 
            />
            <h2>{qrData.qr_code.name}</h2>
          </div>
          
          {qrData.resource && (
            <div className={styles.resourceInfo}>
              <h3>{qrData.resource.type === 'room' ? 'Room Information' : 'Area Information'}</h3>
              
              {qrData.resource.type === 'room' && (
                <div className={styles.roomDetails}>
                  <p><strong>Room Number:</strong> {qrData.resource.data.room_number}</p>
                  <p><strong>Type:</strong> {qrData.resource.data.room_type}</p>
                  <p><strong>Floor:</strong> {qrData.resource.data.floor}</p>
                  {qrData.resource.data.description && (
                    <p className={styles.description}>{qrData.resource.data.description}</p>
                  )}
                </div>
              )}
              
              {qrData.resource.type === 'area' && (
                <div className={styles.areaDetails}>
                  <p><strong>Area Name:</strong> {qrData.resource.data.name}</p>
                  <p><strong>Type:</strong> {qrData.resource.data.area_type}</p>
                  <p><strong>Floor:</strong> {qrData.resource.data.floor}</p>
                  {qrData.resource.data.description && (
                    <p className={styles.description}>{qrData.resource.data.description}</p>
                  )}
                  {qrData.resource.data.opening_hours && (
                    <p><strong>Opening Hours:</strong> {qrData.resource.data.opening_hours} - {qrData.resource.data.closing_hours}</p>
                  )}
                </div>
              )}
            </div>
          )}
          
          {qrData.custom_content && (
            <div className={styles.customContent}>
              {qrData.custom_content}
            </div>
          )}
          
          <div className={styles.actions}>
            <button 
              className={styles.chatButton}
              onClick={() => router.push(`/qr/${code}/chat`)}
            >
              Start Chat
            </button>
            <button 
              className={styles.backButton}
              onClick={() => router.push('/')}
            >
              Back to Home
            </button>
          </div>
        </div>
      )}
    </div>
  );
}