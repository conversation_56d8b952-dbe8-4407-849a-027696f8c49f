import useSWR from 'swr';
import { Tenant } from '@/services/TenantService';

const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    const error = new Error('An error occurred while fetching the data.');
    const data = await response.json();
    (error as any).info = data.error;
    (error as any).status = response.status;
    throw error;
  }
  return response.json();
};

export interface TenantsDropdownResponse {
  data: Tenant[];
}

/**
 * Hook để lấy danh sách tenants cho dropdown
 */
export function useTenantsDropdown() {
  const { data, error, isLoading } = useSWR<TenantsDropdownResponse>(
    '/api/tenants?dropdown=true',
    fetcher
  );
  
  return {
    tenants: data?.data || [],
    isLoading,
    isError: error,
  };
}
