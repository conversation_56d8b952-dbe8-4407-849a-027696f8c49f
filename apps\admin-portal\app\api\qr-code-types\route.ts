import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Lấy danh sách loại QR code
export async function GET(request: NextRequest) {
  try {
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }

    // Lấy tham số truy vấn
    const searchParams = request.nextUrl.searchParams;
    const action = searchParams.get('action');
    
    // Tạo Supabase client
    const supabase = createAdminClient(cookies());
    
    // Tạo query lấy dữ liệu loại QR code
    let query = supabase
      .from('tenant_qr_code_types')
      .select('*')
      .eq('tenant_id', tenant_id);
    
    // Lọc theo default_action nếu có
    if (action) {
      query = query.eq('default_action', action);
    }
    
    // Thực hiện truy vấn
    const { data, error, count } = await query;
    
    if (error) {
      console.error('Error fetching QR code types:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    // Trả về kết quả
    return NextResponse.json({ data, count });
    
  } catch (error: any) {
    console.error('Error in GET qr-code-types:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 });
  }
}

// POST: Tạo loại QR code mới
export async function POST(request: NextRequest) {
  try {
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }
    
    // Lấy dữ liệu từ request body
    const requestData = await request.json();
    const { 
      name, 
      description, 
      default_action, 
      icon_url, 
      color_code 
    } = requestData;
    
    // Kiểm tra dữ liệu đầu vào
    if (!name || !default_action) {
      return NextResponse.json({ 
        error: 'Name and default_action are required' 
      }, { status: 400 });
    }
    
    // Kiểm tra default_action hợp lệ
    const validActions = ['chat', 'info', 'service', 'feedback'];
    if (!validActions.includes(default_action)) {
      return NextResponse.json({ 
        error: `Default action must be one of: ${validActions.join(', ')}` 
      }, { status: 400 });
    }
    
    // Tạo Supabase client
    const supabase = createAdminClient(cookies());
    
    // Kiểm tra xem loại QR code đã tồn tại chưa
    const { data: existingType, error: checkError } = await supabase
      .from('tenant_qr_code_types')
      .select('id')
      .eq('tenant_id', tenant_id)
      .eq('name', name)
      .maybeSingle();
    
    if (checkError) {
      console.error('Error checking QR code type:', checkError);
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }
    
    if (existingType) {
      return NextResponse.json({ 
        error: `QR code type with name "${name}" already exists` 
      }, { status: 409 });
    }
    
    // Tạo loại QR code mới
    const { data, error } = await supabase
      .from('tenant_qr_code_types')
      .insert({
        tenant_id,
        name,
        description,
        default_action,
        icon_url,
        color_code
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating QR code type:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    // Trả về kết quả
    return NextResponse.json({ 
      data, 
      message: 'QR code type created successfully' 
    }, { status: 201 });
    
  } catch (error: any) {
    console.error('Error in POST qr-code-types:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 });
  }
}

