import bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import supabase from '../utils/supabase';
import { 
  User, 
  LoginCredentials, 
  RegisterData, 
  TokenPayload, 
  AuthTokens, 
  TemporaryUser
} from '../types';
import { generateAuthTokens } from '../utils/jwt';

export const register = async (data: RegisterData): Promise<User | null> => {
  try {
    console.log('Bắt đầu quá trình đăng ký:', data.email);
    
    // Hash password
    console.log('Đang hash mật khẩu...');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(data.password, salt);
    
    // Create user in database
    console.log('Đang tạo user trong cơ sở dữ liệu...');
    const { data: userData, error } = await supabase
      .from('users')
      .insert([{
        email: data.email,
        password_hash: hashedPassword,
        full_name: data.full_name,
        preferred_language: data.preferred_language || 'en',
        is_verified: false
      }])
      .select('*')
      .single();

    if (error) {
      console.error('Lỗi khi tạo user trong Supabase:', error);
      console.error('Mã lỗi:', error.code);
      console.error('Chi tiết:', error.details);
      console.error('Message:', error.message);
      throw error;
    }

    if (userData) {
      console.log('User đã được tạo, ID:', userData.id);
      
      // Assign default guest role to new user
      try {
        console.log('Đang lấy role guest...');
        const { data: roleData, error: roleError } = await supabase
          .from('roles')
          .select('id')
          .eq('name', 'guest')
          .single();

        if (roleError) {
          console.error('Lỗi khi lấy role guest:', roleError);
        }

        if (roleData) {
          console.log('Đang gán role guest cho user...');
          const { error: userRoleError } = await supabase.from('user_roles').insert([{
            user_id: userData.id,
            role_id: roleData.id
          }]);
          
          if (userRoleError) {
            console.error('Lỗi khi gán role:', userRoleError);
          } else {
            console.log('Đã gán role guest thành công.');
          }
        } else {
          console.error('Không tìm thấy role guest.');
        }
      } catch (roleError) {
        console.error('Lỗi khi xử lý role:', roleError);
      }

      // Remove password_hash from returned user
      // @ts-ignore
      const { password_hash, ...user } = userData;
      
      return user as User;
    }

    console.log('Không nhận được dữ liệu user sau khi tạo.');
    return null;
  } catch (error) {
    console.error('Lỗi chi tiết trong quá trình đăng ký:', error);
    throw error;
  }
};
export const login = async (credentials: LoginCredentials): Promise<AuthTokens | null> => {
  try {
    // Find user by email
    const { data: userData, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', credentials.email)
      .single();

    if (error || !userData) {
      return null;
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(
      credentials.password,
      // @ts-ignore
      userData.password_hash
    );

    if (!isValidPassword) {
      return null;
    }

    // Get user roles
    const { data: userRoles } = await supabase
      .from('user_roles')
      .select('roles(name)')
      // @ts-ignore
      .eq('user_id', userData.id);

    const roles = userRoles?.map(ur => ur.roles.name) || [];

    // Create token payload
    const tokenPayload: TokenPayload = {
      // @ts-ignore
      userId: userData.id,
      // @ts-ignore
      email: userData.email,
      roles
    };

    // Generate tokens
    return generateAuthTokens(tokenPayload);
  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
};

export const getUserById = async (userId: string): Promise<User | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error || !data) {
      return null;
    }

    // @ts-ignore
    const { password_hash, ...user } = data;
    return user as User;
  } catch (error) {
    console.error('Get user error:', error);
    return null;
  }
};

export const updateUser = async (userId: string, updates: Partial<User>): Promise<User | null> => {
  try {
    // Prevent updating sensitive fields
    const { id, created_at, updated_at, ...allowedUpdates } = updates;

    const { data, error } = await supabase
      .from('users')
      .update(allowedUpdates)
      .eq('id', userId)
      .select('*')
      .single();

    if (error || !data) {
      return null;
    }

    // @ts-ignore
    const { password_hash, ...user } = data;
    return user as User;
  } catch (error) {
    console.error('Update user error:', error);
    return null;
  }
};

export const changePassword = async (
  userId: string, 
  currentPassword: string, 
  newPassword: string
): Promise<boolean> => {
  try {
    // Get current user with password
    const { data: userData, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error || !userData) {
      return false;
    }

    // Verify current password
    // @ts-ignore
    const isValidPassword = await bcrypt.compare(currentPassword, userData.password_hash);

    if (!isValidPassword) {
      return false;
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    const { error: updateError } = await supabase
      .from('users')
      .update({ password_hash: hashedPassword })
      .eq('id', userId);

    return !updateError;
  } catch (error) {
    console.error('Change password error:', error);
    return false;
  }
};

export const forgotPassword = async (email: string): Promise<boolean> => {
  try {
    // Check if user exists
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (error || !data) {
      return false;
    }

    // In a real implementation, you would:
    // 1. Generate a reset token
    // 2. Store it in the database with an expiration
    // 3. Send an email with a reset link

    // For demo purposes we're just returning true if the email exists
    return true;
  } catch (error) {
    console.error('Forgot password error:', error);
    return false;
  }
};

export const verifyEmail = async (userId: string, token: string): Promise<boolean> => {
  try {
    // In a real implementation, you would:
    // 1. Verify the token is valid for this user
    // 2. Check that it hasn't expired

    // For demo purposes we're just marking the user as verified
    const { error } = await supabase
      .from('users')
      .update({ is_verified: true })
      .eq('id', userId);

    return !error;
  } catch (error) {
    console.error('Verify email error:', error);
    return false;
  }
};

export const getUserRoles = async (userId: string): Promise<string[]> => {
  try {
    const { data, error } = await supabase
      .from('user_roles')
      .select('roles(name)')
      .eq('user_id', userId);

    if (error || !data) {
      return [];
    }

    return data.map(item => item.roles.name);
  } catch (error) {
    console.error('Get user roles error:', error);
    return [];
  }
};
// Make sure to add this line
export { generateAuthTokens } from '../utils/jwt';
