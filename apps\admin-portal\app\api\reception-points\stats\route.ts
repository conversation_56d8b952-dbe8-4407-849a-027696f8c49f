import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET() {
  try {
    // Lấy tenant_id từ file config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json(
        { error: 'Tenant ID not found. Please activate your license.' },
        { status: 400 }
      );
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Query để lấy thông tin reception points và số lượng nhân viên
    const { data: receptionPoints, error } = await supabase
      .from('tenant_message_reception_points')
      .select(`
        id,
        name,
        code,
        description,
        is_active,
        priority
      `)
      .eq('tenant_id', tenant_id)
      .order('view_order', { ascending: true });

    if (error) {
      console.error('Error fetching reception points:', error);
      return NextResponse.json(
        { error: 'Failed to fetch reception points' },
        { status: 500 }
      );
    }

    // Lấy số lượng nhân viên được gán cho mỗi reception point
    const receptionPointsWithStats = await Promise.all(
      receptionPoints.map(async (point) => {
        // Số lượng nhân viên
        const { count: staffCount, error: staffError } = await supabase
          .from('tenant_user_reception_points')
          .select('*', { count: 'exact', head: true })
          .eq('reception_point_id', point.id);

        // Số lượng tin nhắn
        const { count: messageCount, error: msgError } = await supabase
          .from('tenant_chat_sessions')
          .select('*', { count: 'exact', head: true })
          .eq('reception_point_id', point.id);

        if (staffError) console.error('Error fetching staff count:', staffError);
        if (msgError) console.error('Error fetching message count:', msgError);

        return {
          ...point,
          assigned_staff_count: staffCount || 0,
          message_count: messageCount || 0,
        };
      })
    );

    return NextResponse.json({ data: receptionPointsWithStats });
  } catch (error: any) {
    console.error('Error in GET reception points stats:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
