# Quy ước đóng góp cho dự án LoaLoa

## Quy ước đặt tên nhánh

Sử dụng các tiền tố sau cho tên nhánh:

- `feature/` - <PERSON> các tính năng mới
- `fix/` - <PERSON> việc sửa lỗi
- `refactor/` - <PERSON> việc tái cấu trúc code
- `docs/` - <PERSON> việc cập nhật tài liệu
- `test/` - <PERSON> việc thêm hoặc sửa tests
- `chore/` - <PERSON> các công việc như cập nhật dependencies, cấu hình build, v.v.

Ví dụ:
feature/auth-service fix/chat-reconnect docs/api-documentation


## Quy ước commit

Các commit messages nên tuân theo định dạng:

():


Trong đó:
- **type**: Lo<PERSON><PERSON> thay đổi (feat, fix, docs, style, refactor, test, chore)
- **scope**: Phạm vi thay đổi (t<PERSON><PERSON> chọ<PERSON>)
- **subject**: <PERSON><PERSON> tả ngắn gọn về thay đổi

Ví dụ:
feat(auth): thêm xác thực JWT fix(chat): sửa lỗi mất kết nối WebSocket docs(api): cập nhật tài liệu API cho translation service


## Quy trình phát triển

1. Tạo nhánh mới từ `main` với tên tuân theo quy ước
2. Phát triển và commit các thay đổi
3. Push nhánh lên remote repository
4. Tạo Pull Request vào nhánh `main`
5. Yêu cầu review và được approve từ ít nhất 1 người
6. Merge Pull Request sau khi CI/CD pass và được approve