'use client';

/**
 * Simple Enhanced Staff Dashboard - Minimal changes for testing
 * Focus only on faster polling without complex monitoring
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { createClientSupabase } from '@/lib/supabase';
import styles from '../dashboard.module.scss';

interface ChatSession {
  id: string;
  guest_name: string;
  room_number?: string;
  language: string;
  status: 'active' | 'pending' | 'waiting';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  last_message: string;
  last_message_time: string;
  unread_count: number;
  source: string;
  session_ids?: string[];
}

interface ChatMessage {
  id: string;
  content: string;
  sender_type: 'guest' | 'staff';
  sender_name: string;
  timestamp: string;
}

interface User {
  id: string;
  name: string;
  tenant_id: string;
}

export default function SimpleEnhancedStaffDashboard() {
  // Core state
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeChatSessions, setActiveChatSessions] = useState<ChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'polling' | 'disconnected'>('disconnected');

  // Refs
  const router = useRouter();
  const supabaseRef = useRef<any>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Utility functions
  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Initialize Supabase
  const initializeSupabase = useCallback(() => {
    try {
      supabaseRef.current = createClientSupabase();
      console.log('✅ Simple Enhanced: Supabase client initialized');
      
      // Try realtime first, fallback to polling quickly
      setupRealtimeWithQuickFallback();
    } catch (err) {
      console.error('❌ Simple Enhanced: Failed to initialize Supabase:', err);
      setConnectionStatus('disconnected');
    }
  }, []);

  // Setup realtime with quick fallback
  const setupRealtimeWithQuickFallback = useCallback(() => {
    if (!supabaseRef.current || !user) return;

    console.log('🔄 Simple Enhanced: Trying realtime connection...');
    
    let realtimeWorking = false;
    
    try {
      const messagesChannel = supabaseRef.current
        .channel('simple-staff-messages')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'tenant_chat_messages',
            filter: `tenant_id=eq.${user.tenant_id}`
          },
          (payload: any) => {
            console.log('🔔 Simple Enhanced: Message via realtime');
            realtimeWorking = true;
            setConnectionStatus('connected');
            
            // Update messages if for selected session
            if (selectedSession && payload.new.chat_session_id === selectedSession) {
              const newMessage: ChatMessage = {
                id: payload.new.id,
                content: payload.new.content,
                sender_type: payload.new.sender_type,
                sender_name: payload.new.sender_name || (payload.new.sender_type === 'guest' ? 'Guest' : 'Staff'),
                timestamp: payload.new.created_at
              };

              setMessages(prev => {
                const exists = prev.some(msg => msg.id === newMessage.id);
                if (exists) return prev;
                return [...prev, newMessage];
              });

              setTimeout(scrollToBottom, 100);
            }
          }
        )
        .subscribe((status: string) => {
          console.log('📡 Simple Enhanced: Subscription status:', status);
          
          if (status === 'SUBSCRIBED') {
            realtimeWorking = true;
            setConnectionStatus('connected');
            console.log('✅ Simple Enhanced: Realtime active');
          } else if (status === 'CHANNEL_ERROR' || status === 'CLOSED') {
            console.warn('⚠️ Simple Enhanced: Realtime failed, using polling');
            setConnectionStatus('polling');
            startFastPolling();
          }
        });

      // Fallback to polling after 2 seconds if realtime doesn't work
      setTimeout(() => {
        if (!realtimeWorking) {
          console.warn('⚠️ Simple Enhanced: Realtime timeout, switching to polling');
          setConnectionStatus('polling');
          startFastPolling();
        }
      }, 2000);

    } catch (err) {
      console.error('❌ Simple Enhanced: Realtime setup failed:', err);
      setConnectionStatus('polling');
      startFastPolling();
    }

    // Additional fallback - always start polling after 1 second as safety net
    setTimeout(() => {
      console.log('🔄 Simple Enhanced: Safety net - ensuring polling is active');
      if (connectionStatus !== 'connected') {
        setConnectionStatus('polling');
        startFastPolling();
      }
    }, 1000);
  }, [user, selectedSession]);

  // Fast polling implementation
  const startFastPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      console.log('🔄 Simple Enhanced: Polling already running, skipping');
      return;
    }

    console.log('🔄 Simple Enhanced: Starting fast polling (1.5s interval)');

    const poll = async () => {
      if (!document.hidden && user) {
        try {
          console.log('📡 Simple Enhanced: Polling tick...');
          await refreshSessionList();
        } catch (err) {
          console.error('Simple Enhanced: Polling error:', err);
        }
      }
    };

    // Start with immediate poll, then every 1.5 seconds
    poll();
    pollingIntervalRef.current = setInterval(poll, 1500);
    console.log('✅ Simple Enhanced: Fast polling started');
  }, [user, refreshSessionList]);

  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
      console.log('⏹️ Simple Enhanced: Polling stopped');
    }
  }, []);

  // Session list refresh
  const refreshSessionList = useCallback(async () => {
    if (!user) return;

    try {
      const response = await fetch(`/api/chat-sessions?tenant_id=${user.tenant_id}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.sessions)) {
          // Simple transformation without complex grouping
          const transformedSessions: ChatSession[] = data.sessions.map((session: any) => ({
            id: session.id,
            guest_name: session.qr_info?.room_number ?
              `Room ${session.qr_info.room_number} Guest` :
              'Guest User',
            room_number: session.qr_info?.room_number || undefined,
            language: session.guest_language?.toUpperCase() || 'EN',
            status: session.status as 'active' | 'pending' | 'waiting',
            priority: session.priority as 'low' | 'normal' | 'high' | 'urgent',
            last_message: 'Loading...',
            last_message_time: formatTimeAgo(session.updated_at),
            unread_count: 0,
            source: session.qr_info?.location || session.reception_point?.name || 'Direct',
            session_ids: [session.id]
          }));

          setActiveChatSessions(transformedSessions);
          console.log('✅ Simple Enhanced: Session list refreshed');
        }
      }
    } catch (error) {
      console.error('❌ Simple Enhanced: Error refreshing session list:', error);
    }
  }, [user]);

  // Send message
  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!selectedSession || !content.trim()) return false;

    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: selectedSession,
          sender_type: 'staff',
          sender_name: user?.name || 'Staff',
          content: content.trim()
        }),
      });

      if (response.ok) {
        console.log('✅ Simple Enhanced: Message sent successfully');
        return true;
      }
      
      throw new Error('Failed to send message');
    } catch (err) {
      console.error('❌ Simple Enhanced: Failed to send message:', err);
      return false;
    }
  }, [selectedSession, user]);

  // Load messages for selected session
  const loadMessages = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/messages?session_id=${sessionId}&limit=50`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.messages)) {
          setMessages(data.messages);
          setTimeout(scrollToBottom, 100);
        }
      }
    } catch (err) {
      console.error('❌ Simple Enhanced: Failed to load messages:', err);
    }
  }, []);

  // Handle session selection
  const handleSessionSelect = useCallback((sessionId: string) => {
    setSelectedSession(sessionId);
    loadMessages(sessionId);
  }, [loadMessages]);

  // Initialize
  useEffect(() => {
    console.log('🚀 Simple Enhanced Staff Dashboard: Starting...');
    
    const token = localStorage.getItem('staff_token');
    const userData = localStorage.getItem('staff_user');

    if (!token || !userData) {
      router.push('/staff');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
      console.log('✅ Simple Enhanced: User loaded:', parsedUser.name);
    } catch (error) {
      console.error('Error parsing user data:', error);
      router.push('/staff');
    } finally {
      setLoading(false);
    }
  }, [router]);

  // Setup when user is loaded
  useEffect(() => {
    if (user) {
      initializeSupabase();
      refreshSessionList();

      // Force start polling immediately as backup
      setTimeout(() => {
        if (connectionStatus === 'disconnected') {
          console.log('🔄 Simple Enhanced: Force starting polling as backup');
          setConnectionStatus('polling');
          startFastPolling();
        }
      }, 2000);
    }

    return () => {
      stopPolling();
    };
  }, [user, initializeSupabase, refreshSessionList, stopPolling, connectionStatus, startFastPolling]);

  if (loading) {
    return <div className={styles.loading}>Loading Simple Enhanced Dashboard...</div>;
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h1>Simple Enhanced Staff Dashboard</h1>
        <div className={styles.connectionStatus}>
          <span className={`${styles.statusIndicator} ${connectionStatus === 'connected' ? styles.connected : styles.disconnected}`}>
            {connectionStatus === 'connected' ? '🟢 Realtime' : 
             connectionStatus === 'polling' ? '🟡 Fast Polling (1.5s)' : 
             '🔴 Disconnected'}
          </span>
        </div>
      </div>

      <div className={styles.content}>
        {/* Session List */}
        <div className={styles.sidebar}>
          <div className={styles.sessionList}>
            {activeChatSessions.map((session) => (
              <div 
                key={session.id} 
                className={`${styles.sessionItem} ${selectedSession === session.id ? styles.selected : ''}`}
                onClick={() => handleSessionSelect(session.id)}
              >
                <div className={styles.sessionHeader}>
                  <span className={styles.guestName}>{session.guest_name}</span>
                  <span className={styles.language}>{session.language}</span>
                </div>
                <div className={styles.lastMessage}>{session.last_message}</div>
                <div className={styles.sessionMeta}>
                  <span className={styles.time}>{session.last_message_time}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Chat Area */}
        <div className={styles.chatArea}>
          {selectedSession ? (
            <>
              <div className={styles.messagesContainer}>
                {messages.map((message) => (
                  <div key={message.id} className={`${styles.message} ${styles[message.sender_type]}`}>
                    <div className={styles.messageContent}>
                      <span className={styles.senderName}>{message.sender_name}</span>
                      <p>{message.content}</p>
                      <span className={styles.timestamp}>{formatTimeAgo(message.timestamp)}</span>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
              
              <div className={styles.messageInput}>
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message..."
                  onKeyPress={async (e) => {
                    if (e.key === 'Enter' && newMessage.trim()) {
                      const success = await sendMessage(newMessage);
                      if (success) {
                        setNewMessage('');
                      }
                    }
                  }}
                />
                <button onClick={async () => {
                  if (newMessage.trim()) {
                    const success = await sendMessage(newMessage);
                    if (success) {
                      setNewMessage('');
                    }
                  }
                }}>
                  Send
                </button>
              </div>
            </>
          ) : (
            <div className={styles.noSelection}>
              <p>Select a chat session to start messaging</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
