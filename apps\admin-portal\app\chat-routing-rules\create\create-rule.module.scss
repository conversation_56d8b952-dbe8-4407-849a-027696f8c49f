.container {
  padding: 1rem;
}

.header {
  margin-bottom: 1.5rem;
}

.backLink {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #666;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;

  &:hover {
    color: #1976d2;
  }

  svg {
    stroke: currentColor;
  }
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.formContainer {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
