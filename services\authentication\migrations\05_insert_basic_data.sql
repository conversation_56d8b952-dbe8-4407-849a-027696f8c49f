-- Insert basic roles
INSERT INTO roles (name, description)
VALUES 
  ('admin', 'Administrator with full access'),
  ('staff', 'Hotel staff member'),
  ('guest', 'Hotel guest'),
  ('temporary_guest', 'Temporary guest account')
ON CONFLICT (name) DO NOTHING;

-- Insert basic permissions
INSERT INTO permissions (name, resource, action, description)
VALUES 
  ('user:read', 'user', 'read', 'Can read user information'),
  ('user:create', 'user', 'create', 'Can create users'),
  ('user:update', 'user', 'update', 'Can update user information'),
  ('user:delete', 'user', 'delete', 'Can delete users'),
  ('chat:read', 'chat', 'read', 'Can read chats'),
  ('chat:create', 'chat', 'create', 'Can create chat messages'),
  ('chat:manage', 'chat', 'manage', 'Can manage chats'),
  ('staff:dashboard', 'dashboard', 'access', 'Can access staff dashboard'),
  ('admin:dashboard', 'dashboard', 'admin', 'Can access admin dashboard')
ON CONFLICT (resource, action) DO NOTHING;

-- Assign permissions to roles
DO $$
DECLARE
  admin_role_id UUID;
  staff_role_id UUID;
  guest_role_id UUID;
  temp_guest_role_id UUID;
  
  -- Permission IDs
  user_read_id UUID;
  user_create_id UUID;
  user_update_id UUID;
  user_delete_id UUID;
  chat_read_id UUID;
  chat_create_id UUID;
  chat_manage_id UUID;
  staff_dashboard_id UUID;
  admin_dashboard_id UUID;
BEGIN
  -- Get role IDs
  SELECT id INTO admin_role_id FROM roles WHERE name = 'admin';
  SELECT id INTO staff_role_id FROM roles WHERE name = 'staff';
  SELECT id INTO guest_role_id FROM roles WHERE name = 'guest';
  SELECT id INTO temp_guest_role_id FROM roles WHERE name = 'temporary_guest';
  
  -- Get permission IDs
  SELECT id INTO user_read_id FROM permissions WHERE name = 'user:read';
  SELECT id INTO user_create_id FROM permissions WHERE name = 'user:create';
  SELECT id INTO user_update_id FROM permissions WHERE name = 'user:update';
  SELECT id INTO user_delete_id FROM permissions WHERE name = 'user:delete';
  SELECT id INTO chat_read_id FROM permissions WHERE name = 'chat:read';
  SELECT id INTO chat_create_id FROM permissions WHERE name = 'chat:create';
  SELECT id INTO chat_manage_id FROM permissions WHERE name = 'chat:manage';
  SELECT id INTO staff_dashboard_id FROM permissions WHERE name = 'staff:dashboard';
  SELECT id INTO admin_dashboard_id FROM permissions WHERE name = 'admin:dashboard';
  
  -- Admin role permissions (all)
  INSERT INTO role_permissions (role_id, permission_id)
  VALUES 
    (admin_role_id, user_read_id),
    (admin_role_id, user_create_id),
    (admin_role_id, user_update_id),
    (admin_role_id, user_delete_id),
    (admin_role_id, chat_read_id),
    (admin_role_id, chat_create_id),
    (admin_role_id, chat_manage_id),
    (admin_role_id, staff_dashboard_id),
    (admin_role_id, admin_dashboard_id)
  ON CONFLICT (role_id, permission_id) DO NOTHING;
  
  -- Staff role permissions
  INSERT INTO role_permissions (role_id, permission_id)
  VALUES 
    (staff_role_id, user_read_id),
    (staff_role_id, chat_read_id),
    (staff_role_id, chat_create_id),
    (staff_role_id, chat_manage_id),
    (staff_role_id, staff_dashboard_id)
  ON CONFLICT (role_id, permission_id) DO NOTHING;
  
  -- Guest role permissions
  INSERT INTO role_permissions (role_id, permission_id)
  VALUES 
    (guest_role_id, chat_read_id),
    (guest_role_id, chat_create_id)
  ON CONFLICT (role_id, permission_id) DO NOTHING;
  
  -- Temporary guest role permissions
  INSERT INTO role_permissions (role_id, permission_id)
  VALUES 
    (temp_guest_role_id, chat_read_id),
    (temp_guest_role_id, chat_create_id)
  ON CONFLICT (role_id, permission_id) DO NOTHING;
END $$;
