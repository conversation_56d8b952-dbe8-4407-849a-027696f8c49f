.container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 16px;
}

.profileImage {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  background-color: #f5f5f5;
  border: 1px solid var(--color-border);
}

.userInfo {
  display: flex;
  flex-direction: column;
}

.userName {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--color-text);
}

.userEmail {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  margin: 0;
}

.roleBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  margin-top: 8px;
}

.super_admin {
  background-color: #FFD700;
  color: #8B4513;
}

.admin {
  background-color: #E8F5E9;
  color: #2E7D32;
}

.user {
  background-color: #E3F2FD;
  color: #1565C0;
}

.actions {
  display: flex;
  gap: 8px;
}

.section {
  margin-bottom: 24px;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--color-text);
}

.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.infoCard {
  padding: 16px;
}

.cardTitle {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--color-text);
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.infoLabel {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  margin-bottom: 4px;
}

.infoValue {
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--color-text);
  word-break: break-word;
}

.roleInfo {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.roleValue {
  font-size: 0.95rem;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-top: 4px;
}

.permissionsList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

.permissionItem {
  font-size: 0.8rem;
  padding: 2px 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  display: inline-block;
}

.tenantLink {
  color: var(--color-text);
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    color: var(--color-primary);
    text-decoration: underline;
  }
}

.errorContainer {
  text-align: center;
  padding: 40px;
  
  h2 {
    font-size: 1.5rem;
    margin-bottom: 16px;
    color: var(--color-text);
  }
  
  p {
    margin-bottom: 24px;
    color: var(--color-text-secondary);
  }
}

.errorMessage {
  padding: 12px;
  background-color: #FFEBEE;
  color: #C62828;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #C62828;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1rem;
  color: var(--color-text-secondary);
}
