"use client";

import React, { useState, useEffect } from "react";
import styles from "./QRCodeTable.module.scss";
import { QRCode, QRCodeFilters } from "@/app/types/qr-codes";
import QRCodeFiltersComponent from "./QRCodeFilters";
import QRCodeItem from "./QRCodeItem";
import Link from "next/link";
import { Button } from "../../ui/button";
import { PlusIcon } from "@heroicons/react/24/outline";
import QRCodeStats from "./QRCodeStats";

const QRCodeTable = () => {
  const [qrCodes, setQRCodes] = useState<QRCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<QRCodeFilters>({});
  const [stats, setStats] = useState({
    totalCount: 0,
    totalScans: 0,
    todayScans: 0,
    percentChange: 0,
  });

  // Fetch QR codes based on current filters
  const fetchQRCodes = async () => {
    setLoading(true);
    try {
      // Build query params from filters
      const params = new URLSearchParams();
      if (filters.search) params.append("search", filters.search);
      if (filters.type) params.append("type", filters.type);
      if (filters.location) params.append("location", filters.location);
      if (filters.status) params.append("status", filters.status);

      const response = await fetch(`/api/qr-codes?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch QR codes");
      
      const data = await response.json();
      setQRCodes(data);
      setStats((prev) => ({ ...prev, totalCount: data.length }));
    } catch (error) {
      console.error("Error fetching QR codes:", error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch stats separately
  const fetchStats = async () => {
    try {
      const response = await fetch("/api/qr-codes/stats");
      if (!response.ok) throw new Error("Failed to fetch QR code stats");
      
      const data = await response.json();
      setStats((prev) => ({
        ...prev,
        totalScans: data.totalScans || 0,
        todayScans: data.todayScans || 0,
        percentChange: data.percentChange || 0,
      }));
    } catch (error) {
      console.error("Error fetching QR code stats:", error);
    }
  };

  // Initial load and when filters change
  useEffect(() => {
    fetchQRCodes();
    fetchStats();
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = (newFilters: QRCodeFilters) => {
    setFilters(newFilters);
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>QR Code Management</h1>
        <p>Create and manage QR codes for different areas and purposes</p>
        <div className={styles.actions}>
          <Link href="/qr-codes/create">
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Create QR Code
            </Button>
          </Link>
        </div>
      </div>

      <QRCodeStats
        totalCount={stats.totalCount}
        totalScans={stats.totalScans}
        todayScans={stats.todayScans}
        percentChange={stats.percentChange}
      />

      <QRCodeFiltersComponent onFilterChange={handleFilterChange} />

      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>QR Code</th>
              <th>Name</th>
              <th>Type</th>
              <th>Location</th>
              <th>Reception Point</th>
              <th>Action</th>
              <th>Scans</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={9} className={styles.loading}>
                  Loading QR codes...
                </td>
              </tr>
            ) : qrCodes.length === 0 ? (
              <tr>
                <td colSpan={9} className={styles.noData}>
                  No QR codes found. Create one to get started.
                </td>
              </tr>
            ) : (
              qrCodes.map((qrCode) => (
                <QRCodeItem key={qrCode.id} qrCode={qrCode} onUpdate={fetchQRCodes} />
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default QRCodeTable;