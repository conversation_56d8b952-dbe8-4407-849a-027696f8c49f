import React from 'react';

export interface ButtonProps {
  /**
   * Button contents
   */
  label: string;
  /**
   * Optional button variant
   */
  variant?: 'primary' | 'secondary' | 'accent' | 'outline';
  /**
   * Optional button size
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Optional disabled state
   */
  disabled?: boolean;
  /**
   * Optional click handler
   */
  onClick?: () => void;
}

/**
 * Primary UI component for user interaction
 */
export const Button = ({
  label,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  onClick,
  ...props
}: ButtonProps) => {
  // Định nghĩa styles inline
  const baseStyle: React.CSSProperties = {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '4px',
    fontFamily: 'Inter, sans-serif',
    fontWeight: 500,
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: 'none',
    textTransform: 'uppercase',
    opacity: disabled ? 0.6 : 1,
  };

  // Styles cho các biến thể
  const variantStyles: Record<string, React.CSSProperties> = {
    primary: {
      backgroundColor: '#FF4D00',
      color: '#FFFFFF',
    },
    secondary: {
      backgroundColor: '#EBEBEB',
      color: '#010103',
    },
    accent: {
      backgroundColor: '#104EC7',
      color: '#FFFFFF',
    },
    outline: {
      backgroundColor: 'transparent',
      color: '#FF4D00',
      border: '1px solid #FF4D00',
    },
  };

  // Styles cho kích thước
  const sizeStyles: Record<string, React.CSSProperties> = {
    small: {
      padding: '6px 12px',
      fontSize: '12px',
    },
    medium: {
      padding: '8px 16px',
      fontSize: '14px',
    },
    large: {
      padding: '10px 20px',
      fontSize: '16px',
    },
  };

  // Kết hợp tất cả styles
  const buttonStyle = {
    ...baseStyle,
    ...variantStyles[variant],
    ...sizeStyles[size],
  };

  return (
    <button
      style={buttonStyle}
      onClick={onClick}
      disabled={disabled}
      type="button"
      {...props}
    >
      {label}
    </button>
  );
};

export default Button;
