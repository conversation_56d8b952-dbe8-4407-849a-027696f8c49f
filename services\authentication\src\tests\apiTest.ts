import axios from 'axios';

// API base URL
const API_URL = 'http://localhost:3001/api';

// Store tokens
let accessToken: string;
let refreshToken: string;
let userId: string;
let tempUserQRToken: string;
let tempUserId: string;

// Test user
const testUser = {
  email: `test_${Date.now()}@example.com`,
  password: 'password123',
  full_name: 'Test User'
};

// Temporary user metadata
const tempUserData = {
  preferred_language: 'en',
  hotel_id: '12345678-1234-1234-1234-123456789012',
  room_number: '101',
  metadata: { source: 'api_test' }
};

const deviceId = `test-device-${Date.now()}`;

const runTests = async () => {
  console.log('🔍 Starting API tests\n');

  try {
    // 1. Register a new user
    console.log('📝 Testing user registration...');
    const registerResponse = await axios.post(`${API_URL}/auth/register`, testUser);
    userId = registerResponse.data.data.id;
    console.log('✅ User registration successful:', {
      id: userId,
      email: registerResponse.data.data.email
    });

    // 2. Login
    console.log('\n🔑 Testing user login...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    
    accessToken = loginResponse.data.data.access_token;
    refreshToken = loginResponse.data.data.refresh_token;
    console.log('✅ Login successful, tokens received');

    // 3. Get profile
    console.log('\n👤 Testing get profile...');
    const profileResponse = await axios.get(
      `${API_URL}/auth/profile`,
      { headers: { Authorization: `Bearer ${accessToken}` } }
    );
    console.log('✅ Profile retrieved:', {
      email: profileResponse.data.data.email,
      name: profileResponse.data.data.full_name
    });

    // 4. Update profile
    console.log('\n✏️ Testing update profile...');
    const updatedName = 'Updated Test User';
    const updateResponse = await axios.put(
      `${API_URL}/auth/profile`,
      { full_name: updatedName },
      { headers: { Authorization: `Bearer ${accessToken}` } }
    );
    console.log('✅ Profile updated:', {
      name: updateResponse.data.data.full_name
    });

    // 5. Create temporary user with QR code (this requires admin/staff role)
    console.log('\n👥 Testing create temporary user (Note: This may fail if current user is not staff/admin)...');
    try {
      const tempUserResponse = await axios.post(
        `${API_URL}/temporary-users`,
        tempUserData,
        { headers: { Authorization: `Bearer ${accessToken}` } }
      );
      
      tempUserId = tempUserResponse.data.data.temporary_user.id;
      // Extract token from QR code URL (this is a simplified example)
      const qrUrl = tempUserResponse.data.data.qr_code_url;
      console.log('✅ Temporary user created with QR code');
      
      // In a real scenario, we would extract the token from the QR code
      // For testing, we'll simulate this process
      
      // 6. Activate temporary user
      console.log('\n🔓 Testing temporary user activation...');
      // Note: This is a simplified example - in real app, token would come from QR code
      // We'd need to call this with the actual token from the QR
      console.log('ℹ️ Skipping actual QR code scanning in this test');
      
    } catch (error) {
      console.log('⚠️ Temporary user creation failed - likely due to permissions');
    }

    // 7. Refresh token
    console.log('\n🔄 Testing token refresh...');
    const refreshResponse = await axios.post(
      `${API_URL}/auth/refresh-token`,
      { refresh_token: refreshToken }
    );
    const newAccessToken = refreshResponse.data.data.access_token;
    console.log('✅ Token refreshed successfully');

    // 8. Change password (optional test - commented out to avoid changing the test account password)
    /*
    console.log('\n🔒 Testing change password...');
    const changePasswordResponse = await axios.post(
      `${API_URL}/auth/change-password`,
      {
        current_password: testUser.password,
        new_password: 'newpassword123'
      },
      { headers: { Authorization: `Bearer ${newAccessToken}` } }
    );
    console.log('✅ Password changed successfully');
    */

    console.log('\n✅ All tests completed successfully!');

  } catch (error: any) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
};

// Install axios first with: npm install axios
runTests();
