'use client';
import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { DashboardLayout } from '@loaloa/ui/src/components/Layout/DashboardLayout';
import { HomeIcon, BuildingIcon, UsersIcon, LicenseIcon, SettingsIcon } from '@loaloa/ui/src/components/Icons/icons';
import { Button, Card } from '@loaloa/ui';
import Link from 'next/link';
import styles from './page.module.scss';
import { useUser } from '../../../../hooks/useUser';
import { useUserActions } from '../../../../hooks/useUserActions';
import { useTenantsDropdown } from '../../../../hooks/useTenants';
import ErrorBoundary from '../../../../components/ErrorBoundary';

// Định nghĩa interface cho form data
interface UserFormData {
  full_name: string;
  role: string;
  phone?: string;
  is_active: boolean;
  tenants: Array<{
    id: string;
    name?: string;
    role: string;
  }>;
}

export default function EditUser() {
  const params = useParams();
  const router = useRouter();
  const userId = params.id as string;
  
  // Lấy thông tin user hiện tại
  const { user, isLoading: isLoadingUser, isError: userError, mutate } = useUser(userId);
  
  // Lấy danh sách tenants cho dropdown
  const { tenants: availableTenants, isLoading: isLoadingTenants } = useTenantsDropdown();
  
  // User actions
  const { updateUser, isLoading: isUpdating, error: updateError } = useUserActions();
  
  // State cho form
  const [formData, setFormData] = useState<UserFormData>({
    full_name: '',
    role: 'user',
    phone: '',
    is_active: true,
    tenants: []
  });
  
  // State cho validation
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // State cho tenant selection
  const [selectedTenantId, setSelectedTenantId] = useState<string>('');
  const [selectedTenantRole, setSelectedTenantRole] = useState<string>('member');
  
  // Đổ dữ liệu user vào form khi load xong
  useEffect(() => {
    if (user) {
      setFormData({
        full_name: user.full_name,
        role: user.role,
        phone: user.phone || '',
        is_active: user.is_active,
        tenants: user.tenants ? [...user.tenants] : []
      });
    }
  }, [user]);
  
  // Xử lý thay đổi input
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setFormData({
        ...formData,
        [name]: checkbox.checked
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
    
    // Reset error khi user nhập lại
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };
  
  // Thêm tenant
  const handleAddTenant = () => {
    if (!selectedTenantId) return;
    
    // Kiểm tra xem tenant đã được thêm chưa
    if (formData.tenants.some(t => t.id === selectedTenantId)) {
      setErrors({
        ...errors,
        tenant: 'This tenant has already been added'
      });
      return;
    }
    
    const selectedTenant = availableTenants.find(t => t.id === selectedTenantId);
    const tenantToAdd = {
      id: selectedTenantId,
      name: selectedTenant?.name,
      role: selectedTenantRole
    };
    
    setFormData({
      ...formData,
      tenants: [...formData.tenants, tenantToAdd]
    });
    
    // Reset selection
    setSelectedTenantId('');
    setSelectedTenantRole('member');
    
    // Clear tenant error if exists
    if (errors.tenant) {
      setErrors({
        ...errors,
        tenant: ''
      });
    }
  };
  
  // Xóa tenant
  const handleRemoveTenant = (tenantId: string) => {
    setFormData({
      ...formData,
      tenants: formData.tenants.filter(t => t.id !== tenantId)
    });
  };
  
  // Validate form trước khi submit
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.full_name) {
      newErrors.full_name = 'Full name is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Xử lý submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    const result = await updateUser(userId, formData);
    
    if (result) {
      // Refresh dữ liệu user
      mutate();
      // Redirect về trang user detail
      router.push(`/users/${userId}`);
    }
  };

  const sidebarItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      href: '/',
      icon: <HomeIcon />,
    },
    {
      id: 'tenants',
      label: 'Tenants',
      href: '/tenants',
      icon: <BuildingIcon />,
    },
    {
      id: 'users',
      label: 'Users',
      href: '/users',
      icon: <UsersIcon />,
      active: true,
    },
    {
      id: 'licenses',
      label: 'Licenses',
      href: '/licenses',
      icon: <LicenseIcon />,
    },
    {
      id: 'settings',
      label: 'Settings',
      href: '/settings',
      icon: <SettingsIcon />,
    },
  ];

  // Loading state
  if (isLoadingUser) {
    return (
      <DashboardLayout
        sidebarItems={sidebarItems}
        title="Loading User..."
        username="Admin User"
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Users', href: '/users' },
          { label: 'Loading...' }
        ]}
      >
        <div className={styles.loadingContainer}>
          Loading user information...
        </div>
      </DashboardLayout>
    );
  }

  // Error state
  if (userError || !user) {
    return (
      <DashboardLayout
        sidebarItems={sidebarItems}
        title="Error"
        username="Admin User"
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Users', href: '/users' },
          { label: 'Error' }
        ]}
      >
        <div className={styles.errorContainer}>
          <h2>User not found</h2>
          <p>The user you're trying to edit may have been deleted or you don't have permission to view it.</p>
          <Button variant="primary" label="Back to Users" onClick={() => router.push('/users')} />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      sidebarItems={sidebarItems}
      title={`Edit User: ${user.full_name}`}
      username="Admin User"
      breadcrumbs={[
        { label: 'Dashboard', href: '/' },
        { label: 'Users', href: '/users' },
        { label: user.full_name, href: `/users/${userId}` },
        { label: 'Edit' }
      ]}
    >
	<ErrorBoundary>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Edit User: {user.full_name}</h1>
          <div className={styles.actions}>
            <Button variant="outline" label="Cancel" onClick={() => router.push(`/users/${userId}`)} />
            <Button variant="primary" label="Save Changes" onClick={handleSubmit} disabled={isUpdating} />
          </div>
        </div>
        
        {updateError && (
          <div className={styles.errorMessage}>
            {updateError}
          </div>
        )}
        
        <form className={styles.form} onSubmit={handleSubmit}>
          <div className={styles.formGrid}>
            <Card className={styles.formCard}>
              <h2 className={styles.sectionTitle}>Basic Information</h2>
              
              <div className={styles.formGroup}>
                <label htmlFor="email" className={styles.label}>
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  value={user.email}
                  className={`${styles.input} ${styles.disabled}`}
                  disabled
                />
                <div className={styles.helperText}>
                  Email address cannot be changed
                </div>
              </div>
              
              <div className={styles.formGroup}>
                <label htmlFor="full_name" className={styles.label}>
                  Full Name <span className={styles.required}>*</span>
                </label>
                <input
                  type="text"
                  id="full_name"
                  name="full_name"
                  value={formData.full_name}
                  onChange={handleInputChange}
                  className={`${styles.input} ${errors.full_name ? styles.inputError : ''}`}
                  placeholder="Enter full name"
                />
                {errors.full_name && <div className={styles.errorText}>{errors.full_name}</div>}
              </div>
              
              <div className={styles.formGroup}>
                <label htmlFor="phone" className={styles.label}>
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone || ''}
                  onChange={handleInputChange}
                  className={styles.input}
                  placeholder="Enter phone number (optional)"
                />
              </div>
              
              <div className={styles.formGroup}>
                <label htmlFor="role" className={styles.label}>
                  System Role <span className={styles.required}>*</span>
                </label>
                <select
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  className={styles.select}
                >
                  <option value="user">User</option>
                  <option value="admin">Admin</option>
                  <option value="super_admin">Super Admin</option>
                </select>
                <div className={styles.helperText}>
                  {formData.role === 'user' && 'Basic access only'}
                  {formData.role === 'admin' && 'Can manage users within assigned tenants'}
                  {formData.role === 'super_admin' && 'Full system access'}
                </div>
              </div>
              
              <div className={styles.formGroup}>
                <div className={styles.checkboxGroup}>
                  <input
                    type="checkbox"
                    id="is_active"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={handleInputChange}
                    className={styles.checkbox}
                  />
                  <label htmlFor="is_active" className={styles.checkboxLabel}>
                    User is active
                  </label>
                </div>
                <div className={styles.helperText}>
                  If unchecked, the user will not be able to log in.
                </div>
              </div>
            </Card>
            
            <Card className={styles.formCard}>
              <h2 className={styles.sectionTitle}>Tenant Associations</h2>
              
              <div className={styles.tenantSelector}>
                <div className={styles.selectorGroup}>
                  <label className={styles.label}>Add Tenant</label>
                  <div className={styles.selectorRow}>
                    <select
                      value={selectedTenantId}
                      onChange={(e) => setSelectedTenantId(e.target.value)}
                      className={styles.select}
                      disabled={isLoadingTenants}
                    >
                      <option value="">Select a tenant</option>
                      {availableTenants.map(tenant => (
                        <option key={tenant.id} value={tenant.id}>
                          {tenant.name}
                        </option>
                      ))}
                    </select>
                    
                    <select
                      value={selectedTenantRole}
                      onChange={(e) => setSelectedTenantRole(e.target.value)}
                      className={`${styles.select} ${styles.roleSelect}`}
                    >
                      <option value="member">Member</option>
                      <option value="admin">Admin</option>
                      <option value="owner">Owner</option>
                    </select>
                    
                    <Button
                      size="small"
                      variant="outline"
                      label="Add"
                      onClick={handleAddTenant}
                      disabled={!selectedTenantId || isLoadingTenants}
                    />
                  </div>
                  {errors.tenant && <div className={styles.errorText}>{errors.tenant}</div>}
                  {isLoadingTenants && <div className={styles.loadingText}>Loading tenants...</div>}
                </div>
                
                <div className={styles.tenantList}>
                  {formData.tenants.length === 0 ? (
                    <div className={styles.emptyState}>
                      No tenants added yet. User will not have access to any tenant.
                    </div>
                  ) : (
                    <ul className={styles.tenantItems}>
                      {formData.tenants.map(tenant => (
                        <li key={tenant.id} className={styles.tenantItem}>
                          <div className={styles.tenantInfo}>
                            <span className={styles.tenantName}>{tenant.name}</span>
                            <span className={styles.tenantRole}>{tenant.role}</span>
                          </div>
                          <button
                            type="button"
                            className={styles.removeButton}
                            onClick={() => handleRemoveTenant(tenant.id)}
                            aria-label="Remove tenant"
                          >
                            &times;
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
              
              <div className={styles.noteBox}>
                <h3 className={styles.noteTitle}>Important Note</h3>
                <p>
                  Removing a tenant association will revoke the user's access to that tenant. 
                  This action cannot be undone.
                </p>
              </div>
            </Card>
          </div>
        </form>
      </div>
	</ErrorBoundary>
    </DashboardLayout>
  );
}
