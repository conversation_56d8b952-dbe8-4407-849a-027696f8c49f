// Core chat types
export interface ChatMessage {
  id: string
  chat_session_id: string
  sender_type: 'guest' | 'staff'
  sender_id: string
  content: string
  original_content?: string
  translated_content?: string
  original_language?: string
  translated_language?: string
  is_translated: boolean
  created_at: string
  metadata?: any
}

export interface ChatSession {
  id: string
  tenant_id: string
  guest_id?: string
  status: 'active' | 'ended' | 'pending'
  guest_language?: string
  staff_language?: string
  auto_translate: boolean
  source_type?: string
  source_qr_code_id?: string
  reception_point_id?: string
  priority: string
  created_at: string
  updated_at: string
  ended_at?: string
}

export interface TemporaryUser {
  id: string
  qr_code_id: string
  device_id: string
  preferred_language: string
  created_at: string
  expires_at: string
  is_activated: boolean
  room_number?: string
  hotel_id?: string
  metadata?: any
}

export interface QRCodeInfo {
  id: string
  tenant_id: string
  code_value: string
  location?: string
  description?: string
  room_number?: string
  target_type?: string
  status: string
}

// Language types
export type SupportedLanguage = 'en' | 'vi' | 'ko' | 'ja' | 'zh' | 'th' | 'id' | 'ms' | 'es' | 'fr' | 'de' | 'ar'

export interface Language {
  code: SupportedLanguage
  name: string
  nativeName: string
  flag: string
  rtl?: boolean
}
