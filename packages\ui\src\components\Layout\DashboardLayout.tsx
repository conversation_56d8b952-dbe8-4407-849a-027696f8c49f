import React, { ReactNode, useState } from 'react';
import styles from './DashboardLayout.module.scss';

export interface SidebarItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  href?: string;
  onClick?: () => void;
  items?: SidebarItem[];
  active?: boolean;
}

export interface DashboardLayoutProps {
  children: ReactNode;
  sidebarItems: SidebarItem[];
  title?: string;
  username?: string;
  userAvatar?: string;
  onLogout?: () => void;
  logo?: React.ReactNode;
  headerActions?: React.ReactNode;
  breadcrumbs?: {
    label: string;
    href?: string;
  }[];
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  sidebarItems,
  title = 'LoaLoa Admin',
  username,
  userAvatar,
  onLogout,
  logo,
  headerActions,
  breadcrumbs,
}) => {
  const [collapsed, setCollapsed] = useState(false);
  
  return (
    <div className={styles.dashboardContainer}>
      <aside className={`${styles.sidebar} ${collapsed ? styles.collapsed : ''}`}>
        <div className={styles.logoContainer}>
          {logo ? (
            <div className={collapsed ? styles.logoSmall : styles.logo}>
              {logo}
            </div>
          ) : (
            <h2 className={styles.logoText}>{!collapsed ? title : 'LA'}</h2>
          )}
          <button 
            className={styles.collapseBtn}
            onClick={() => setCollapsed(!collapsed)}
            aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {collapsed ? '→' : '←'}
          </button>
        </div>
        
        <nav className={styles.navigation}>
          <ul>
            {sidebarItems.map((item) => (
              <li key={item.id} className={styles.navItem}>
                <a 
                  href={item.href || '#'}
                  onClick={item.onClick}
                  className={`${styles.navLink} ${item.active ? styles.active : ''}`}
                >
                  {item.icon && <span className={styles.icon}>{item.icon}</span>}
                  {!collapsed && <span className={styles.navLabel}>{item.label}</span>}
                </a>
                {!collapsed && item.items && item.items.length > 0 && (
                  <ul className={styles.subNav}>
                    {item.items.map((subItem) => (
                      <li key={subItem.id}>
                        <a 
                          href={subItem.href || '#'}
                          onClick={subItem.onClick}
                          className={subItem.active ? styles.active : ''}
                        >
                          {subItem.icon && <span className={styles.subIcon}>{subItem.icon}</span>}
                          <span>{subItem.label}</span>
                        </a>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </nav>
        
        <div className={styles.userSection}>
          {username && (
            <div className={styles.user}>
              {userAvatar && (
                <img 
                  src={userAvatar} 
                  alt={username} 
                  className={styles.avatar}
                />
              )}
              {!collapsed && (
                <>
                  <span className={styles.username}>{username}</span>
                  {onLogout && (
                    <button 
                      className={styles.logoutBtn}
                      onClick={onLogout}
                    >
                      Logout
                    </button>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </aside>
      
      <main className={styles.content}>
        <header className={styles.header}>
          <div className={styles.headerLeft}>
            {breadcrumbs && breadcrumbs.length > 0 && (
              <div className={styles.breadcrumbs}>
                {breadcrumbs.map((crumb, index) => (
                  <React.Fragment key={index}>
                    {index > 0 && <span className={styles.breadcrumbSeparator}>/</span>}
                    {crumb.href ? (
                      <a href={crumb.href} className={styles.breadcrumbLink}>
                        {crumb.label}
                      </a>
                    ) : (
                      <span className={styles.breadcrumbCurrent}>{crumb.label}</span>
                    )}
                  </React.Fragment>
                ))}
              </div>
            )}
            {title && <h1 className={styles.pageTitle}>{title}</h1>}
          </div>
          
          {headerActions && (
            <div className={styles.headerActions}>
              {headerActions}
            </div>
          )}
        </header>
        
        <div className={styles.pageContent}>
          {children}
        </div>
      </main>
    </div>
  );
};
