-- Bật RLS cho tất cả các bảng
ALTER TABLE chat_rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_translations ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_reactions ENABLE ROW LEVEL SECURITY;

-- Policy cho chat_rooms
-- Người dùng có thể xem phòng chat nếu họ là người tham gia hoặc phòng là công khai
CREATE POLICY "Users can view rooms they are participants in" 
  ON chat_rooms FOR SELECT 
  USING (
    room_type = 'general' OR 
    EXISTS (
      SELECT 1 FROM chat_participants 
      WHERE chat_participants.chat_room_id = chat_rooms.id 
      AND chat_participants.user_id = auth.uid()
      AND chat_participants.is_active = true
    )
  );

-- Ngư<PERSON>i dùng có thể tạo phòng chat (sẽ được kiểm soát qua API)
CREATE POLICY "Users can create rooms" 
  ON chat_rooms FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

-- Người dùng có thể cập nhật phòng chat nếu họ là admin
CREATE POLICY "Admins can update rooms" 
  ON chat_rooms FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM chat_participants 
      WHERE chat_participants.chat_room_id = chat_rooms.id 
      AND chat_participants.user_id = auth.uid()
      AND chat_participants.participant_role = 'admin'
      AND chat_participants.is_active = true
    )
  );

-- Policy cho chat_participants
-- Người dùng có thể xem danh sách người tham gia trong các phòng họ tham gia
CREATE POLICY "Users can see participants in their rooms" 
  ON chat_participants FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM chat_participants AS my_participation
      WHERE my_participation.chat_room_id = chat_participants.chat_room_id 
      AND my_participation.user_id = auth.uid()
      AND my_participation.is_active = true
    )
  );

-- Người dùng có thể tham gia vào phòng chat (sẽ được kiểm soát qua API)
CREATE POLICY "Users can join rooms" 
  ON chat_participants FOR INSERT 
  WITH CHECK (user_id = auth.uid() OR auth.uid() IS NOT NULL);

-- Người dùng chỉ có thể cập nhật thông tin tham gia của chính họ
CREATE POLICY "Users can update their own participation" 
  ON chat_participants FOR UPDATE 
  USING (user_id = auth.uid());

-- Policy cho chat_messages
-- Người dùng có thể xem tin nhắn trong phòng họ tham gia
CREATE POLICY "Users can view messages in their rooms" 
  ON chat_messages FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM chat_participants
      WHERE chat_participants.chat_room_id = chat_messages.chat_room_id 
      AND chat_participants.user_id = auth.uid()
      AND chat_participants.is_active = true
    )
  );

-- Người dùng có thể gửi tin nhắn trong phòng họ tham gia
CREATE POLICY "Users can send messages in their rooms" 
  ON chat_messages FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM chat_participants
      WHERE chat_participants.id = chat_messages.participant_id
      AND chat_participants.user_id = auth.uid()
      AND chat_participants.is_active = true
    )
  );

-- Người dùng chỉ có thể cập nhật tin nhắn của chính họ
CREATE POLICY "Users can update their own messages" 
  ON chat_messages FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM chat_participants
      WHERE chat_participants.id = chat_messages.participant_id
      AND chat_participants.user_id = auth.uid()
    )
  );

-- Policy cho message_translations
-- Bất kỳ ai cũng có thể xem bản dịch tin nhắn mà họ có quyền xem tin nhắn gốc
CREATE POLICY "Users can view translations of accessible messages" 
  ON message_translations FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM chat_messages
      JOIN chat_participants ON chat_participants.chat_room_id = chat_messages.chat_room_id
      WHERE message_translations.message_id = chat_messages.id
      AND chat_participants.user_id = auth.uid()
      AND chat_participants.is_active = true
    )
  );

-- Policy tương tự cho các bảng còn lại
-- message_status, message_attachments, message_reactions
