import useSWR from 'swr';
import { User, UserFilterOptions, PaginationOptions } from '@/services/UserService';

const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    const error = new Error('An error occurred while fetching the data.');
    const data = await response.json();
    (error as any).info = data.error;
    (error as any).status = response.status;
    throw error;
  }
  return response.json();
};

export interface UsersResponse {
  data: User[];
  meta: {
    total: number;
    page: number;
    limit: number;
    pageCount: number;
  };
}

export function useUsers(
  pagination: PaginationOptions = { page: 1, limit: 10 },
  filter: UserFilterOptions = {}
) {
  // Tạo query string từ pagination và filter
  const queryParams = new URLSearchParams();
  queryParams.append('page', pagination.page.toString());
  queryParams.append('limit', pagination.limit.toString());
  
  if (filter.status) queryParams.append('status', filter.status);
  if (filter.role) queryParams.append('role', filter.role);
  if (filter.tenant_id) queryParams.append('tenant_id', filter.tenant_id);
  if (filter.search) queryParams.append('search', filter.search);
  
  const url = `/api/users?${queryParams.toString()}`;
  
  const { data, error, mutate, isLoading } = useSWR<UsersResponse>(url, fetcher);
  
  return {
    users: data?.data || [],
    meta: data?.meta,
    isLoading,
    isError: error,
    mutate,
  };
}
