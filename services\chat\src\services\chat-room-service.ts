import supabase from '../utils/supabase';
import { ChatRoom, ChatParticipant } from '../types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Lấy phòng chat theo ID
 */
export const getRoomById = async (roomId: string): Promise<ChatRoom | null> => {
  try {
    const { data, error } = await supabase
      .from('chat_rooms')
      .select('*')
      .eq('id', roomId)
      .eq('is_active', true)
      .single();
    
    if (error) {
      console.error('Error fetching room:', error);
      return null;
    }
    
    return data as ChatRoom;
  } catch (error) {
    console.error('Error in getRoomById:', error);
    return null;
  }
};

/**
 * Lấy danh sách phòng chat cho người dùng
 */
export const getRoomsForUser = async (userId?: string, temporaryUserId?: string): Promise<ChatRoom[]> => {
  try {
    if (!userId && !temporaryUserId) {
      return [];
    }
    
    // Tìm danh sách phòng chat mà người dùng đang tham gia
    const { data: participants, error: participantsError } = await supabase
      .from('chat_participants')
      .select('chat_room_id')
      .or(`user_id.eq.${userId || null},temporary_user_id.eq.${temporaryUserId || null}`)
      .eq('is_active', true);
    
    if (participantsError || !participants || participants.length === 0) {
      return [];
    }
    
    const roomIds = participants.map(p => p.chat_room_id);
    
    // Lấy thông tin chi tiết về các phòng
    const { data: rooms, error: roomsError } = await supabase
      .from('chat_rooms')
      .select('*')
      .in('id', roomIds)
      .eq('is_active', true);
    
    if (roomsError) {
      console.error('Error fetching rooms:', roomsError);
      return [];
    }
    
    return rooms as ChatRoom[];
  } catch (error) {
    console.error('Error in getRoomsForUser:', error);
    return [];
  }
};

/**
 * Tạo phòng chat mới
 */
export const createRoom = async (
  name: string,
  description: string,
  roomType: string,
  createdById?: string,
  hotelId?: string,
  roomNumber?: string,
  metadata?: Record<string, any>
): Promise<ChatRoom | null> => {
  try {
    const { data, error } = await supabase
      .from('chat_rooms')
      .insert([{
        name,
        description,
        room_type: roomType,
        created_by: createdById,
        hotel_id: hotelId,
        room_number: roomNumber,
        metadata
      }])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating room:', error);
      return null;
    }
    
    return data as ChatRoom;
  } catch (error) {
    console.error('Error in createRoom:', error);
    return null;
  }
};

/**
 * Cập nhật thông tin phòng chat
 */
export const updateRoom = async (
  roomId: string,
  updates: Partial<ChatRoom>
): Promise<ChatRoom | null> => {
  try {
    // Chỉ cho phép cập nhật một số trường nhất định
    const allowedUpdates = {
      name: updates.name,
      description: updates.description,
      is_active: updates.is_active,
      metadata: updates.metadata
    };
    
    const { data, error } = await supabase
      .from('chat_rooms')
      .update(allowedUpdates)
      .eq('id', roomId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating room:', error);
      return null;
    }
    
    return data as ChatRoom;
  } catch (error) {
    console.error('Error in updateRoom:', error);
    return null;
  }
};

/**
 * Thêm người tham gia vào phòng chat
 */
export const addParticipant = async (
  roomId: string,
  userId?: string,
  temporaryUserId?: string,
  role: string = 'member',
  displayName?: string,
  preferredLanguage: string = 'en'
): Promise<ChatParticipant | null> => {
  try {
    if (!userId && !temporaryUserId) {
      throw new Error('Either userId or temporaryUserId is required');
    }
    
    // Kiểm tra người dùng đã trong phòng chưa
    const { data: existingParticipant, error: checkError } = await supabase
      .from('chat_participants')
      .select('*')
      .eq('chat_room_id', roomId)
      .or(`user_id.eq.${userId || null},temporary_user_id.eq.${temporaryUserId || null}`)
      .single();
    
    if (existingParticipant) {
      // Nếu đã tồn tại nhưng không active, kích hoạt lại
      if (!existingParticipant.is_active) {
        const { data: reactivated, error: updateError } = await supabase
          .from('chat_participants')
          .update({
            is_active: true,
            left_at: null,
            display_name: displayName || existingParticipant.display_name,
            preferred_language: preferredLanguage || existingParticipant.preferred_language
          })
          .eq('id', existingParticipant.id)
          .select()
          .single();
        
        if (updateError) {
          console.error('Error reactivating participant:', updateError);
          return null;
        }
        
        return reactivated as ChatParticipant;
      }
      
      // Nếu đã active, trả về thông tin hiện tại
      return existingParticipant as ChatParticipant;
    }
    
    // Thêm người tham gia mới
    const { data, error } = await supabase
      .from('chat_participants')
      .insert([{
        chat_room_id: roomId,
        user_id: userId,
        temporary_user_id: temporaryUserId,
        participant_role: role,
        display_name: displayName,
        preferred_language: preferredLanguage
      }])
      .select()
      .single();
    
    if (error) {
      console.error('Error adding participant:', error);
      return null;
    }
    
    return data as ChatParticipant;
  } catch (error) {
    console.error('Error in addParticipant:', error);
    return null;
  }
};

/**
 * Cập nhật thông tin người tham gia
 */
export const updateParticipant = async (
  participantId: string,
  updates: Partial<ChatParticipant>
): Promise<ChatParticipant | null> => {
  try {
    // Chỉ cho phép cập nhật một số trường nhất định
    const allowedUpdates = {
      is_active: updates.is_active,
      left_at: updates.left_at,
      display_name: updates.display_name,
      preferred_language: updates.preferred_language,
      last_read_message_id: updates.last_read_message_id,
      metadata: updates.metadata
    };
    
    const { data, error } = await supabase
      .from('chat_participants')
      .update(allowedUpdates)
      .eq('id', participantId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating participant:', error);
      return null;
    }
    
    return data as ChatParticipant;
  } catch (error) {
    console.error('Error in updateParticipant:', error);
    return null;
  }
};

/**
 * Xóa người tham gia khỏi phòng chat (soft delete)
 */
export const removeParticipant = async (
  roomId: string,
  userId?: string,
  temporaryUserId?: string
): Promise<boolean> => {
  try {
    if (!userId && !temporaryUserId) {
      throw new Error('Either userId or temporaryUserId is required');
    }
    
    // Thực hiện soft delete bằng cách cập nhật trạng thái
    const { error } = await supabase
      .from('chat_participants')
      .update({
        is_active: false,
        left_at: new Date().toISOString()
      })
      .eq('chat_room_id', roomId)
      .or(`user_id.eq.${userId || null},temporary_user_id.eq.${temporaryUserId || null}`);
    
    if (error) {
      console.error('Error removing participant:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in removeParticipant:', error);
    return false;
  }
};

/**
 * Kiểm tra người dùng có quyền tham gia phòng chat hay không
 */
/**
 * Kiểm tra người dùng có quyền tham gia phòng chat hay không
 */
export const canJoinRoom = async (
  roomId: string,
  userId?: string,
  temporaryUserId?: string
): Promise<boolean> => {
  try {
    if (!userId && !temporaryUserId) {
      return false;
    }
    
    // Ghi log thông tin đầu vào để debug
    console.log(`Checking if user can join room - roomId: ${roomId}, userId: ${userId}, temporaryUserId: ${temporaryUserId}`);
    
    // Kiểm tra phòng chat có tồn tại và active không
    const { data: room, error: roomError } = await supabase
      .from('chat_rooms')
      .select('*')
      .eq('id', roomId)
      .eq('is_active', true)
      .single();
    
    if (roomError) {
      console.error('Error fetching room:', roomError);
      return false;
    }
    
    if (!room) {
      console.error('Room not found');
      return false;
    }
    
    console.log('Room found:', room);
    
    // Nếu là phòng công khai, cho phép tham gia
    if (room.room_type === 'general') {
      console.log('Room is public, allowing access');
      return true;
    }
    
    // TEMP: Tạm thời cho phép tham gia mọi phòng để kiểm tra
    console.log('TEMPORARY: Allowing access to all rooms for testing');
    return true;
    
    // Đoạn code bên dưới sẽ được sử dụng sau khi đã kiểm tra xong
    /*
    // Kiểm tra người dùng đã tham gia phòng chưa
    let query = supabase
      .from('chat_participants')
      .select('*')
      .eq('chat_room_id', roomId)
      .eq('is_active', true);
    
    if (userId) {
      query = query.eq('user_id', userId);
    } else if (temporaryUserId) {
      query = query.eq('temporary_user_id', temporaryUserId);
    }
    
    const { data: participant, error: participantError } = await query;
    
    if (participantError) {
      console.error('Error checking participant:', participantError);
      return false;
    }
    
    console.log('Participant check result:', participant);
    
    // Nếu đã tham gia và active, cho phép vào phòng
    if (participant && participant.length > 0) {
      return true;
    }
    
    return false;
    */
  } catch (error) {
    console.error('Error in canJoinRoom:', error);
    return false;
  }
};

/**
 * Lấy danh sách người tham gia trong phòng chat
 */
export const getRoomParticipants = async (roomId: string): Promise<ChatParticipant[]> => {
  try {
    const { data, error } = await supabase
      .from('chat_participants')
      .select('*')
      .eq('chat_room_id', roomId)
      .eq('is_active', true);
    
    if (error) {
      console.error('Error fetching participants:', error);
      return [];
    }
    
    return data as ChatParticipant[];
  } catch (error) {
    console.error('Error in getRoomParticipants:', error);
    return [];
  }
};