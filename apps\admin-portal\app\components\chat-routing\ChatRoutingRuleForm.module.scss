.form {
  width: 100%;
}

.formSection {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #333;
}

.formGroup {
  margin-bottom: 1.25rem;

  label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #333;

    .required {
      color: #f44336;
      margin-left: 0.25rem;
    }
  }

  input[type="text"],
  input[type="number"],
  input[type="time"],
  select,
  textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    
    &:focus {
      outline: none;
      border-color: #1976d2;
      box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    }
  }

  input[type="number"] {
    width: 150px;
  }

  .inputError {
    border-color: #f44336;
  }

  .errorText {
    color: #f44336;
    font-size: 0.85rem;
    margin-top: 0.25rem;
  }

  .fieldDescription {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.5rem;
  }
}

.checkboxContainer {
  display: flex;
  align-items: center;

  input[type="checkbox"] {
    margin-right: 0.5rem;
  }

  .checkboxLabel {
    margin-bottom: 0;
    cursor: pointer;
  }
}

.radioGroup {
  display: flex;
  gap: 1.5rem;

  .radioOption {
    display: flex;
    align-items: center;

    input[type="radio"] {
      margin-right: 0.5rem;
    }

    label {
      margin-bottom: 0;
      cursor: pointer;
    }
  }
}

.formActions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1rem;
  color: #666;
}
