@import '../../../styles/variables';

.container {
  display: flex;
  align-items: flex-end;
  padding: 8px;
  background-color: white;
  border: 1px solid var(--color-border, #E1E1E1);
  border-radius: 12px;
}

.inputWrapper {
  flex: 1;
  margin-right: 8px;
}

.input {
  width: 100%;
  padding: 8px;
  border: none;
  outline: none;
  resize: none;
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.5;
  max-height: 120px;
  overflow-y: auto;
  
  &:disabled {
    background-color: transparent;
    color: var(--color-gray, #7D8491);
  }
  
  &::placeholder {
    color: var(--color-gray, #7D8491);
  }
}

.sendButton {
  align-self: flex-end;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 0;
  
  svg {
    margin: 0;
  }
}
