import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Lấy chi tiết phân công nhân viên theo ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const assignmentId = params.id;

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Truy vấn chi tiết phân công
    const { data, error } = await supabase
      .from('tenant_staff_assignments')
      .select(`
        *,
        tenant_users!inner (
          id,
          role,
          tenant_users_details (
            email,
            display_name,
            avatar_url,
            phone
          )
        )
      `)
      .eq('id', assignmentId)
      .eq('tenant_id', tenant_id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Assignment not found' }, { status: 404 });
      }
      console.error('Error fetching assignment:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Định dạng lại dữ liệu
    const formattedData = {
      id: data.id,
      user_id: data.user_id,
      user: {
        id: data.tenant_users.id,
        role: data.tenant_users.role,
        email: data.tenant_users.tenant_users_details?.email,
        display_name: data.tenant_users.tenant_users_details?.display_name,
        avatar_url: data.tenant_users.tenant_users_details?.avatar_url,
        phone: data.tenant_users.tenant_users_details?.phone,
      },
      department: data.department,
      assignment_type: data.assignment_type,
      resource_id: data.resource_id,
      working_hours: data.working_hours,
      priority: data.priority,
      is_active: data.is_active,
      created_at: data.created_at,
      updated_at: data.updated_at
    };

    // Trả về kết quả
    return NextResponse.json({ data: formattedData });

  } catch (error: any) {
    console.error('Error in GET staff assignment:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 });
  }
}

// PUT: Cập nhật phân công nhân viên
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const assignmentId = params.id;
    const updateData = await request.json();

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra phân công có tồn tại không
    const { data: existingAssignment, error: checkError } = await supabase
      .from('tenant_staff_assignments')
      .select('id')
      .eq('id', assignmentId)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Assignment not found' }, { status: 404 });
      }
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    // Chuẩn bị dữ liệu cập nhật
    const assignmentUpdateData: any = {
      updated_at: new Date().toISOString()
    };

    // Chỉ cập nhật các trường được cung cấp
    if (updateData.department !== undefined) assignmentUpdateData.department = updateData.department;
    if (updateData.assignment_type !== undefined) assignmentUpdateData.assignment_type = updateData.assignment_type;
    if (updateData.resource_id !== undefined) assignmentUpdateData.resource_id = updateData.resource_id;
    if (updateData.working_hours !== undefined) assignmentUpdateData.working_hours = updateData.working_hours;
    if (updateData.priority !== undefined) assignmentUpdateData.priority = updateData.priority;
    if (updateData.is_active !== undefined) assignmentUpdateData.is_active = updateData.is_active;

    // Cập nhật phân công
    const { data, error } = await supabase
      .from('tenant_staff_assignments')
      .update(assignmentUpdateData)
      .eq('id', assignmentId)
      .eq('tenant_id', tenant_id)
      .select(`
        *,
        tenant_users!inner (
          id,
          role,
          tenant_users_details (
            email,
            display_name,
            avatar_url
          )
        )
      `)
      .single();

    if (error) {
      console.error('Error updating staff assignment:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Định dạng lại dữ liệu
    const formattedData = {
      id: data.id,
      user_id: data.user_id,
      user: {
        id: data.tenant_users.id,
        role: data.tenant_users.role,
        email: data.tenant_users.tenant_users_details?.email,
        display_name: data.tenant_users.tenant_users_details?.display_name,
        avatar_url: data.tenant_users.tenant_users_details?.avatar_url,
      },
      department: data.department,
      assignment_type: data.assignment_type,
      resource_id: data.resource_id,
      working_hours: data.working_hours,
      priority: data.priority,
      is_active: data.is_active,
      created_at: data.created_at,
      updated_at: data.updated_at
    };

    // Trả về kết quả
    return NextResponse.json({ 
      data: formattedData, 
      message: 'Staff assignment updated successfully' 
    });

  } catch (error: any) {
    console.error('Error in PUT staff assignment:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 });
  }
}

// DELETE: Xóa phân công nhân viên
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const assignmentId = params.id;

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra phân công có tồn tại không
    const { data: existingAssignment, error: checkError } = await supabase
      .from('tenant_staff_assignments')
      .select('id')
      .eq('id', assignmentId)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Assignment not found' }, { status: 404 });
      }
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    // Xóa phân công
    const { error } = await supabase
      .from('tenant_staff_assignments')
      .delete()
      .eq('id', assignmentId)
      .eq('tenant_id', tenant_id);

    if (error) {
      console.error('Error deleting staff assignment:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({ message: 'Staff assignment deleted successfully' });

  } catch (error: any) {
    console.error('Error in DELETE staff assignment:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 });
  }
}