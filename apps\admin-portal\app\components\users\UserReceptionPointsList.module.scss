.loadingContainer {
  padding: 2rem 0;
  text-align: center;
  color: #64748b;
}

.emptyState {
  padding: 2rem;
  text-align: center;
  border: 1px dashed #cbd5e1;
  border-radius: 0.5rem;
  background-color: #f8fafc;
  margin: 1rem 0;
  
  p {
    margin: 0;
    color: #64748b;
    font-size: 0.9375rem;
  }
  
  .emptyStateSubtext {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #94a3b8;
  }
}

.receptionPointsList {
  margin-top: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  overflow: hidden;
}

.header {
  display: flex;
  background-color: #f1f5f9;
  padding: 0.75rem 1rem;
  font-weight: 600;
  font-size: 0.875rem;
  color: #475569;
}

.nameHeader {
  flex: 1;
}

.priorityHeader {
  width: 100px;
  text-align: center;
}

.actionHeader {
  width: 200px;
  text-align: right;
}

.list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-top: 1px solid #e2e8f0;
}

.name {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.primaryBadge {
  display: inline-block;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 1rem;
  background-color: #0ea5e9;
  color: white;
}

.priority {
  width: 100px;
  text-align: center;
}

.priorityInput {
  width: 60px;
  padding: 0.25rem;
  text-align: center;
  border: 1px solid #cbd5e1;
  border-radius: 0.25rem;
}

.actions {
  width: 200px;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.primaryButton {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border: 1px solid #0ea5e9;
  background-color: transparent;
  color: #0ea5e9;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #0ea5e9;
    color: white;
  }
}

.removeButton {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border: 1px solid #ef4444;
  background-color: transparent;
  color: #ef4444;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #ef4444;
    color: white;
  }
}