@import '../styles/_variables';

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: white;
  border-radius: $border-radius-lg;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: $shadow-lg;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modalHeader {
  padding: $spacing-md $spacing-lg;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: $dark-gray;
  }
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  color: $gray;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  padding: 0;
  
  &:hover {
    background-color: #f5f5f5;
    color: $dark-gray;
  }
}

.modalBody {
  padding: $spacing-lg;
  overflow-y: auto;
  flex: 1;
}

.modalFooter {
  padding: $spacing-md $spacing-lg;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: $spacing-md;
}

.cancelButton, .confirmButton {
  padding: 10px 16px;
  border-radius: $border-radius-md;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancelButton {
  background-color: transparent;
  color: $gray;
  border: 1px solid #ddd;
  
  &:hover {
    background-color: #f5f5f5;
    color: $dark-gray;
  }
}

.confirmButton {
  background-color: $primary-color;
  color: white;
  border: none;
  
  &:hover:not(:disabled) {
    background-color: darken($primary-color, 10%);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.loading {
  text-align: center;
  padding: $spacing-lg;
  color: $gray;
}

.roomGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
}

.roomCard {
  border: 1px solid #ddd;
  border-radius: $border-radius-md;
  padding: $spacing-md;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    border-color: $primary-color;
    box-shadow: $shadow-sm;
  }
  
  &.selected {
    border-color: $primary-color;
    background-color: #f0f9ff;
  }
}

.roomNumber {
  font-size: 16px;
  font-weight: 600;
  color: $dark-gray;
  margin-bottom: 4px;
}

.roomType {
  font-size: 12px;
  color: $gray;
}

.roomFloor {
  font-size: 12px;
  color: $gray;
  margin-top: 2px;
}

.noRooms {
  text-align: center;
  padding: $spacing-lg;
  color: $gray;
}

.customRoomSection {
  margin-top: $spacing-lg;
  border-top: 1px solid #eee;
  padding-top: $spacing-md;
}

.checkboxGroup {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: $spacing-sm;
  
  label {
    font-size: 14px;
    color: $dark-gray;
    cursor: pointer;
  }
}

.customRoomInput {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: $border-radius-sm;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
  }
}
