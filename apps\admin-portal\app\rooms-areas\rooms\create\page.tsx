'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import DashboardLayout from '../../../dashboard-layout';
import RoomForm from '../../../components/rooms/RoomForm';
import styles from './create-room.module.scss';

export default function CreateRoomPage() {
  const router = useRouter();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [createdRoom, setCreatedRoom] = useState<any>(null);
  
  const handleSubmit = async (formData: any) => {
    try {
      setSubmitError(null);
      const response = await fetch('/api/rooms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create room');
      }
      
      // Xử lý kết quả thành công
      const data = await response.json();
      setCreatedRoom(data.data);
      setIsSuccess(true);
      
    } catch (error: any) {
      setSubmitError(error.message);
      throw error;
    }
  };
  
  const handleCreateAnother = () => {
    setIsSuccess(false);
    setCreatedRoom(null);
  };
  
  return (
    <DashboardLayout>
      <div className={styles.container}>
        {!isSuccess ? (
          <>
            <div className={styles.header}>
              <Link href="/rooms-areas/rooms" className={styles.backButton}>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M10.6667 2.66667L5.33333 8.00001L10.6667 13.3333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Quay lại
              </Link>
              <h1 className={styles.title}>Tạo phòng mới</h1>
            </div>
            
            {submitError && (
              <div className={styles.error}>
                <p>Lỗi: {submitError}</p>
              </div>
            )}
            
            <div className={styles.formContainer}>
              <RoomForm onSubmit={handleSubmit} />
            </div>
          </>
        ) : (
          <div className={styles.successContainer}>
            <div className={styles.successCard}>
              <div className={styles.successIcon}>
                <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="24" cy="24" r="24" fill="#10B981" fillOpacity="0.1"/>
                  <path d="M32 20L22 30L18 26" stroke="#10B981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <h2 className={styles.successTitle}>Tạo phòng thành công!</h2>
              <p className={styles.successDesc}>
                Phòng {createdRoom?.room_number} đã được tạo thành công.
              </p>
              <div className={styles.successActions}>
                <button 
                  className={styles.primaryButton}
                  onClick={() => router.push('/rooms-areas/rooms')}
                >
                  Quay về danh sách phòng
                </button>
                <button 
                  className={styles.secondaryButton}
                  onClick={handleCreateAnother}
                >
                  Tạo phòng mới
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}