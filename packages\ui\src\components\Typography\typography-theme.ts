// <PERSON>ác giá trị typography theo design system
export const typographyTokens = {
  // Font family
  fontFamily: {
    primary: 'Inter, sans-serif',
  },
  
  // Font sizes
  fontSize: {
    h1: '36px',
    h2: '28px',
    h3: '24px',
    h4: '20px',
    h5: '16px',
    bodyRegular: '16px',
    bodyMedium: '14px',
    bodySmall: '12px',
    buttonLabel: '14px',
    captionLabel: '12px',
    smallLabel: '10px',
  },
  
  // Font weights
  fontWeight: {
    regular: 400,
    medium: 500,
    semiBold: 600, 
    bold: 700,
  },
  
  // Line heights
  lineHeight: {
    tight: 1.2,
    normal: 1.5,
    loose: 1.8,
  },
  
  // Colors - Light theme
  lightColors: {
    heading: '#010103', // Black
    body: '#464646',    // Outer Space
    muted: '#7D8491',   // Slate Gray
    link: '#FF4D00',    // Aerospace Orange
  },
  
  // Colors - Dark theme
  darkColors: {
    heading: '#FFFFFF',  // White
    body: '#EBEBEB',     // Antiflash White
    muted: '#7D8491',    // Slate Gray
    link: '#F9F871',     // Icterine
  },
  
  // Colors - Studio theme
  studioColors: {
    heading: '#FFFFFF',  // White
    body: '#EBEBEB',     // Antiflash White
    muted: '#7D8491',    // Slate Gray
    link: '#FF4D00',     // Aerospace Orange
  },
};

// Default theme to use - light
export const defaultTheme = typographyTokens.lightColors;
