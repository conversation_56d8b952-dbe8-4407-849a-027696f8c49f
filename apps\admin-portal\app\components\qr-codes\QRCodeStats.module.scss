.statsContainer {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
  
  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

.statCard {
  display: flex;
  align-items: center;
  padding: 1.25rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
}

.statIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background-color: #f0f9ff;
  border-radius: 0.5rem;
  margin-right: 1rem;
  
  .icon {
    width: 1.5rem;
    height: 1.5rem;
    color: #3b82f6;
  }
}

.statContent {
  display: flex;
  flex-direction: column;
}

.statValue {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.statTitle {
  font-size: 0.875rem;
  color: #6b7280;
}

.statWithChange {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.positive {
  font-size: 0.75rem;
  font-weight: 500;
  color: #10b981;
  background-color: #ecfdf5;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
}

.negative {
  font-size: 0.75rem;
  font-weight: 500;
  color: #ef4444;
  background-color: #fef2f2;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
}