import React, { useState } from 'react';
import { 
  TopNavigation, 
  BottomNavigation,
  Card, 
  Button, 
  Heading3,
  Heading4,
  Heading5,
  BodyRegular, 
  BodySmall,
  Badge,
  Tooltip
} from '@loaloa/ui';
import { 
  HomeIcon, 
  SearchIcon, 
  SettingsIcon,
  ArrowIcon,
  StarIcon,
  LocationIcon,
  HeartIcon,
  WifiIcon,
  PoolIcon,
  RestaurantIcon,
  ParkingIcon
} from '@loaloa/ui';

// Mock hotel data
const mockHotel = {
  id: 1,
  name: 'Sunset Resort & Spa',
  location: 'Phuket, Thailand',
  description: 'Experience luxury beachfront living at Sunset Resort & Spa. Our 5-star resort offers stunning ocean views, world-class amenities, and unparalleled service to make your stay unforgettable.',
  rating: 4.8,
  reviews: 352,
  price: 199,
  currency: '$',
  images: [
    'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
    'https://images.unsplash.com/photo-1582719508461-905c673771fd?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
    'https://images.unsplash.com/photo-1590073242678-70ee3fc28f8a?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80'
  ],
  tags: ['Beach', 'Luxury', 'Spa'],
  amenities: ['Free WiFi', 'Swimming Pool', 'Restaurant', 'Parking', 'Spa', 'Fitness Center', '24/7 Service', 'Bar']
};

export interface HotelDetailScreenProps {
  onBack?: () => void;
  variant?: 'light' | 'dark' | 'studio';
}

export const HotelDetailScreen: React.FC<HotelDetailScreenProps> = ({ 
  onBack,
  variant = 'light'
}) => {
  const [activeNavItem, setActiveNavItem] = useState('explore');
  const [favorite, setFavorite] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0);

  // Generate amenity icon based on name
  const getAmenityIcon = (name: string) => {
    switch(name.toLowerCase()) {
      case 'free wifi':
        return <WifiIcon size="small" />;
      case 'swimming pool':
        return <PoolIcon size="small" />;
      case 'restaurant':
        return <RestaurantIcon size="small" />;
      case 'parking':
        return <ParkingIcon size="small" />;
      default:
        return null;
    }
  };

  // Styles
  const themeColors = {
    light: {
      background: '#F9FAFB',
      text: '#010103',
      secondaryText: '#464646',
      border: '#EBEBEB',
      card: '#FFFFFF'
    },
    dark: {
      background: '#161616',
      text: '#EBEBEB',
      secondaryText: '#ABABAB',
      border: '#2E2E2E',
      card: '#1E1E1E'
    },
    studio: {
      background: '#16262E',
      text: '#EBEBEB',
      secondaryText: '#ABABAB',
      border: '#2E4756',
      card: '#16262E'
    }
  };

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    minHeight: '100vh',
    backgroundColor: themeColors[variant].background,
    color: themeColors[variant].text
  };

  const contentStyle: React.CSSProperties = {
    padding: '16px',
    paddingBottom: '80px', // Space for bottom navigation
    flex: 1
  };

  const imageContainerStyle: React.CSSProperties = {
    position: 'relative',
    height: '300px',
    overflow: 'hidden',
    borderRadius: '8px',
    marginBottom: '16px'
  };

  const imageStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  };

  const imagePaginationStyle: React.CSSProperties = {
    position: 'absolute',
    bottom: '16px',
    left: '50%',
    transform: 'translateX(-50%)',
    display: 'flex',
    gap: '8px'
  };

  const paginationDotStyle = (active: boolean): React.CSSProperties => ({
    width: '8px',
    height: '8px',
    borderRadius: '50%',
    backgroundColor: active ? '#FFFFFF' : 'rgba(255, 255, 255, 0.5)',
    transition: 'all 0.3s ease'
  });

   const locationStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    marginBottom: '8px'
  };

  const ratingStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    marginBottom: '16px'
  };

  const tagsContainerStyle: React.CSSProperties = {
    display: 'flex',
    gap: '8px',
    marginBottom: '16px'
  };

  const sectionStyle: React.CSSProperties = {
    marginBottom: '24px'
  };

  const amenitiesContainerStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: '12px',
    marginBottom: '24px'
  };

  const amenityItemStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  };

  const bookingCardStyle: React.CSSProperties = {
    backgroundColor: themeColors[variant].card,
    padding: '16px',
    borderRadius: '8px',
    marginTop: '16px'
  };

  const priceContainerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'baseline',
    gap: '4px',
    marginBottom: '16px'
  };

  const topButtonsStyle: React.CSSProperties = {
    position: 'absolute',
    top: '16px',
    left: '16px',
    right: '16px',
    display: 'flex',
    justifyContent: 'space-between',
    zIndex: 10
  };
const headerStyle: React.CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  gap: '8px',
  marginBottom: '16px',
  padding: '4px 0'
};
  const iconButtonStyle: React.CSSProperties = {
    width: '40px',
    height: '40px',
    borderRadius: '50%',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#FFFFFF',
    cursor: 'pointer',
    border: 'none'
  };

  const Logo = (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <div style={{ 
        width: '32px', 
        height: '32px', 
        backgroundColor: '#FF4D00', 
        borderRadius: '4px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontWeight: 'bold',
        fontSize: '18px'
      }}>L</div>
      <span>LoaLoa</span>
    </div>
  );
  
  // Bottom navigation items
  const navItems = [
    { label: 'Explore', href: '#', icon: <HomeIcon />, active: activeNavItem === 'explore' },
    { label: 'Search', href: '#', icon: <SearchIcon />, active: activeNavItem === 'search' },
    { label: 'Settings', href: '#', icon: <SettingsIcon />, active: activeNavItem === 'settings' }
  ];

  return (
    <div style={containerStyle}>
      <TopNavigation
        logo={Logo}
        links={[]}
        variant={variant}
      />
      
      <div style={contentStyle}>
        <div style={imageContainerStyle}>
          <div style={topButtonsStyle}>
            <button 
              style={iconButtonStyle} 
              onClick={onBack}
              aria-label="Back"
            >
              <ArrowIcon size="small" />
            </button>
            <button 
              style={iconButtonStyle} 
              onClick={() => setFavorite(!favorite)}
              aria-label={favorite ? "Remove from favorites" : "Add to favorites"}
            >
              <HeartIcon size="small" color={favorite ? "#FF4D00" : "#FFFFFF"} />
            </button>
          </div>
          
          <img 
            src={mockHotel.images[activeImageIndex]} 
            alt={mockHotel.name}
            style={imageStyle}
          />
          
          <div style={imagePaginationStyle}>
            {mockHotel.images.map((_, index) => (
              <div 
                key={index}
                style={paginationDotStyle(index === activeImageIndex)}
                onClick={() => setActiveImageIndex(index)}
              />
            ))}
          </div>
        </div>

        <div style={headerStyle}>
          <div>
            <Heading3>{mockHotel.name}</Heading3>
            <div style={locationStyle}>
              <LocationIcon size="small" />
              <BodySmall style={{ color: themeColors[variant].secondaryText }}>
                {mockHotel.location}
              </BodySmall>
            </div>
          </div>
          
          <div style={ratingStyle}>
            <StarIcon size="small" color="#FFD700" />
            <BodyRegular style={{ fontWeight: 600 }}>{mockHotel.rating}</BodyRegular>
            <BodySmall style={{ color: themeColors[variant].secondaryText }}>
              ({mockHotel.reviews} reviews)
            </BodySmall>
          </div>
        </div>

        <div style={tagsContainerStyle}>
          {mockHotel.tags.map((tag, index) => (
            <Badge key={index} variant="secondary" label={tag} />
          ))}
        </div>

        <div style={sectionStyle}>
          <Heading5 style={{ marginBottom: '8px' }}>Description</Heading5>
          <BodyRegular style={{ color: themeColors[variant].secondaryText }}>
            {mockHotel.description}
          </BodyRegular>
        </div>

        <div style={sectionStyle}>
          <Heading5 style={{ marginBottom: '12px' }}>Amenities</Heading5>
          <div style={amenitiesContainerStyle}>
            {mockHotel.amenities.slice(0, 6).map((amenity, index) => (
              <div key={index} style={amenityItemStyle}>
                <Tooltip content={amenity}>
                  <div>
                    {getAmenityIcon(amenity) || <div style={{ width: '16px', height: '16px' }} />}
                  </div>
                </Tooltip>
                <BodySmall>{amenity}</BodySmall>
              </div>
            ))}
          </div>
        </div>

        <Card style={bookingCardStyle}>
          <div style={priceContainerStyle}>
            <Heading4>{mockHotel.currency}{mockHotel.price}</Heading4>
            <BodySmall style={{ color: themeColors[variant].secondaryText }}>per night</BodySmall>
          </div>
          
          <Button 
            variant="primary"
            label="Book Now"
            onClick={() => alert('Booking functionality would go here')}
            fullWidth
          />
          
          <Button 
            variant="outline"
            label="Contact Hotel"
            onClick={() => {}}
            fullWidth
            style={{ marginTop: '12px' }}
          />
        </Card>
      </div>

      <BottomNavigation
        items={navItems}
        variant={variant}
      />
    </div>
  );
};

export default HotelDetailScreen;
