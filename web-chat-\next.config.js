/** @type {import('next').NextConfig} */
const path = require('path');

const nextConfig = {
  reactStrictMode: true,

  // Configure transpilation for workspace packages
  transpilePackages: ['@loaloa/ui', '@loaloa/design-tokens', '@loaloa/license-client'],

  // Configure webpack for path aliases
  webpack: (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname, './app'),
      '@/license-client': path.resolve(__dirname, '../../packages/license-client/src/web-only')
    };
    return config;
  },

  // SCSS support
  sassOptions: {
    includePaths: [path.join(__dirname, 'styles')],
  },
};

module.exports = nextConfig;