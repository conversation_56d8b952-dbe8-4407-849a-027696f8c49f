[{"view_name": "tenant_active_staff_view", "view_definition": " SELECT tu.id AS user_id,\n    tu.tenant_id,\n    tud.display_name,\n    tud.email,\n    tu.role,\n    tsa.department,\n    tsa.assignment_type,\n    tsa.resource_id,\n    tsa.working_hours,\n        CASE\n            WHEN (tsa.working_hours IS NULL) THEN true\n            WHEN ((EXTRACT(hour FROM CURRENT_TIME) >= (COALESCE(((tsa.working_hours ->> 'start_hour'::text))::integer, 0))::numeric) AND (EXTRACT(hour FROM CURRENT_TIME) < (COALESCE(((tsa.working_hours ->> 'end_hour'::text))::integer, 24))::numeric)) THEN true\n            ELSE false\n        END AS is_currently_working\n   FROM ((tenant_users tu\n     JOIN tenant_users_details tud ON ((tu.id = tud.tenant_user_id)))\n     LEFT JOIN tenant_staff_assignments tsa ON ((tu.id = tsa.user_id)))\n  WHERE ((tu.is_active = true) AND ((tu.role = 'user'::tenant_user_role) OR (tu.role = 'manager'::tenant_user_role)));", "view_comment": "<PERSON><PERSON>n thị nhân viên đang hoạt động và thông tin phân công"}, {"view_name": "tenant_chat_routing_summary_view", "view_definition": " SELECT tc.tenant_id,\n    tc.id AS chat_session_id,\n    tc.created_at,\n    tc.status,\n    tc.source_type,\n    tc.source_id,\n    tca.assigned_user_id,\n    tca.assignment_status,\n    tca.assigned_at,\n    tca.accepted_at,\n        CASE\n            WHEN (tca.accepted_at IS NOT NULL) THEN EXTRACT(epoch FROM (tca.accepted_at - tca.assigned_at))\n            ELSE NULL::numeric\n        END AS response_time_seconds\n   FROM (tenant_chat_sessions tc\n     LEFT JOIN tenant_chat_session_assignments tca ON ((tc.id = tca.chat_session_id)));", "view_comment": "<PERSON><PERSON><PERSON> hợp thông tin định tuyến phiên chat và thời gian phản hồi"}]