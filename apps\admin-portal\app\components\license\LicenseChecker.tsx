'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import styles from './LicenseChecker.module.css';

interface LicenseInfo {
  licenseKey: string;
  customerName: string;
  tenant_id?: string;
  isValid: boolean;
  error?: string;
}

export default function LicenseChecker() {
  const [licenseInfo, setLicenseInfo] = useState<LicenseInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const checkLicense = async () => {
      try {
        const response = await fetch('/api/license/check');
        const data = await response.json();

        if (response.ok) {
          setLicenseInfo({
            licenseKey: data.licenseKey,
            customerName: data.customerName,
            tenant_id: data.tenant_id,
            isValid: true
          });
        } else {
          setLicenseInfo({
            licenseKey: data.licenseKey || '',
            customerName: data.customerName || '',
            isValid: false,
            error: data.error
          });

          // Chuyển hướng đến trang kích hoạt sau 3 giây
          setTimeout(() => {
            router.push('/activate');
          }, 3000);
        }
      } catch (error) {
        console.error('Error checking license:', error);
        setLicenseInfo({
          licenseKey: '',
          customerName: '',
          isValid: false,
          error: 'Không thể kiểm tra license'
        });

        // Chuyển hướng đến trang kích hoạt sau 3 giây
        setTimeout(() => {
          router.push('/activate');
        }, 3000);
      } finally {
        setLoading(false);
      }
    };

    checkLicense();
  }, [router]);

  if (loading) {
    return (
      <div className={styles.checker}>
        <div className={styles.spinner}></div>
        <p>Đang kiểm tra license...</p>
      </div>
    );
  }

  if (!licenseInfo?.isValid) {
    return (
      <div className={styles.checker}>
        <div className={styles.error}>
          <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          <h3>License không hợp lệ</h3>
          <p>{licenseInfo?.error || 'License không hợp lệ hoặc đã hết hạn'}</p>
          <p>Đang chuyển hướng đến trang kích hoạt...</p>
        </div>
      </div>
    );
  }

  return null; // Nếu license hợp lệ, không hiển thị gì
}