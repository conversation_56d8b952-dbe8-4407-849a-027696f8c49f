import { createClient } from '../../utils/supabase/server';

// Đ<PERSON>nh nghĩa các kiểu dữ liệu
export interface Tenant {
  id: string;
  name: string;
  domain: string;
  contact_email: string;
  contact_phone?: string;
  address?: string;
  logo_url?: string;
  primary_color?: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  metadata?: Record<string, any>;
}

export interface TenantFilterOptions {
  status?: 'active' | 'inactive' | 'all';
  search?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
}
export async function getAllTenants(): Promise<Tenant[]> {
  try {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from('tenants')
      .select('id, name, domain, is_active')
      .eq('is_active', true)
      .order('name');
    
    if (error) {
      console.error('Error fetching tenants:', error);
      return [];
    }
    
    return data;
  } catch (error) {
    console.error('Error in getAllTenants:', error);
    return [];
  }
}

export class TenantService {
  /**
   * L<PERSON>y danh sách tenants với phân trang và lọc
   */
  static async getTenants(
    pagination: PaginationOptions = { page: 1, limit: 10 },
    filter: TenantFilterOptions = {}
  ): Promise<{ data: Tenant[]; count: number }> {
    try {
      const supabase = await createClient();
      const { page, limit } = pagination;
      const offset = (page - 1) * limit;

      // Bắt đầu query
      let query = supabase
        .from('tenants')
        .select('*', { count: 'exact' });

      // Áp dụng các bộ lọc
      if (filter.status && filter.status !== 'all') {
        query = query.eq('is_active', filter.status === 'active');
      }

      if (filter.search) {
        query = query.or(`name.ilike.%${filter.search}%,domain.ilike.%${filter.search}%,contact_email.ilike.%${filter.search}%`);
      }

      // Thêm phân trang
      query = query.range(offset, offset + limit - 1).order('created_at', { ascending: false });

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching tenants:', error);
        throw new Error(`Failed to fetch tenants: ${error.message}`);
      }

      return {
        data: data as Tenant[],
        count: count || 0
      };
    } catch (error) {
      console.error('Error in getTenants:', error);
      throw error;
    }
  }

  /**
   * Lấy tất cả tenants để sử dụng trong dropdowns
   */
  static async getAllTenantsForDropdown(): Promise<Tenant[]> {
    try {
      const supabase = await createClient();
      
      const { data, error } = await supabase
        .from('tenants')
        .select('id, name')
        .eq('is_active', true)
        .order('name');

      if (error) {
        console.error('Error fetching tenants for dropdown:', error);
        throw new Error(`Failed to fetch tenants: ${error.message}`);
      }

      return data as Tenant[];
    } catch (error) {
      console.error('Error in getAllTenantsForDropdown:', error);
      throw error;
    }
  }
}
