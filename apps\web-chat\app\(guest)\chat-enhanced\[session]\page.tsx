'use client';

/**
 * Enhanced Guest Chat Page with Performance Monitoring
 * Uses the enhanced useChat hook with monitoring and optimization
 */

import React, { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useChatEnhanced } from '@/hooks/useChat.enhanced';
import debugUtils from '@/lib/debug-utils';
import styles from '../../chat/[session]/chat.module.scss';

interface PerformanceMetrics {
  avgLatency: number;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'critical';
  realtimeConnected: boolean;
  usePollingFallback: boolean;
}

export default function EnhancedGuestChatPage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.session as string;
  
  // State
  const [guestId, setGuestId] = useState<string>('');
  const [guestLanguage, setGuestLanguage] = useState<string>('en');
  const [newMessage, setNewMessage] = useState('');
  const [showPerformancePanel, setShowPerformancePanel] = useState(false);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const performanceIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Enhanced chat hook
  const {
    session,
    messages,
    loading,
    connected,
    sendMessage,
    isTyping,
    startTyping,
    stopTyping,
    error,
    realtimeConnected,
    usePollingFallback,
    getPerformanceMetrics,
    logPerformance
  } = useChatEnhanced({
    sessionId,
    guestId,
    guestLanguage,
    autoTranslate: true
  });

  // Utility functions
  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Handle message sending
  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    const success = await sendMessage(newMessage);
    if (success) {
      setNewMessage('');
      setTimeout(scrollToBottom, 100);
    } else {
      console.error('Failed to send message');
    }
  };

  // Handle typing indicators
  const handleInputChange = (value: string) => {
    setNewMessage(value);
    
    if (value.trim()) {
      startTyping();
    } else {
      stopTyping();
    }
  };

  // Initialize guest session
  useEffect(() => {
    // Run debug check
    debugUtils.runQuickDebug();

    // Get guest info from localStorage or generate new
    const storedGuestId = localStorage.getItem(`guest_id_${sessionId}`);
    const storedLanguage = localStorage.getItem('guest_language') ||
                          navigator.language.split('-')[0] || 'en';

    if (storedGuestId) {
      setGuestId(storedGuestId);
    } else {
      const newGuestId = `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem(`guest_id_${sessionId}`, newGuestId);
      setGuestId(newGuestId);
    }

    setGuestLanguage(storedLanguage);
    localStorage.setItem('guest_language', storedLanguage);

    console.log('🚀 Enhanced Guest Chat: Initialized for session:', sessionId);
  }, [sessionId]);

  // Auto-scroll when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Performance monitoring
  useEffect(() => {
    performanceIntervalRef.current = setInterval(() => {
      const metrics = getPerformanceMetrics();
      setPerformanceMetrics({
        avgLatency: metrics.avgLatency,
        connectionQuality: metrics.connectionQuality,
        realtimeConnected,
        usePollingFallback
      });
    }, 10000); // Update every 10 seconds

    return () => {
      if (performanceIntervalRef.current) {
        clearInterval(performanceIntervalRef.current);
      }
    };
  }, [getPerformanceMetrics, realtimeConnected, usePollingFallback]);

  // Log performance on unmount
  useEffect(() => {
    return () => {
      logPerformance();
    };
  }, [logPerformance]);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Connecting to chat...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <h2>Connection Error</h2>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>
          Retry Connection
        </button>
      </div>
    );
  }

  return (
    <div className={styles.chatContainer}>
      {/* Enhanced Header with Connection Status */}
      <div className={styles.chatHeader}>
        <div className={styles.headerLeft}>
          <h1>Chat Support</h1>
          {session && (
            <span className={styles.sessionInfo}>
              Session: {session.id.substring(0, 8)}...
            </span>
          )}
        </div>
        
        <div className={styles.headerRight}>
          {/* Connection Status Indicator */}
          <div className={styles.connectionStatus}>
            <span className={`${styles.statusDot} ${connected ? styles.connected : styles.disconnected}`}></span>
            <span className={styles.statusText}>
              {realtimeConnected ? 'Real-time' : usePollingFallback ? 'Polling' : 'Disconnected'}
            </span>
          </div>

          {/* Performance Toggle */}
          <button 
            className={styles.performanceToggle}
            onClick={() => setShowPerformancePanel(!showPerformancePanel)}
            title="Toggle Performance Panel"
          >
            📊
          </button>
        </div>
      </div>

      {/* Performance Panel */}
      {showPerformancePanel && performanceMetrics && (
        <div className={styles.performancePanel}>
          <h3>Performance Metrics</h3>
          <div className={styles.metricsGrid}>
            <div className={styles.metric}>
              <span className={styles.metricLabel}>Latency:</span>
              <span className={styles.metricValue}>{performanceMetrics.avgLatency}ms</span>
            </div>
            <div className={styles.metric}>
              <span className={styles.metricLabel}>Quality:</span>
              <span className={`${styles.metricValue} ${styles[performanceMetrics.connectionQuality]}`}>
                {performanceMetrics.connectionQuality.toUpperCase()}
              </span>
            </div>
            <div className={styles.metric}>
              <span className={styles.metricLabel}>Mode:</span>
              <span className={styles.metricValue}>
                {performanceMetrics.realtimeConnected ? 'Realtime' : 'Polling'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Messages Area */}
      <div className={styles.messagesContainer}>
        {messages.length === 0 ? (
          <div className={styles.welcomeMessage}>
            <h2>Welcome to Chat Support</h2>
            <p>Send a message to start the conversation. Our staff will respond shortly.</p>
          </div>
        ) : (
          messages.map((message) => (
            <div 
              key={message.id} 
              className={`${styles.message} ${styles[message.sender_type]}`}
            >
              <div className={styles.messageContent}>
                <div className={styles.messageHeader}>
                  <span className={styles.senderName}>{message.sender_name}</span>
                  <span className={styles.timestamp}>
                    {formatTimeAgo(message.timestamp)}
                  </span>
                </div>
                <div className={styles.messageText}>
                  {message.content}
                </div>
                {message.is_translated && (
                  <div className={styles.translationInfo}>
                    <span className={styles.translationBadge}>Translated</span>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
        
        {/* Typing Indicator */}
        {isTyping && (
          <div className={styles.typingIndicator}>
            <div className={styles.typingDots}>
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span className={styles.typingText}>Staff is typing...</span>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className={styles.messageInputContainer}>
        <div className={styles.inputWrapper}>
          <input
            type="text"
            value={newMessage}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleSendMessage();
              }
            }}
            placeholder="Type your message..."
            className={styles.messageInput}
            disabled={!connected}
          />
          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || !connected}
            className={styles.sendButton}
          >
            Send
          </button>
        </div>
        
        {/* Connection Status Footer */}
        <div className={styles.inputFooter}>
          <span className={styles.connectionInfo}>
            {connected ? (
              <>
                ✅ Connected via {realtimeConnected ? 'Real-time' : 'Polling'}
                {performanceMetrics && (
                  <span className={styles.latencyInfo}>
                    • {performanceMetrics.avgLatency}ms latency
                  </span>
                )}
              </>
            ) : (
              '❌ Disconnected - Trying to reconnect...'
            )}
          </span>
        </div>
      </div>
    </div>
  );
}
