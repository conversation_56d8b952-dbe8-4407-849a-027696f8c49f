'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import styles from './ReceptionPointInfo.module.scss';

interface ReceptionPointInfoProps {
  receptionPointId: string | null;
}

export default function ReceptionPointInfo({ receptionPointId }: ReceptionPointInfoProps) {
  const [receptionPoint, setReceptionPoint] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReceptionPoint = async () => {
      if (!receptionPointId) return;

      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/reception-points/${receptionPointId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            return; // Not found is not an error, just no reception point assigned
          }
          throw new Error('Failed to fetch reception point');
        }
        
        const { data } = await response.json();
        setReceptionPoint(data);
      } catch (err) {
        console.error('Error fetching reception point:', err);
        setError('Failed to load reception point information');
      } finally {
        setLoading(false);
      }
    };

    fetchReceptionPoint();
  }, [receptionPointId]);

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <span>Loading reception point...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.error}>
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M10 18.3333C14.6024 18.3333 18.3333 14.6024 18.3333 10C18.3333 5.39763 14.6024 1.66667 10 1.66667C5.39763 1.66667 1.66667 5.39763 1.66667 10C1.66667 14.6024 5.39763 18.3333 10 18.3333Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M10 6.66667V10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M10 13.3333H10.0083" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        <span>{error}</span>
      </div>
    );
  }

  if (!receptionPointId || !receptionPoint) {
    return (
      <div className={styles.notAssigned}>
        <p>No reception point assigned</p>
        <div className={styles.actionLinks}>
          <Link href="/reception-points">View Reception Points</Link>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.receptionPointInfo}>
      <div className={styles.header}>
        <h4 className={styles.name}>{receptionPoint.name}</h4>
        <span className={`${styles.statusBadge} ${receptionPoint.is_active ? styles.active : styles.inactive}`}>
          {receptionPoint.is_active ? 'Active' : 'Inactive'}
        </span>
      </div>
      
      <div className={styles.details}>
        <div className={styles.detailItem}>
          <span className={styles.label}>Code:</span>
          <span className={styles.value}>{receptionPoint.code}</span>
        </div>
        
        {receptionPoint.description && (
          <div className={styles.description}>
            {receptionPoint.description}
          </div>
        )}
      </div>
      
      <div className={styles.actions}>
        <Link href={`/reception-points/${receptionPointId}`} className={styles.viewLink}>
          View Details
        </Link>
        <Link href={`/reception-points/${receptionPointId}/edit`} className={styles.editLink}>
          Change
        </Link>
      </div>
    </div>
  );
}