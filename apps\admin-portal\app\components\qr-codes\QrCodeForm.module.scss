// Form container
.formContainer {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  
  @media (min-width: 768px) {
    padding: 2rem;
  }
}

// Tab navigation
.tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
  overflow-x: auto;
  
  &::-webkit-scrollbar {
    height: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 2px;
  }
}

.tab {
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  white-space: nowrap;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: transparent;
    transition: background-color 0.2s;
  }
  
  &:hover {
    color: #0369a1;
  }
  
  &.active {
    color: #0284c7;
    
    &::after {
      background-color: #0284c7;
    }
  }
}

// Tab content
.tabContent {
  display: none;
  animation: fadeIn 0.3s ease;
  
  &.active {
    display: block;
  }
}

// Form sections
.formSection {
  margin-bottom: 2rem;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #334155;
}

.helpText {
  margin-bottom: 1.5rem;
  color: #64748b;
  font-size: 0.95rem;
}

// Form grid
.formGrid {
  display: grid;
  gap: 1.5rem;
  
  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

// Form groups
.formGroup {
  margin-bottom: 1.5rem;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #334155;
  }
  
  input[type="text"],
  input[type="number"],
  textarea,
  select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    background-color: #fff;
    font-size: 1rem;
    color: #334155;
    transition: border-color 0.2s;
    
    &:focus {
      outline: none;
      border-color: #0ea5e9;
      box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
    }
    
    &::placeholder {
      color: #94a3b8;
    }
    
    &.inputError {
      border-color: #ef4444;
    }
  }
  
  textarea {
    min-height: 100px;
    resize: vertical;
  }
}

// Field description
.fieldDescription {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
}

// Required indicator
.required {
  color: #ef4444;
}

// Error text
.errorText {
  margin-top: 0.5rem;
  color: #ef4444;
  font-size: 0.875rem;
}

// Checkbox container
.checkboxContainer {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  
  input[type="checkbox"] {
    margin-right: 0.5rem;
    width: 18px;
    height: 18px;
    cursor: pointer;
  }
  
  .checkboxLabel {
    margin-bottom: 0;
    cursor: pointer;
  }
}

.radioGroup {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.5rem;
}

.radioOption {
  display: flex;
  align-items: center;
  
  input[type="radio"] {
    margin-right: 0.5rem;
    cursor: pointer;
  }
  
  label {
    margin-bottom: 0;
    cursor: pointer;
    font-weight: normal;
  }
}

// Form actions
.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

// QR Preview
.qrPreviewContainer {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  padding: 2rem;
  background-color: #f8fafc;
  border-radius: 8px;
}

.qrImage {
  img {
    width: 200px;
    height: 200px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
  }
}

.qrActions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  
  .downloadButton {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background-color: #0284c7;
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: #0369a1;
    }
    
    svg {
      width: 16px;
      height: 16px;
    }
  }
  
  .qrCodeValue, .scanCount {
    font-size: 0.95rem;
    color: #64748b;
    
    span {
      font-weight: 500;
      color: #334155;
    }
  }
}

.noPreview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 3rem;
  background-color: #f1f5f9;
  color: #64748b;
  font-style: italic;
  border-radius: 8px;
}

// Info box
.infoBox {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background-color: #f0f9ff;
  border-left: 4px solid #0ea5e9;
  border-radius: 6px;
  margin-top: 1.5rem;
  
  .infoIcon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #0ea5e9;
    color: white;
    font-weight: bold;
    font-style: italic;
    font-family: serif;
  }
  
  ol {
    margin: 0.5rem 0 0;
    padding-left: 1.5rem;
    
    li {
      margin-bottom: 0.25rem;
    }
  }
}

// Loading state
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #64748b;
  
  &::before {
    content: "";
    width: 30px;
    height: 30px;
    margin-bottom: 1rem;
    border: 3px solid #e5e7eb;
    border-top-color: #0284c7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}