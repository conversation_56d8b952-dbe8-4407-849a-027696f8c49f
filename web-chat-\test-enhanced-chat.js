/**
 * Quick Test Script for Enhanced Chat System
 * Run this in browser console to test enhanced implementations
 */

// Test Enhanced Chat Performance
function testEnhancedChatPerformance() {
  console.log('🧪 Testing Enhanced Chat Performance...');
  
  // Check if enhanced monitoring is available
  if (typeof window.realtimeMonitor !== 'undefined') {
    console.log('✅ Realtime Monitor available');
    window.realtimeMonitor.logPerformanceSummary();
  } else {
    console.log('⚠️ Realtime Monitor not found - check if enhanced implementation is loaded');
  }
  
  // Check if performance tester is available
  if (typeof window.performanceTester !== 'undefined') {
    console.log('✅ Performance Tester available');
  } else {
    console.log('⚠️ Performance Tester not found');
  }
  
  // Test connection quality
  const connectionElements = document.querySelectorAll('[class*="connectionStatus"], [class*="statusIndicator"]');
  if (connectionElements.length > 0) {
    console.log('✅ Connection status indicators found:', connectionElements.length);
  } else {
    console.log('⚠️ No connection status indicators found');
  }
  
  // Test performance panels
  const performancePanels = document.querySelectorAll('[class*="performancePanel"], [class*="performanceToggle"]');
  if (performancePanels.length > 0) {
    console.log('✅ Performance panels found:', performancePanels.length);
  } else {
    console.log('⚠️ No performance panels found');
  }
}

// Test Message Latency
function testMessageLatency() {
  console.log('📊 Testing Message Latency...');
  
  const startTime = Date.now();
  
  // Simulate message send
  const testMessage = `Test message - ${startTime}`;
  console.log('📤 Sending test message:', testMessage);
  
  // Check for message in DOM after delay
  setTimeout(() => {
    const latency = Date.now() - startTime;
    console.log(`📥 Message latency test completed: ${latency}ms`);
    
    if (latency < 2000) {
      console.log('✅ Excellent latency (< 2 seconds)');
    } else if (latency < 5000) {
      console.log('🟡 Good latency (< 5 seconds)');
    } else {
      console.log('🔴 Poor latency (> 5 seconds)');
    }
  }, 1000);
}

// Test Enhanced Features
function testEnhancedFeatures() {
  console.log('🔍 Testing Enhanced Features...');
  
  // Check for enhanced CSS classes
  const enhancedElements = [
    'connectionStatus',
    'performancePanel',
    'statusIndicator',
    'qualityIndicator',
    'performanceToggle',
    'metricsGrid',
    'loadingSpinner',
    'typingIndicator'
  ];
  
  enhancedElements.forEach(className => {
    const elements = document.querySelectorAll(`[class*="${className}"]`);
    if (elements.length > 0) {
      console.log(`✅ ${className}: ${elements.length} elements found`);
    } else {
      console.log(`⚠️ ${className}: not found`);
    }
  });
}

// Test Realtime Connection
function testRealtimeConnection() {
  console.log('📡 Testing Realtime Connection...');
  
  // Check for Supabase in global scope
  if (typeof window.supabase !== 'undefined') {
    console.log('✅ Supabase client available');
  } else {
    console.log('⚠️ Supabase client not found in global scope');
  }
  
  // Check for WebSocket connections
  const wsConnections = performance.getEntriesByType('navigation').filter(entry => 
    entry.name.includes('ws://') || entry.name.includes('wss://')
  );
  
  if (wsConnections.length > 0) {
    console.log('✅ WebSocket connections detected:', wsConnections.length);
  } else {
    console.log('⚠️ No WebSocket connections detected');
  }
  
  // Check console for realtime logs
  console.log('🔍 Check console for realtime connection logs:');
  console.log('  - Look for "✅ Realtime messages subscription active"');
  console.log('  - Look for "🔔 New message received via realtime"');
  console.log('  - Look for "📊 RealtimeMonitor: Performance Summary"');
}

// Compare Original vs Enhanced
function compareImplementations() {
  console.log('⚖️ Comparing Original vs Enhanced Implementations...');
  
  const currentUrl = window.location.pathname;
  
  if (currentUrl.includes('/enhanced') || currentUrl.includes('/chat-enhanced')) {
    console.log('✅ Currently using Enhanced implementation');
    console.log('📊 Enhanced features available:');
    console.log('  - Real-time performance monitoring');
    console.log('  - Adaptive polling fallback');
    console.log('  - Connection quality indicators');
    console.log('  - Enhanced error handling');
    console.log('  - Optimistic UI updates');
  } else {
    console.log('📝 Currently using Original implementation');
    console.log('🔄 To test Enhanced version:');
    
    if (currentUrl.includes('/staff/dashboard')) {
      console.log('  - Navigate to: /staff/dashboard/enhanced');
    } else if (currentUrl.includes('/chat/')) {
      const sessionId = currentUrl.split('/chat/')[1];
      console.log(`  - Navigate to: /chat-enhanced/${sessionId}`);
    }
  }
}

// Run All Tests
function runAllTests() {
  console.group('🚀 Enhanced Chat System Tests');
  
  testEnhancedChatPerformance();
  console.log('---');
  
  testEnhancedFeatures();
  console.log('---');
  
  testRealtimeConnection();
  console.log('---');
  
  compareImplementations();
  console.log('---');
  
  testMessageLatency();
  
  console.groupEnd();
  
  console.log('');
  console.log('📋 Test Summary:');
  console.log('✅ = Working correctly');
  console.log('🟡 = Working but could be improved');
  console.log('⚠️ = Not found or not working');
  console.log('🔴 = Issue detected');
  console.log('');
  console.log('💡 Tips:');
  console.log('- Open browser DevTools to see detailed logs');
  console.log('- Check Network tab for WebSocket connections');
  console.log('- Monitor Console for realtime events');
  console.log('- Use Performance tab to measure actual latency');
}

// Auto-run tests if script is executed directly
if (typeof window !== 'undefined') {
  // Make functions available globally for manual testing
  window.testEnhancedChat = {
    runAllTests,
    testEnhancedChatPerformance,
    testEnhancedFeatures,
    testRealtimeConnection,
    testMessageLatency,
    compareImplementations
  };
  
  console.log('🧪 Enhanced Chat Test Suite loaded!');
  console.log('Run testEnhancedChat.runAllTests() to start testing');
  console.log('Or run individual tests:');
  console.log('- testEnhancedChat.testEnhancedChatPerformance()');
  console.log('- testEnhancedChat.testEnhancedFeatures()');
  console.log('- testEnhancedChat.testRealtimeConnection()');
  console.log('- testEnhancedChat.testMessageLatency()');
  console.log('- testEnhancedChat.compareImplementations()');
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    testEnhancedChatPerformance,
    testEnhancedFeatures,
    testRealtimeConnection,
    testMessageLatency,
    compareImplementations
  };
}
