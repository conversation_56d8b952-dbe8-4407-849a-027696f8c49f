import * as z from 'zod';

export const createUserSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  full_name: z.string().min(2, { message: 'Full name must be at least 2 characters' }),
  role: z.enum(['user', 'admin', 'super_admin']),
  phone: z.string().optional(),
  is_active: z.boolean(),
  tenants: z.array(
    z.object({
      id: z.string(),
      name: z.string().optional(),
      role: z.string()
    })
  )
});

export const updateUserSchema = createUserSchema.omit({ email: true });

export type CreateUserFormData = z.infer<typeof createUserSchema>;
export type UpdateUserFormData = z.infer<typeof updateUserSchema>;
