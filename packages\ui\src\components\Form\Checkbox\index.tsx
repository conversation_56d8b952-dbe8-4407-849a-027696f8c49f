import React, { InputHTMLAttributes } from 'react';

export interface CheckboxProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type'> {
  /**
   * Label for the checkbox
   */
  label: string;
  /**
   * Whether the checkbox is in error state
   */
  error?: boolean;
  /**
   * Helper text displayed below the checkbox
   */
  helperText?: string;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  id,
  label,
  error = false,
  helperText,
  style,
  className,
  ...props
}) => {
  // Generate a random ID if not provided
  const checkboxId = id || `checkbox-${Math.random().toString(36).substring(2, 9)}`;
  
  // Container style
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    marginBottom: '16px',
    ...style,
  };
  
  // Checkbox group style
  const checkboxGroupStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'flex-start',
    cursor: props.disabled ? 'not-allowed' : 'pointer',
  };
  
  // Custom checkbox style
  const customCheckboxStyle: React.CSSProperties = {
    position: 'relative',
    width: '18px',
    height: '18px',
    borderRadius: '4px',
    border: `1px solid ${error ? '#B91C1C' : props.disabled ? '#D1D5DB' : '#6B7280'}`,
    backgroundColor: props.disabled ? '#F9FAFB' : '#FFFFFF',
    marginRight: '8px',
    marginTop: '2px',
  };
  
  // Checked style (to be applied conditionally)
  const checkedStyle: React.CSSProperties = {
    backgroundColor: props.disabled ? '#D1D5DB' : error ? '#B91C1C' : '#FF4D00',
    border: `1px solid ${props.disabled ? '#D1D5DB' : error ? '#B91C1C' : '#FF4D00'}`,
  };
  
  // Label style
  const labelStyle: React.CSSProperties = {
    fontSize: '14px',
    color: props.disabled ? '#9CA3AF' : error ? '#B91C1C' : '#464646',
    cursor: props.disabled ? 'not-allowed' : 'pointer',
  };
  
  // Helper text style
  const helperTextStyle: React.CSSProperties = {
    fontSize: '12px',
    marginTop: '4px',
    marginLeft: '26px',
    color: error ? '#B91C1C' : '#6B7280',
  };
  
  // Check mark style (for checked state)
  const checkMarkStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    color: '#FFFFFF',
  };
  
  // Hide the native checkbox but keep it accessible
  const hiddenInputStyle: React.CSSProperties = {
    position: 'absolute',
    opacity: 0,
    width: 0,
    height: 0,
  };

  return (
    <div style={containerStyle} className={className}>
      <div style={checkboxGroupStyle}>
        <div style={{
          ...customCheckboxStyle,
          ...(props.checked ? checkedStyle : {})
        }}>
          {props.checked && (
            <span style={checkMarkStyle}>
              <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.6667 3.5L5.25 9.91667L2.33334 7" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </span>
          )}
        </div>
        
        <input
          id={checkboxId}
          type="checkbox"
          style={hiddenInputStyle}
          {...props}
        />
        
        <label htmlFor={checkboxId} style={labelStyle}>
          {label}
        </label>
      </div>
      
      {helperText && <p style={helperTextStyle}>{helperText}</p>}
    </div>
  );
};

export default Checkbox;
