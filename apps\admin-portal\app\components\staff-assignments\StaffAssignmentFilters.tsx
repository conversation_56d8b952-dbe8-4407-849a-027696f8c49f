'use client';
import { useState } from 'react';
import styles from './StaffAssignmentFilters.module.scss';

interface StaffAssignmentFiltersProps {
  onFilterChange: (filterType: string, value: string) => void;
}

export default function StaffAssignmentFilters({
  onFilterChange,
}: StaffAssignmentFiltersProps) {
  const [department, setDepartment] = useState('');
  const [status, setStatus] = useState('');

  const departments = [
    { value: '', label: 'All Departments' },
    { value: 'reception', label: 'Reception' },
    { value: 'housekeeping', label: 'Housekeeping' },
    { value: 'restaurant', label: 'Restaurant' },
    { value: 'spa', label: 'Spa' },
    { value: 'concierge', label: 'Concierge' },
    { value: 'maintenance', label: 'Maintenance' },
  ];

  const statuses = [
    { value: '', label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
  ];

  const handleDepartmentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setDepartment(value);
    onFilterChange('department', value);
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setStatus(value);
    onFilterChange('status', value);
  };

  return (
    <div className={styles.container}>
      <div className={styles.filterGroup}>
        <label htmlFor="department" className={styles.filterLabel}>Department</label>
        <select
          id="department"
          value={department}
          onChange={handleDepartmentChange}
          className={styles.filterSelect}
        >
          {departments.map((dept) => (
            <option key={dept.value} value={dept.value}>
              {dept.label}
            </option>
          ))}
        </select>
      </div>

      <div className={styles.filterGroup}>
        <label htmlFor="status" className={styles.filterLabel}>Status</label>
        <select
          id="status"
          value={status}
          onChange={handleStatusChange}
          className={styles.filterSelect}
        >
          {statuses.map((statusOption) => (
            <option key={statusOption.value} value={statusOption.value}>
              {statusOption.label}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}