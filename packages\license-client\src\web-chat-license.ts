import * as fs from 'fs';
import * as path from 'path';
import { createClient } from '@supabase/supabase-js';
import type { LicenseConfig, LicenseValidationResult, TenantContext } from './web-chat-types';

/**
 * Web Chat License Service
 * Handles license validation for web applications (Admin Portal, Web Chat)
 */
export class WebChatLicenseService {
  private static instance: WebChatLicenseService;
  private cachedConfig: LicenseConfig | null = null;

  private constructor() {}

  public static getInstance(): WebChatLicenseService {
    if (!WebChatLicenseService.instance) {
      WebChatLicenseService.instance = new WebChatLicenseService();
    }
    return WebChatLicenseService.instance;
  }

  /**
   * Get license configuration from file system
   * Searches multiple paths for license_config.json
   */
  public getLicenseConfig(): LicenseConfig | null {
    if (this.cachedConfig) {
      return this.cachedConfig;
    }

    // Try multiple paths for license config
    const possiblePaths = [
      path.resolve(process.cwd(), 'license_config.json'),           // App root
      path.resolve(process.cwd(), '../../license_config.json'),    // Monorepo root
      path.resolve(__dirname, '../../../license_config.json'),     // Relative to package
      path.resolve(process.cwd(), '../license_config.json'),       // Parent directory
    ];

    for (const configPath of possiblePaths) {
      try {
        if (fs.existsSync(configPath)) {
          const fileContent = fs.readFileSync(configPath, 'utf8');
          if (fileContent.trim()) {
            const config = JSON.parse(fileContent) as LicenseConfig;
            
            // Validate required fields
            if (config.licenseKey && config.tenant_id) {
              this.cachedConfig = config;
              console.log(`License config loaded from: ${configPath}`);
              return config;
            }
          }
        }
      } catch (error) {
        console.warn(`Failed to read license config from ${configPath}:`, error);
        continue;
      }
    }

    return null;
  }

  /**
   * Create empty license config file at monorepo root
   */
  public createEmptyLicenseConfig(): boolean {
    const emptyConfig: Partial<LicenseConfig> = {
      licenseKey: "",
      customerName: "",
      email: "",
      tenant_id: ""
    };

    // Create at monorepo root by default
    const configPath = path.resolve(process.cwd(), '../../license_config.json');
    
    try {
      if (!fs.existsSync(configPath)) {
        fs.writeFileSync(configPath, JSON.stringify(emptyConfig, null, 2), 'utf8');
        console.log(`Empty license config created at: ${configPath}`);
      }
      return true;
    } catch (error) {
      console.error('Failed to create license config:', error);
      return false;
    }
  }

  /**
   * Validate license with Supabase database
   */
  public async validateLicense(): Promise<LicenseValidationResult> {
    try {
      const licenseConfig = this.getLicenseConfig();
      
      if (!licenseConfig || !licenseConfig.licenseKey || !licenseConfig.tenant_id) {
        return {
          success: false,
          error: 'License not activated. Please activate your license first.',
          licenseKey: licenseConfig?.licenseKey || '',
          customerName: licenseConfig?.customerName || ''
        };
      }

      // Get Supabase credentials from environment
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

      if (!supabaseUrl || !supabaseKey) {
        return {
          success: false,
          error: 'Supabase configuration missing'
        };
      }

      // Create Supabase client
      const supabase = createClient(supabaseUrl, supabaseKey);

      // Validate license key in database
      const { data: license, error: licenseError } = await supabase
        .from('licenses')
        .select('*')
        .eq('license_key', licenseConfig.licenseKey)
        .single();

      if (licenseError || !license) {
        return {
          success: false,
          error: 'Invalid license key or license not found',
          licenseKey: licenseConfig.licenseKey,
          customerName: licenseConfig.customerName
        };
      }

      // Check expiry date
      const now = new Date();
      const expiryDate = license.expiry_date ? new Date(license.expiry_date) : null;
      const daysRemaining = expiryDate 
        ? Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
        : 0;

      if (expiryDate && now > expiryDate) {
        return {
          success: false,
          error: `License expired on ${expiryDate.toLocaleDateString('en-US')}`,
          licenseKey: licenseConfig.licenseKey,
          customerName: licenseConfig.customerName,
          daysRemaining: 0
        };
      }

      // Check if active
      if (license.is_active === false) {
        return {
          success: false,
          error: 'License has been deactivated',
          licenseKey: licenseConfig.licenseKey,
          customerName: licenseConfig.customerName
        };
      }

      // Check tenant_id match
      if (license.tenant_id !== licenseConfig.tenant_id) {
        return {
          success: false,
          error: 'Tenant ID does not match license',
          licenseKey: licenseConfig.licenseKey,
          customerName: licenseConfig.customerName
        };
      }

      // Update last check-in
      await supabase
        .from('licenses')
        .update({
          last_check_in: new Date().toISOString(),
          check_in_count: (license.check_in_count || 0) + 1
        })
        .eq('id', license.id);

      // Return successful validation
      return {
        success: true,
        licenseKey: license.license_key,
        customerName: license.customer_name,
        product_id: license.product_id,
        tenant_id: license.tenant_id,
        issueDate: license.issue_date,
        expiryDate: license.expiry_date,
        isActive: license.is_active,
        daysRemaining: Math.max(0, daysRemaining)
      };

    } catch (error) {
      console.error('Error validating license:', error);
      return {
        success: false,
        error: 'Internal error occurred while validating license'
      };
    }
  }

  /**
   * Get current tenant context
   */
  public async getCurrentTenant(): Promise<TenantContext | null> {
    const validation = await this.validateLicense();
    
    if (!validation.success || !validation.tenant_id) {
      return null;
    }

    return {
      tenant_id: validation.tenant_id,
      license_key: validation.licenseKey!,
      customer_name: validation.customerName!,
      is_active: validation.isActive!,
      expires_at: validation.expiryDate
    };
  }

  /**
   * Get tenant ID only (for web chat)
   */
  public async getCurrentTenantId(): Promise<string | null> {
    const tenant = await this.getCurrentTenant();
    return tenant?.tenant_id || null;
  }

  /**
   * Check if license config exists (quick check)
   */
  public hasValidLicenseConfig(): boolean {
    const config = this.getLicenseConfig();
    return !!(config && config.licenseKey && config.tenant_id);
  }

  /**
   * Clear cached config (for testing)
   */
  public clearCache(): void {
    this.cachedConfig = null;
  }
}

// Export singleton instance and utility functions
export const webChatLicense = WebChatLicenseService.getInstance();
export const getLicenseConfig = () => webChatLicense.getLicenseConfig();
export const validateLicense = () => webChatLicense.validateLicense();
export const getCurrentTenant = () => webChatLicense.getCurrentTenant();
export const getCurrentTenantId = () => webChatLicense.getCurrentTenantId();
export const hasValidLicenseConfig = () => webChatLicense.hasValidLicenseConfig();
