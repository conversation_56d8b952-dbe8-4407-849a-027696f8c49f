LoaLoa Admin Portal - Tài liệu tổng hợp
1. Tổng quan dự án
LoaLoa là một hệ thống chat và dịch thuật đa ngôn ngữ toàn diện dành cho khách sạn và resort, đư<PERSON>c phát triển với mục tiêu tạo ra trải nghiệm giao tiếp liền mạch giữa khách hàng và nhân viên, vư<PERSON>t qua rào cản ngôn ngữ. Dự án được xây dựng trên nền tảng công nghệ hiện đại gồm NodeJS, Next.js, TypeScript và Supabase, theo kiến trúc Monorepo để quản lý mã nguồn hiệu quả.

1.1. Đặc điểm chính
Đa ngôn ngữ: Hỗ trợ dịch thuật tự động giữa nhiều ngôn ngữ để khách và nhân viên có thể giao tiếp dễ dàng bằng ngôn ngữ của mình.
Multi-tenant: <PERSON><PERSON><PERSON><PERSON> kế theo mô hình đa người thuê, cho phép mỗi khách sạn/resort vận hành hệ thống độc lập.
Database-per-tenant: Mỗi tenant có cơ sở dữ liệu riêng để đảm bảo tính cách ly dữ liệu.
Triển khai on-premise: Sử dụng Docker để triển khai tại chỗ, đảm bảo quyền kiểm soát và bảo mật dữ liệu.
QR Code: Hệ thống tích hợp QR code để khách hàng dễ dàng quét và kết nối với dịch vụ.
Định tuyến chat thông minh: Tự động chuyển tin nhắn đến đúng nhân viên hoặc bộ phận phù hợp.
1.2. Cấu trúc dự án
Dự án LoaLoa được tổ chức theo kiến trúc Monorepo với các thành phần chính:

/loaloa
  /apps
    /admin-portal      # Cổng quản trị cho quản lý hệ thống
    /guest-app         # Ứng dụng dành cho khách (đang phát triển)
    /web-chat-client   # Giao diện chat web cho khách (đang phát triển)
    /staff-dashboard   # Giao diện cho nhân viên (đang phát triển)
  /packages
    /ui               # Thư viện UI components dùng chung
    /translation-core # Logic xử lý dịch thuật (đang phát triển)
    /chat-core        # Logic xử lý chat (đang phát triển)
2. Admin Portal
Admin Portal là công cụ quản trị dành cho LoaLoa, cung cấp giao diện cho quản trị viên để quản lý khách, phòng, mã QR, nhân viên và các cài đặt hệ thống.

2.1. Cấu trúc thư mục Admin Portal
/admin-portal
  /app                  # Ứng dụng Next.js
    /api                # API endpoints
      /areas            # API quản lý khu vực
      /chat-routing-rules # API quản lý quy tắc định tuyến chat
      /guests           # API quản lý khách
      /qr-codes         # API quản lý mã QR
      /reception-points # API quản lý điểm nhận tin nhắn
      /rooms            # API quản lý phòng
      /staff-assignments # API quản lý phân công nhân viên
      /users            # API quản lý người dùng
    /components         # React components
      /areas            # Components khu vực
      /chat-routing     # Components định tuyến chat
      /modals           # Components hộp thoại
      /qr-codes         # Components QR code
      /reception-points # Components điểm nhận tin nhắn
      /rooms            # Components phòng
      /staff-assignments # Components phân công nhân viên
      /users            # Components người dùng
    /areas              # Trang quản lý khu vực
    /chat-routing-rules # Trang quản lý quy tắc định tuyến chat
    /dashboard          # Trang tổng quan
    /guests             # Trang quản lý khách
    /qr-codes           # Trang quản lý QR code
    /reception-points   # Trang quản lý điểm nhận tin nhắn
    /rooms-areas        # Trang quản lý phòng và khu vực
    /settings           # Trang cài đặt
    /staff-assignments  # Trang quản lý phân công nhân viên
    /users              # Trang quản lý người dùng
  /lib                  # Thư viện và utilities
    /supabase           # Kết nối Supabase
  /public               # Tài nguyên tĩnh
3. Các module chức năng chính
3.1. Quản lý khách hàng (Guests)
Module Quản lý khách hàng cho phép quản trị viên theo dõi và quản lý khách đang lưu trú và khách đã checkout.

3.1.1. Tính năng chính
Xem danh sách khách đang lưu trú và khách đã check-out
Tạo mới khách và check-in vào phòng
Chỉnh sửa thông tin khách
Check-out khách
Kích hoạt lại khách đã check-out
Xóa khách đã check-out
3.1.2. Cấu trúc dữ liệu
Bảng chính: tenant_guests

CopyCREATE TABLE tenant_guests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID REFERENCES tenants(id),
  full_name VARCHAR,
  email VARCHAR,
  phone VARCHAR,
  room_number VARCHAR,
  check_in TIMESTAMP,
  check_out TIMESTAMP,
  qr_code_id UUID REFERENCES tenant_qr_codes(id),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now(),
  device_id VARCHAR(255),
  is_profile_completed BOOLEAN DEFAULT FALSE,
  preferred_language VARCHAR(10)
);
3.1.3. API Endpoints
GET /api/guests - Lấy danh sách khách
POST /api/guests - Tạo mới khách
GET /api/guests/{id} - Lấy thông tin chi tiết khách
PUT /api/guests/{id} - Cập nhật thông tin khách
DELETE /api/guests/{id} - Xóa khách
POST /api/guests/{id}/checkin - Check-in khách
POST /api/guests/{id}/checkout - Check-out khách
POST /api/guests/{id}/reactivate - Kích hoạt lại khách
3.2. Quản lý phòng và khu vực (Rooms & Areas)
Module Quản lý phòng và khu vực giúp quản lý các phòng và khu vực công cộng trong khách sạn/resort.

3.2.1. Tính năng chính
Quản lý phòng: Thêm, sửa, xóa phòng và theo dõi trạng thái
Quản lý khu vực: Thêm, sửa, xóa khu vực công cộng
Liên kết phòng/khu vực với các mã QR và điểm nhận tin nhắn
3.2.2. Cấu trúc dữ liệu
Bảng phòng: tenant_rooms

CopyCREATE TABLE tenant_rooms (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID REFERENCES tenants(id),
  room_number VARCHAR NOT NULL,
  room_type VARCHAR,
  floor VARCHAR,
  status VARCHAR DEFAULT 'available',
  description TEXT,
  qr_code_id UUID REFERENCES tenant_qr_codes(id),
  reception_point_id UUID REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL,
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now(),
  UNIQUE(tenant_id, room_number)
);
Bảng khu vực: tenant_areas

CopyCREATE TABLE tenant_areas (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  area_type VARCHAR(50) NOT NULL,
  floor VARCHAR(10),
  location VARCHAR(255),
  description TEXT,
  qr_code_id UUID REFERENCES tenant_qr_codes(id),
  staff_count INTEGER DEFAULT 0,
  opening_hours VARCHAR(100),
  closing_hours VARCHAR(100),
  image_url VARCHAR(255),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  reception_point_id UUID REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL
);
3.2.3. API Endpoints
GET /api/rooms - Lấy danh sách phòng

POST /api/rooms - Tạo mới phòng

GET /api/rooms/{id} - Lấy thông tin chi tiết phòng

PUT /api/rooms/{id} - Cập nhật thông tin phòng

DELETE /api/rooms/{id} - Xóa phòng

GET /api/areas - Lấy danh sách khu vực

POST /api/areas - Tạo mới khu vực

GET /api/areas/{id} - Lấy thông tin chi tiết khu vực

PUT /api/areas/{id} - Cập nhật thông tin khu vực

DELETE /api/areas/{id} - Xóa khu vực

3.3. Quản lý QR code
Module Quản lý QR code cho phép tạo và quản lý các mã QR để khách hàng có thể quét và truy cập các dịch vụ.

3.3.1. Tính năng chính
Tạo và quản lý mã QR cho các khu vực và phòng
Theo dõi lượt quét mã QR
Liên kết mã QR với khu vực cụ thể
Tải xuống và in mã QR
Thiết lập điểm nhận tin nhắn (Reception Point) cho mỗi mã QR
3.3.2. Cấu trúc dữ liệu
Bảng chính: tenant_qr_codes

CopyCREATE TABLE tenant_qr_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  code_value VARCHAR(255) NOT NULL,
  name VARCHAR(255),
  description TEXT,
  location VARCHAR(255),
  status VARCHAR(50) DEFAULT 'active',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  last_used TIMESTAMPTZ,
  qr_type USER-DEFINED DEFAULT 'TENANT',
  room_number VARCHAR(50),
  scan_count INTEGER DEFAULT 0,
  target_type VARCHAR,
  target_id UUID,
  qr_type_id UUID REFERENCES tenant_qr_code_types(id),
  custom_action JSONB,
  target_department VARCHAR(50),
  reception_point_id UUID REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT TRUE,
  UNIQUE(tenant_id, code_value),
  CHECK (target_type IN ('room', 'area', 'guest', 'general'))
);
Bảng quét QR: tenant_qr_code_scans

CopyCREATE TABLE tenant_qr_code_scans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID REFERENCES tenants(id),
  qr_code_id UUID REFERENCES tenant_qr_codes(id),
  guest_id UUID REFERENCES tenant_guests(id),
  device_id VARCHAR(255),
  ip_address VARCHAR(50),
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  scan_location JSONB,
  metadata JSONB
);
3.3.3. API Endpoints
GET /api/qr-codes - Lấy danh sách mã QR
POST /api/qr-codes - Tạo mới mã QR
GET /api/qr-codes/{id} - Lấy thông tin chi tiết mã QR
PUT /api/qr-codes/{id} - Cập nhật thông tin mã QR
DELETE /api/qr-codes/{id} - Xóa mã QR
GET /api/qr-codes/{id}/download - Tải xuống hình ảnh mã QR
GET /api/qr-codes/{id}/image - Lấy hình ảnh mã QR
GET /api/qr-codes/stats - Lấy thống kê về mã QR
GET /api/scan/{code} - Xử lý khi mã QR được quét
3.4. Điểm nhận tin nhắn (Reception Points)
Module Điểm nhận tin nhắn quản lý nơi tin nhắn từ khách hàng được gửi đến và xử lý.

3.4.1. Tính năng chính
Tạo và quản lý các điểm nhận tin nhắn
Liên kết điểm nhận với phòng, khu vực hoặc mã QR
Phân công nhân viên cho các điểm nhận
3.4.2. Cấu trúc dữ liệu
Bảng chính: tenant_message_reception_points

CopyCREATE TABLE tenant_message_reception_points (
  id UUID DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(50) NOT NULL,
  description TEXT,
  icon_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  priority INTEGER DEFAULT 1,
  created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  view_order INTEGER DEFAULT 0,
  UNIQUE (tenant_id, code)
);

COMMENT ON TABLE tenant_message_reception_points IS 'Các điểm nhận tin nhắn, dùng để định tuyến tin nhắn từ khách đến nhân viên phù hợp';
3.4.3. API Endpoints
GET /api/reception-points - Lấy danh sách điểm nhận tin nhắn
POST /api/reception-points - Tạo mới điểm nhận tin nhắn
GET /api/reception-points/{id} - Lấy thông tin chi tiết điểm nhận tin nhắn
PUT /api/reception-points/{id} - Cập nhật thông tin điểm nhận tin nhắn
DELETE /api/reception-points/{id} - Xóa điểm nhận tin nhắn
3.5. Quy tắc định tuyến chat (Chat Routing Rules)
Module Quy tắc định tuyến chat giúp tự động chuyển tin nhắn từ khách hàng đến nhân viên hoặc bộ phận phù hợp.

3.5.1. Tính năng chính
Tạo và quản lý quy tắc định tuyến chat
Thiết lập điều kiện và mục tiêu cho quy tắc
Thiết lập độ ưu tiên cho các quy tắc
3.5.2. Cấu trúc dữ liệu
Bảng chính: tenant_chat_routing_rules

CopyCREATE TABLE tenant_chat_routing_rules (
  id UUID DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  rule_name VARCHAR(100) NOT NULL,
  rule_type VARCHAR(50) NOT NULL,
  rule_condition JSONB NOT NULL,
  target_department VARCHAR(50),
  target_user_id UUID REFERENCES tenant_users(id),
  target_reception_point_id UUID REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL,
  priority INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  view_order INTEGER DEFAULT 0
);

COMMENT ON TABLE tenant_chat_routing_rules IS 'Quy tắc định tuyến chat từ khách hàng tới nhân viên hoặc điểm nhận tin nhắn';
3.5.3. API Endpoints
GET /api/chat-routing-rules - Lấy danh sách quy tắc định tuyến chat
POST /api/chat-routing-rules - Tạo mới quy tắc định tuyến chat
GET /api/chat-routing-rules/{id} - Lấy thông tin chi tiết quy tắc định tuyến chat
PUT /api/chat-routing-rules/{id} - Cập nhật thông tin quy tắc định tuyến chat
DELETE /api/chat-routing-rules/{id} - Xóa quy tắc định tuyến chat
3.5.4. Loại quy tắc định tuyến
QR Code: Định tuyến dựa trên mã QR được quét
Guest: Định tuyến dựa trên loại khách (VIP, thường, loyalty...)
Time: Định tuyến dựa trên thời gian trong ngày hoặc ngày trong tuần
Language: Định tuyến dựa trên ngôn ngữ của khách
Custom: Định tuyến dựa trên điều kiện tùy chỉnh
3.5.5. Cách hoạt động
Khi khách quét QR code hoặc gửi tin nhắn, hệ thống kiểm tra các quy tắc theo thứ tự ưu tiên
Quy tắc đầu tiên khớp với điều kiện sẽ được áp dụng
Tin nhắn được chuyển đến mục tiêu được xác định (bộ phận, nhân viên cụ thể hoặc điểm nhận tin nhắn)
3.6. Phân công nhân viên (Staff Assignments)
Module Phân công nhân viên quản lý việc gán nhân viên vào các bộ phận và điểm nhận tin nhắn.

3.6.1. Tính năng chính
Phân công nhân viên vào các bộ phận
Gán nhân viên cho các điểm nhận tin nhắn
Thiết lập độ ưu tiên và lịch làm việc cho nhân viên
3.6.2. Cấu trúc dữ liệu
Bảng chính: tenant_staff_assignments

CopyCREATE TABLE tenant_staff_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  user_id UUID NOT NULL REFERENCES tenant_users(id),
  department VARCHAR(50),
  assignment_type VARCHAR(50),
  resource_id UUID,
  reception_point_id UUID REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL,
  priority INTEGER DEFAULT 1,
  working_hours JSONB,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(tenant_id, user_id, assignment_type, resource_id, department)
);
3.6.3. API Endpoints
GET /api/staff-assignments - Lấy danh sách phân công nhân viên
POST /api/staff-assignments - Tạo mới phân công nhân viên
GET /api/staff-assignments/{id} - Lấy thông tin chi tiết phân công nhân viên
PUT /api/staff-assignments/{id} - Cập nhật thông tin phân công nhân viên
DELETE /api/staff-assignments/{id} - Xóa phân công nhân viên
3.7. Quản lý người dùng (Users)
Module Quản lý người dùng giúp quản lý các tài khoản người dùng trong hệ thống, bao gồm quản trị viên và nhân viên.

3.7.1. Tính năng chính
Tạo, xem, chỉnh sửa và xóa người dùng
Phân vai trò và quyền hạn cho người dùng
Gán người dùng (nhân viên) vào Reception Points
3.7.2. Cấu trúc dữ liệu
Bảng chính: tenant_users và tenant_users_details

CopyCREATE TABLE tenant_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role tenant_user_role DEFAULT 'user',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now(),
  UNIQUE(tenant_id, user_id)
);

CREATE TABLE tenant_users_details (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_user_id UUID NOT NULL REFERENCES tenant_users(id) ON DELETE CASCADE,
  display_name VARCHAR(255),
  title VARCHAR(100),
  department VARCHAR(100),
  email VARCHAR(255),
  phone VARCHAR(50),
  avatar_url TEXT,
  preferred_language VARCHAR(10) DEFAULT 'en',
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now(),
  UNIQUE(tenant_user_id)
);
3.7.3. API Endpoints
GET /api/users - Lấy danh sách người dùng
POST /api/users - Tạo mới người dùng
GET /api/users/{id} - Lấy thông tin chi tiết người dùng
PUT /api/users/{id} - Cập nhật thông tin người dùng
DELETE /api/users/{id} - Xóa người dùng
GET /api/users/{id}/reception-points - Lấy danh sách Reception Points của người dùng
POST /api/users/{id}/reception-points - Gán người dùng vào Reception Point
DELETE /api/users/{id}/reception-points/{linkId} - Xóa liên kết người dùng với Reception Point
3.8. Chat Sessions và Messages
Module Chat Sessions và Messages quản lý các phiên chat và tin nhắn giữa khách và nhân viên.

3.8.1. Tính năng chính
Tạo và quản lý phiên chat
Lưu trữ và quản lý tin nhắn
Xử lý dịch thuật tin nhắn
Định tuyến phiên chat đến nhân viên
3.8.2. Cấu trúc dữ liệu
Bảng phiên chat: tenant_chat_sessions

CopyCREATE TABLE tenant_chat_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  guest_id UUID,
  status VARCHAR(50) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  ended_at TIMESTAMP WITH TIME ZONE,
  source_qr_code_id UUID REFERENCES tenant_qr_codes(id),
  guest_language VARCHAR(10),
  priority VARCHAR(20) DEFAULT 'normal',
  staff_language VARCHAR(10),
  auto_translate BOOLEAN DEFAULT TRUE,
  source_type VARCHAR(50),
  source_id UUID,
  reception_point_id UUID REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL
);
Bảng tin nhắn: tenant_chat_messages

CopyCREATE TABLE tenant_chat_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  chat_session_id UUID REFERENCES tenant_chat_sessions(id),
  sender_type VARCHAR(20) NOT NULL,
  sender_id UUID,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  is_translated BOOLEAN DEFAULT FALSE,
  original_language VARCHAR(10),
  metadata JSONB,
  original_content TEXT,
  translated_content TEXT,
  translated_language VARCHAR(10),
  translation_provider VARCHAR(50),
  translation_confidence NUMERIC,
  show_translation BOOLEAN DEFAULT TRUE
);
Bảng phân công phiên chat: tenant_chat_session_assignments

CopyCREATE TABLE tenant_chat_session_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL,
  chat_session_id UUID NOT NULL REFERENCES tenant_chat_sessions(id),
  assigned_user_id UUID NOT NULL REFERENCES tenant_users(id),
  assignment_status VARCHAR(50) NOT NULL,
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  accepted_at TIMESTAMP WITH TIME ZONE,
  closed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
4. Luồng hoạt động của hệ thống
4.1. Quét QR code và chat
Khách quét QR code được đặt tại phòng hoặc khu vực công cộng
Hệ thống đọc mã QR và tạo profile tạm thời cho khách (nếu chưa có)
Hệ thống xác định điểm nhận tin nhắn dựa trên QR code hoặc quy tắc định tuyến
Khách nhập tin nhắn bằng ngôn ngữ của họ
Tin nhắn được gửi đến điểm nhận tin nhắn và được định tuyến đến nhân viên phù hợp
Nhân viên nhận và trả lời tin nhắn, với hệ thống tự động dịch giữa ngôn ngữ của khách và nhân viên
4.2. Định tuyến tin nhắn
Khi có tin nhắn mới, hệ thống áp dụng các quy tắc định tuyến chat
Quy tắc được đánh giá theo thứ tự ưu tiên (priority) từ cao xuống thấp
Quy tắc đầu tiên khớp với điều kiện được áp dụng
Tin nhắn được gửi đến target_department, target_user_id hoặc target_reception_point_id
Nếu là reception_point, hệ thống tìm nhân viên được phân công cho reception_point đó
Nhân viên có ưu tiên cao nhất và đang trực tuyến/làm việc sẽ nhận được tin nhắn
4.3. Dịch thuật
Hệ thống tự động phát hiện ngôn ngữ của tin nhắn
Tin nhắn được dịch sang ngôn ngữ của người nhận
Cả tin nhắn gốc và tin nhắn đã dịch được lưu trữ
Người nhận có thể xem cả tin nhắn gốc và bản dịch
5. Định hướng phát triển tiếp theo
5.1. Web Chat Client
Phát triển giao diện web để khách quét QR code và chat trên trình duyệt
Tối ưu hóa trải nghiệm UI/UX cho thiết bị di động
Triển khai PWA (Progressive Web App) để nâng cao trải nghiệm
5.2. Guest App
Phát triển ứng dụng di động native cho khách
Tích hợp quét QR code và chat
Bổ sung tính năng đặt dịch vụ và xem thông tin
5.3. Staff Dashboard
Phát triển dashboard cho nhân viên tiếp nhận và xử lý tin nhắn
Tích hợp thông báo thời gian thực
Thêm tính năng quản lý tin nhắn và báo cáo
6. Cấu trúc API và kết nối
6.1. Kết nối Supabase
Admin Portal sử dụng Supabase như backend chính
Kết nối được thực hiện thông qua Admin Client và Client tùy theo quyền hạn
6.2. Cấu trúc API
REST API với các endpoint được tổ chức theo tài nguyên
Xác thực thông qua JWT tokens
Phân quyền dựa trên vai trò và tenant
6.3. Realtime Updates
Sử dụng Supabase Realtime để cập nhật tin nhắn và trạng thái thời gian thực
Đăng ký lắng nghe các thay đổi trên bảng chat_messages và chat_sessions