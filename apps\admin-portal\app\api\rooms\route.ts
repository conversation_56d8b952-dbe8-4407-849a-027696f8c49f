import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { createAdminClient } from '../../../lib/supabase/admin';

// Function to get tenant_id from license_config.json
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get('category');
    const floor = searchParams.get('floor');
    const status = searchParams.get('status');
    const limitStr = searchParams.get('limit');
    const limit = limitStr ? parseInt(limitStr) : undefined;
    const reception_point_id = searchParams.get('reception_point_id') || '';

    // Get tenant_id from config file
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }

    console.log('Using tenant_id for fetching rooms:', tenant_id);
    
    const supabase = createAdminClient();

    // Query rooms by tenant_id
    let query = supabase
      .from('tenant_rooms')
      .select('*', { count: 'exact' })
      .eq('tenant_id', tenant_id);

    // Apply filters
    if (category) {
      query = query.eq('room_category', category);
    }
    if (floor) {
      query = query.eq('floor', floor);
    }
    if (status === 'occupied') {
      query = query.eq('status', 'occupied');
    } else if (status === 'available') {
      query = query.eq('status', 'available');
    } else if (status === 'maintenance') {
      query = query.eq('status', 'maintenance');
    }
    if (reception_point_id) {
      query = query.eq('reception_point_id', reception_point_id);
    }

    // Apply limit if provided
    if (limit) {
      query = query.limit(limit);
    }

    const { data, error, count } = await query;
    
    if (error) {
      console.error('Error fetching rooms:', error);
      return NextResponse.json({ 
        error: error.message 
      }, { status: 500 });
    }

    // After getting rooms, fetch related guest information
    let roomsWithGuests = data || [];
    if (roomsWithGuests.length > 0) {
      try {
        const roomNumbers = roomsWithGuests.map(room => room.room_number);
        
        const { data: guestsData, error: guestsError } = await supabase
          .from('tenant_guests')
          .select('id, full_name, email, phone, check_in, check_out, is_active, room_number')
          .in('room_number', roomNumbers)
          .eq('tenant_id', tenant_id)
          .eq('is_active', true);

        if (!guestsError && guestsData) {
          // Assign guest information to corresponding rooms
          roomsWithGuests = roomsWithGuests.map(room => ({
            ...room,
            tenant_guests: guestsData.filter(guest => guest.room_number === room.room_number)
          }));
        }
      } catch (err) {
        console.error('Error fetching guests:', err);
      }
    }

    return NextResponse.json({
      data: roomsWithGuests,
      count: count || 0
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error',
      details: error.message
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    // Get tenant_id from config file
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }
    console.log('Using tenant_id for creating room:', tenant_id);
    
    const supabase = createAdminClient();
    
    const { 
      room_number, 
      room_type, 
      floor, 
      room_category, 
      description, 
      image_url,
      reception_point_id // Thêm reception_point_id
    } = body;

    // Check if room already exists
    const { data: existingRoom, error: checkError } = await supabase
      .from('tenant_rooms')
      .select('id')
      .eq('room_number', room_number)
      .eq('tenant_id', tenant_id)
      .maybeSingle();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing room:', checkError);
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    if (existingRoom) {
      return NextResponse.json({ error: `Room with number ${room_number} already exists` }, { status: 400 });
    }

    // Kiểm tra reception_point_id nếu được cung cấp
    if (reception_point_id) {
      const { data: receptionPoint, error: rpError } = await supabase
        .from('tenant_message_reception_points')
        .select('id')
        .eq('id', reception_point_id)
        .eq('tenant_id', tenant_id)
        .single();

      if (rpError || !receptionPoint) {
        return NextResponse.json({ error: 'Reception point không tồn tại hoặc không thuộc tenant này' }, { status: 400 });
      }
    }

    // Create new room with tenant_id
    const { data, error } = await supabase
      .from('tenant_rooms')
      .insert([
        { 
          tenant_id, 
          room_number, 
          room_type, 
          floor, 
          room_category, 
          description, 
          image_url, 
          status: 'available', 
          is_active: true,
          reception_point_id: reception_point_id || null // Thêm trường reception_point_id
        }
      ])
      .select();

    if (error) {
      console.error('Error creating room:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data: data[0] }, { status: 201 });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
