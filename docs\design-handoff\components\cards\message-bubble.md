# Component Design Handoff: Message Bubble

## Overview
- **Component Name:** MessageBubble
- **Designer:** UI Team
- **Last Updated:** 2025-04-28
- **Status:** Ready for Handoff

## Visual Design

### Desktop/Mobile View
[Screenshot from Figma - message bubble examples]

### Variants
- **User Message:** Right-aligned, primary color background
- **Other Message:** Left-aligned, light gray background
- **System Message:** Centered, subtle background

## Specifications

### Layout & Spacing
- **Padding:** spacing-3 (12px) all around
- **Max Width:** 80% of container width
- **Margin:** spacing-2 (8px) between messages
- **Avatar Size:** 32x32px (if shown)

### Typography
- **Message Text:**
  - Font Family: fontFamily.base (Roboto)
  - Font Size: fontSize.md (16px)
  - Font Weight: fontWeight.regular (400)
  - Line Height: 1.5
- **Timestamp:**
  - Font Size: fontSize.xs (12px)
  - Font Weight: fontWeight.regular (400)
  - Opacity: 0.7

### Colors
- **Background:**
  - User Message: colors.primary (#3B82F6)
  - Other Message: colors.background.paper (#FFFFFF)
  - System Message: colors.background.input (#F3F4F6)
- **Text:**
  - User Message: White (#FFFFFF)
  - Other Message: colors.text.primary (#111827)
  - System Message: colors.text.secondary (#6B7280)
- **Border:**
  - Other Message: 1px solid colors.border.light (#F3F4F6)
  - User/System: None

### Other Properties
- **Border Radius:**
  - General: borderRadius.lg (8px)
  - User Message: Custom (8px with flat corner on bottom right)
  - Other Message: Custom (8px with flat corner on bottom left)
- **Shadow:** Subtle shadow for Other Message only

## Behavior
- **Long Messages:** Will wrap to multiple lines
- **Media Content:** Can contain images that expand to full width
- **Timestamp:** Shows on hover or tap (mobile)

## Accessibility
- **Screen Reader:** Messages should be properly announced with sender info
- **Color Contrast:** Ensure text is readable on all background variants

## Implementation Notes
- Support for message status indicators (sent, delivered, read)
- Original language and translated versions should be shown together
- Consider performance for chat with many messages
- Support for retries on failed messages

## Assets
- Message status icons (checkmarks)