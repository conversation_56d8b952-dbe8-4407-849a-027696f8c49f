import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '../../../../../utils/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const licenseId = params.id;
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    
    const supabase = await createClient();
    
    // Kiểm tra license có tồn tại không
    const { data: license, error: licenseError } = await supabase
      .from('licenses')
      .select('*')
      .eq('id', licenseId)
      .single();
      
    if (licenseError || !license) {
      return NextResponse.json(
        { error: 'License not found' },
        { status: 404 }
      );
    }
    
    // T<PERSON>h toán offset cho phân trang
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    
    // <PERSON><PERSON>y danh sách hoạt động của license
    const { data, error, count } = await supabase
      .from('license_activities')
      .select('*', { count: 'exact' })
      .eq('license_id', licenseId)
      .order('timestamp', { ascending: false })
      .range(from, to);
      
    if (error) {
      console.error('Error fetching license activities:', error);
      return NextResponse.json(
        { error: 'Failed to fetch license activities' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      data,
      meta: {
        total: count || 0,
        page,
        limit,
        pageCount: Math.ceil((count || 0) / limit)
      }
    });
    
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch license activities' },
      { status: 500 }
    );
  }
}