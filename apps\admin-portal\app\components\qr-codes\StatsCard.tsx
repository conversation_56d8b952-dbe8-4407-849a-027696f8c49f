import React from 'react';
import styles from './StatsCard.module.scss';

interface StatsCardProps {
  title: string;
  value: number | string;
  icon?: 'qr-code' | 'scan' | 'chart';
  trend?: string;
  trendDirection?: 'up' | 'down' | 'neutral';
  loading?: boolean;
}

export default function StatsCard({
  title,
  value,
  icon = 'chart',
  trend,
  trendDirection = 'neutral',
  loading = false
}: StatsCardProps) {
  const renderIcon = () => {
    switch(icon) {
      case 'qr-code':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <rect x="4" y="4" width="6" height="6" stroke="currentColor" strokeWidth="2" />
            <rect x="14" y="4" width="6" height="6" stroke="currentColor" strokeWidth="2" />
            <rect x="4" y="14" width="6" height="6" stroke="currentColor" strokeWidth="2" />
            <rect x="14" y="14" width="6" height="6" stroke="currentColor" strokeWidth="2" />
          </svg>
        );
      case 'scan':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M3 7V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M17 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M21 17V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M7 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <rect x="7" y="7" width="10" height="10" rx="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
      default:
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M3 12H7L10 20L14 4L17 12H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
    }
  };

  const renderTrend = () => {
    if (!trend) return null;

    return (
      <div className={`${styles.trend} ${styles[trendDirection]}`}>
        {trendDirection === 'up' && (
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M8 12V4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M4 8L8 4L12 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        )}
        {trendDirection === 'down' && (
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M8 4V12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M12 8L8 12L4 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        )}
        {trend}
      </div>
    );
  };

  return (
    <div className={styles.card}>
      <div className={styles.iconContainer}>
        {renderIcon()}
      </div>
      <div className={styles.content}>
        <div className={styles.title}>{title}</div>
        {loading ? (
          <div className={styles.loading}>
            <div className={styles.skeleton}></div>
          </div>
        ) : (
          <div className={styles.valueContainer}>
            <div className={styles.value}>{value}</div>
            {renderTrend()}
          </div>
        )}
      </div>
    </div>
  );
}
