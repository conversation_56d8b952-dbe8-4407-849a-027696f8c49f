// Chat Room
export interface ChatRoom {
  id: string;
  name: string;
  description?: string;
  room_type: 'general' | 'support' | 'private' | 'group';
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  hotel_id?: string;
  room_number?: string;
  metadata?: Record<string, any>;
}

// Chat Participant
export interface ChatParticipant {
  id: string;
  chat_room_id: string;
  user_id: string;
  temporary_user_id: string;
  participant_role: 'member' | 'admin' | 'moderator';
  is_active: boolean;
  joined_at: string;
  left_at: string;
  display_name: string;
  preferred_language?: string;
  last_read_message_id: string;
  metadata: Record<string, any>;
}

// Chat Message
export interface ChatMessage {
  id: string;
  chat_room_id: string;
  sender_id?: string;
  temporary_sender_id?: string;
  content: string;
  original_language: string;
  sent_at: string;
  is_translated: boolean;
  metadata?: Record<string, any>;
}


// Message Translation
export interface MessageTranslation {
  id: string;
  message_id: string;
  language: string;
  translated_content: string;
  is_machine_translation: boolean;
  created_at: string;
  updated_at: string;
}

// Message Status
export interface MessageStatus {
  id: string;
  message_id: string;
  participant_id: string;
  is_delivered: boolean;
  is_read: boolean;
  delivered_at?: string;
  read_at?: string;
}

// Message Attachment
export interface MessageAttachment {
  id: string;
  message_id: string;
  file_url: string;
  file_name: string;
  file_type?: string;
  file_size?: number;
  thumbnail_url?: string;
  created_at: string;
}

// Message Reaction
export interface MessageReaction {
  id: string;
  message_id: string;
  participant_id: string;
  reaction: string;
  created_at: string;
}

// User from auth service
export interface User {
  id: string;
  email: string;
  full_name?: string;
  preferred_language: string;
  avatar_url?: string;
}

// Temporary User from auth service
export interface TemporaryUser {
  id: string;
  qr_code_id: string;
  device_id?: string;
  preferred_language: string;
  created_at: string;
  expires_at: string;
  is_activated: boolean;
  room_number?: string;
  hotel_id?: string;
  metadata?: Record<string, any>;
}

// Socket events
export enum SocketEvents {
  // Connection events
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  
  // Room events
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  ROOM_JOINED = 'room_joined',
  ROOM_LEFT = 'room_left',
  
  // Message events
  SEND_MESSAGE = 'send_message',
  MESSAGE_RECEIVED = 'message_received',
  MESSAGE_UPDATED = 'message_updated',
  MESSAGE_DELETED = 'message_deleted',
  
  // Status events
  MESSAGE_READ = 'message_read',
  MESSAGE_DELIVERED = 'message_delivered',
  TYPING_START = 'typing_start',
  TYPING_END = 'typing_end',
  
  // Translation events
  REQUEST_TRANSLATION = 'request_translation',
  TRANSLATION_RECEIVED = 'translation_received',
  
  // Reaction events
  ADD_REACTION = 'add_reaction',
  REMOVE_REACTION = 'remove_reaction',
  REACTION_UPDATED = 'reaction_updated',
  
  // Error events
  ERROR = 'error'
}

// WebSocket connection data
export interface ConnectionData {
  userId?: string;
  temporaryUserId?: string;
  preferredLanguage: string;
  deviceId?: string;
}

// Message data for sending via WebSocket
export interface SendMessageData {
  roomId: string;
  content: string;
  contentType?: string;
  originalLanguage: string;
  metadata?: Record<string, any>;
}

// Socket response format
export interface SocketResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}
