/**
 * Performance Testing Utility
 * Compare original vs enhanced implementations
 */

import { realtimeMonitor } from './realtime-monitor';

interface TestResult {
  implementation: 'original' | 'enhanced';
  avgLatency: number;
  maxLatency: number;
  minLatency: number;
  errorCount: number;
  connectionTime: number;
  messagesProcessed: number;
  testDuration: number;
}

interface TestConfig {
  duration: number; // Test duration in milliseconds
  messageInterval: number; // Interval between test messages
  messageCount: number; // Number of messages to send
}

class PerformanceTester {
  private testResults: TestResult[] = [];
  private currentTest: {
    startTime: number;
    messagesSent: number;
    messagesReceived: number;
    latencies: number[];
    errors: number;
  } | null = null;

  // Start a performance test
  startTest(implementation: 'original' | 'enhanced', config: TestConfig): void {
    console.log(`🧪 Starting performance test for ${implementation} implementation`);
    
    this.currentTest = {
      startTime: Date.now(),
      messagesSent: 0,
      messagesReceived: 0,
      latencies: [],
      errors: 0
    };

    // Reset monitor for clean test
    realtimeMonitor.reset();
    realtimeMonitor.startConnectionMonitoring();
  }

  // Record test message
  recordTestMessage(messageId: string, latency: number): void {
    if (!this.currentTest) return;

    this.currentTest.messagesReceived++;
    this.currentTest.latencies.push(latency);
    
    console.log(`📊 Test message ${messageId}: ${latency}ms latency`);
  }

  // Record test error
  recordTestError(): void {
    if (!this.currentTest) return;
    this.currentTest.errors++;
  }

  // End test and calculate results
  endTest(implementation: 'original' | 'enhanced'): TestResult | null {
    if (!this.currentTest) return null;

    const endTime = Date.now();
    const testDuration = endTime - this.currentTest.startTime;
    const latencies = this.currentTest.latencies;

    const result: TestResult = {
      implementation,
      avgLatency: latencies.length > 0 ? latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length : 0,
      maxLatency: latencies.length > 0 ? Math.max(...latencies) : 0,
      minLatency: latencies.length > 0 ? Math.min(...latencies) : 0,
      errorCount: this.currentTest.errors,
      connectionTime: realtimeMonitor.getPerformanceSummary().metrics.connectionTime,
      messagesProcessed: this.currentTest.messagesReceived,
      testDuration
    };

    this.testResults.push(result);
    this.currentTest = null;

    console.log(`✅ Test completed for ${implementation}:`, result);
    return result;
  }

  // Compare two implementations
  compareImplementations(): {
    original?: TestResult;
    enhanced?: TestResult;
    improvement: {
      latencyImprovement: number;
      errorReduction: number;
      connectionImprovement: number;
    };
  } | null {
    const original = this.testResults.find(r => r.implementation === 'original');
    const enhanced = this.testResults.find(r => r.implementation === 'enhanced');

    if (!original || !enhanced) {
      console.warn('⚠️ Cannot compare - missing test results');
      return null;
    }

    const latencyImprovement = ((original.avgLatency - enhanced.avgLatency) / original.avgLatency) * 100;
    const errorReduction = ((original.errorCount - enhanced.errorCount) / Math.max(original.errorCount, 1)) * 100;
    const connectionImprovement = ((original.connectionTime - enhanced.connectionTime) / original.connectionTime) * 100;

    const comparison = {
      original,
      enhanced,
      improvement: {
        latencyImprovement: Math.round(latencyImprovement * 100) / 100,
        errorReduction: Math.round(errorReduction * 100) / 100,
        connectionImprovement: Math.round(connectionImprovement * 100) / 100
      }
    };

    console.group('📊 Performance Comparison Results');
    console.log('Original Implementation:', original);
    console.log('Enhanced Implementation:', enhanced);
    console.log('Improvements:');
    console.log(`  Latency: ${comparison.improvement.latencyImprovement}% better`);
    console.log(`  Errors: ${comparison.improvement.errorReduction}% reduction`);
    console.log(`  Connection: ${comparison.improvement.connectionImprovement}% faster`);
    console.groupEnd();

    return comparison;
  }

  // Generate test report
  generateReport(): string {
    const comparison = this.compareImplementations();
    if (!comparison) return 'No test data available';

    return `
# Performance Test Report

## Test Results Summary

### Original Implementation
- Average Latency: ${comparison.original.avgLatency}ms
- Max Latency: ${comparison.original.maxLatency}ms
- Min Latency: ${comparison.original.minLatency}ms
- Error Count: ${comparison.original.errorCount}
- Connection Time: ${comparison.original.connectionTime}ms
- Messages Processed: ${comparison.original.messagesProcessed}
- Test Duration: ${comparison.original.testDuration}ms

### Enhanced Implementation
- Average Latency: ${comparison.enhanced.avgLatency}ms
- Max Latency: ${comparison.enhanced.maxLatency}ms
- Min Latency: ${comparison.enhanced.minLatency}ms
- Error Count: ${comparison.enhanced.errorCount}
- Connection Time: ${comparison.enhanced.connectionTime}ms
- Messages Processed: ${comparison.enhanced.messagesProcessed}
- Test Duration: ${comparison.enhanced.testDuration}ms

## Performance Improvements
- **Latency Improvement**: ${comparison.improvement.latencyImprovement}%
- **Error Reduction**: ${comparison.improvement.errorReduction}%
- **Connection Speed**: ${comparison.improvement.connectionImprovement}% faster

## Recommendations
${this.generateRecommendations(comparison)}
    `;
  }

  // Generate recommendations based on test results
  private generateRecommendations(comparison: any): string {
    const recommendations: string[] = [];

    if (comparison.improvement.latencyImprovement > 20) {
      recommendations.push('✅ Significant latency improvement detected. Enhanced implementation is recommended.');
    } else if (comparison.improvement.latencyImprovement < 0) {
      recommendations.push('⚠️ Enhanced implementation shows higher latency. Consider investigating bottlenecks.');
    }

    if (comparison.improvement.errorReduction > 50) {
      recommendations.push('✅ Major error reduction achieved. Enhanced implementation is more stable.');
    }

    if (comparison.enhanced.avgLatency < 1000) {
      recommendations.push('✅ Excellent response times achieved (< 1 second average).');
    } else if (comparison.enhanced.avgLatency > 3000) {
      recommendations.push('⚠️ High latency detected (> 3 seconds). Consider further optimization.');
    }

    if (recommendations.length === 0) {
      recommendations.push('📊 Performance is within acceptable ranges. Monitor for consistency.');
    }

    return recommendations.join('\n');
  }

  // Clear all test results
  clearResults(): void {
    this.testResults = [];
    this.currentTest = null;
    console.log('🧹 Test results cleared');
  }

  // Get all test results
  getAllResults(): TestResult[] {
    return [...this.testResults];
  }
}

// Singleton instance
export const performanceTester = new PerformanceTester();

// Helper function to run automated test
export async function runAutomatedTest(
  sendMessageFunction: (content: string) => Promise<boolean>,
  config: TestConfig = {
    duration: 60000, // 1 minute
    messageInterval: 5000, // 5 seconds
    messageCount: 10
  }
): Promise<TestResult | null> {
  console.log('🚀 Starting automated performance test...');
  
  performanceTester.startTest('enhanced', config);
  
  let messageCount = 0;
  const testInterval = setInterval(async () => {
    if (messageCount >= config.messageCount) {
      clearInterval(testInterval);
      return;
    }

    const testMessage = `Test message ${messageCount + 1} - ${Date.now()}`;
    const startTime = Date.now();
    
    try {
      const success = await sendMessageFunction(testMessage);
      if (success) {
        const latency = Date.now() - startTime;
        performanceTester.recordTestMessage(`test_${messageCount}`, latency);
      } else {
        performanceTester.recordTestError();
      }
    } catch (error) {
      performanceTester.recordTestError();
      console.error('Test message failed:', error);
    }
    
    messageCount++;
  }, config.messageInterval);

  // End test after duration
  setTimeout(() => {
    clearInterval(testInterval);
    const result = performanceTester.endTest('enhanced');
    console.log('🏁 Automated test completed');
    return result;
  }, config.duration);

  return null;
}

export default PerformanceTester;
