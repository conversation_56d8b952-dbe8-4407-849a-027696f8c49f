'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import ChatMessage from './components/ChatMessage';
import ChatInput from './components/ChatInput';
import styles from './dashboard.module.scss';
import NotificationCenter from './components/notifications/NotificationCenter';
import StaffStatus from './components/StaffStatus';
import ChatSearch from './components/ChatSearch';
import ChatStats from './components/ChatStats';
import QuickActions from './components/QuickActions';
import { createClientSupabase } from '../../lib/supabase';

interface User {
  id: string;
  email: string;
  display_name: string;
  role: string;
  tenant_id: string;
  department?: string;
  title?: string;
  avatar_url?: string;
  reception_points?: any[];
}

interface ChatSession {
  id: string;
  guest_name: string;
  room_number?: string;
  language: string;
  status: 'active' | 'pending' | 'waiting';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  last_message: string;
  last_message_time: string;
  unread_count: number;
  source: string;
  session_ids?: string[];
}

interface Message {
  id: string;
  content: string;
  original_content?: string;
  translated_content?: string;
  sender_type: 'guest' | 'staff';
  sender_name: string;
  timestamp: string;
  is_translated: boolean;
  original_language?: string;
  translated_language?: string;
  translation_confidence?: number;
  show_translation: boolean;
}

export default function StaffDashboard() {
  const router = useRouter();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeChatSessions, setActiveChatSessions] = useState<ChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [onlineStatus, setOnlineStatus] = useState<'online' | 'busy' | 'away'>('online');
  const [autoTranslate, setAutoTranslate] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [chatFilters, setChatFilters] = useState({
    status: 'all' as 'all' | 'active' | 'pending' | 'waiting',
    priority: 'all' as 'all' | 'low' | 'normal' | 'high' | 'urgent',
    language: 'all' as 'all' | 'en' | 'vi' | 'ko' | 'ja' | 'es' | 'fr' | 'de' | 'th' | 'id',
    timeRange: 'all' as 'all' | 'today' | 'week' | 'month',
    unreadOnly: false
  });
  const [filteredChatSessions, setFilteredChatSessions] = useState<ChatSession[]>([]);

  // ===== REALTIME STATE - Simplified =====
  const [realtimeConnected, setRealtimeConnected] = useState(false);
  const [subscriptionStatus, setSubscriptionStatus] = useState<string>('Not started');
  const supabaseRef = useRef<any>(null);
  const messagesChannelRef = useRef<any>(null);
  const sessionsChannelRef = useRef<any>(null);
  
  // ===== POLLING FALLBACK (only when realtime fails) =====
  const [isPollingActive, setIsPollingActive] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // ===== HELPER FUNCTIONS =====
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    return `${diffDays} days ago`;
  };

  const addRealtimeLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] Realtime: ${message}`);
  };

  // ===== SUPABASE REALTIME SETUP (Clean implementation) =====
  const initializeSupabase = useCallback(() => {
    try {
      if (supabaseRef.current) {
        addRealtimeLog('⚠️ Supabase already initialized');
        return;
      }

      supabaseRef.current = createClientSupabase();
      addRealtimeLog('✅ Supabase client initialized');
      addRealtimeLog(`📡 URL: ${supabaseRef.current.supabaseUrl}`);
      
      return true;
    } catch (err) {
      addRealtimeLog(`❌ Failed to initialize Supabase: ${err}`);
      return false;
    }
  }, []);

  const setupRealtimeSubscriptions = useCallback(() => {
    if (!supabaseRef.current || !user?.tenant_id) {
      addRealtimeLog('❌ Cannot setup subscriptions: missing client or tenant_id');
      return;
    }

    addRealtimeLog('🔄 Setting up realtime subscriptions...');

    try {
      // ===== MESSAGES SUBSCRIPTION =====
      if (messagesChannelRef.current) {
        messagesChannelRef.current.unsubscribe();
      }

      messagesChannelRef.current = supabaseRef.current
        .channel('staff-messages-v2')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'tenant_chat_messages',
            filter: `tenant_id=eq.${user.tenant_id}`
          },
          (payload: any) => {
            addRealtimeLog(`🔔 New message received: ${payload.new.id}`);
            
            // Handle new message immediately
            handleRealtimeMessage(payload.new);
          }
        )
        .subscribe((status: string) => {
          setSubscriptionStatus(status);
          addRealtimeLog(`📡 Messages subscription: ${status}`);
          
          if (status === 'SUBSCRIBED') {
            setRealtimeConnected(true);
            stopPolling(); // Stop polling when realtime is working
            addRealtimeLog('✅ Realtime messages active');
          } else if (status === 'CHANNEL_ERROR' || status === 'CLOSED') {
            setRealtimeConnected(false);
            startPollingFallback(); // Start polling as fallback
            addRealtimeLog('❌ Realtime failed, switching to polling');
          }
        });

      // ===== SESSIONS SUBSCRIPTION =====
      if (sessionsChannelRef.current) {
        sessionsChannelRef.current.unsubscribe();
      }

      sessionsChannelRef.current = supabaseRef.current
        .channel('staff-sessions-v2')
        .on(
          'postgres_changes',
          {
            event: '*', // All events
            schema: 'public',
            table: 'tenant_chat_sessions',
            filter: `tenant_id=eq.${user.tenant_id}`
          },
          (payload: any) => {
            addRealtimeLog(`🔄 Session updated: ${payload.new?.id || payload.old?.id}`);
            
            // Refresh session list after short delay
            setTimeout(() => {
              loadChatSessions();
            }, 500);
          }
        )
        .subscribe((status: string) => {
          addRealtimeLog(`📡 Sessions subscription: ${status}`);
        });

    } catch (err) {
      addRealtimeLog(`❌ Subscription setup failed: ${err}`);
      setRealtimeConnected(false);
      startPollingFallback();
    }
  }, [user?.tenant_id]);

  // ===== REALTIME MESSAGE HANDLER =====
  const handleRealtimeMessage = useCallback((newMessage: any) => {
    const sessionId = newMessage.chat_session_id;
    
    // Add to current chat if it's the selected session
    if (selectedSession && sessionId === selectedSession) {
      const transformedMessage: Message = {
        id: newMessage.id,
        content: newMessage.content,
        sender_type: newMessage.sender_type,
        sender_name: newMessage.sender_name || (newMessage.sender_type === 'guest' ? 'Guest' : 'Staff'),
        timestamp: newMessage.created_at,
        is_translated: newMessage.is_translated || false,
        show_translation: autoTranslate && newMessage.is_translated
      };

      setMessages(prev => {
        // Prevent duplicates
        if (prev.some(msg => msg.id === transformedMessage.id)) {
          return prev;
        }
        return [...prev, transformedMessage];
      });

      setTimeout(scrollToBottom, 100);
    }

    // Update session list
    setActiveChatSessions(prev => prev.map(session => {
      const belongsToSession = session.id === sessionId ||
        (session.session_ids && session.session_ids.includes(sessionId));

      if (belongsToSession) {
        return {
          ...session,
          last_message: newMessage.content.substring(0, 50) + (newMessage.content.length > 50 ? '...' : ''),
          last_message_time: formatTimeAgo(newMessage.created_at),
          unread_count: newMessage.sender_type === 'guest' && session.id !== selectedSession
            ? session.unread_count + 1
            : session.unread_count
        };
      }
      return session;
    }));

    // Show notification for guest messages
    if (newMessage.sender_type === 'guest' && document.hidden) {
      if (Notification.permission === 'granted') {
        new Notification('New message from guest', {
          body: newMessage.content.substring(0, 100),
          icon: '/favicon.ico'
        });
      }
    }
  }, [selectedSession, autoTranslate]);

  // ===== POLLING FALLBACK (simplified) =====
  const startPollingFallback = useCallback(() => {
    if (isPollingActive || realtimeConnected) return;

    addRealtimeLog('🔄 Starting polling fallback...');
    setIsPollingActive(true);

    pollingIntervalRef.current = setInterval(() => {
      if (!document.hidden && selectedSession) {
        checkForNewMessages();
      }
    }, 5000); // Poll every 5 seconds
  }, [isPollingActive, realtimeConnected, selectedSession]);

  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    setIsPollingActive(false);
    addRealtimeLog('⏹️ Polling stopped');
  }, []);

  const checkForNewMessages = useCallback(async () => {
    if (!selectedSession || !user) return;

    try {
      const response = await fetch(`/api/messages?session_id=${selectedSession}&limit=5`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.messages?.length > 0) {
          // Simple check for new messages by comparing with current messages
          const currentIds = new Set(messages.map(m => m.id));
          const newMessages = data.messages.filter((msg: any) => !currentIds.has(msg.id));
          
          if (newMessages.length > 0) {
            addRealtimeLog(`📥 Polling found ${newMessages.length} new messages`);
            // Transform and add new messages
            const transformed = newMessages.map((msg: any) => ({
              id: msg.id,
              content: msg.content,
              sender_type: msg.sender_type,
              sender_name: msg.sender_name || (msg.sender_type === 'guest' ? 'Guest' : 'Staff'),
              timestamp: msg.created_at,
              is_translated: msg.is_translated || false,
              show_translation: autoTranslate && msg.is_translated
            }));
            
            setMessages(prev => [...prev, ...transformed]);
            setTimeout(scrollToBottom, 100);
          }
        }
      }
    } catch (error) {
      addRealtimeLog(`❌ Polling error: ${error}`);
    }
  }, [selectedSession, user, messages, autoTranslate]);

  // ===== CLEANUP FUNCTION =====
  const cleanupConnections = useCallback(() => {
    addRealtimeLog('🧹 Cleaning up connections...');
    
    if (messagesChannelRef.current) {
      messagesChannelRef.current.unsubscribe();
      messagesChannelRef.current = null;
    }
    
    if (sessionsChannelRef.current) {
      sessionsChannelRef.current.unsubscribe();
      sessionsChannelRef.current = null;
    }
    
    stopPolling();
    setRealtimeConnected(false);
    setSubscriptionStatus('Disconnected');
  }, [stopPolling]);

  // ===== DATA LOADING FUNCTIONS =====
  const loadChatSessions = useCallback(async () => {
    if (!user) return;
    
    try {
      addRealtimeLog('📋 Loading chat sessions...');
      
      const response = await fetch(`/api/chat-sessions?tenant_id=${user.tenant_id}&status=active&limit=100`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.sessions) {
          // Group sessions by guest (same logic as before)
          const sessionGroups = new Map<string, any[]>();
          
          data.sessions.forEach((session: any) => {
            const groupKey = session.qr_info?.room_number || 
                            session.reception_point?.name || 
                            session.qr_info?.location || 
                            'unknown';
            
            if (!sessionGroups.has(groupKey)) {
              sessionGroups.set(groupKey, []);
            }
            sessionGroups.get(groupKey)!.push(session);
          });
          
          // Transform grouped sessions
          const transformedSessions: ChatSession[] = [];
          
          for (const [groupKey, sessions] of sessionGroups) {
            const latestSession = sessions.sort((a, b) => 
              new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
            )[0];
            
            const sessionIds = sessions.map(s => s.id);
            
            const transformedSession: ChatSession = {
              id: latestSession.id,
              guest_name: latestSession.qr_info?.room_number ?
                `Room ${latestSession.qr_info.room_number} Guest` :
                'Guest User',
              room_number: latestSession.qr_info?.room_number || undefined,
              language: latestSession.guest_language?.toUpperCase() || 'EN',
              status: latestSession.status as 'active' | 'pending' | 'waiting',
              priority: latestSession.priority as 'low' | 'normal' | 'high' | 'urgent',
              last_message: 'Loading messages...',
              last_message_time: formatTimeAgo(latestSession.updated_at),
              unread_count: 0,
              source: latestSession.qr_info?.location || latestSession.reception_point?.name || 'Direct',
              session_ids: sessionIds
            };
            
            transformedSessions.push(transformedSession);
          }
          
          setActiveChatSessions(transformedSessions);
          setFilteredChatSessions(transformedSessions);
          
          // Load message counts
          loadMessageCounts(transformedSessions);
          
          addRealtimeLog(`✅ Loaded ${transformedSessions.length} sessions`);
        }
      }
    } catch (error) {
      addRealtimeLog(`❌ Error loading sessions: ${error}`);
      setActiveChatSessions([]);
      setFilteredChatSessions([]);
    }
  }, [user]);

  const loadMessageCounts = async (sessions: ChatSession[]) => {
    for (const session of sessions) {
      try {
        const sessionIds = session.session_ids || [session.id];
        const allMessages: any[] = [];

        for (const sessionId of sessionIds) {
          const response = await fetch(`/api/messages?session_id=${sessionId}&limit=20`);
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.messages) {
              allMessages.push(...data.messages);
            }
          }
        }

        if (allMessages.length > 0) {
          allMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
          const lastMessage = allMessages[allMessages.length - 1];
          const unreadCount = session.id === selectedSession ? 0 : 
            allMessages.filter((msg: any) => msg.sender_type === 'guest').length;

          setActiveChatSessions(prev => prev.map(s =>
            s.id === session.id ? {
              ...s,
              last_message: lastMessage.content.substring(0, 50) + (lastMessage.content.length > 50 ? '...' : ''),
              last_message_time: formatTimeAgo(lastMessage.created_at),
              unread_count: unreadCount
            } : s
          ));
        }
      } catch (error) {
        addRealtimeLog(`❌ Error loading messages for session ${session.id}: ${error}`);
      }
    }
  };

  const loadMessages = async (sessionId: string) => {
    try {
      addRealtimeLog(`📥 Loading messages for session: ${sessionId}`);
      
      const sessionData = activeChatSessions.find(s => s.id === sessionId);
      const sessionIds = sessionData?.session_ids || [sessionId];
      
      const allMessages: any[] = [];
      
      for (const sId of sessionIds) {
        const response = await fetch(`/api/messages?session_id=${sId}&limit=100`);
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.messages) {
            allMessages.push(...data.messages);
          }
        }
      }
      
      allMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      
      const transformedMessages: Message[] = allMessages.map((msg: any) => ({
        id: msg.id,
        content: msg.translated_content && msg.show_translation ? 
          msg.translated_content : msg.content,
        original_content: msg.original_content || msg.content,
        translated_content: msg.translated_content,
        sender_type: msg.sender_type,
        sender_name: msg.sender_name || (msg.sender_type === 'guest' ? 'Guest' : 'Staff'),
        timestamp: msg.created_at,
        is_translated: msg.is_translated || false,
        original_language: msg.original_language,
        translated_language: msg.translated_language,
        translation_confidence: msg.translation_confidence,
        show_translation: autoTranslate && msg.is_translated
      }));
      
      setMessages(transformedMessages);
      
      // Mark as read
      setActiveChatSessions(prev => prev.map(session => 
        session.id === sessionId ? { ...session, unread_count: 0 } : session
      ));
      
      addRealtimeLog(`✅ Loaded ${transformedMessages.length} messages`);
      
    } catch (error) {
      addRealtimeLog(`❌ Error loading messages: ${error}`);
      setMessages([]);
    }
  };

  // ===== EVENT HANDLERS =====
  const handleChatSelect = (sessionId: string) => {
    setSelectedSession(sessionId);
    
    // Mark as read immediately
    setActiveChatSessions(prev => prev.map(session =>
      session.id === sessionId ? { ...session, unread_count: 0 } : session
    ));

    loadMessages(sessionId);
  };

  const handleSendMessage = async (content: string) => {
    if (!selectedSession || !user) return;

    try {
      addRealtimeLog(`📤 Sending message to session: ${selectedSession}`);
      
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: selectedSession,
          content: content.trim(),
          sender_type: 'staff',
          sender_name: user.display_name,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success && data.message) {
        addRealtimeLog('✅ Message sent successfully');
        
        // Add message to UI immediately (realtime will also trigger, but prevent duplicates)
        const newMessage: Message = {
          id: data.message.id,
          content: data.message.content,
          sender_type: 'staff',
          sender_name: user.display_name,
          timestamp: data.message.created_at,
          is_translated: false,
          show_translation: false
        };
        
        setMessages(prev => {
          if (prev.some(msg => msg.id === newMessage.id)) return prev;
          return [...prev, newMessage];
        });
        
        setTimeout(scrollToBottom, 100);
        
      } else {
        throw new Error(data.error || 'Failed to send message');
      }
    } catch (error) {
      addRealtimeLog(`❌ Send error: ${error}`);
      alert('Failed to send message: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  // ===== MAIN EFFECTS =====
  
  // Authentication check
  useEffect(() => {
    const token = localStorage.getItem('staff_token');
    const userData = localStorage.getItem('staff_user');

    if (!token || !userData) {
      router.push('/staff');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
    } catch (error) {
      console.error('Error parsing user data:', error);
      router.push('/staff');
    } finally {
      setLoading(false);
    }
  }, [router]);

  // Initialize everything when user is loaded
  useEffect(() => {
    if (user) {
      const success = initializeSupabase();
      if (success) {
        loadChatSessions();
        // Setup realtime after short delay
        setTimeout(() => {
          setupRealtimeSubscriptions();
        }, 1000);
      }
    }
  }, [user, initializeSupabase, loadChatSessions, setupRealtimeSubscriptions]);

  // Request notification permission
  useEffect(() => {
    if (user && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        addRealtimeLog(`Notification permission: ${permission}`);
      });
    }
  }, [user]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupConnections();
    };
  }, [cleanupConnections]);

  // ===== OTHER HANDLERS (unchanged) =====
  const handleLogout = () => {
    cleanupConnections();
    localStorage.removeItem('staff_token');
    localStorage.removeItem('staff_user');
    router.push('/staff');
  };

  const handleStatusChange = (status: 'online' | 'busy' | 'away') => {
    setOnlineStatus(status);
  };

  const handleToggleTranslation = () => {
    setAutoTranslate(!autoTranslate);
    setMessages(prev =>
      prev.map(msg => ({
        ...msg,
        show_translation: !autoTranslate
      }))
    );
  };

  const handleTransferChat = () => {
    alert('Chat transfer feature coming soon!');
  };

  const handleEndChat = () => {
    if (selectedSession && confirm('Are you sure you want to end this chat?')) {
      setActiveChatSessions(sessions =>
        sessions.filter(session => session.id !== selectedSession)
      );
      setSelectedSession(null);
      setMessages([]);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    applyFiltersAndSearch(activeChatSessions, query, chatFilters);
  };

  const handleFilter = (filters: typeof chatFilters) => {
    setChatFilters(filters);
    applyFiltersAndSearch(activeChatSessions, searchQuery, filters);
  };

  const applyFiltersAndSearch = (
    sessions: ChatSession[], 
    query: string, 
    filters: typeof chatFilters
  ) => {
    let filtered = [...sessions];

    if (query.trim()) {
      const lowerQuery = query.toLowerCase();
      filtered = filtered.filter(session =>
        session.guest_name.toLowerCase().includes(lowerQuery) ||
        session.room_number?.toLowerCase().includes(lowerQuery) ||
        session.source.toLowerCase().includes(lowerQuery) ||
        session.last_message.toLowerCase().includes(lowerQuery)
      );
    }

    if (filters.status !== 'all') {
      filtered = filtered.filter(session => session.status === filters.status);
    }

    if (filters.priority !== 'all') {
      filtered = filtered.filter(session => session.priority === filters.priority);
    }

    if (filters.language !== 'all') {
      filtered = filtered.filter(session => 
        session.language.toLowerCase() === filters.language
      );
    }

    if (filters.unreadOnly) {
      filtered = filtered.filter(session => session.unread_count > 0);
    }

    setFilteredChatSessions(filtered);
  };

  const handleNotificationClick = (notification: any) => {
    console.log('Notification clicked:', notification);
    
    if (notification.type === 'new_message' || notification.type === 'new_chat') {
      const relatedSession = activeChatSessions.find(session => 
        notification.message.includes(session.guest_name) ||
        notification.message.includes(session.room_number)
      );
      
      if (relatedSession) {
        handleChatSelect(relatedSession.id);
      }
    }
  };

  const handleQuickAction = (action: string) => {
    console.log('Quick action:', action);
    
    switch (action) {
      case 'broadcast':
        alert('Broadcast message feature coming soon!');
        break;
      case 'templates':
        alert('Quick templates feature coming soon!');
        break;
      case 'transfer':
        alert('Transfer all chats feature coming soon!');
        break;
      case 'reports':
        alert('Generate report feature coming soon!');
        break;
      case 'settings':
        alert('Chat settings feature coming soon!');
        break;
      case 'break':
        handleStatusChange('away');
        break;
      default:
        console.log('Unknown action:', action);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '#ef4444';
      case 'high': return '#f97316';
      case 'normal': return '#3b82f6';
      case 'low': return '#6b7280';
      default: return '#6b7280';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#10b981';
      case 'pending': return '#f59e0b';
      case 'waiting': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const selectedSessionData = activeChatSessions.find(s => s.id === selectedSession);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading Dashboard...</p>
      </div>
    );
  }

  return (
    <div className={styles.dashboard}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerLeft}>
          <div className={styles.logo}>
            <h1>Staff Dashboard</h1>
          </div>
        </div>

        <div className={styles.headerCenter}>
          <StaffStatus initialStatus={onlineStatus} onStatusChange={handleStatusChange} />
          
          {/* Realtime status indicator */}
          <div style={{ marginLeft: '1rem', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <div style={{ 
              width: '8px', 
              height: '8px', 
              borderRadius: '50%', 
              backgroundColor: realtimeConnected ? '#10b981' : (isPollingActive ? '#f59e0b' : '#ef4444') 
            }}></div>
            <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
              {realtimeConnected ? '🔴 Real-time' : (isPollingActive ? '🟡 Polling' : '⚫ Offline')}
            </span>
            <span style={{ fontSize: '0.75rem', color: '#9ca3af' }}>
              ({subscriptionStatus})
            </span>
          </div>
        </div>

        <div className={styles.headerRight}>
          <NotificationCenter onNotificationClick={handleNotificationClick} />
          
          <div className={styles.userInfo}>
            <div className={styles.userAvatar}>
              {user?.display_name?.charAt(0) || 'S'}
            </div>
            <div className={styles.userDetails}>
              <span className={styles.userName}>{user?.display_name}</span>
              <span className={styles.userRole}>{user?.title || user?.role}</span>
            </div>
          </div>
          <button onClick={handleLogout} className={styles.logoutButton}>
            Logout
          </button>
        </div>
      </header>

      {/* Main Content */}
      <div className={styles.mainContent}>
                {/* Sidebar - Chat Sessions */}
        <aside className={styles.sidebar}>
          <div className={styles.sidebarHeader}>
            <h2>Active Chats</h2>
            <span className={styles.chatCount}>{filteredChatSessions.length}</span>
          </div>
          
          <ChatSearch
            onSearch={handleSearch}
            onFilter={handleFilter}
            totalChats={filteredChatSessions.length}
            activeFilters={chatFilters}
          />

          <div className={styles.chatList}>
            {filteredChatSessions.map((session) => (
              <div 
                key={session.id} 
                className={`${styles.chatItem} ${selectedSession === session.id ? styles.selected : ''} ${session.unread_count > 0 ? styles.hasUnread : ''}`} 
                onClick={() => handleChatSelect(session.id)}
              >
                {/* Unread indicator */}
                {session.unread_count > 0 && (
                  <div className={styles.unreadIndicator}>
                    {session.unread_count}
                  </div>
                )}
                
                <div className={styles.chatItemHeader}>
                  <div className={styles.guestInfo}>
                    <span className={styles.guestName}>{session.guest_name}</span>
                    <span className={styles.roomInfo}>
                      {session.room_number ? `Room ${session.room_number}` : session.source}
                    </span>
                  </div>
                  <div className={styles.chatMeta}>
                    <span 
                      className={styles.priority}
                      style={{ color: getPriorityColor(session.priority) }}
                    >
                      {session.priority.toUpperCase()}
                    </span>
                    <span className={styles.language}>{session.language}</span>
                  </div>
                </div>

                <div className={styles.lastMessage}>
                  {session.last_message}
                </div>

                <div className={styles.chatItemFooter}>
                  <span className={styles.time}>{session.last_message_time}</span>
                  <div className={styles.badges}>
                    <span 
                      className={styles.status}
                      style={{ backgroundColor: getStatusColor(session.status) }}
                    >
                      {session.status}
                    </span>
                    {session.unread_count > 0 && (
                      <span className={styles.unreadBadge}>
                        {session.unread_count}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {filteredChatSessions.length === 0 && activeChatSessions.length > 0 && (
              <div className={styles.noResults}>
                <div className={styles.noResultsIcon}></div>
                <p>No chats match your search criteria</p>
                <button 
                  className={styles.clearFiltersButton}
                  onClick={() => {
                    setSearchQuery('');
                    setChatFilters({
                      status: 'all',
                      priority: 'all', 
                      language: 'all',
                      timeRange: 'all',
                      unreadOnly: false
                    });
                    setFilteredChatSessions(activeChatSessions);
                  }}
                >
                  Clear all filters
                </button>
              </div>
            )}

            {activeChatSessions.length === 0 && (
              <div className={styles.noChats}>
                <div className={styles.noChatsIcon}>💬</div>
                <p>No active chats</p>
                <small>New guest messages will appear here</small>
              </div>
            )}
          </div>
        </aside>

        {/* Chat Area */}
        <main className={styles.chatArea}>
          {selectedSession ? (
            <>
              {/* Chat Header */}
              <div className={styles.chatHeader}>
                <div className={styles.chatHeaderLeft}>
                  <div className={styles.guestAvatar}>
                    {selectedSessionData?.guest_name?.charAt(0) || 'G'}
                  </div>
                  <div className={styles.guestDetails}>
                    <h3>{selectedSessionData?.guest_name}</h3>
                    <div className={styles.guestMeta}>
                      <span className={styles.guestRoom}>
                        {selectedSessionData?.room_number ? 
                          `Room ${selectedSessionData.room_number}` : 
                          selectedSessionData?.source
                        }
                      </span>
                      <span 
                        className={styles.guestStatus}
                        style={{ color: getStatusColor(selectedSessionData?.status || 'active') }}
                      >
                        {selectedSessionData?.status}
                      </span>
                      <span className={styles.guestLanguage}>
                        {selectedSessionData?.language}
                      </span>
                    </div>
                  </div>
                </div>

                <div className={styles.chatHeaderRight}>
                  <button 
                    className={`${styles.translationToggle} ${autoTranslate ? styles.active : ''}`}
                    onClick={handleToggleTranslation}
                    title="Toggle auto-translation"
                  >
                    🌐 {autoTranslate ? 'ON' : 'OFF'}
                  </button>
                  
                  <button 
                    className={styles.transferButton}
                    onClick={handleTransferChat}
                    title="Transfer chat"
                  >
                    👥
                  </button>
                  
                  <button 
                    className={styles.endChatButton}
                    onClick={handleEndChat}
                    title="End chat"
                  >
                    ❌
                  </button>
                </div>
              </div>

              {/* Messages Area */}
              <div className={styles.messagesArea}>
                <div className={styles.messagesList}>
                  {messages.map((message) => (
                    <ChatMessage
                      key={message.id}
                      message={message}
                      autoTranslate={autoTranslate}
                    />
                  ))}
                  
                  {isTyping && (
                    <div className={styles.typingIndicator}>
                      <div className={styles.typingDots}>
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                      <span>Guest is typing...</span>
                    </div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>
              </div>

              {/* Chat Input */}
              <div className={styles.chatInputArea}>
                <ChatInput
                  onSendMessage={handleSendMessage}
                  disabled={false}
                  placeholder={`Message ${selectedSessionData?.guest_name}...`}
                />
              </div>
            </>
          ) : (
            /* No Chat Selected */
            <div className={styles.noChatSelected}>
              <div className={styles.noChatIcon}>💬</div>
              <h3>Select a chat to start messaging</h3>
              <p>Choose a conversation from the sidebar to view and respond to messages.</p>
              
              {/* Connection Status */}
              <div className={styles.connectionStatus}>
                <div className={styles.statusItem}>
                  <span className={styles.statusLabel}>Connection:</span>
                  <span className={`${styles.statusValue} ${realtimeConnected ? styles.connected : styles.disconnected}`}>
                    {realtimeConnected ? '✅ Real-time Active' : (isPollingActive ? '🟡 Polling Active' : '❌ Disconnected')}
                  </span>
                </div>
                <div className={styles.statusItem}>
                  <span className={styles.statusLabel}>Status:</span>
                  <span className={styles.statusValue}>{subscriptionStatus}</span>
                </div>
                <div className={styles.statusItem}>
                  <span className={styles.statusLabel}>Active Chats:</span>
                  <span className={styles.statusValue}>{activeChatSessions.length}</span>
                </div>
              </div>
            </div>
          )}
        </main>

        {/* Right Sidebar - Stats & Quick Actions */}
        <aside className={styles.rightSidebar}>
          {/* Staff Status */}
          <div className={styles.statsSection}>
            <h3>Dashboard Stats</h3>
            <ChatStats 
              totalChats={activeChatSessions.length}
              unreadCount={activeChatSessions.reduce((sum, session) => sum + session.unread_count, 0)}
              onlineStatus={onlineStatus}
            />
          </div>

          {/* Quick Actions */}
          <div className={styles.quickActionsSection}>
            <h3>Quick Actions</h3>
            <QuickActions onAction={handleQuickAction} />
          </div>

          {/* Connection Debug Info (only in development) */}
          {process.env.NODE_ENV === 'development' && (
            <div className={styles.debugSection}>
              <h4>🔧 Debug Info</h4>
              <div className={styles.debugInfo}>
                <div className={styles.debugItem}>
                  <span>Realtime:</span>
                  <span className={realtimeConnected ? styles.connected : styles.disconnected}>
                    {realtimeConnected ? '✅' : '❌'}
                  </span>
                </div>
                <div className={styles.debugItem}>
                  <span>Polling:</span>
                  <span className={isPollingActive ? styles.active : styles.inactive}>
                    {isPollingActive ? '🔄' : '⏸️'}
                  </span>
                </div>
                <div className={styles.debugItem}>
                  <span>Status:</span>
                  <span>{subscriptionStatus}</span>
                </div>
                <div className={styles.debugItem}>
                  <span>User:</span>
                  <span>{user?.display_name}</span>
                </div>
                <div className={styles.debugItem}>
                  <span>Tenant:</span>
                  <span>{user?.tenant_id}</span>
                </div>
              </div>
              
              {/* Manual refresh button for testing */}
              <button 
                className={styles.debugButton}
                onClick={() => {
                  addRealtimeLog('🔄 Manual refresh triggered');
                  loadChatSessions();
                }}
              >
                🔄 Refresh Sessions
              </button>
              
              {/* Reconnect button */}
              <button 
                className={styles.debugButton}
                onClick={() => {
                  addRealtimeLog('🔄 Manual reconnect triggered');
                  cleanupConnections();
                  setTimeout(() => {
                    setupRealtimeSubscriptions();
                  }, 1000);
                }}
              >
                🔌 Reconnect
              </button>
            </div>
          )}
        </aside>
      </div>
    </div>
  );
}
