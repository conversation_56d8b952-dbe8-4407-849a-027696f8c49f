'use client';

import React, { useState } from 'react';
import { DashboardLayout, Button, Card, Badge, Tabs } from '@loaloa/ui';
import { HomeIcon, BuildingIcon, UsersIcon, LicenseIcon, SettingsIcon } from '@loaloa/ui/src/components/Icons/icons';
import { useParams } from 'next/navigation';
import styles from './licenseDetail.module.scss';
import useSWR from 'swr';

// Components
import LicenseStatusBadge from '../../../components/licenses/LicenseStatusBadge';
import LicenseActivityTable from '../../../components/licenses/LicenseActivityTable';
import ExtendLicenseModal from '../../../components/licenses/ExtendLicenseModal';
import RevokeLicenseModal from '../../../components/licenses/RevokeLicenseModal';
import ActivateLicenseModal from '../../../components/licenses/ActivateLicenseModal';

// Hooks
import { useLicense } from '../../../hooks/useLicenses';

export default function LicenseDetail() {
  const params = useParams();
  //const router = useRouter();
  const licenseId = params?.id as string;

  // State
  const [activeTab, setActiveTab] = useState('details');
  const [showExtendModal, setShowExtendModal] = useState(false);
  const [showRevokeModal, setShowRevokeModal] = useState(false);
  const [showActivateModal, setShowActivateModal] = useState(false);

  // Fetch license data directly with SWR instead of hook to ensure data freshness
  const { data, error, isLoading, mutate } = useSWR<{ data: any }>(
    `/api/licenses/${licenseId}`,
    async (url) => {
      const response = await fetch(url);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch license');
      }
      return response.json();
    }
  );

  const license = data?.data;

  // Sidebar items
  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', href: '/', icon: <HomeIcon /> },
    { id: 'tenants', label: 'Tenants', href: '/tenants', icon: <BuildingIcon /> },
    { id: 'users', label: 'Users', href: '/users', icon: <UsersIcon /> },
    { id: 'licenses', label: 'Licenses', href: '/licenses', icon: <LicenseIcon />, active: true },
    { id: 'settings', label: 'Settings', href: '/settings', icon: <SettingsIcon /> },
  ];

  // Handle license update
  const handleLicenseUpdate = (updatedLicense: any) => {
    mutate({ data: updatedLicense }, false);
  };

  if (isLoading) {
    return (
      <DashboardLayout
        sidebarItems={sidebarItems}
        title="License Details"
        username="Admin User"
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Licenses', href: '/licenses' },
          { label: 'Loading...' }
        ]}
      >
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>Loading license details...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !license) {
    return (
      <DashboardLayout
        sidebarItems={sidebarItems}
        title="License Not Found"
        username="Admin User"
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Licenses', href: '/licenses' },
          { label: 'Not Found' }
        ]}
      >
        <div className={styles.errorContainer}>
          <h2>License Not Found</h2>
          <p>The license you are looking for does not exist or has been removed.</p>
          <Button
            variant="primary"
			label="Return to Licenses"
            onClick={() => router.push('/licenses')}
          >
            Return to Licenses
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      sidebarItems={sidebarItems}
      title={`License: ${license.license_key}`}
      username="Admin User"
      breadcrumbs={[
        { label: 'Dashboard', href: '/' },
        { label: 'Licenses', href: '/licenses' },
        { label: license.license_key }
      ]}
    >
      <div className={styles.licenseDetail}>
        {/* Header */}
        <div className={styles.header}>
          <div className={styles.licenseInfo}>
            <h1 className={styles.licenseKey}>{license.license_key}</h1>
            <div className={styles.licenseMeta}>
              <LicenseStatusBadge status={license.status} />
              <span className={styles.customerName}>{license.customer_name}</span>
            </div>
          </div>
          <div className={styles.headerActions}>
            {license.is_active ? (
              <>
                <Button
                  variant="outline"
				  label="Extend License"				  
                  onClick={() => setShowExtendModal(true)}
                >
                  Extend License
                </Button>
                <Button
                  variant="danger"
				  label="Revoke License"
                  onClick={() => setShowRevokeModal(true)}
                >
                  Revoke License
                </Button>
              </>
            ) : (
              <Button
                variant="primary"
				label="Activate License"
                onClick={() => setShowActivateModal(true)}
              >
                Activate License
              </Button>
            )}
          </div>
        </div>

       <div className={styles.tabsContainer}>
  <div className={styles.tabButtons}>
    <button 
      className={`${styles.tabButton} ${activeTab === 'details' ? styles.activeTab : ''}`} 
      onClick={() => setActiveTab('details')}
    >
      Details
    </button>
    <button 
      className={`${styles.tabButton} ${activeTab === 'activity' ? styles.activeTab : ''}`}
      onClick={() => setActiveTab('activity')}
    >
      Activity
    </button>
    <button 
      className={`${styles.tabButton} ${activeTab === 'clones' ? styles.activeTab : ''}`}
      onClick={() => setActiveTab('clones')}
    >
      Clone Alerts
      {license.unreviewed_clone_alerts > 0 && (
        <span className={styles.alertBadge}>
          {license.unreviewed_clone_alerts}
        </span>
      )}
    </button>
  </div>

          <div className={styles.tabContent}>
            {activeTab === 'details' && (
              <Card>
                <div className={styles.detailsGrid}>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Customer Name</span>
                    <span className={styles.detailValue}>{license.customer_name}</span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>License Key</span>
                    <span className={styles.detailValue}>
                      <code className={styles.codeBlock}>{license.license_key}</code>
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Status</span>
                    <span className={styles.detailValue}>
                      <LicenseStatusBadge status={license.status} />
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Issue Date</span>
                    <span className={styles.detailValue}>
                      {new Date(license.issue_date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Expiry Date</span>
                    <span className={styles.detailValue}>
                      {new Date(license.expiry_date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Days Remaining</span>
                    <span className={styles.detailValue}>
                      {license.days_remaining > 0 ? license.days_remaining : 'Expired'}
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Activation Date</span>
                    <span className={styles.detailValue}>
                      {license.activation_date 
                        ? new Date(license.activation_date).toLocaleDateString()
                        : 'Not Activated'
                      }
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Last Check-in</span>
                    <span className={styles.detailValue}>
                      {license.last_check_in
                        ? new Date(license.last_check_in).toLocaleString()
                        : 'Never'
                      }
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Check-in Count</span>
                    <span className={styles.detailValue}>{license.check_in_count}</span>
                  </div>
                  {!license.is_active && license.revocation_reason && (
                    <div className={`${styles.detailItem} ${styles.fullWidth}`}>
                      <span className={styles.detailLabel}>Revocation Reason</span>
                      <span className={`${styles.detailValue} ${styles.revocationReason}`}>
                        {license.revocation_reason}
                      </span>
                    </div>
                  )}
                  {license.hardware_fingerprint && (
                    <div className={`${styles.detailItem} ${styles.fullWidth}`}>
                      <span className={styles.detailLabel}>Hardware Fingerprint</span>
                      <span className={styles.detailValue}>
                        <code className={styles.fingerprint}>{license.hardware_fingerprint}</code>
                      </span>
                    </div>
                  )}
                  {license.metadata && (
                    <div className={`${styles.detailItem} ${styles.fullWidth}`}>
                      <span className={styles.detailLabel}>Metadata</span>
                      <div className={styles.metadataContainer}>
                        <pre className={styles.metadataCode}>
                          {JSON.stringify(license.metadata, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            )}
            
            {activeTab === 'activity' && (
              <Card>
                <div className={styles.activityContainer}>
                  <h3 className={styles.sectionTitle}>Activity Log</h3>
                  <LicenseActivityTable licenseId={license.id} />
                </div>
              </Card>
            )}
            
            {activeTab === 'clones' && (
              <Card>
                {license.clone_alerts > 0 ? (
                  <div className={styles.cloneAlerts}>
                    <h3 className={styles.sectionTitle}>Clone Alerts</h3>
                    <p>This license has {license.clone_alerts} potential clone detection(s).</p>
                    <p>Please review and take appropriate action.</p>
                    {/* TODO: Implement clone alerts table */}
                  </div>
                ) : (
                  <div className={styles.noCloneAlerts}>
                    <h3 className={styles.sectionTitle}>No Clone Alerts</h3>
                    <p>No potential license clones have been detected.</p>
                  </div>
                )}
              </Card>
            )}
          </div>
        </div>
      </div>
      
      {/* Modals */}
      {showExtendModal && (
        <ExtendLicenseModal
          licenseId={license.id}
          currentExpiryDate={license.expiry_date}
          onClose={() => setShowExtendModal(false)}
          onSuccess={(updatedLicense) => {
            setShowExtendModal(false);
            handleLicenseUpdate(updatedLicense);
          }}
        />
      )}
      
      {showRevokeModal && (
        <RevokeLicenseModal
          licenseId={license.id}
          licenseKey={license.license_key}
          onClose={() => setShowRevokeModal(false)}
          onSuccess={(updatedLicense) => {
            setShowRevokeModal(false);
            handleLicenseUpdate(updatedLicense);
          }}
        />
      )}
      
      {showActivateModal && (
        <ActivateLicenseModal
          licenseId={license.id}
          licenseKey={license.license_key}
          onClose={() => setShowActivateModal(false)}
          onSuccess={(updatedLicense) => {
            setShowActivateModal(false);
            handleLicenseUpdate(updatedLicense);
          }}
        />
      )}
    </DashboardLayout>
  );
}