'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { DashboardLayout, Button, Card, Form, Table, Tabs } from '@loaloa/ui';
import { BuildingIcon, HomeIcon, UsersIcon, LicenseIcon, SettingsIcon } from '@loaloa/ui/src/components/Icons/icons';
import styles from './tenant-detail.module.scss';

// Mock data - sẽ thay thế bằng dữ liệu thật sau
const TENANT_DATA = {
  '1': {
    id: '1',
    name: 'Hotel California',
    domain: 'hotel-california.loaloa.app',
    contact_email: '<EMAIL>',
    contact_phone: '+****************',
    address: '123 Ocean Drive, Los Angeles, CA 90210',
    plan: 'premium',
    status: 'active',
    created_at: '2025-04-01T10:30:00Z',
    updated_at: '2025-05-10T14:45:00Z',
    primary_color: '#E54D42',
    logo_url: 'https://placehold.co/200x100?text=Hotel+California',
    users: [
      { id: '101', email: '<EMAIL>', role: 'owner', status: 'active', last_login: '2025-05-12T08:45:00Z' },
      { id: '102', email: '<EMAIL>', role: 'admin', status: 'active', last_login: '2025-05-11T14:30:00Z' },
      { id: '103', email: '<EMAIL>', role: 'staff', status: 'active', last_login: '2025-05-10T09:15:00Z' },
      { id: '104', email: '<EMAIL>', role: 'staff', status: 'inactive', last_login: '2025-04-28T11:20:00Z' },
    ],
    license: {
      id: 'L101',
      type: 'premium',
      starts_at: '2025-04-01T00:00:00Z',
      expires_at: '2026-04-01T00:00:00Z',
      user_limit: 30,
      storage_limit: **********0, // 10 GB in bytes
      payment_status: 'paid',
      payment_method: 'credit_card',
      billing_cycle: 'yearly',
      price: 999.99,
      currency: 'USD',
      features: {
        translation_languages: 20,
        ai_assistant: true,
        custom_branding: true,
        analytics: true
      }
    }
  },
  // Thêm dữ liệu cho các tenant khác
};

export default function TenantDetail() {
  const params = useParams();
  const tenantId = params?.id as string;
  
  const [tenant, setTenant] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('details');

  // Form fields
  const [name, setName] = useState('');
  const [domain, setDomain] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [address, setAddress] = useState('');
  const [color, setColor] = useState('');
  const [status, setStatus] = useState('active');

  useEffect(() => {
    // Giả lập API call
    setTimeout(() => {
      const tenantData = TENANT_DATA[tenantId];
      setTenant(tenantData);
      
      // Populate form fields
      if (tenantData) {
        setName(tenantData.name);
        setDomain(tenantData.domain);
        setEmail(tenantData.contact_email);
        setPhone(tenantData.contact_phone);
        setAddress(tenantData.address);
        setColor(tenantData.primary_color);
        setStatus(tenantData.status);
      }
      
      setIsLoading(false);
    }, 1000);
  }, [tenantId]);

  const handleSubmit = (e) => {
    e.preventDefault();
    // In a real app, this would update the tenant via API
    setTenant({
      ...tenant,
      name,
      domain,
      contact_email: email,
      contact_phone: phone,
      address,
      primary_color: color,
      status,
      updated_at: new Date().toISOString()
    });
    setIsEditing(false);
  };

  const handleCancel = () => {
    // Reset form fields to tenant data
    setName(tenant.name);
    setDomain(tenant.domain);
    setEmail(tenant.contact_email);
    setPhone(tenant.contact_phone);
    setAddress(tenant.address);
    setColor(tenant.primary_color);
    setStatus(tenant.status);
    setIsEditing(false);
  };

  const sidebarItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      href: '/',
      icon: <HomeIcon />,
    },
    {
      id: 'tenants',
      label: 'Tenants',
      href: '/tenants',
      icon: <BuildingIcon />,
      active: true,
    },
    {
      id: 'users',
      label: 'Users',
      href: '/users',
      icon: <UsersIcon />,
    },
    {
      id: 'licenses',
      label: 'Licenses',
      href: '/licenses',
      icon: <LicenseIcon />,
    },
    {
      id: 'settings',
      label: 'Settings',
      href: '/settings',
      icon: <SettingsIcon />,
    },
  ];

  const userColumns = [
    {
      header: 'Email',
      accessor: 'email',
    },
    {
      header: 'Role',
      accessor: 'role',
      width: '15%',
    },
    {
      header: 'Status',
      accessor: (user) => {
        const statusClass = `status-badge status-${user.status}`;
        return <span className={statusClass}>{user.status}</span>;
      },
      width: '15%',
    },
    {
      header: 'Last Login',
      accessor: (user) => new Date(user.last_login).toLocaleString(),
      width: '25%',
    },
    {
      header: 'Actions',
      accessor: (user) => (
        <div className={styles.actions}>
          <Button size="small" variant="outline">Edit</Button>
          {user.status === 'active' ? (
            <Button size="small" variant="danger">Deactivate</Button>
          ) : (
            <Button size="small" variant="success">Activate</Button>
          )}
        </div>
      ),
      width: '20%',
    },
  ];

  if (isLoading) {
    return (
      <DashboardLayout
        sidebarItems={sidebarItems}
        title="Tenant Details"
        username="Admin User"
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Tenants', href: '/tenants' },
          { label: 'Loading...' }
        ]}
      >
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading tenant details...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!tenant) {
    return (
      <DashboardLayout
        sidebarItems={sidebarItems}
        title="Tenant Not Found"
        username="Admin User"
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Tenants', href: '/tenants' },
          { label: 'Not Found' }
        ]}
      >
        <div className={styles.notFound}>
          <h2>Tenant Not Found</h2>
          <p>The tenant you are looking for does not exist or has been removed.</p>
          <Button variant="primary" onClick={() => window.location.href = '/tenants'}>
            Return to Tenants
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      sidebarItems={sidebarItems}
      title={tenant.name}
      username="Admin User"
      breadcrumbs={[
        { label: 'Dashboard', href: '/' },
        { label: 'Tenants', href: '/tenants' },
        { label: tenant.name }
      ]}
    >
      <div className={styles.tenantDetail}>
        <div className={styles.header}>
          <div className={styles.tenantInfo}>
            {tenant.logo_url && (
              <div className={styles.logoContainer}>
                <img src={tenant.logo_url} alt={tenant.name} className={styles.logo} />
              </div>
            )}
            <div>
              <h1 className={styles.tenantName}>{tenant.name}</h1>
              <div className={styles.tenantMeta}>
                <span className={`status-badge status-${tenant.status}`}>{tenant.status}</span>
                <span className={`${styles.planBadge} ${styles[tenant.plan]}`}>{tenant.plan}</span>
                <span className={styles.domainBadge}>{tenant.domain}</span>
              </div>
            </div>
          </div>
          <div className={styles.headerActions}>
            {!isEditing ? (
              <Button variant="primary" onClick={() => setIsEditing(true)}>Edit Tenant</Button>
            ) : (
              <div className={styles.formActions}>
                <Button variant="outline" onClick={handleCancel}>Cancel</Button>
                <Button variant="primary" onClick={handleSubmit}>Save Changes</Button>
              </div>
            )}
          </div>
        </div>

        <div className={styles.tabContainer}>
          <div className={styles.tabHeader}>
            <button 
              className={`${styles.tabButton} ${activeTab === 'details' ? styles.active : ''}`}
              onClick={() => setActiveTab('details')}
            >
              Details
            </button>
            <button 
              className={`${styles.tabButton} ${activeTab === 'users' ? styles.active : ''}`}
              onClick={() => setActiveTab('users')}
            >
              Users
            </button>
            <button 
              className={`${styles.tabButton} ${activeTab === 'license' ? styles.active : ''}`}
              onClick={() => setActiveTab('license')}
            >
              License
            </button>
            <button 
              className={`${styles.tabButton} ${activeTab === 'settings' ? styles.active : ''}`}
              onClick={() => setActiveTab('settings')}
            >
              Settings
            </button>
          </div>

          <div className={styles.tabContent}>
            {activeTab === 'details' && (
              <Card>
                <h2 className={styles.sectionTitle}>Tenant Information</h2>
                <form className={styles.tenantForm} onSubmit={handleSubmit}>
                  <div className={styles.formGrid}>
                    <Form.FormGroup label="Tenant Name" htmlFor="name">
                      <Form.Input
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        disabled={!isEditing}
                        required
                      />
                    </Form.FormGroup>
                    
                    <Form.FormGroup label="Domain" htmlFor="domain">
                      <Form.Input
                        id="domain"
                        value={domain}
                        onChange={(e) => setDomain(e.target.value)}
                        disabled={!isEditing}
                      />
                    </Form.FormGroup>
                    
                    <Form.FormGroup label="Contact Email" htmlFor="email">
                      <Form.Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={!isEditing}
                        required
                      />
                    </Form.FormGroup>
                    
                    <Form.FormGroup label="Contact Phone" htmlFor="phone">
                      <Form.Input
                        id="phone"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        disabled={!isEditing}
                      />
                    </Form.FormGroup>
                    
                    <Form.FormGroup label="Address" htmlFor="address" className={styles.fullWidth}>
                      <Form.Input
                        id="address"
                        value={address}
                        onChange={(e) => setAddress(e.target.value)}
                        disabled={!isEditing}
                      />
                    </Form.FormGroup>
                    
                    <Form.FormGroup label="Primary Color" htmlFor="color">
                      <div className={styles.colorField}>
                        <Form.Input
                          id="color"
                          value={color}
                          onChange={(e) => setColor(e.target.value)}
                          disabled={!isEditing}
                        />
                        <div 
                          className={styles.colorPreview}
                          style={{ backgroundColor: color }}
                        ></div>
                      </div>
                    </Form.FormGroup>
                    
                    <Form.FormGroup label="Status" htmlFor="status">
                      <Form.Select
                        id="status"
                        value={status}
                        onChange={(e) => setStatus(e.target.value)}
                        disabled={!isEditing}
                        options={[
                          { value: 'active', label: 'Active' },
                          { value: 'inactive', label: 'Inactive' },
                          { value: 'pending', label: 'Pending' }
                        ]}
                      />
                    </Form.FormGroup>
                  </div>
                </form>
                
                <div className={styles.metaInfo}>
                  <div className={styles.metaItem}>
                    <span className={styles.metaLabel}>Created:</span>
                    <span>{new Date(tenant.created_at).toLocaleString()}</span>
                  </div>
                  <div className={styles.metaItem}>
                    <span className={styles.metaLabel}>Last Updated:</span>
                    <span>{new Date(tenant.updated_at).toLocaleString()}</span>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 'users' && (
              <Card>
                <div className={styles.sectionHeader}>
                  <h2 className={styles.sectionTitle}>Users</h2>
                  <Button size="small" variant="primary">Add User</Button>
                </div>
                <Table
                  columns={userColumns}
                  data={tenant.users}
                  keyExtractor={(item) => item.id}
                />
              </Card>
            )}

            {activeTab === 'license' && (
              <Card>
                <h2 className={styles.sectionTitle}>License Details</h2>
                <div className={styles.licenseDetails}>
                  <div className={styles.licenseSection}>
                    <h3>Plan Information</h3>
                    <div className={styles.licenseInfo}>
                      <div className={styles.licenseItem}>
                        <span className={styles.licenseLabel}>License Type:</span>
                        <span className={`${styles.licenseValue} ${styles[tenant.license.type]}`}>
                          {tenant.license.type.charAt(0).toUpperCase() + tenant.license.type.slice(1)}
                        </span>
                      </div>
                      <div className={styles.licenseItem}>
                        <span className={styles.licenseLabel}>User Limit:</span>
                        <span className={styles.licenseValue}>{tenant.license.user_limit} users</span>
                      </div>
                      <div className={styles.licenseItem}>
                        <span className={styles.licenseLabel}>Storage Limit:</span>
                        <span className={styles.licenseValue}>
                          {(tenant.license.storage_limit / **********).toFixed(0)} GB
                        </span>
                      </div>
                      <div className={styles.licenseItem}>
                        <span className={styles.licenseLabel}>Valid From:</span>
                        <span className={styles.licenseValue}>
                          {new Date(tenant.license.starts_at).toLocaleDateString()}
                        </span>
                      </div>
                      <div className={styles.licenseItem}>
                        <span className={styles.licenseLabel}>Expires On:</span>
                        <span className={styles.licenseValue}>
                          {new Date(tenant.license.expires_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className={styles.licenseSection}>
                    <h3>Billing Information</h3>
                    <div className={styles.licenseInfo}>
                      <div className={styles.licenseItem}>
                        <span className={styles.licenseLabel}>Payment Status:</span>
                        <span className={styles.licenseValue}>{tenant.license.payment_status}</span>
                      </div>
                      <div className={styles.licenseItem}>
                        <span className={styles.licenseLabel}>Payment Method:</span>
                        <span className={styles.licenseValue}>{tenant.license.payment_method}</span>
                      </div>
                      <div className={styles.licenseItem}>
                        <span className={styles.licenseLabel}>Billing Cycle:</span>
                        <span className={styles.licenseValue}>{tenant.license.billing_cycle}</span>
                      </div>
                      <div className={styles.licenseItem}>
                        <span className={styles.licenseLabel}>Price:</span>
                        <span className={styles.licenseValue}>
                          {tenant.license.price} {tenant.license.currency}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className={styles.licenseSection}>
                    <h3>Features</h3>
                    <div className={styles.featureList}>
                      {Object.entries(tenant.license.features).map(([key, value]) => (
                        <div key={key} className={styles.featureItem}>
                          <span className={styles.featureName}>
                            {key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}:
                          </span>
                          <span className={styles.featureValue}>
                            {typeof value === 'boolean' ? 
                              value ? 'Enabled' : 'Disabled' : 
                              value
                            }
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className={styles.licenseCta}>
                  <Button variant="outline">Renew License</Button>
                  <Button variant="outline">Upgrade Plan</Button>
                </div>
              </Card>
            )}

            {activeTab === 'settings' && (
              <Card>
                <h2 className={styles.sectionTitle}>Tenant Settings</h2>
                <div className={styles.dangerZone}>
		 <h3 className={styles.dangerTitle}>Danger Zone</h3>
                  <div className={styles.dangerActions}>
                    <div className={styles.dangerAction}>
                      <div>
                        <h4>Deactivate Tenant</h4>
                        <p>Temporarily suspend access to this tenant. Users will not be able to log in.</p>
                      </div>
                      <Button variant="danger">Deactivate</Button>
                    </div>
                    <div className={styles.dangerAction}>
                      <div>
                        <h4>Delete Tenant</h4>
                        <p>Permanently remove this tenant and all associated data. This action cannot be undone.</p>
                      </div>
                      <Button variant="danger">Delete</Button>
                    </div>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}