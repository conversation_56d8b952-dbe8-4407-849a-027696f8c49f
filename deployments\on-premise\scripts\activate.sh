#!/bin/bash
# Script kích hoạt license cho hệ thống LoaLoa On-Premise

# Kiểm tra nếu đã nhập license key
if [ -z "$1" ]; then
    echo "Sử dụng: $0 <license_key> <customer_name> <email>"
    echo "Ví dụ: $0 LLHM-ABCDE-12345-FGHIJ-67890 \"Resort ABC\" <EMAIL>"
    exit 1
fi

LICENSE_KEY=$1
CUSTOMER_NAME=$2
EMAIL=$3

# Kiểm tra container admin-portal đang chạy
if ! docker ps | grep -q "loaloa-admin-portal"; then
    echo "Container admin-portal không hoạt động. Vui lòng chạy install.sh trước."
    exit 1
fi

# Tạo file cấu hình license
echo "Đang kích hoạt license..."
docker exec -it loaloa-admin-portal sh -c "echo '{\"licenseKey\":\"$LICENSE_KEY\",\"customerName\":\"$CUSTOMER_NAME\",\"email\":\"$EMAIL\"}' > /app/license_config.json"

echo "License đã được kích hoạt!"
echo "Vui lòng truy cập Admin Portal tại http://localhost:3000 để xác nhận kích hoạt thành công."
