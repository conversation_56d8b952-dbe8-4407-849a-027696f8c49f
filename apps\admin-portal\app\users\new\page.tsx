'use client';
import { useState, FormEvent } from 'react';
import Link from 'next/link';
import { Alert } from '@ui';
import styles from './newUser.module.scss';

interface NewUserData {
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  avatar?: string;
  phone?: string;
  department?: string;
  languages: string[];
  password: string;
  confirmPassword: string;
}

export default function NewUserPage() {
  const [userData, setUserData] = useState<NewUserData>({
    name: '',
    email: '',
    role: 'user',
    isActive: true,
    avatar: '',
    phone: '',
    department: '',
    languages: ['vi'],
    password: '',
    confirmPassword: ''
  });
  
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);

  // Các vai trò có sẵn trong hệ thống
  const availableRoles = [
    { value: 'admin', label: 'Admin' },
    { value: 'manager', label: 'Manager' },
    { value: 'staff', label: 'Staff' },
    { value: 'user', label: 'User' }
  ];

  // Danh sách ngôn ngữ được hỗ trợ
  const availableLanguages = [
    { code: 'vi', name: 'Tiếng Việt' },
    { code: 'en', name: 'English' },
    { code: 'ja', name: '日本語' },
    { code: 'ko', name: '한국어' },
    { code: 'zh', name: '中文' }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'password' || name === 'confirmPassword') {
      setPasswordError(null);
    }
    
    setUserData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setUserData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleLanguageChange = (languageCode: string) => {
    setUserData(prev => {
      const currentLanguages = [...prev.languages];
      
      if (currentLanguages.includes(languageCode)) {
        return {
          ...prev,
          languages: currentLanguages.filter(code => code !== languageCode)
        };
      } else {
        return {
          ...prev,
          languages: [...currentLanguages, languageCode]
        };
      }
    });
  };

  const validatePasswords = () => {
    if (userData.password !== userData.confirmPassword) {
      setPasswordError('Mật khẩu xác nhận không khớp');
      return false;
    }
    
    if (userData.password.length < 6) {
      setPasswordError('Mật khẩu phải có ít nhất 6 ký tự');
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!validatePasswords()) {
      return;
    }
    
    setSaving(true);
    setError(null);
    setSaveSuccess(false);
    
    try {
      // Trong môi trường thực tế, bạn sẽ gọi API để tạo người dùng mới
      // Đây chỉ là mô phỏng thành công
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Giả lập ID được tạo từ server
      const newUserId = Math.floor(Math.random() * 1000).toString();
      
      setSaveSuccess(true);
      
      // Chuyển hướng sau khi tạo thành công (trong môi trường thực tế)
      setTimeout(() => {
        window.location.href = `/users/${newUserId}`;
      }, 1500);
    } catch (error) {
      console.error('Error creating user:', error);
      setError('Không thể tạo người dùng mới. Vui lòng thử lại sau.');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    setUserData({
      name: '',
      email: '',
      role: 'user',
      isActive: true,
      avatar: '',
      phone: '',
      department: '',
      languages: ['vi'],
      password: '',
      confirmPassword: ''
    });
    setError(null);
    setSaveSuccess(false);
    setPasswordError(null);
  };

  return (
    <div className={styles.container}>
      <div className={styles.pageHeader}>
        <div className={styles.titleSection}>
          <Link href="/users" className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.33333 5.83331L4.16666 9.99998L8.33333 14.1666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Quay lại danh sách người dùng
          </Link>
          <h1 className={styles.pageTitle}>Tạo người dùng mới</h1>
        </div>
      </div>

      {error && (
        <Alert
          variant="error"
          title="Lỗi"
          closable
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {saveSuccess && (
        <Alert
          variant="success"
          title="Thành công"
          closable
          onClose={() => setSaveSuccess(false)}
        >
          Người dùng mới đã được tạo thành công. Đang chuyển hướng...
        </Alert>
      )}

      <form onSubmit={handleSubmit} className={styles.userForm}>
        <div className={styles.formLayout}>
          <div className={styles.formSection}>
            <h2 className={styles.sectionTitle}>Thông tin cơ bản</h2>
            
            <div className={styles.formGroup}>
              <label htmlFor="name" className={styles.label}>Họ và tên</label>
              <input
                type="text"
                id="name"
                name="name"
                value={userData.name}
                onChange={handleInputChange}
                className={styles.input}
                required
              />
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="email" className={styles.label}>Email</label>
              <input
                type="email"
                id="email"
                name="email"
                value={userData.email}
                onChange={handleInputChange}
                className={styles.input}
                required
              />
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="role" className={styles.label}>Vai trò</label>
              <select
                id="role"
                name="role"
                value={userData.role}
                onChange={handleInputChange}
                className={styles.select}
                required
              >
                {availableRoles.map(role => (
                  <option key={role.value} value={role.value}>{role.label}</option>
                ))}
              </select>
            </div>
            
            <div className={styles.formGroup}>
              <div className={styles.checkboxField}>
                <label htmlFor="isActive" className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={userData.isActive}
                    onChange={handleCheckboxChange}
                  />
                  <span>Người dùng đang hoạt động</span>
                </label>
              </div>
            </div>
          </div>

          <div className={styles.formSection}>
            <h2 className={styles.sectionTitle}>Mật khẩu</h2>
            
            {passwordError && (
              <div className={styles.fieldError}>
                {passwordError}
              </div>
            )}
            
            <div className={styles.formGroup}>
              <label htmlFor="password" className={styles.label}>Mật khẩu</label>
              <input
                type="password"
                id="password"
                name="password"
                value={userData.password}
                onChange={handleInputChange}
                className={styles.input}
                required
              />
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="confirmPassword" className={styles.label}>Xác nhận mật khẩu</label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={userData.confirmPassword}
                onChange={handleInputChange}
                className={styles.input}
                required
              />
            </div>
          </div>

          <div className={styles.formSection}>
            <h2 className={styles.sectionTitle}>Thông tin liên hệ</h2>
            
            <div className={styles.formGroup}>
              <label htmlFor="avatar" className={styles.label}>URL Ảnh đại diện</label>
              <div className={styles.avatarField}>
                <input
                  type="text"
                  id="avatar"
                  name="avatar"
                  value={userData.avatar}
                  onChange={handleInputChange}
                  className={styles.input}
                />
                {userData.avatar && (
                  <div className={styles.avatarPreview}>
                    <img 
                      src={userData.avatar} 
                      alt="Avatar Preview" 
                      className={styles.avatarImage}
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'https://via.placeholder.com/100x100?text=Error';
                      }}
                    />
                  </div>
                )}
              </div>
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="phone" className={styles.label}>Số điện thoại</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={userData.phone}
                onChange={handleInputChange}
                className={styles.input}
              />
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="department" className={styles.label}>Phòng ban</label>
              <input
                type="text"
                id="department"
                name="department"
                value={userData.department}
                onChange={handleInputChange}
                className={styles.input}
              />
            </div>
          </div>

          <div className={styles.formSection}>
            <h2 className={styles.sectionTitle}>Cài đặt ngôn ngữ</h2>
            
            <div className={styles.formGroup}>
              <label className={styles.label}>Ngôn ngữ</label>
              <div className={styles.languageGrid}>
                {availableLanguages.map(lang => (
                  <label key={lang.code} className={styles.languageCheckbox}>
                    <input
                      type="checkbox"
                      checked={userData.languages.includes(lang.code)}
                      onChange={() => handleLanguageChange(lang.code)}
                    />
                    <span>{lang.name}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className={styles.formActions}>
          <button 
            type="button" 
            className={styles.secondaryButton}
            onClick={handleReset}
          >
            Hủy
          </button>
          <button 
            type="submit" 
            className={styles.primaryButton}
            disabled={saving}
          >
            {saving ? 'Đang tạo...' : 'Tạo người dùng'}
          </button>
        </div>
      </form>
    </div>
  );
}
