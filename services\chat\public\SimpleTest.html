<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple Supabase Test</title>
  <style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    .output { background: #f4f4f4; border: 1px solid #ddd; padding: 10px; margin-top: 20px; min-height: 200px; }
    button { padding: 10px; background: #4CAF50; color: white; border: none; cursor: pointer; }
  </style>
</head>
<body>
  <h1>Simple Supabase Test</h1>
  
  <div>
    <p>Nhấn nút bên dưới để thử kết nối với REST API của Supabase (không cần thư viện)</p>
    <button id="test-button">Test REST API Connection</button>
  </div>
  
  <div class="output" id="output">
    <!-- <PERSON>ết quả sẽ hiển thị ở đây -->
  </div>
  
  <script>
    document.getElementById('test-button').addEventListener('click', async () => {
      const outputDiv = document.getElementById('output');
      outputDiv.innerHTML = 'Đang kiểm tra kết nối...';
      
      try {
        // Supabase URL và anon key
        const SUPABASE_URL = 'https://iwzwbrbmojvvvfstbqow.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3endicmJtb2p2dnZmc3RicW93Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMjkyNjEsImV4cCI6MjA2MTkwNTI2MX0.tyVtaSclUKC5fGh7I7Ohpm7c4FniXphYe34-cxBvo6E';
        
        // Thử kết nối đơn giản bằng fetch API
        const response = await fetch(`${SUPABASE_URL}/rest/v1/users?select=id,email&limit=5`, {
          headers: {
            'apikey': SUPABASE_KEY,
            'Authorization': `Bearer ${SUPABASE_KEY}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Hiển thị kết quả
        outputDiv.innerHTML = `
          <p style="color: green;">Kết nối thành công!</p>
          <p>Số lượng users nhận được: ${data.length}</p>
          <pre>${JSON.stringify(data, null, 2)}</pre>
        `;
      } catch (error) {
        outputDiv.innerHTML = `
          <p style="color: red;">Lỗi kết nối:</p>
          <p>${error.message}</p>
          <p><strong>Giải thích lỗi:</strong></p>
          <ul>
            <li>Nếu là lỗi CORS: Bạn cần mở file này thông qua máy chủ web</li>
            <li>Nếu là lỗi 403/401: Kiểm tra lại API key</li>
            <li>Nếu là lỗi 404: API endpoint không tồn tại</li>
          </ul>
        `;
      }
    });
  </script>
</body>
</html>
