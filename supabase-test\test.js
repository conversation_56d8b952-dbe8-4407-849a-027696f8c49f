// test.js
const { createClient } = require('@supabase/supabase-js');

// Thông tin kết nối Supabase
const supabaseUrl = 'https://uvfosdvduemcktgayllz.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV2Zm9zZHZkdWVtY2t0Z2F5bGx6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2ODQ5MjQ0MjksImV4cCI6MjAwMDUwMDQyOX0.vZn916S2nYhXsVO6ublrZ_mV9FxKlrJbKD9mgmrkSUE';

// Tạo client Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    console.log('Đang kiểm tra kết nối Supabase...');
    
    // Thử truy vấn đơn giản
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .limit(5);
      
    if (error) {
      throw new Error(`Lỗi truy vấn: ${error.message}`);
    }
    
    console.log('<PERSON><PERSON><PERSON> nối thành công!');
    console.log(`Số lượng users: ${data.length}`);
    console.log(data);
  } catch (error) {
    console.error('Lỗi kết nối Supabase:', error.message);
  }
}

testConnection();
