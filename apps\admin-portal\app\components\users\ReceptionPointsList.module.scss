.container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.pointItem {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #f9fafb;
  
  &.primary {
    border-color: #93c5fd;
    background-color: #eff6ff;
  }
}

.pointIcon {
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  overflow: hidden;
  flex-shrink: 0;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.defaultIcon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e5e7eb;
  color: #4b5563;
  font-size: 1.25rem;
  font-weight: 600;
  text-transform: uppercase;
}

.pointInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.pointHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pointName {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.primaryBadge {
  background-color: #dbeafe;
  color: #1d4ed8;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  
  &::before {
    content: '';
    width: 0.375rem;
    height: 0.375rem;
    border-radius: 50%;
    margin-right: 0.25rem;
  }

  &.active {
    background-color: #d1fae5;
    color: #065f46;
    
    &::before {
      background-color: #10b981;
    }
  }

  &.inactive {
    background-color: #f3f4f6;
    color: #4b5563;
    
    &::before {
      background-color: #9ca3af;
    }
  }
}

.pointCode {
  font-size: 0.75rem;
  color: #6b7280;
  
  span {
    font-family: monospace;
    background-color: #f3f4f6;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
  }
}

.pointDescription {
  font-size: 0.875rem;
  color: #4b5563;
  margin: 0.25rem 0;
}

.pointActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.setPrimaryButton, .removeButton {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  font-weight: 500;
  cursor: pointer;
  background-color: transparent;
}

.setPrimaryButton {
  color: #2563eb;
  border: 1px solid #93c5fd;
  
  &:hover {
    background-color: #dbeafe;
  }
}

.removeButton {
  color: #dc2626;
  border: 1px solid #fecaca;
  
  &:hover {
    background-color: #fee2e2;
  }
}

.confirmActions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  span {
    font-size: 0.75rem;
    color: #4b5563;
  }
}

.confirmButton, .cancelButton {
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  font-weight: 500;
  cursor: pointer;
}

.confirmButton {
  background-color: #dc2626;
  color: white;
  border: 1px solid #dc2626;
  
  &:hover {
    background-color: #b91c1c;
  }
}

.cancelButton {
  background-color: transparent;
  color: #4b5563;
  border: 1px solid #d1d5db;
  
  &:hover {
    background-color: #f3f4f6;
  }
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6b7280;
  
  .spinner {
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    width: 1.5rem;
    height: 1.5rem;
    animation: spin 1s linear infinite;
    margin-bottom: 0.5rem;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

.error {
  color: #b91c1c;
  padding: 1rem;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  background-color: #fee2e2;
  text-align: center;
  
  p {
    margin-top: 0;
  }
  
  .retryButton {
    background-color: #b91c1c;
    color: white;
    border: none;
    padding: 0.25rem 1rem;
    border-radius: 0.25rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    
    &:hover {
      background-color: #991b1b;
    }
  }
}

.empty {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  
  p {
    margin: 0.5rem 0;
  }
  
  .emptySubtext {
    font-size: 0.875rem;
    color: #9ca3af;
  }
}
