import React from 'react';
import { Modal } from '../Modal';
import { Button } from '../Button';
import styles from './DeleteConfirmModal.module.css';

interface DeleteConfirmModalProps {
  isOpen: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  error?: string | null;
  confirmLoading?: boolean;
}

export const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  isOpen,
  title,
  message,
  onConfirm,
  onCancel,
  error,
  confirmLoading,
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onCancel} title={title}>
      <div className={styles.container}>
        <div className={styles.warningIcon}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 9V11M12 15H12.01M5.07183 19H18.9282C20.4678 19 21.4301 17.3333 20.6603 16L13.7321 4C12.9623 2.66667 11.0378 2.66667 10.268 4L3.33978 16C2.56998 17.3333 3.53223 19 5.07183 19Z"
              stroke="#DC2626"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
        <p className={styles.message}>{message}</p>
        
        {error && (
          <div className={styles.error}>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <circle cx="8" cy="8" r="8" fill="#FEE2E2" />
              <path
                d="M10.6667 5.33337L5.33334 10.6667M5.33334 5.33337L10.6667 10.6667"
                stroke="#DC2626"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span>{error}</span>
          </div>
        )}
        
        <div className={styles.actions}>
          <Button 
            variant="secondary" 
            onClick={onCancel} 
            disabled={confirmLoading}
          >
            Cancel
          </Button>
          <Button 
            variant="danger" 
            onClick={onConfirm} 
            loading={confirmLoading}
            disabled={confirmLoading}
          >
            Delete
          </Button>
        </div>
      </div>
    </Modal>
  );
};
