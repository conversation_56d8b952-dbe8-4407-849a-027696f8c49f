import { createClient } from '../../../../lib/supabase/server';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Hàm đọc tenant_id từ file license_config.json
function getTenantIdFromConfig() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Lấy tenant_id từ config file
    const tenant_id = getTenantIdFromConfig();
    
    if (!tenant_id) {
      return NextResponse.json({
        success: false,
        error: 'Không thể tìm thấy Tenant ID. Vui lòng kích hoạt license.'
      }, { status: 400 });
    }
    
    // Lấy tổng số khu vực
    const { count: totalAreas, error: totalError } = await supabase
      .from('tenant_areas')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenant_id);
    
    if (totalError && totalError.code !== 'PGRST116') {
      return NextResponse.json({ 
        success: false, 
        error: totalError.message 
      }, { status: 500 });
    }
    
    // Lấy số khu vực theo trạng thái
    const { data: statusData, error: statusError } = await supabase
      .from('tenant_areas')
      .select('is_active')
      .eq('tenant_id', tenant_id);
    
    if (statusError && statusError.code !== 'PGRST116') {
      return NextResponse.json({ 
        success: false, 
        error: statusError.message 
      }, { status: 500 });
    }
    
    // Tính toán thống kê
    const activeAreas = statusData?.filter(area => area.is_active === true).length || 0;
    const inactiveAreas = statusData?.filter(area => area.is_active === false).length || 0;
    
    return NextResponse.json({
      success: true,
      stats: {
        total: totalAreas || 0,
        active: activeAreas,
        inactive: inactiveAreas
      }
    });
  } catch (error: any) {
    console.error('Error fetching area stats:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal Server Error',
      stats: {
        total: 0,
        active: 0,
        inactive: 0
      }
    });
  }
}
