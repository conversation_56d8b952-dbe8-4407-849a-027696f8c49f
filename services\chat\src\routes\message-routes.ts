import { Router } from 'express';
import * as chatMessageController from '../controllers/chat-message-controller';
import { authenticate } from '../middlewares/auth-middleware';

const router = Router();

// Lấy tin nhắn theo ID
router.get('/:id', authenticate, chatMessageController.getMessageById);

// Cập nhật tin nhắn
router.put('/:id', authenticate, chatMessageController.updateMessage);

// Xóa tin nhắn
router.delete('/:id', authenticate, chatMessageController.deleteMessage);

// Dịch tin nhắn
router.post('/:id/translate', authenticate, chatMessageController.translateMessage);

// L<PERSON>y danh sách tin nhắn trong phòng
router.get('/room/:roomId', authenticate, chatMessageController.getRoomMessages);

// Tạo tin nhắn mới (API này được sử dụng khi không thể sử dụng WebSocket)
router.post('/', authenticate, chatMessageController.createMessage);

export default router;
