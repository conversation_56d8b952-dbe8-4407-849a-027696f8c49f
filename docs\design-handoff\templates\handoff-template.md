# Component Design Handoff: [COMPONENT_NAME]

## Overview
- **Component Name:** [Name]
- **Designer:** [Designer Name]
- **Last Updated:** [Date]
- **Status:** [Draft/Ready for Handoff/In Development]

## Visual Design

### Desktop/Mobile View
[Screenshot or link to Figma frame]

### States
- **Default:** [Screenshot or description]
- **Hover:** [Screenshot or description]
- **Active:** [Screenshot or description]
- **Disabled:** [Screenshot or description]
- **Error:** [Screenshot or description]

## Specifications

### Layout & Spacing
- **Padding:** [Values in tokens, e.g., spacing-4 (16px)]
- **Margin:** [Values in tokens]
- **Width/Height:** [Fixed or responsive values]
- **Alignment:** [Left/Center/Right/etc.]

### Typography
- **Font Family:** [Token value]
- **Font Size:** [Token value]
- **Font Weight:** [Token value]
- **Line Height:** [Token value]
- **Color:** [Token value]

### Colors
- **Background:** [Token value]
- **Text:** [Token value]
- **Border:** [Token value]
- **Icon:** [Token value]

### Other Properties
- **Border Radius:** [Token value]
- **Shadow:** [Token value]
- **Opacity:** [Value if applicable]

## Behavior
- **Interactions:** [Click/hover/focus behaviors]
- **Animations:** [Description or link to prototypes]
- **Transitions:** [Description of timing and easing]

## Accessibility
- **Keyboard Navigation:** [Tab order, keyboard shortcuts]
- **Screen Reader Text:** [Additional context for assistive tech]
- **ARIA Attributes:** [If specific ARIA roles/props needed]

## Implementation Notes
- [Any special considerations for developers]
- [Edge cases to handle]
- [Performance considerations]

## Assets
- [Links to icons, images, or other assets]

## Questions & Answers
- [Record of Q&A between design and development]