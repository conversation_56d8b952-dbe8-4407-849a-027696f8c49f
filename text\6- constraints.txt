[{"table_name": "temporary_users", "constraint_name": "temporary_users_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "temporary_users", "constraint_name": "temporary_users_qr_code_id_key", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (qr_code_id)", "constraint_comment": null}, {"table_name": "tenant_areas", "constraint_name": "tenant_areas_qr_code_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (qr_code_id) REFERENCES tenant_qr_codes(id)", "constraint_comment": null}, {"table_name": "tenant_areas", "constraint_name": "tenant_areas_reception_point_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (reception_point_id) REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL", "constraint_comment": null}, {"table_name": "tenant_areas", "constraint_name": "tenant_areas_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_areas", "constraint_name": "tenant_areas_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_chat_messages", "constraint_name": "tenant_chat_messages_chat_session_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (chat_session_id) REFERENCES tenant_chat_sessions(id)", "constraint_comment": null}, {"table_name": "tenant_chat_messages", "constraint_name": "tenant_chat_messages_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_chat_routing_rules", "constraint_name": "fk_tenant_chat_routing_rules_reception_point", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (target_reception_point_id) REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL", "constraint_comment": null}, {"table_name": "tenant_chat_routing_rules", "constraint_name": "tenant_chat_routing_rules_target_reception_point_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (target_reception_point_id) REFERENCES tenant_message_reception_points(id)", "constraint_comment": null}, {"table_name": "tenant_chat_routing_rules", "constraint_name": "tenant_chat_routing_rules_target_user_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (target_user_id) REFERENCES tenant_users(id)", "constraint_comment": null}, {"table_name": "tenant_chat_routing_rules", "constraint_name": "tenant_chat_routing_rules_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id)", "constraint_comment": null}, {"table_name": "tenant_chat_routing_rules", "constraint_name": "tenant_chat_routing_rules_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_chat_session_assignments", "constraint_name": "tenant_chat_session_assignments_assigned_user_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (assigned_user_id) REFERENCES tenant_users(id)", "constraint_comment": null}, {"table_name": "tenant_chat_session_assignments", "constraint_name": "tenant_chat_session_assignments_chat_session_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (chat_session_id) REFERENCES tenant_chat_sessions(id)", "constraint_comment": null}, {"table_name": "tenant_chat_session_assignments", "constraint_name": "tenant_chat_session_assignments_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id)", "constraint_comment": null}, {"table_name": "tenant_chat_session_assignments", "constraint_name": "tenant_chat_session_assignments_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_chat_sessions", "constraint_name": "fk_tenant_chat_sessions_reception_point", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (reception_point_id) REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL", "constraint_comment": null}, {"table_name": "tenant_chat_sessions", "constraint_name": "tenant_chat_sessions_reception_point_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (reception_point_id) REFERENCES tenant_message_reception_points(id)", "constraint_comment": null}, {"table_name": "tenant_chat_sessions", "constraint_name": "tenant_chat_sessions_source_qr_code_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (source_qr_code_id) REFERENCES tenant_qr_codes(id)", "constraint_comment": null}, {"table_name": "tenant_chat_sessions", "constraint_name": "tenant_chat_sessions_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_guests", "constraint_name": "check_active_guest_has_room", "constraint_type": "CHECK", "constraint_definition": "CHECK (((is_active = false) OR ((is_active = true) AND (room_number IS NOT NULL))))", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> kh<PERSON>ch đang active phải đư<PERSON><PERSON> gán vào một phòng cụ thể"}, {"table_name": "tenant_guests", "constraint_name": "tenant_guests_qr_code_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (qr_code_id) REFERENCES tenant_qr_codes(id)", "constraint_comment": "<PERSON>ên kết khách với mã QR cá nhân của họ nếu có"}, {"table_name": "tenant_guests", "constraint_name": "tenant_guests_room_number_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (room_number, tenant_id) REFERENCES tenant_rooms(room_number, tenant_id)", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> kh<PERSON> chỉ có thể check-in vào một phòng có thật trong cùng tenant"}, {"table_name": "tenant_guests", "constraint_name": "tenant_guests_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id)", "constraint_comment": "<PERSON><PERSON><PERSON> bảo mỗi khách phải thuộc về một tenant hợp lệ trong hệ thống"}, {"table_name": "tenant_guests", "constraint_name": "tenant_guests_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_message_reception_points", "constraint_name": "tenant_message_reception_points_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_message_reception_points", "constraint_name": "tenant_message_reception_points_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_message_reception_points", "constraint_name": "tenant_message_reception_points_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_message_reception_points", "constraint_name": "tenant_message_reception_points_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_message_reception_points", "constraint_name": "tenant_message_reception_points_tenant_id_code_key", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (tenant_id, code)", "constraint_comment": null}, {"table_name": "tenant_message_reception_points", "constraint_name": "tenant_message_reception_points_tenant_id_code_key", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (tenant_id, code)", "constraint_comment": null}, {"table_name": "tenant_qr_codes", "constraint_name": "check_room_number_target_type", "constraint_type": "CHECK", "constraint_definition": "CHECK (((room_number IS NULL) OR ((room_number IS NOT NULL) AND ((target_type)::text = 'room'::text))))", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> nhất quán: nếu room_number đ<PERSON><PERSON><PERSON> đ<PERSON>, target_type phải là room"}, {"table_name": "tenant_qr_codes", "constraint_name": "check_target_consistency", "constraint_type": "CHECK", "constraint_definition": "CHECK (((((target_type)::text = 'room'::text) AND (room_number IS NOT NULL) AND (target_id IS NULL)) OR (((target_type)::text = 'area'::text) AND (target_id IS NOT NULL) AND (room_number IS NULL)) OR ((target_type IS NULL) AND (target_id IS NULL) AND (room_number IS NULL)) OR (((target_type)::text = 'general'::text) AND (target_id IS NULL) AND (room_number IS NULL))))", "constraint_comment": "Ensures consistent relationship between target_type, room_number and target_id"}, {"table_name": "tenant_qr_codes", "constraint_name": "check_target_type_values", "constraint_type": "CHECK", "constraint_definition": "CHECK (((target_type IS NULL) OR ((target_type)::text = ANY ((ARRAY['room'::character varying, 'area'::character varying, 'guest'::character varying, 'general'::character varying])::text[]))))", "constraint_comment": "<PERSON><PERSON><PERSON> bảo target_type chỉ nhận các giá trị hợp lệ"}, {"table_name": "tenant_qr_codes", "constraint_name": "ensure_qr_code_consistency", "constraint_type": "CHECK", "constraint_definition": "CHECK ((((code_type = 'USER'::qr_code_type) AND (counts_against_limit = false)) OR (code_type = 'TENANT'::qr_code_type)))", "constraint_comment": "Đảm bảo QR code loại USER luôn có counts_against_limit = FALSE"}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_target_type_check", "constraint_type": "CHECK", "constraint_definition": "CHECK (((target_type)::text = ANY ((ARRAY['room'::character varying, 'area'::character varying, 'guest'::character varying, 'general'::character varying])::text[])))", "constraint_comment": null}, {"table_name": "tenant_qr_codes", "constraint_name": "fk_tenant_qr_codes_reception_point", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (reception_point_id) REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL", "constraint_comment": null}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_qr_type_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (qr_type_id) REFERENCES tenant_qr_code_types(id)", "constraint_comment": null}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_reception_point_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (reception_point_id) REFERENCES tenant_message_reception_points(id)", "constraint_comment": null}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_room_number_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (room_number, tenant_id) REFERENCES tenant_rooms(room_number, tenant_id)", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON>o mã QR chỉ liên kết với một phòng có thật trong cùng tenant"}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_user_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (user_id) REFERENCES tenant_users(id) ON DELETE SET NULL", "constraint_comment": null}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_tenant_id_code_value_key", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (tenant_id, code_value)", "constraint_comment": null}, {"table_name": "tenant_qr_code_scans", "constraint_name": "tenant_qr_code_scans_guest_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (guest_id) REFERENCES tenant_guests(id)", "constraint_comment": null}, {"table_name": "tenant_qr_code_scans", "constraint_name": "tenant_qr_code_scans_qr_code_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (qr_code_id) REFERENCES tenant_qr_codes(id)", "constraint_comment": null}, {"table_name": "tenant_qr_code_scans", "constraint_name": "tenant_qr_code_scans_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id)", "constraint_comment": null}, {"table_name": "tenant_qr_code_scans", "constraint_name": "tenant_qr_code_scans_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_qr_code_types", "constraint_name": "tenant_qr_code_types_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id)", "constraint_comment": null}, {"table_name": "tenant_qr_code_types", "constraint_name": "tenant_qr_code_types_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_qr_code_types", "constraint_name": "tenant_qr_code_types_tenant_id_name_key", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (tenant_id, name)", "constraint_comment": null}, {"table_name": "tenant_rooms", "constraint_name": "tenant_rooms_qr_code_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (qr_code_id) REFERENCES tenant_qr_codes(id)", "constraint_comment": null}, {"table_name": "tenant_rooms", "constraint_name": "tenant_rooms_reception_point_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (reception_point_id) REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL", "constraint_comment": null}, {"table_name": "tenant_rooms", "constraint_name": "tenant_rooms_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_rooms", "constraint_name": "tenant_rooms_tenant_id_room_number_key", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (tenant_id, room_number)", "constraint_comment": null}, {"table_name": "tenant_staff_assignments", "constraint_name": "fk_tenant_staff_assignments_reception_point", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (reception_point_id) REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL", "constraint_comment": null}, {"table_name": "tenant_staff_assignments", "constraint_name": "tenant_staff_assignments_reception_point_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (reception_point_id) REFERENCES tenant_message_reception_points(id)", "constraint_comment": null}, {"table_name": "tenant_staff_assignments", "constraint_name": "tenant_staff_assignments_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id)", "constraint_comment": null}, {"table_name": "tenant_staff_assignments", "constraint_name": "tenant_staff_assignments_user_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (user_id) REFERENCES tenant_users(id)", "constraint_comment": null}, {"table_name": "tenant_staff_assignments", "constraint_name": "tenant_staff_assignments_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_staff_assignments", "constraint_name": "tenant_staff_assignments_tenant_id_user_id_assignment_type__key", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (tenant_id, user_id, assignment_type, resource_id, department)", "constraint_comment": null}, {"table_name": "tenant_translation_cache", "constraint_name": "tenant_translation_cache_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id)", "constraint_comment": null}, {"table_name": "tenant_translation_cache", "constraint_name": "tenant_translation_cache_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_translation_settings", "constraint_name": "fk_translation_tenant", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_translation_settings", "constraint_name": "tenant_translation_settings_guest_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (guest_id) REFERENCES tenant_guests(id)", "constraint_comment": null}, {"table_name": "tenant_translation_settings", "constraint_name": "tenant_translation_settings_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id)", "constraint_comment": null}, {"table_name": "tenant_translation_settings", "constraint_name": "tenant_translation_settings_user_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (user_id) REFERENCES tenant_users(id)", "constraint_comment": null}, {"table_name": "tenant_translation_settings", "constraint_name": "tenant_translation_settings_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_translation_settings", "constraint_name": "tenant_translation_settings_tenant_id_user_id_guest_id_sess_key", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (tenant_id, user_id, guest_id, session_id)", "constraint_comment": null}, {"table_name": "tenant_translation_settings", "constraint_name": "uq_translation_settings_tenant", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (tenant_id)", "constraint_comment": null}, {"table_name": "tenant_typing_status", "constraint_name": "tenant_typing_status_user_type_check", "constraint_type": "CHECK", "constraint_definition": "CHECK (((user_type)::text = ANY ((ARRAY['guest'::character varying, 'staff'::character varying])::text[])))", "constraint_comment": null}, {"table_name": "tenant_typing_status", "constraint_name": "fk_typing_status_session", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (session_id) REFERENCES tenant_chat_sessions(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_typing_status", "constraint_name": "fk_typing_status_tenant", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_typing_status", "constraint_name": "tenant_typing_status_session_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (session_id) REFERENCES tenant_chat_sessions(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_typing_status", "constraint_name": "tenant_typing_status_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_users", "constraint_name": "tenant_users_tenant_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_users", "constraint_name": "tenant_users_user_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_users", "constraint_name": "tenant_users_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_users", "constraint_name": "tenant_users_tenant_id_user_id_key", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (tenant_id, user_id)", "constraint_comment": null}, {"table_name": "tenant_users_details", "constraint_name": "tenant_users_details_tenant_user_id_fkey", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_user_id) REFERENCES tenant_users(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_users_details", "constraint_name": "tenant_users_details_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_users_details", "constraint_name": "tenant_users_details_tenant_user_id_key", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (tenant_user_id)", "constraint_comment": null}, {"table_name": "tenant_voice_messages", "constraint_name": "fk_voice_messages_message", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (message_id) REFERENCES tenant_chat_messages(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_voice_messages", "constraint_name": "fk_voice_messages_tenant", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_voice_messages", "constraint_name": "tenant_voice_messages_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_web_sessions", "constraint_name": "fk_web_sessions_guest", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (guest_id) REFERENCES tenant_guests(id) ON DELETE SET NULL", "constraint_comment": null}, {"table_name": "tenant_web_sessions", "constraint_name": "fk_web_sessions_temp_user", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (temporary_user_id) REFERENCES temporary_users(id) ON DELETE SET NULL", "constraint_comment": null}, {"table_name": "tenant_web_sessions", "constraint_name": "fk_web_sessions_tenant", "constraint_type": "FOREIGN KEY", "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE", "constraint_comment": null}, {"table_name": "tenant_web_sessions", "constraint_name": "tenant_web_sessions_pkey", "constraint_type": "PRIMARY KEY", "constraint_definition": "PRIMARY KEY (id)", "constraint_comment": null}, {"table_name": "tenant_web_sessions", "constraint_name": "tenant_web_sessions_session_token_key", "constraint_type": "UNIQUE", "constraint_definition": "UNIQUE (session_token)", "constraint_comment": null}]