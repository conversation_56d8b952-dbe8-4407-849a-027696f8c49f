@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for LoaLoa Theme */
:root {
  /* Primary Colors */
  --color-primary: #f97316;
  --color-primary-light: #fb923c;
  --color-primary-dark: #ea580c;
  --color-primary-50: #fff7ed;
  --color-primary-100: #ffedd5;
  --color-primary-500: #f97316;
  --color-primary-600: #ea580c;
  --color-primary-700: #c2410c;

  /* Neutral Colors */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Status Colors */
  --color-success: #10b981;
  --color-success-light: #34d399;
  --color-error: #ef4444;
  --color-error-light: #f87171;
  --color-warning: #f59e0b;
  --color-info: #3b82f6;

  /* Typography */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Courier New', monospace;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Z-Index */
  --z-dropdown: 1000;
  --z-modal: 1050;
  --z-tooltip: 1100;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family-sans);
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  color: var(--color-gray-900);
  background-color: var(--color-gray-50);
  font-feature-settings: 'rlig' 1, 'calt' 1;
}

/* Typography Utilities */
.font-display {
  font-family: var(--font-family-sans);
  font-weight: 700;
  letter-spacing: -0.025em;
}

.font-mono {
  font-family: var(--font-family-mono);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-sm);
  border: 2px solid var(--color-gray-100);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

::-webkit-scrollbar-corner {
  background: var(--color-gray-100);
}

/* Focus Styles */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px var(--color-primary-500) / 0.1;
}

/* Button Base Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px var(--color-primary-500) / 0.1;
  }
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  color: white;
  box-shadow: var(--shadow-sm);
  
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }
}

.btn-secondary {
  background: white;
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
  
  &:hover:not(:disabled) {
    background: var(--color-gray-50);
    border-color: var(--color-gray-400);
  }
}

.btn-ghost {
  background: transparent;
  color: var(--color-gray-600);
  
  &:hover:not(:disabled) {
    background: var(--color-gray-100);
    color: var(--color-gray-700);
  }
}

/* Input Base Styles */
.input {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: var(--color-gray-900);
  background: white;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  &::placeholder {
    color: var(--color-gray-400);
  }
  
  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px var(--color-primary-500) / 0.1;
  }
  
  &:disabled {
    background: var(--color-gray-50);
    color: var(--color-gray-500);
    cursor: not-allowed;
  }
  
  &.error {
    border-color: var(--color-error);
    
    &:focus {
      border-color: var(--color-error);
      box-shadow: 0 0 0 3px var(--color-error) / 0.1;
    }
  }
}

/* Animation Utilities */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-30px);
  }
  70% {
    transform: translateY(-15px);  
  }
  90% {
    transform: translateY(-4px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Utility Classes */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideUp {
  animation: slideUp 0.3s ease-out;
}

.animate-slideDown {
  animation: slideDown 0.3s ease-out;
}

.animate-slideLeft {
  animation: slideLeft 0.3s ease-out;
}

.animate-slideRight {
  animation: slideRight 0.3s ease-out;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Layout Utilities */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

/* Status Indicator */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
  
  &::before {
    content: '';
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
  }
  
  &.online::before {
    background: var(--color-success);
    animation: pulse 2s infinite;
  }
  
  &.offline::before {
    background: var(--color-error);
  }
  
  &.connecting::before {
    background: var(--color-warning);
    animation: pulse 1s infinite;
  }
}

/* Toast Notifications */
.toast {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: var(--z-tooltip);
  max-width: 24rem;
  padding: 1rem;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  animation: slideLeft 0.3s ease-out;
  
  &.success {
    border-left: 4px solid var(--color-success);
  }
  
  &.error {
    border-left: 4px solid var(--color-error);
  }
  
  &.warning {
    border-left: 4px solid var(--color-warning);
  }
  
  &.info {
    border-left: 4px solid var(--color-info);
  }
}

/* Loading States */
.loading {
  position: relative;
  pointer-events: none;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid var(--color-gray-300);
    border-top-color: var(--color-primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

/* Responsive Text */
.text-responsive {
  font-size: clamp(0.875rem, 2vw, 1rem);
}

.heading-responsive {
  font-size: clamp(1.5rem, 4vw, 3rem);
  line-height: 1.2;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .btn {
    display: none !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --color-gray-50: #ffffff;
    --color-gray-100: #f0f0f0;
    --color-gray-900: #000000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
