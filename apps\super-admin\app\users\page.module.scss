.usersContainer {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.titleSection {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.pageTitle {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
  color: var(--color-text);
}

.pageDescription {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  margin: 0;
}

.filtersContainer {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.searchBar {
  width: 100%;
  max-width: 400px;
}

.searchInput {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 0.875rem;
}

.filters {
  display: flex;
  gap: 12px;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 0.875rem;
  background-color: white;
}

.statsCards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 16px;

  @media (max-width: 1200px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
}

.statCard {
  padding: 16px;
  height: 100%;
}

.statTitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-secondary);
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statValue {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: 8px;
}

.statChange {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.positive {
  color: var(--color-success);
}

.negative {
  color: var(--color-error);
}

.statFooter {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.userTableCard {
  width: 100%;
  overflow: hidden;
}

.tenantLink {
  color: var(--color-text);
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    color: var(--color-primary);
    text-decoration: underline;
  }
}

.userEmail {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
}

.roleBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.super_admin {
  background-color: #FFD700;
  color: #8B4513;
}

.admin {
  background-color: #E8F5E9;
  color: #2E7D32;
}

.user {
  background-color: #E3F2FD;
  color: #1565C0;
}

.tenantsCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tenantBadge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
  background: #f5f5f5;
  margin-right: 4px;
}

.actions {
  display: flex;
  gap: 8px;
}

.errorMessage {
  padding: 12px;
  background-color: #FFEBEE;
  color: #C62828;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #C62828;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1rem;
  color: var(--color-text-secondary);
}