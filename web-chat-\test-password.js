const bcrypt = require('bcryptjs');

// CLI chay file: cd D:\loaloa\apps\web-chat node test-password.js
const storedHash = '$2b$10$rOZyqvjS.Gk8YmA8sP3.Ou1hV8LnX8oS4N5nGxHoF.NHqA8VnJgIC';

// Test password
const testPassword = 'demo123';

console.log('Testing password verification...');
console.log('Password:', testPassword);
console.log('Hash:', storedHash);

bcrypt.compare(testPassword, storedHash, (err, result) => {
  if (err) {
    console.error('Error:', err);
  } else {
    console.log('Password match:', result);
  }
});
