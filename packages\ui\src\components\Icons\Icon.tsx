import React from 'react';

export interface IconProps {
  /**
   * Icon size
   */
  size?: 'small' | 'medium' | 'large' | number;
  /**
   * Icon color
   */
  color?: string;
  /**
   * Icon children (SVG paths)
   */
  children?: React.ReactNode;
  /**
   * ViewBox for SVG
   */
  viewBox?: string;
  /**
   * Accessibility label
   */
  ariaLabel?: string;
  /**
   * Additional CSS properties
   */
  style?: React.CSSProperties;
  /**
   * Optional CSS class name
   */
  className?: string;
}

export const Icon: React.FC<IconProps> = ({
  size = 'medium',
  color,
  children,
  viewBox = '0 0 24 24',
  ariaLabel,
  style,
  className,
  ...props
}) => {
  // Convert size to pixels
  const getSize = () => {
    if (typeof size === 'number') {
      return `${size}px`;
    }
    
    switch (size) {
      case 'small':
        return '16px';
      case 'large':
        return '32px';
      case 'medium':
      default:
        return '24px';
    }
  };

  // Base icon style
  const iconStyle: React.CSSProperties = {
    display: 'inline-block',
    width: getSize(),
    height: getSize(),
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: 2,
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    color: color || 'currentColor',
    ...style,
  };

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox={viewBox}
      style={iconStyle}
      className={className}
      aria-hidden={!ariaLabel}
      role={ariaLabel ? 'img' : undefined}
      aria-label={ariaLabel}
      {...props}
    >
      {children}
    </svg>
  );
};

export default Icon;
