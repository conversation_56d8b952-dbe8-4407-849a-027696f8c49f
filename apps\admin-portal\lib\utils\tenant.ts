import { createClient } from '../supabase/server';
import { cookies } from 'next/headers';

export async function getCurrentTenantId() {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Lấy session hiện tại
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      throw new Error('Không có phiên đăng nhập');
    }
    
    // Thử lấy tenant_id từ user metadata
    let tenantId = session.user?.user_metadata?.tenant_id;
    
    // Nếu không có trong metadata, thử lấy từ JWT claims
    if (!tenantId) {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: tenantUser } = await supabase
          .from('tenant_users')
          .select('tenant_id')
          .eq('user_id', user.id)
          .single();
          
        if (tenantUser) {
          tenantId = tenantUser.tenant_id;
        }
      }
    }
    
    // N<PERSON>u vẫn không có, thử lấy tenant mặc định
    if (!tenantId) {
      const { data: defaultTenant } = await supabase
        .from('tenants')
        .select('id')
        .limit(1)
        .single();
        
      if (defaultTenant) {
        tenantId = defaultTenant.id;
      }
    }
    
    if (!tenantId) {
      throw new Error('Không tìm thấy Tenant ID');
    }
    
    return tenantId;
  } catch (error) {
    console.error('Error getting tenant ID:', error);
    throw error;
  }
}
