.loading {
  padding: 2rem;
  text-align: center;
  color: #64748b;
}

.receptionPointInfo {
  margin-bottom: 1.5rem;
  
  .receptionPointName {
    font-size: 1.125rem;
    margin: 0 0 0.25rem;
    color: #334155;
  }
  
  .receptionPointCode {
    font-size: 0.875rem;
    color: #64748b;
  }
}

.formGroup {
  margin-bottom: 1.5rem;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #1e293b;
    font-size: 0.9375rem;
  }
  
  select, input[type="number"] {
    width: 100%;
    padding: 0.625rem;
    border: 1px solid #cbd5e1;
    border-radius: 0.375rem;
    font-size: 0.9375rem;
    transition: border-color 0.2s;
    
    &:focus {
      outline: none;
      border-color: #0ea5e9;
      box-shadow: 0 0 0 1px rgba(14, 165, 233, 0.2);
    }
  }
  
  .helpText {
    margin-top: 0.375rem;
    font-size: 0.8125rem;
    color: #64748b;
  }
  
  .noStaffMessage {
    padding: 0.75rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    color: #64748b;
    font-size: 0.9375rem;
  }
}

.required {
  color: #ef4444;
  margin-left: 2px;
}

.checkboxContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #0ea5e9;
  }
  
  label {
    margin-bottom: 0;
    cursor: pointer;
  }
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 2rem;
}
