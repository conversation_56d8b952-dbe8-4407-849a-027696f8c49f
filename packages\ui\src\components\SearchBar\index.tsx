import React, { useState, useRef } from 'react';
import styles from './SearchBar.module.css';

interface SearchBarProps {
  placeholder?: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSearch?: (value: string) => void;
  value?: string;
  className?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Search...',
  onChange,
  onSearch,
  value,
  className,
}) => {
  const [controlledValue, setControlledValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (value === undefined) {
      setControlledValue(e.target.value);
    }
    onChange(e);
  };
  
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && onSearch) {
      onSearch(value !== undefined ? value : controlledValue);
    }
  };
  
  const handleClear = () => {
    if (value === undefined) {
      setControlledValue('');
    }
    
    if (inputRef.current) {
      inputRef.current.value = '';
      onChange({
        target: { value: '' },
      } as React.ChangeEvent<HTMLInputElement>);
      
      if (onSearch) {
        onSearch('');
      }
      
      inputRef.current.focus();
    }
  };
  
  return (
    <div className={`${styles.container} ${className || ''}`}>
      <svg 
        className={styles.searchIcon} 
        width="16" 
        height="16" 
        viewBox="0 0 16 16"
        fill="none"
      >
        <path
          d="M7.33333 12.6667C10.2789 12.6667 12.6667 10.2789 12.6667 7.33333C12.6667 4.38781 10.2789 2 7.33333 2C4.38781 2 2 4.38781 2 7.33333C2 10.2789 4.38781 12.6667 7.33333 12.6667Z"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M14 14L11 11"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
      <input
        ref={inputRef}
        type="text"
        placeholder={placeholder}
        value={value !== undefined ? value : controlledValue}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        className={styles.input}
      />
      {(value || controlledValue) && (
        <button 
          className={styles.clearButton} 
          onClick={handleClear} 
          type="button"
          aria-label="Clear search"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path
              d="M12 4L4 12"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M4 4L12 12"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      )}
    </div>
  );
};
