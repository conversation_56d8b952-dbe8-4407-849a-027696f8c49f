import { Server as SocketIOServer, Socket } from 'socket.io';
import { SocketEvents, SendMessageData, SocketResponse } from '../../types';
import * as chatMessageService from '../../services/chat-message-service';
import * as translationService from '../../services/translation-service';

export const registerMessageHandlers = (io: SocketIOServer, socket: Socket) => {
  // Handler cho event SEND_MESSAGE
  socket.on(SocketEvents.SEND_MESSAGE, async (messageData: SendMessageData, callback?: (response: SocketResponse) => void) => {
    try {
      const userId = socket.data.userId;
      const temporaryUserId = socket.data.temporaryUserId;
      
      if (!userId && !temporaryUserId) {
        const response: SocketResponse = {
          success: false,
          error: 'Authentication required'
        };
        if (callback) callback(response);
        return;
      }
      
      // Kiểm tra quyền gửi tin nhắn trong phòng chat
      const canSendMessage = await chatMessageService.canSendMessage(
        messageData.roomId, 
        userId, 
        temporaryUserId
      );
      
      if (!canSendMessage) {
        const response: SocketResponse = {
          success: false,
          error: 'You do not have permission to send messages in this room'
        };
        if (callback) callback(response);
        return;
      }
      
      // Lưu tin nhắn vào database
      const savedMessage = await chatMessageService.createMessage({
        roomId: messageData.roomId,
        userId,
        temporaryUserId,
        content: messageData.content,
        contentType: messageData.contentType || 'text',
        originalLanguage: messageData.originalLanguage || socket.data.preferredLanguage || 'en',
        metadata: messageData.metadata
      });
      
      if (!savedMessage) {
        const response: SocketResponse = {
          success: false,
          error: 'Failed to save message'
        };
        if (callback) callback(response);
        return;
      }
      
      // Thông báo cho người dùng đã gửi tin nhắn thành công
      const response: SocketResponse = {
        success: true,
        data: { message: savedMessage }
      };
      if (callback) callback(response);
      
      // Thông báo cho người dùng khác trong phòng
      socket.to(messageData.roomId).emit(SocketEvents.MESSAGE_RECEIVED, {
        message: savedMessage,
        sender: {
          userId,
          temporaryUserId,
          socketId: socket.id
        }
      });
      
      console.log(`Message sent to room ${messageData.roomId} by user ${userId || temporaryUserId}`);
      
      // Xử lý dịch tin nhắn cho người dùng có ngôn ngữ khác
      try {
        const translations = await translationService.translateMessageForRoom(
          savedMessage.id,
          messageData.roomId,
          messageData.originalLanguage
        );
        
        // Gửi bản dịch cho người dùng trong phòng
        if (translations && translations.length > 0) {
          translations.forEach(translation => {
            io.to(messageData.roomId).emit(SocketEvents.TRANSLATION_RECEIVED, {
              messageId: savedMessage.id,
              language: translation.language,
              translatedContent: translation.translatedContent
            });
          });
        }
      } catch (translationError) {
        console.error('Error translating message:', translationError);
        // Không trả lỗi dịch cho người dùng, tin nhắn vẫn được gửi thành công
      }
    } catch (error) {
      console.error('Error in SEND_MESSAGE handler:', error);
      const response: SocketResponse = {
        success: false,
        error: 'Failed to send message'
      };
      if (callback) callback(response);
    }
  });  
  // Handler cho event REQUEST_TRANSLATION
  socket.on(SocketEvents.REQUEST_TRANSLATION, async (data: { messageId: string, targetLanguage: string }, callback?: (response: SocketResponse) => void) => {
    try {
      const { messageId, targetLanguage } = data;
      
      // Dịch tin nhắn sang ngôn ngữ mục tiêu
      const translation = await translationService.translateMessage(
        messageId,
        targetLanguage
      );
      
      if (!translation) {
        const response: SocketResponse = {
          success: false,
          error: 'Failed to translate message'
        };
        if (callback) callback(response);
        return;
      }
      
      // Trả về bản dịch cho người dùng
      const response: SocketResponse = {
        success: true,
        data: { translation }
      };
      if (callback) callback(response);
    } catch (error) {
      console.error('Error in REQUEST_TRANSLATION handler:', error);
      const response: SocketResponse = {
        success: false,
        error: 'Failed to request translation'
      };
      if (callback) callback(response);
    }
  });
};
