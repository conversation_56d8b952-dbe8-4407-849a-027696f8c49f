import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Alert } from './index';

const meta = {
  title: 'UI/Alert',
  component: Alert,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['info', 'success', 'warning', 'error'],
      description: 'Alert variant',
    },
    title: {
      control: 'text',
      description: 'Alert title',
    },
    children: {
      control: 'text',
      description: 'Alert content',
    },
    closable: {
      control: 'boolean',
      description: 'Show close button',
    },
    onClose: { 
      action: 'closed',
      description: 'Close button click handler',
    },
    icon: {
      control: { disable: true },
      description: 'Custom icon to display',
    },
  },
} satisfies Meta<typeof Alert>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Info: Story = {
  args: {
    variant: 'info',
    children: 'This is an information alert.',
  },
};

export const Success: Story = {
  args: {
    variant: 'success',
    children: 'Operation completed successfully!',
  },
};

export const Warning: Story = {
  args: {
    variant: 'warning',
    children: 'Warning: This action cannot be undone.',
  },
};

export const Error: Story = {
  args: {
    variant: 'error',
    children: 'An error occurred. Please try again.',
  },
};

export const WithTitle: Story = {
  args: {
    variant: 'info',
    title: 'Information',
    children: 'This is an information alert with a title.',
  },
};

export const Closable: Story = {
  args: {
    variant: 'success',
    closable: true,
    children: 'This alert can be closed.',
  },
};

export const LongContent: Story = {
  args: {
    variant: 'warning',
    title: 'Warning',
    children: 'This is an alert with longer content. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed ac nulla at felis dapibus commodo. Vivamus consequat nisi vel sagittis ultricies.',
  },
};

export const AlertsShowcase: Story = {
  name: 'All Alerts',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Alert variant="info">
        This is an information alert.
      </Alert>
      
      <Alert variant="success">
        Operation completed successfully!
      </Alert>
      
      <Alert variant="warning">
        Warning: This action cannot be undone.
      </Alert>
      
      <Alert variant="error">
        An error occurred. Please try again.
      </Alert>
      
      <Alert variant="info" title="Information">
        This is an information alert with a title.
      </Alert>
      
      <Alert variant="success" title="Success" closable onClose={() => console.log('Closed')}>
        This is a success alert with a title and close button.
      </Alert>
    </div>
  ),
};

export const CustomIcons: Story = {
  name: 'Custom Icons',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Alert
        variant="info"
        icon={
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 1.91667C4.59642 1.91667 1.91667 4.59642 1.91667 8C1.91667 11.4036 4.59642 14.0833 8 14.0833C11.4036 14.0833 14.0833 11.4036 14.0833 8C14.0833 4.59642 11.4036 1.91667 8 1.91667ZM0.75 8C0.75 3.99594 3.99594 0.75 8 0.75C12.0041 0.75 15.25 3.99594 15.25 8C15.25 12.0041 12.0041 15.25 8 15.25C3.99594 15.25 0.75 12.0041 0.75 8ZM8 6.83333C8.31971 6.83333 8.58333 7.09695 8.58333 7.41667V10.75C8.58333 11.0697 8.31971 11.3333 8 11.3333C7.68029 11.3333 7.41667 11.0697 7.41667 10.75V7.41667C7.41667 7.09695 7.68029 6.83333 8 6.83333ZM8 5.08333C8.31971 5.08333 8.58333 4.81971 8.58333 4.5C8.58333 4.18029 8.31971 3.91667 8 3.91667C7.68029 3.91667 7.41667 4.18029 7.41667 4.5C7.41667 4.81971 7.68029 5.08333 8 5.08333Z" fill="currentColor"/>
          </svg>
        }
      >
        This is an information alert with custom icon.
      </Alert>
      
      <Alert
        variant="success"
        icon={
          <span style={{ fontSize: '16px' }}>✅</span>
        }
      >
        This is a success alert with emoji icon.
      </Alert>
    </div>
  ),
};

export const NestedContent: Story = {
  name: 'Nested Content',
  parameters: { controls: { disable: true } },
  render: () => (
    <Alert variant="info" title="Terms and Conditions">
      <div>
        <p style={{ margin: '0 0 8px 0' }}>Please read the following terms:</p>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li>Item 1: Lorem ipsum dolor sit amet</li>
          <li>Item 2: Consectetur adipiscing elit</li>
          <li>Item 3: Sed do eiusmod tempor incididunt</li>
        </ul>
      </div>
    </Alert>
  ),
};
