@import '../../styles/_variables.scss';

// <PERSON><PERSON>i sử dụng hầu hết CSS từ trang chỉnh sửa người dùng
.container {
  padding: $spacing-md;
  max-width: 1200px;
  margin: 0 auto;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
}

.titleSection {
  display: flex;
  flex-direction: column;
}

.backLink {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $gray;
  text-decoration: none;
  margin-bottom: $spacing-xs;
  font-size: 14px;
  
  &:hover {
    color: $primary-color;
  }
}

.pageTitle {
  font-size: 24px;
  font-weight: 600;
  color: $black;
  margin: 0;
}

.userForm {
  margin-top: $spacing-lg;
}

.formLayout {
  display: flex;
  flex-direction: column;
  gap: $spacing-xl;
}

.formSection {
  background-color: $white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  padding: $spacing-lg;
}

.sectionTitle {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 $spacing-lg;
  padding-bottom: $spacing-sm;
  border-bottom: 1px solid $secondary-color;
}

.formGroup {
  margin-bottom: $spacing-md;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  display: block;
  font-weight: 500;
  margin-bottom: $spacing-sm;
}

.input, .select, .textarea {
  display: block;
  width: 100%;
  padding: 10px 12px;
  border: 1px solid $secondary-color;
  border-radius: $border-radius-md;
  font-size: 16px;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
}

.select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6 9L2 5h8L6 9z' fill='%23464646'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 30px;
}

.textarea {
  resize: vertical;
  min-height: 100px;
}

.checkboxField {
  display: flex;
  align-items: center;
  padding: $spacing-sm 0;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  cursor: pointer;
  user-select: none;
  
  input {
    margin: 0;
  }
  
  span {
    font-size: 16px;
  }
}

.avatarField {
  display: flex;
  gap: $spacing-md;
  align-items: flex-start;
  
  .input {
    flex-grow: 1;
  }
}

.avatarPreview {
  width: 60px;
  height: 60px;
  border-radius: $border-radius-sm;
  overflow: hidden;
  border: 1px solid $secondary-color;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.languageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: $spacing-md;
}

.languageCheckbox {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-xs;
  cursor: pointer;
  user-select: none;
  
  input {
    margin: 0;
  }
  
  span {
    font-size: 16px;
  }
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: $spacing-md;
  margin-top: $spacing-lg;
  padding: $spacing-lg;
  background-color: $white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
}

.primaryButton {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: 10px 20px;
  background-color: $primary-color;
  color: $white;
  border-radius: $border-radius-md;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    opacity: 0.9;
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.secondaryButton {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: 10px 20px;
  background-color: $secondary-color;
  color: $black;
  border-radius: $border-radius-md;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    opacity: 0.9;
  }
}

.fieldError {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-sm;
  font-size: 14px;
  margin-bottom: $spacing-md;
}
