@import '../styles/_variables';

.container {
  width: 100%;
  max-width: 1200px;
  padding: 24px;
  margin: 0 auto;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-lg;
}

.pageTitle {
  font-size: 24px;
  font-weight: 600;
  color: $dark-gray;
  margin: 0;
}

.pageDescription {
  font-size: 14px;
  color: $gray;
  margin: $spacing-xs 0 0;
}

.addButton {
  display: flex;
  align-items: center;
  background-color: $primary-color;
  color: white;
  padding: 8px 16px;
  border-radius: $border-radius-md;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: darken($primary-color, 10%);
  }

  svg {
    margin-right: 8px;
  }
  
  &:disabled, &[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.limitSection {
  background-color: white;
  border-radius: $border-radius-md;
  padding: $spacing-md;
  margin-bottom: $spacing-md;
  box-shadow: $shadow-sm;
}

.filterBar {
  background-color: white;
  border-radius: $border-radius-md;
  padding: $spacing-md;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-sm;
}

.filterForm {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-md;
}

.filterGroup {
  flex: 1;
  min-width: 200px;
}

.filterInput, .filterSelect {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: $border-radius-sm;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
  }
}

.filterButton {
  background-color: $primary-color;
  color: white;
  border: none;
  border-radius: $border-radius-sm;
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: darken($primary-color, 10%);
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: $gray;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: $primary-color;
  animation: spin 1s linear infinite;
  margin-bottom: $spacing-md;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.qrGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: $spacing-lg;
  margin-bottom: $spacing-xl;
}

.qrGridItem {
  height: 100%;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  background-color: white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  text-align: center;
  margin: $spacing-xl 0;
  
  h3 {
    font-size: 18px;
    margin: $spacing-md 0 $spacing-sm;
    color: $dark-gray;
  }
  
  p {
    color: $gray;
    margin: 0 0 $spacing-lg;
    max-width: 400px;
  }
}

.emptyIcon {
  width: 64px;
  height: 64px;
  color: $gray;
  opacity: 0.5;
}

.emptyButton {
  background-color: $primary-color;
  color: white;
  padding: 10px 16px;
  border-radius: $border-radius-md;
  font-weight: 500;
  text-decoration: none;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: darken($primary-color, 10%);
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: $spacing-md;
  margin-top: $spacing-xl;
}

.paginationButton {
  display: flex;
  align-items: center;
  background: none;
  border: 1px solid #ddd;
  padding: 6px 12px;
  border-radius: $border-radius-sm;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  color: $dark-gray;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &:not(:disabled):hover {
    border-color: $primary-color;
    color: $primary-color;
  }
  
  svg {
    margin-right: 4px;
    
    &:last-child {
      margin-right: 0;
      margin-left: 4px;
    }
  }
}

.paginationPages {
  display: flex;
  gap: 4px;
}

.paginationPage {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: $border-radius-sm;
  border: none;
  background: none;
  cursor: pointer;
  color: $dark-gray;
  font-size: 14px;
  
  &:hover:not(.active):not(:disabled) {
    background-color: #f5f5f5;
  }
  
  &.active {
    background-color: $primary-color;
    color: white;
  }
  
  &:disabled {
    cursor: not-allowed;
  }
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.headerActions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.extraActions {
  display: flex;
  gap: 0.5rem;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  background-color: #f3f4f6;
  color: #4b5563;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #e5e7eb;
  }
  
  svg {
    width: 18px;
    height: 18px;
  }
}

.viewToggle {
  display: flex;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  overflow: hidden;
}

.viewButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  background-color: #f9fafb;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f3f4f6;
  }
  
  &.active {
    background-color: #e5e7eb;
    color: #1f2937;
  }
  
  svg {
    width: 20px;
    height: 20px;
  }
}

// Danh sách dạng hàng
.qrList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.qrRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.qrInfo {
  flex: 1;
}

.qrLocation {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.qrDescription {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  max-width: 400px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.qrStats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-right: 1.5rem;
}

.scanCount {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
  color: #4b5563;
  margin-bottom: 0.25rem;
  
  svg {
    width: 16px;
    height: 16px;
    color: #6b7280;
  }
}

.qrStatus {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
  color: #4b5563;
}

.statusIndicator {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  
  &.active {
    background-color: #10b981;
  }
  
  &.inactive {
    background-color: #ef4444;
  }
}

.qrActions {
  display: flex;
  gap: 0.5rem;
}

.qrActions .actionButton {
  padding: 0.375rem;
  
  svg {
    width: 16px;
    height: 16px;
  }
}

@media (max-width: 768px) {
  .pageHeader {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .headerActions {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .extraActions {
    width: 100%;
    justify-content: space-between;
  }
  
  .qrRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .qrStats {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-right: 0;
  }
  
  .qrActions {
    width: 100%;
    justify-content: flex-end;
  }
}
