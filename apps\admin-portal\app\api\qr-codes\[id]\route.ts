import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// To Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// GET: Lay thong tin chi tiet mot QR code
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const qrCodeId = params.id;
    
    // Tao Supabase client
    const supabase = createSupabaseClient();
    
    // Lay thong tin QR code
    const { data, error } = await supabase
      .from('tenant_qr_codes')
      .select(`
        *,
        tenant_rooms!tenant_qr_codes_room_number_tenant_id_fkey(
          room_type,
          floor,
          status
        ),
        reception_point:tenant_message_reception_points!tenant_qr_codes_reception_point_id_fkey(
          id, 
          name, 
          code
        )
      `)
      .eq('id', qrCodeId)
      .single();
      
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'QR code not found' }, { status: 404 });
      }
      console.error('Error fetching QR code:', error);
      throw error;
    }
    
    // Lay thong tin ve so lan quet va lich su dung
    const { data: scanStats, error: scanError } = await supabase
      .from('tenant_qr_code_scans')
      .select('created_at')
      .eq('qr_code_id', qrCodeId)
      .order('created_at', { ascending: false })
      .limit(10);
      
    // Tra ve ket qua
    return NextResponse.json({
      data,
      scan_history: scanError ? [] : scanStats || []
    });
    
  } catch (error) {
    console.error('Error in GET QR code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT: Cap nhat thong tin QR code
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const qrCodeId = params.id;
    const { 
      name,
      description, 
      location, 
      room_number, 
      status, 
      reception_point_id,
      target_department,
      custom_action,
      qr_type_id,
      target_type,
      target_id,
      is_active 
    } = await request.json();
    
    // Tao Supabase client
    const supabase = createSupabaseClient();
    
    // Kiem tra QR code co ton tai khong
    const { data: existingQR, error: fetchError } = await supabase
      .from('tenant_qr_codes')
      .select('id, tenant_id')
      .eq('id', qrCodeId)
      .single();
      
    if (fetchError || !existingQR) {
      return NextResponse.json({ error: 'QR code not found' }, { status: 404 });
    }
    
    // Cap nhat thong tin QR code
    const updateData: any = {
      updated_at: new Date().toISOString()
    };
    
    // Chi cap nhat cac truong duoc cung cap
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (location !== undefined) updateData.location = location;
    if (room_number !== undefined) updateData.room_number = room_number || null; // Set null if empty string
    if (status !== undefined) updateData.status = status;
    if (reception_point_id !== undefined) updateData.reception_point_id = reception_point_id || null;
    if (target_department !== undefined) updateData.target_department = target_department || null;
    if (custom_action !== undefined) updateData.custom_action = custom_action;
    if (qr_type_id !== undefined) updateData.qr_type_id = qr_type_id;
    if (is_active !== undefined) updateData.is_active = is_active;
    
    // Handle target_type and target_id
    if (target_type && target_id) {
      updateData.target_type = target_type;
      updateData.target_id = target_id;
      
      // For backward compatibility
      if (target_type === 'room') {
        updateData.room_id = target_id;
      }
    } else if (target_type === 'none') {
      // Clear target information
      updateData.target_type = null;
      updateData.target_id = null;
      updateData.room_id = null;
    }
    
    const { data, error } = await supabase
      .from('tenant_qr_codes')
      .update(updateData)
      .eq('id', qrCodeId)
      .select()
      .single();
      
    if (error) {
      console.error('Error updating QR code:', error);
      throw error;
    }
    
    // Tra ve ket qua
    return NextResponse.json({ data, message: 'QR code updated successfully' });
    
  } catch (error) {
    console.error('Error in PUT QR code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
// DELETE: Xoa QR code
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const qrCodeId = params.id;
    
    // Tao Supabase client
    const supabase = createSupabaseClient();
    
    // Kiem tra QR code co ton tai khong
    const { data: existingQR, error: fetchError } = await supabase
      .from('tenant_qr_codes')
      .select('id, tenant_id')
      .eq('id', qrCodeId)
      .single();
      
    if (fetchError || !existingQR) {
      return NextResponse.json({ error: 'QR code not found' }, { status: 404 });
    }
    
    // Xoa QR code
    const { error } = await supabase
      .from('tenant_qr_codes')
      .delete()
      .eq('id', qrCodeId);
      
    if (error) {
      console.error('Error deleting QR code:', error);
      throw error;
    }
    
    // Lay thong tin gioi han QR codes moi nhat
    const { data: limitInfo } = await supabase.rpc('get_tenant_qr_code_usage', {
      tenant_id_param: existingQR.tenant_id
    });
    
    // Tra ve ket qua
    return NextResponse.json({
      message: 'QR code deleted successfully',
      limits: limitInfo && limitInfo[0] ? limitInfo[0] : null
    });
    
  } catch (error) {
    console.error('Error in DELETE QR code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
