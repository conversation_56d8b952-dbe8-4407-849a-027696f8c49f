import express from 'express';
import http from 'http';
import cors from 'cors';
import dotenv from 'dotenv';
import { initializeSocketServer } from './websocket/socket-server';
import roomRoutes from './routes/room-routes';
import messageRoutes from './routes/message-routes';
import { checkTableAccess } from './services/chat-message-service';

// Kiểm tra quyền truy cập database khi khởi động
checkTableAccess().then(hasAccess => {
  if (hasAccess) {
    console.log('✅ Có quyền truy cập vào bảng chat_messages');
  } else {
    console.log('❌ Không có quyền truy cập vào bảng chat_messages');
    console.log('🛠 Cần thực hiện script SQL để vô hiệu hóa RLS hoặc cấp quyền');
  }
});

// Tải biến môi trường
dotenv.config();

// Khởi tạo express app
const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(cors({
  origin: '*', // Trong môi trường sản xuất, hãy giới hạn nguồn gốc
  credentials: true
}));
app.use(express.json());

// API routes
app.use('/api/rooms', roomRoutes);
app.use('/api/messages', messageRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', service: 'chat-service' });
});

// Tạo HTTP server
const server = http.createServer(app);

// Khởi tạo Socket.IO server
const io = initializeSocketServer(server);

// Khởi động server
server.listen(PORT, () => {
  console.log(`Chat service running on port ${PORT}`);
  console.log(`WebSocket server running at ws://localhost:${PORT}`);
});

export default app;
