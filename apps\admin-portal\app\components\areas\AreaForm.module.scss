.form {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.error {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 0.875rem;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-bottom: 24px;
  
  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroupFull {
  display: flex;
  flex-direction: column;
  grid-column: 1 / -1;
}

.formGroupCheckbox {
  grid-column: 1 / -1;
  margin-top: 8px;
}

.label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.required {
  color: #ef4444;
  margin-left: 2px;
}

.input,
.select,
.textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #111827;
  transition: all 0.2s;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
}

.textarea {
  resize: vertical;
  min-height: 100px;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  cursor: pointer;
  
  span {
    font-size: 0.875rem;
    color: #374151;
    margin-left: 8px;
  }
}

.checkbox {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  cursor: pointer;
}

.buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancelButton {
  padding: 10px 16px;
  background-color: white;
  color: #4b5563;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #f9fafb;
    color: #111827;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.submitButton {
  padding: 10px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #2563eb;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}