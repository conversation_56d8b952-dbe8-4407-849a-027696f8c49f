'use client';
import { useState, useEffect } from 'react';
import styles from './RoomSelectionModal.module.scss';

interface Room {
  room_number: string;
  room_type: string;
  floor: string;
  status: string;
}

interface RoomSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (roomNumber: string) => void;
  title: string;
}

export default function RoomSelectionModal({
  isOpen,
  onClose,
  onConfirm,
  title
}: RoomSelectionModalProps) {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRoom, setSelectedRoom] = useState<string>('');
  const [customRoomNumber, setCustomRoomNumber] = useState<string>('');
  const [useCustomRoom, setUseCustomRoom] = useState(false);

  useEffect(() => {
    // Reset state khi modal đóng
    if (!isOpen) {
      setSelectedRoom('');
      setCustomRoomNumber('');
      setUseCustomRoom(false);
      return;
    }

    // Lấy danh sách phòng trống
    const fetchAvailableRooms = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/rooms?status=available');
        const result = await response.json();
        
        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch rooms');
        }
        
        setRooms(result.data || []);
      } catch (err) {
        console.error('Error fetching rooms:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchAvailableRooms();
  }, [isOpen]);

  const handleConfirm = () => {
    const roomNumber = useCustomRoom ? customRoomNumber : selectedRoom;
    if (!roomNumber) return;
    
    onConfirm(roomNumber);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modal}>
        <div className={styles.modalHeader}>
          <h2>{title}</h2>
          <button className={styles.closeButton} onClick={onClose}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
        
        <div className={styles.modalBody}>
          {loading ? (
            <div className={styles.loading}>Đang tải danh sách phòng...</div>
          ) : (
            <>
              {rooms.length > 0 ? (
                <div className={styles.roomGrid}>
                  {rooms.map(room => (
                    <div 
                      key={room.room_number}
                      className={`${styles.roomCard} ${selectedRoom === room.room_number ? styles.selected : ''}`}
                      onClick={() => {
                        setSelectedRoom(room.room_number);
                        setUseCustomRoom(false);
                      }}
                    >
                      <div className={styles.roomNumber}>{room.room_number}</div>
                      <div className={styles.roomType}>{room.room_type}</div>
                      <div className={styles.roomFloor}>Tầng {room.floor}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className={styles.noRooms}>Không tìm thấy phòng trống.</div>
              )}
              
              <div className={styles.customRoomSection}>
                <div className={styles.checkboxGroup}>
                  <input 
                    type="checkbox" 
                    id="use-custom-room" 
                    checked={useCustomRoom}
                    onChange={() => setUseCustomRoom(!useCustomRoom)}
                  />
                  <label htmlFor="use-custom-room">Chỉ định số phòng thủ công</label>
                </div>
                
                {useCustomRoom && (
                  <input
                    type="text"
                    placeholder="Nhập số phòng"
                    value={customRoomNumber}
                    onChange={(e) => setCustomRoomNumber(e.target.value)}
                    className={styles.customRoomInput}
                  />
                )}
              </div>
            </>
          )}
        </div>
        
        <div className={styles.modalFooter}>
          <button className={styles.cancelButton} onClick={onClose}>
            Hủy
          </button>
          <button 
            className={styles.confirmButton} 
            onClick={handleConfirm}
            disabled={!selectedRoom && (!useCustomRoom || !customRoomNumber)}
          >
            Xác nhận
          </button>
        </div>
      </div>
    </div>
  );
}
