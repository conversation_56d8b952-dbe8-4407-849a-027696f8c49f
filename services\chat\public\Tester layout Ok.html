<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LoaLoa Chat Tester v2</title>
  <!-- Supabase SDK -->
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <!-- Socket.IO Client -->
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f7fa;
      padding: 20px;
    }
    
    .app-container {
      max-width: 1400px;
      margin: 0 auto;
    }
    
    .main-title {
      margin-bottom: 30px;
      color: #2c3e50;
      text-align: center;
    }
    
    .step-card {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      overflow: hidden;
    }
    
    .step-header {
      background-color: #4CAF50;
      color: white;
      padding: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .step-header h3 {
      margin: 0;
      font-size: 18px;
    }
    
    .step-number {
      background-color: white;
      color: #4CAF50;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 15px;
    }
    
    .step-content {
      padding: 20px;
    }
    
    .status-badge {
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
      margin-left: 10px;
    }
    
    .badge-pending {
      background-color: #f39c12;
      color: white;
    }
    
    .badge-success {
      background-color: #2ecc71;
      color: white;
    }
    
    .badge-error {
      background-color: #e74c3c;
      color: white;
    }
    
    .code-block {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 15px;
      margin: 10px 0;
      overflow-x: auto;
      font-family: 'Courier New', Courier, monospace;
      font-size: 14px;
    }
    
    .form-label {
      font-weight: 500;
      margin-bottom: 8px;
    }
    
    .dual-chat-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      height: 500px;
    }
    
    .chat-window {
      display: flex;
      flex-direction: column;
      border: 1px solid #ddd;
      border-radius: 10px;
      overflow: hidden;
      height: 100%;
    }
    
    .chat-header {
      padding: 15px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #ddd;
    }
    
    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 15px;
      background-color: white;
    }
    
    .chat-input {
      padding: 10px;
      border-top: 1px solid #ddd;
      display: flex;
    }
    
    .message {
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;
    }
    
    .message-content {
      padding: 10px;
      border-radius: 10px;
      max-width: 80%;
    }
    
    .sent {
      align-self: flex-end;
      background-color: #dcf8c6;
    }
    
    .received {
      align-self: flex-start;
      background-color: #f1f0f0;
    }
    
    .message-meta {
      font-size: 12px;
      color: #888;
      margin-top: 5px;
    }
    
    .message-translation {
      font-style: italic;
      font-size: 13px;
      margin-top: 8px;
      padding: 5px;
      border-left: 2px solid #ddd;
    }
    
    .user-pill {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 20px;
      margin-right: 10px;
      margin-bottom: 10px;
      background-color: #e9ecef;
    }
    
    .user-pill.active {
      background-color: #4CAF50;
      color: white;
    }
    
    .room-pill {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 20px;
      margin-right: 10px;
      margin-bottom: 10px;
      background-color: #e9ecef;
      cursor: pointer;
    }
    
    .room-pill.active {
      background-color: #4CAF50;
      color: white;
    }
    
    .debug-log {
      height: 200px;
      overflow-y: auto;
      font-family: 'Courier New', Courier, monospace;
      font-size: 12px;
      background-color: #2c3e50;
      color: #ecf0f1;
      padding: 10px;
      border-radius: 5px;
    }
    
    .collapsible {
      cursor: pointer;
    }
    
    .collapse-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease-out;
    }
    
    .expanded {
      max-height: 1000px;
    }
    
    .btn-primary {
      background-color: #4CAF50;
      border-color: #4CAF50;
    }
    
    .btn-primary:hover {
      background-color: #45a049;
      border-color: #45a049;
    }
    
    .btn-outline-primary {
      color: #4CAF50;
      border-color: #4CAF50;
    }
    
    .btn-outline-primary:hover {
      background-color: #4CAF50;
      color: white;
    }
    
    /* Switch tabs */
    .tab-container {
      display: flex;
      margin-bottom: 15px;
    }
    
    .tab {
      padding: 10px 15px;
      background-color: #f8f9fa;
      cursor: pointer;
      border: 1px solid #dee2e6;
      border-bottom: none;
      border-radius: 5px 5px 0 0;
      margin-right: 5px;
    }
    
    .tab.active {
      background-color: white;
      border-bottom: 2px solid #4CAF50;
      color: #4CAF50;
      font-weight: bold;
    }
    
    .tab-content {
      display: none;
      background-color: white;
      border: 1px solid #dee2e6;
      border-radius: 0 5px 5px 5px;
      padding: 15px;
    }
    
    .tab-content.active {
      display: block;
    }
    
    /* Data table */
    .data-table {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
    }
    
    .data-table th, .data-table td {
      padding: 10px;
      border: 1px solid #dee2e6;
      text-align: left;
    }
    
    .data-table th {
      background-color: #f8f9fa;
      font-weight: bold;
    }
    
    .data-table tr:nth-child(even) {
      background-color: #f8f9fa;
    }
    
    .data-table tr:hover {
      background-color: #f1f1f1;
    }
  </style>
</head>
<body>
  <div class="app-container">
    <h1 class="main-title">LoaLoa Chat Tester v2</h1>
    
    <!-- Step 1: Connect to Supabase -->
    <div class="step-card">
      <div class="step-header">
        <div class="d-flex align-items-center">
          <div class="step-number">1</div>
          <h3>Connect to Supabase</h3>
        </div>
        <span id="supabase-status" class="status-badge badge-pending">Pending</span>
      </div>
      <div class="step-content">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="supabase-url" class="form-label">Supabase URL</label>
              <input type="text" class="form-control" id="supabase-url" value="https://iwzwbrbmojvvvfstbqow.supabase.co">
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="supabase-key" class="form-label">Supabase Key</label>
              <input type="text" class="form-control" id="supabase-key" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3endicmJtb2p2dnZmc3RicW93Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMjkyNjEsImV4cCI6MjA2MTkwNTI2MX0.tyVtaSclUKC5fGh7I7Ohpm7c4FniXphYe34-cxBvo6E">
            </div>
          </div>
        </div>
        <button id="connect-supabase-btn" class="btn btn-primary">Connect to Supabase</button>
      </div>
    </div>
    
    <!-- Step 2: Create/Get JWT Token -->
    <div class="step-card">
      <div class="step-header">
        <div class="d-flex align-items-center">
          <div class="step-number">2</div>
          <h3>Generate JWT Token</h3>
        </div>
        <span id="jwt-status" class="status-badge badge-pending">Pending</span>
      </div>
      <div class="step-content">
        <div class="alert alert-info mb-3">
          <p><strong>Need a JWT Token?</strong> Run this command:</p>
          <div class="code-block">
            cd D:\loaloa\services\chat<br>
            npx ts-node src\tests\generate-token.ts
          </div>
        </div>
        <div class="mb-3">
          <label for="jwt-token" class="form-label">JWT Token</label>
          <textarea class="form-control" id="jwt-token" rows="3"></textarea>
        </div>
        <button id="validate-jwt-btn" class="btn btn-primary">Validate Token</button>
      </div>
    </div>
    
    <!-- Step 3: View Database Structure -->
    <div class="step-card">
      <div class="step-header">
        <div class="d-flex align-items-center">
          <div class="step-number">3</div>
          <h3>Database Explorer</h3>
        </div>
        <span id="database-status" class="status-badge badge-pending">Pending</span>
      </div>
      <div class="step-content">
        <div class="tab-container">
          <div class="tab active" data-tab="users-tab">Users</div>
          <div class="tab" data-tab="rooms-tab">Chat Rooms</div>
          <div class="tab" data-tab="participants-tab">Participants</div>
          <div class="tab" data-tab="messages-tab">Messages</div>
        </div>
        
        <div id="users-tab-content" class="tab-content active">
          <div class="d-flex justify-content-between mb-3">
            <h4>Users</h4>
            <button id="load-users-btn" class="btn btn-primary btn-sm">Load Users</button>
          </div>
          <div id="users-container" class="data-container">
            <p>No data loaded yet</p>
          </div>
        </div>
        
        <div id="rooms-tab-content" class="tab-content">
          <div class="d-flex justify-content-between mb-3">
            <h4>Chat Rooms</h4>
            <button id="load-rooms-btn" class="btn btn-primary btn-sm">Load Rooms</button>
          </div>
          <div id="rooms-container" class="data-container">
            <p>No data loaded yet</p>
          </div>
        </div>
        
        <div id="participants-tab-content" class="tab-content">
          <div class="d-flex justify-content-between mb-3">
            <h4>Chat Participants</h4>
            <button id="load-participants-btn" class="btn btn-primary btn-sm">Load Participants</button>
          </div>
          <div id="participants-container" class="data-container">
            <p>No data loaded yet</p>
          </div>
        </div>
        
        <div id="messages-tab-content" class="tab-content">
          <div class="d-flex justify-content-between mb-3">
            <h4>Chat Messages</h4>
            <button id="load-messages-btn" class="btn btn-primary btn-sm">Load Messages</button>
          </div>
          <div id="messages-container" class="data-container">
            <p>No data loaded yet</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Step 4: Ensure User is in Chat Room -->
    <div class="step-card">
      <div class="step-header">
        <div class="d-flex align-items-center">
          <div class="step-number">4</div>
          <h3>User-Room Management</h3>
        </div>
        <span id="user-room-status" class="status-badge badge-pending">Pending</span>
      </div>
      <div class="step-content">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="selected-user" class="form-label">Select User</label>
              <select class="form-select" id="selected-user">
                <option value="">Select a user</option>
              </select>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="selected-room" class="form-label">Select Room</label>
              <select class="form-select" id="selected-room">
                <option value="">Select a room</option>
              </select>
            </div>
          </div>
        </div>
        <div class="mb-3">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="is-active-switch" checked>
            <label class="form-check-label" for="is-active-switch">User is active in room</label>
          </div>
        </div>
        <button id="check-membership-btn" class="btn btn-outline-primary me-2">Check Membership</button>
        <button id="add-to-room-btn" class="btn btn-primary">Add to Room</button>
        <div id="membership-result" class="mt-3"></div>
      </div>
    </div>
    
    <!-- Step 5: Connect WebSocket -->
    <div class="step-card">
      <div class="step-header">
        <div class="d-flex align-items-center">
          <div class="step-number">5</div>
          <h3>WebSocket Connection</h3>
        </div>
        <span id="websocket-status" class="status-badge badge-pending">Pending</span>
      </div>
      <div class="step-content">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="websocket-url" class="form-label">WebSocket Server URL</label>
              <input type="text" class="form-control" id="websocket-url" value="http://localhost:3002">
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="websocket-room" class="form-label">Room to Join</label>
              <input type="text" class="form-control" id="websocket-room" value="c636ea40-1d87-4982-a6a3-86fa0805e258">
            </div>
          </div>
        </div>
        <button id="connect-ws-btn" class="btn btn-primary">Connect WebSocket</button>
        <div class="mt-3">
          <div class="collapsible d-flex align-items-center">
            <i class="fas fa-chevron-right me-2"></i>
            <span>Show Debug Console</span>
          </div>
          <div class="collapse-content">
            <div id="debug-console" class="debug-log mt-2"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Step 6: Chat Test -->
    <div class="step-card">
      <div class="step-header">
        <div class="d-flex align-items-center">
          <div class="step-number">6</div>
          <h3>Test Chat Interface</h3>
        </div>
        <span id="chat-status" class="status-badge badge-pending">Pending</span>
      </div>
      <div class="step-content">
        <div class="dual-chat-container">
          <!-- User 1 Chat -->
          <div class="chat-window">
            <div class="chat-header">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 id="user1-name">User 1</h5>
                  <small id="user1-language" class="text-muted">Vietnamese</small>
                </div>
                <span id="user1-status" class="badge bg-warning">Disconnected</span>
              </div>
              <div class="mt-2">
                <input type="text" class="form-control form-control-sm" id="user1-id" placeholder="User ID" value="973c8e99-9b06-437a-9ab5-e4bdf2aa4b53">
              </div>
            </div>
            <div id="user1-messages" class="chat-messages"></div>
            <div class="chat-input">
              <input type="text" class="form-control" id="user1-input" placeholder="Type a message...">
              <button id="user1-send" class="btn btn-primary ms-2">Send</button>
            </div>
          </div>
          
          <!-- User 2 Chat -->
          <div class="chat-window">
            <div class="chat-header">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 id="user2-name">User 2</h5>
                  <small id="user2-language" class="text-muted">English</small>
                </div>
                <span id="user2-status" class="badge bg-warning">Disconnected</span>
              </div>
              <div class="mt-2">
                <input type="text" class="form-control form-control-sm" id="user2-id" placeholder="User ID" value="a9813ae-9a46-4dc9-9fa3-6f04062f7e50">
              </div>
            </div>
            <div id="user2-messages" class="chat-messages"></div>
            <div class="chat-input">
              <input type="text" class="form-control" id="user2-input" placeholder="Type a message...">
              <button id="user2-send" class="btn btn-primary ms-2">Send</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Global variables
    let supabase;
    let jwtToken = '';
    let user1Socket, user2Socket;
    const chatRooms = [];
    const users = [];
    
    // Helper functions
    function logDebug(message, level = 'info') {
      const debugConsole = document.getElementById('debug-console');
      const timestamp = new Date().toLocaleTimeString();
      let className = 'text-info';
      
      if (level === 'error') className = 'text-danger';
      if (level === 'success') className = 'text-success';
      if (level === 'warning') className = 'text-warning';
      
      debugConsole.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
      debugConsole.scrollTop = debugConsole.scrollHeight;
    }
    
    function updateStatus(stepId, status, message = '') {
      const statusBadge = document.getElementById(`${stepId}-status`);
      statusBadge.className = `status-badge badge-${status}`;
      
      if (status === 'pending') statusBadge.textContent = 'Pending';
      if (status === 'success') statusBadge.textContent = 'Success';
      if (status === 'error') statusBadge.textContent = 'Error';
      
      if (message) logDebug(message, status);
    }
    
    // Step 1: Connect to Supabase
    document.getElementById('connect-supabase-btn').addEventListener('click', async function() {
      try {
        const url = document.getElementById('supabase-url').value;
        const key = document.getElementById('supabase-key').value;
        
        if (!url || !key) {
          throw new Error('Please enter Supabase URL and key');
        }
        
        // Create Supabase client
        supabase = supabase.createClient(url, key);
        
        // Test connection by fetching user data
        const { data, error } = await supabase.from('auth.users').select('*').limit(1);
        
        if (error) {
          console.error('Error connecting to Supabase:', error);
          updateStatus('supabase', 'error', `Failed to connect to Supabase: ${error.message}`);
          return;
        }
        
        updateStatus('supabase', 'success', 'Connected to Supabase successfully');
      } catch (err) {
        console.error('Error connecting to Supabase:', err);
        updateStatus('supabase', 'error', `Failed to connect to Supabase: ${err.message}`);
      }
    });
    
    // Step 2: Validate JWT Token
    document.getElementById('validate-jwt-btn').addEventListener('click', function() {
      try {
        const token = document.getElementById('jwt-token').value;
        
        if (!token) {
          throw new Error('Please enter a JWT token');
        }
        
        // Decode JWT token (simple client-side validation)
        const parts = token.split('.');
        if (parts.length !== 3) {
          throw new Error('Invalid JWT format');
        }
        
        // Get payload
        const payload = JSON.parse(atob(parts[1]));
        console.log('JWT Payload:', payload);
        
        // Check required fields
        if (!payload.userId) {
          throw new Error('Token must contain userId');
        }
        
        // Check expiration
        if (payload.exp && payload.exp * 1000 < Date.now()) {
          throw new Error('Token has expired');
        }
        
        // Store token
        jwtToken = token;
        
        updateStatus('jwt', 'success', `Valid JWT token for user: ${payload.userId}`);
        
        // Pre-fill user IDs for chat test if available
        if (payload.userId) {
          document.getElementById('user1-id').value = payload.userId;
        }
      } catch (err) {
        console.error('Error validating JWT token:', err);
        updateStatus('jwt', 'error', `Invalid JWT token: ${err.message}`);
      }
    });
    
    // Step 3: Database Explorer
    // Tab switching
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', function() {
        const tabId = this.getAttribute('data-tab');
        
        // Remove active class from all tabs
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
        
        // Add active class to clicked tab and content
        this.classList.add('active');
        document.getElementById(`${tabId}-content`).classList.add('active');
      });
    });
    
    // Load Users
    document.getElementById('load-users-btn').addEventListener('click', async function() {
      try {
        if (!supabase) {
          throw new Error('Please connect to Supabase first');
        }
        
        const container = document.getElementById('users-container');
        container.innerHTML = '<p>Loading users...</p>';
        
        // Fetch users
        const { data: authUsers, error } = await supabase.auth.admin.listUsers();
        
        if (error) {
          throw new Error(`Failed to fetch users: ${error.message}`);
        }
        
        // Fallback to direct DB query if admin API not available
        if (!authUsers || authUsers.length === 0) {
          const { data: dbUsers, error: dbError } = await supabase.from('auth.users').select('*');
          
          if (dbError) {
            throw new Error(`Failed to fetch users: ${dbError.message}`);
          }
          
          if (dbUsers && dbUsers.length > 0) {
            displayUsers(dbUsers);
            return;
          }
          
          // If still no users, use hardcoded test user
          const testUsers = [
            { id: '973c8e99-9b06-437a-9ab5-e4bdf2aa4b53', email: '<EMAIL>', user_metadata: { username: 'tuanson214' } },
            { id: 'a9813ae-9a46-4dc9-9fa3-6f04062f7e50', email: '<EMAIL>', user_metadata: { username: 'admin' } }
          ];
          
          displayUsers(testUsers);
          return;
        }
        
        displayUsers(authUsers);
      } catch (err) {
        console.error('Error loading users:', err);
        document.getElementById('users-container').innerHTML = `<p class="text-danger">Error: ${err.message}</p>`;
        updateStatus('database', 'error', `Failed to load users: ${err.message}`);
      }
      
      function displayUsers(userList) {
        const container = document.getElementById('users-container');
        
        if (!userList || userList.length === 0) {
          container.innerHTML = '<p>No users found</p>';
          return;
        }
        
        // Store users globally
        users.length = 0;
        users.push(...userList);
        
        // Create user select options
        const userSelect = document.getElementById('selected-user');
        userSelect.innerHTML = '<option value="">Select a user</option>';
        
        userList.forEach(user => {
          const option = document.createElement('option');
          option.value = user.id;
          option.textContent = user.email || user.user_metadata?.username || user.id;
          userSelect.appendChild(option);
        });
        
        // Display users in table
        let html = `
          <table class="data-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Email</th>
                <th>Username</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
        `;
        
        userList.forEach(user => {
          html += `
            <tr>
              <td>${user.id}</td>
              <td>${user.email || 'N/A'}</td>
              <td>${user.user_metadata?.username || 'N/A'}</td>
              <td>
                <button class="btn btn-sm btn-outline-primary select-user" data-id="${user.id}">Select</button>
              </td>
            </tr>
          `;
        });
        
        html += `
            </tbody>
          </table>
        `;
        
        container.innerHTML = html;
        
        // Add event listeners
        document.querySelectorAll('.select-user').forEach(btn => {
          btn.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            document.getElementById('selected-user').value = userId;
            document.getElementById('user1-id').value = userId;
          });
        });
        
        updateStatus('database', 'success', `Loaded ${userList.length} users`);
      }
    });
    
    // Load Rooms
    document.getElementById('load-rooms-btn').addEventListener('click', async function() {
      try {
   if (!supabase) {
          throw new Error('Please connect to Supabase first');
        }
        
        const container = document.getElementById('rooms-container');
        container.innerHTML = '<p>Loading rooms...</p>';
        
        // Fetch chat rooms
        const { data: rooms, error } = await supabase.from('chat_rooms').select('*');
        
        if (error) {
          throw new Error(`Failed to fetch rooms: ${error.message}`);
        }
        
        // If API doesn't work, try direct approach
        if (!rooms || rooms.length === 0) {
          // Fallback to hardcoded test rooms
          const testRooms = [
            { id: 'c636ea40-1d87-4982-a6a3-86fa0805e258', name: 'Lễ tân', description: 'Phòng chat lễ tân', room_type: 'support', is_active: true },
            { id: '123abc', name: 'Dịch vụ phòng', description: 'Dịch vụ phòng', room_type: 'support', is_active: true },
            { id: '456def', name: 'Nhà hàng', description: 'Đặt món từ nhà hàng', room_type: 'general', is_active: true }
          ];
          
          displayRooms(testRooms);
          return;
        }
        
        displayRooms(rooms);
      } catch (err) {
        console.error('Error loading rooms:', err);
        document.getElementById('rooms-container').innerHTML = `<p class="text-danger">Error: ${err.message}</p>`;
        updateStatus('database', 'error', `Failed to load rooms: ${err.message}`);
      }
      
      function displayRooms(roomList) {
        const container = document.getElementById('rooms-container');
        
        if (!roomList || roomList.length === 0) {
          container.innerHTML = '<p>No rooms found</p>';
          return;
        }
        
        // Store rooms globally
        chatRooms.length = 0;
        chatRooms.push(...roomList);
        
        // Create room select options
        const roomSelect = document.getElementById('selected-room');
        roomSelect.innerHTML = '<option value="">Select a room</option>';
        
        roomList.forEach(room => {
          const option = document.createElement('option');
          option.value = room.id;
          option.textContent = room.name || room.id;
          roomSelect.appendChild(option);
        });
        
        // Update websocket room field if empty
        const websocketRoom = document.getElementById('websocket-room');
        if (!websocketRoom.value && roomList.length > 0) {
          websocketRoom.value = roomList[0].id;
        }
        
        // Display rooms in table
        let html = `
          <table class="data-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Type</th>
                <th>Active</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
        `;
        
        roomList.forEach(room => {
          html += `
            <tr>
              <td>${room.id}</td>
              <td>${room.name || 'N/A'}</td>
              <td>${room.room_type || 'N/A'}</td>
              <td>${room.is_active ? '✅' : '❌'}</td>
              <td>
                <button class="btn btn-sm btn-outline-primary select-room" data-id="${room.id}">Select</button>
              </td>
            </tr>
          `;
        });
        
        html += `
            </tbody>
          </table>
        `;
        
        container.innerHTML = html;
        
        // Add event listeners
        document.querySelectorAll('.select-room').forEach(btn => {
          btn.addEventListener('click', function() {
            const roomId = this.getAttribute('data-id');
            document.getElementById('selected-room').value = roomId;
            document.getElementById('websocket-room').value = roomId;
          });
        });
        
        updateStatus('database', 'success', `Loaded ${roomList.length} rooms`);
      }
    });
    
    // Load Participants
    document.getElementById('load-participants-btn').addEventListener('click', async function() {
      try {
        if (!supabase) {
          throw new Error('Please connect to Supabase first');
        }
        
        const container = document.getElementById('participants-container');
        container.innerHTML = '<p>Loading participants...</p>';
        
        // Fetch chat participants
        const { data: participants, error } = await supabase.from('chat_participants').select('*');
        
        if (error) {
          throw new Error(`Failed to fetch participants: ${error.message}`);
        }
        
        displayParticipants(participants);
      } catch (err) {
        console.error('Error loading participants:', err);
        document.getElementById('participants-container').innerHTML = `<p class="text-danger">Error: ${err.message}</p>`;
        updateStatus('database', 'error', `Failed to load participants: ${err.message}`);
      }
      
      function displayParticipants(participantList) {
        const container = document.getElementById('participants-container');
        
        if (!participantList || participantList.length === 0) {
          container.innerHTML = '<p>No participants found</p>';
          return;
        }
        
        // Display participants in table
        let html = `
          <table class="data-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Room ID</th>
                <th>User ID</th>
                <th>Active</th>
                <th>Role</th>
              </tr>
            </thead>
            <tbody>
        `;
        
        participantList.forEach(participant => {
          html += `
            <tr>
              <td>${participant.id}</td>
              <td>${participant.chat_room_id}</td>
              <td>${participant.user_id || participant.temporary_user_id || 'N/A'}</td>
              <td>${participant.is_active ? '✅' : '❌'}</td>
              <td>${participant.participant_role || 'member'}</td>
            </tr>
          `;
        });
        
        html += `
            </tbody>
          </table>
        `;
        
        container.innerHTML = html;
        updateStatus('database', 'success', `Loaded ${participantList.length} participants`);
      }
    });
    
    // Load Messages
    document.getElementById('load-messages-btn').addEventListener('click', async function() {
      try {
        if (!supabase) {
          throw new Error('Please connect to Supabase first');
        }
        
        const container = document.getElementById('messages-container');
        container.innerHTML = '<p>Loading messages...</p>';
        
        // Get selected room ID
        const roomId = document.getElementById('websocket-room').value;
        if (!roomId) {
          throw new Error('Please select a room first');
        }
        
        // Fetch chat messages for room
        const { data: messages, error } = await supabase
          .from('chat_messages')
          .select('*')
          .eq('chat_room_id', roomId)
          .order('sent_at', { ascending: false })
          .limit(50);
        
        if (error) {
          throw new Error(`Failed to fetch messages: ${error.message}`);
        }
        
        displayMessages(messages);
      } catch (err) {
        console.error('Error loading messages:', err);
        document.getElementById('messages-container').innerHTML = `<p class="text-danger">Error: ${err.message}</p>`;
        updateStatus('database', 'error', `Failed to load messages: ${err.message}`);
      }
      
      function displayMessages(messageList) {
        const container = document.getElementById('messages-container');
        
        if (!messageList || messageList.length === 0) {
          container.innerHTML = '<p>No messages found</p>';
          return;
        }
        
        // Display messages in table
        let html = `
          <table class="data-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Sender</th>
                <th>Content</th>
                <th>Language</th>
                <th>Time</th>
              </tr>
            </thead>
            <tbody>
        `;
        
        messageList.forEach(message => {
          const time = new Date(message.sent_at).toLocaleString();
          html += `
            <tr>
              <td>${message.id}</td>
              <td>${message.sender_id || 'N/A'}</td>
              <td>${message.content}</td>
              <td>${message.original_language || 'N/A'}</td>
              <td>${time}</td>
            </tr>
          `;
        });
        
        html += `
            </tbody>
          </table>
        `;
        
        container.innerHTML = html;
        updateStatus('database', 'success', `Loaded ${messageList.length} messages`);
      }
    });
    
    // Step 4: User-Room Management
    document.getElementById('check-membership-btn').addEventListener('click', async function() {
      try {
        if (!supabase) {
          throw new Error('Please connect to Supabase first');
        }
        
        const userId = document.getElementById('selected-user').value;
        const roomId = document.getElementById('selected-room').value;
        
        if (!userId || !roomId) {
          throw new Error('Please select both user and room');
        }
        
        const resultDiv = document.getElementById('membership-result');
        resultDiv.innerHTML = '<p>Checking membership...</p>';
        
        // Check if user is already a participant in the room
        const { data: participants, error } = await supabase
          .from('chat_participants')
          .select('*')
          .eq('chat_room_id', roomId)
          .eq('user_id', userId);
        
        if (error) {
          throw new Error(`Failed to check membership: ${error.message}`);
        }
        
        if (participants && participants.length > 0) {
          const participant = participants[0];
          const status = participant.is_active ? 'active' : 'inactive';
          resultDiv.innerHTML = `
            <div class="alert alert-success">
              <p>User is a ${status} member of this room.</p>
              <p><strong>Participant ID:</strong> ${participant.id}</p>
              <p><strong>Role:</strong> ${participant.participant_role || 'member'}</p>
              <p><strong>Joined:</strong> ${new Date(participant.joined_at).toLocaleString()}</p>
            </div>
          `;
          
          // Update switch
          document.getElementById('is-active-switch').checked = participant.is_active;
          
          updateStatus('user-room', 'success', `User is a ${status} member of room`);
        } else {
          resultDiv.innerHTML = `
            <div class="alert alert-warning">
              <p>User is not a member of this room.</p>
            </div>
          `;
          updateStatus('user-room', 'warning', 'User is not a member of room');
        }
      } catch (err) {
        console.error('Error checking membership:', err);
        document.getElementById('membership-result').innerHTML = `
          <div class="alert alert-danger">
            <p>Error: ${err.message}</p>
          </div>
        `;
        updateStatus('user-room', 'error', `Failed to check membership: ${err.message}`);
      }
    });
    
    document.getElementById('add-to-room-btn').addEventListener('click', async function() {
      try {
        if (!supabase) {
          throw new Error('Please connect to Supabase first');
        }
        
        const userId = document.getElementById('selected-user').value;
        const roomId = document.getElementById('selected-room').value;
        const isActive = document.getElementById('is-active-switch').checked;
        
        if (!userId || !roomId) {
          throw new Error('Please select both user and room');
        }
        
        const resultDiv = document.getElementById('membership-result');
        resultDiv.innerHTML = '<p>Adding user to room...</p>';
        
        // Check if user is already a participant in the room
        const { data: existingParticipants, error: checkError } = await supabase
          .from('chat_participants')
          .select('*')
          .eq('chat_room_id', roomId)
          .eq('user_id', userId);
        
        if (checkError) {
          throw new Error(`Failed to check existing participant: ${checkError.message}`);
        }
        
        let participant;
        
        if (existingParticipants && existingParticipants.length > 0) {
          // Update existing participant
          const { data: updated, error: updateError } = await supabase
            .from('chat_participants')
            .update({ 
              is_active: isActive,
              ...(isActive && { left_at: null })
            })
            .eq('id', existingParticipants[0].id)
            .select();
          
          if (updateError) {
            throw new Error(`Failed to update participant: ${updateError.message}`);
          }
          
          participant = updated[0];
          logDebug(`Updated participant (ID: ${participant.id})`, 'success');
        } else {
          // Create new participant
          const { data: inserted, error: insertError } = await supabase
            .from('chat_participants')
            .insert([{
              chat_room_id: roomId,
              user_id: userId,
              participant_role: 'member',
              is_active: isActive,
              joined_at: new Date().toISOString(),
              preferred_language: 'vi'
            }])
            .select();
          
          if (insertError) {
            throw new Error(`Failed to add participant: ${insertError.message}`);
          }
          
          participant = inserted[0];
          logDebug(`Created new participant (ID: ${participant.id})`, 'success');
        }
        
        // Show success message
        resultDiv.innerHTML = `
          <div class="alert alert-success">
            <p>User ${existingParticipants?.length > 0 ? 'updated' : 'added'} successfully!</p>
            <p><strong>Participant ID:</strong> ${participant.id}</p>
            <p><strong>Status:</strong> ${isActive ? 'Active' : 'Inactive'}</p>
          </div>
        `;
        
        updateStatus('user-room', 'success', `User ${existingParticipants?.length > 0 ? 'updated' : 'added'} successfully`);
        
        // Update User 1 input with the user ID
        document.getElementById('user1-id').value = userId;
      } catch (err) {
        console.error('Error adding user to room:', err);
        document.getElementById('membership-result').innerHTML = `
          <div class="alert alert-danger">
            <p>Error: ${err.message}</p>
          </div>
        `;
        updateStatus('user-room', 'error', `Failed to add user to room: ${err.message}`);
      }
    });
    
    // Step 5: Connect WebSocket
    document.getElementById('connect-ws-btn').addEventListener('click', function() {
      try {
        const wsUrl = document.getElementById('websocket-url').value;
        const roomId = document.getElementById('websocket-room').value;
        const token = document.getElementById('jwt-token').value;
        
        if (!wsUrl) {
          throw new Error('Please enter WebSocket URL');
        }
        
        if (!roomId) {
          throw new Error('Please select a room to join');
        }
        
        if (!token) {
          throw new Error('Please enter JWT token');
        }
        
        logDebug(`Attempting to connect to WebSocket server: ${wsUrl}`);
        
        // Create socket connection for User 1
        const user1 = {
          id: document.getElementById('user1-id').value,
          language: 'vi'
        };
        
        // Create socket connection for User 2
        const user2 = {
          id: document.getElementById('user2-id').value,
          language: 'en'
        };
        
        // Connect both users
        connectUser(1, user1.id, user1.language);
        connectUser(2, user2.id, user2.language);
        
        updateStatus('websocket', 'success', 'WebSocket connection initiated');
      } catch (err) {
        console.error('Error connecting WebSocket:', err);
        updateStatus('websocket', 'error', `Failed to connect WebSocket: ${err.message}`);
      }
    });
    
    function connectUser(userNumber, userId, language) {
      try {
        const wsUrl = document.getElementById('websocket-url').value;
        const roomId = document.getElementById('websocket-room').value;
        const token = document.getElementById('jwt-token').value;
        
        // Clear previous connection
        if (userNumber === 1 && user1Socket) {
          user1Socket.disconnect();
          user1Socket = null;
        } else if (userNumber === 2 && user2Socket) {
          user2Socket.disconnect();
          user2Socket = null;
        }
        
        // Update status
        const statusBadge = document.getElementById(`user${userNumber}-status`);
        statusBadge.className = 'badge bg-warning';
        statusBadge.textContent = 'Connecting...';
        
        // Create socket connection
        const socket = io(wsUrl, {
          auth: { token }
        });
        
        socket.on('connect', () => {
          logDebug(`User ${userNumber} connected to WebSocket server`, 'success');
          
          statusBadge.className = 'badge bg-primary';
          statusBadge.textContent = 'Connected';
          
          // Setup user information
          socket.emit('setup', {
            userId: userId,
            preferredLanguage: language,
            deviceId: `tester-${userNumber}-${Date.now()}`
          });
        });
        
        socket.on('setup_success', (response) => {
          logDebug(`User ${userNumber} setup success: ${JSON.stringify(response.data)}`, 'success');
          
          statusBadge.className = 'badge bg-success';
          statusBadge.textContent = 'Setup Complete';
          
          // Join room
          socket.emit('join_room', roomId, (response) => {
            if (response && response.success) {
              logDebug(`User ${userNumber} joined room ${roomId}`, 'success');
              statusBadge.textContent = 'In Room';
              
              // Update chat status
              updateStatus('chat', 'success', `User ${userNumber} ready to chat`);
            } else {
              const errorMsg = response ? response.error : 'Unknown error';
              logDebug(`User ${userNumber} failed to join room: ${errorMsg}`, 'error');
              statusBadge.className = 'badge bg-danger';
              statusBadge.textContent = 'Join Failed';
            }
          });
        });
        
        socket.on('disconnect', () => {
          logDebug(`User ${userNumber} disconnected from WebSocket server`, 'error');
          statusBadge.className = 'badge bg-danger';
          statusBadge.textContent = 'Disconnected';
        });
        
        socket.on('error', (error) => {
          logDebug(`User ${userNumber} error: ${JSON.stringify(error)}`, 'error');
          statusBadge.className = 'badge bg-danger';
          statusBadge.textContent = 'Error';
        });
        
        // Handle message events
        socket.on('message_received', (data) => {
          logDebug(`User ${userNumber} received message: ${JSON.stringify(data.message)}`, 'info');
          addMessageToChat(userNumber, data.message, false);
        });
        
        socket.on('translation_received', (data) => {
          logDebug(`User ${userNumber} received translation: ${JSON.stringify(data)}`, 'info');
          
          const { messageId, language: targetLanguage, translatedContent } = data;
          
          // Add translation to the message
          const messageEl = document.querySelector(`#user${userNumber}-messages [data-message-id="${messageId}"]`);
          if (messageEl) {
            const translationEl = document.createElement('div');
            translationEl.className = 'message-translation';
            translationEl.textContent = `[${targetLanguage}] ${translatedContent}`;
            messageEl.appendChild(translationEl);
          }
        });
        
        // Store socket for later use
        if (userNumber === 1) {
          user1Socket = socket;
        } else {
          user2Socket = socket;
        }
        
        return socket;
      } catch (err) {
        console.error(`Error connecting User ${userNumber}:`, err);
        const statusBadge = document.getElementById(`user${userNumber}-status`);
        statusBadge.className = 'badge bg-danger';
        statusBadge.textContent = 'Error';
        logDebug(`Error connecting User ${userNumber}: ${err.message}`, 'error');
        return null;
      }
    }
    
    // Step 6: Chat Test
    document.getElementById('user1-send').addEventListener('click', () => sendMessage(1));
    document.getElementById('user2-send').addEventListener('click', () => sendMessage(2));
    
    document.getElementById('user1-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') sendMessage(1);
    });
    
    document.getElementById('user2-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') sendMessage(2);
    });
    
    function sendMessage(userNumber) {
      try {
        const socket = userNumber === 1 ? user1Socket : user2Socket;
        const inputEl = document.getElementById(`user${userNumber}-input`);
        const message = inputEl.value.trim();
        const roomId = document.getElementById('websocket-room').value;
        const language = userNumber === 1 ? 'vi' : 'en';
        
        if (!message) {
          return;
        }
        
        if (!socket) {
          logDebug(`User ${userNumber} not connected`, 'error');
          return;
        }
        
        if (!roomId) {
          logDebug(`No room selected`, 'error');
          return;
        }
        
        logDebug(`User ${userNumber} sending message: ${message}`, 'info');
        
        // Emit message
        socket.emit('send_message', {
          roomId,
          content: message,
          originalLanguage: language
        }, (response) => {
          console.log('Send message response:', response);
          
          if (response && response.success) {
            logDebug(`User ${userNumber} sent message successfully`, 'success');
            addMessageToChat(userNumber, response.data.message, true);
            inputEl.value = '';
          } else {
            const errorMsg = response ? response.error : 'Unknown error';
            logDebug(`User ${userNumber} failed to send message: ${errorMsg}`, 'error');
            
            // Try with direct database insert if socket fails
            directDatabaseSend(userNumber, message, language, roomId);
          }
        });
      } catch (err) {
        console.error(`Error sending message for User ${userNumber}:`, err);
        logDebug(`Error sending message for User ${userNumber}: ${err.message}`, 'error');
        
        // Fallback to direct database insert
        const inputEl = document.getElementById(`user${userNumber}-input`);
        const message = inputEl.value.trim();
        const roomId = document.getElementById('websocket-room').value;
        const language = userNumber === 1 ? 'vi' : 'en';
        
        directDatabaseSend(userNumber, message, language, roomId);
      }
    }
    
    async function directDatabaseSend(userNumber, message, language, roomId) {
      try {
        if (!supabase) {
          throw new Error('Supabase not connected');
        }
        
        const userId = document.getElementById(`user${userNumber}-id`).value;
        const inputEl = document.getElementById(`user${userNumber}-input`);
        
        logDebug(`Attempting direct database insert for User ${userNumber}`, 'info');
        
        // First, get participant ID
        const { data: participants, error: participantError } = await supabase
          .from('chat_participants')
          .select('id')
          .eq('chat_room_id', roomId)
          .eq('user_id', userId);
        
        if (participantError) {
          throw new Error(`Failed to find participant: ${participantError.message}`);
        }
        
        let participantId;
        
        if (!participants || participants.length === 0) {
          // Create new participant
          const { data: newParticipant, error: createError } = await supabase
            .from('chat_participants')
            .insert([{
              chat_room_id: roomId,
              user_id: userId,
              participant_role: 'member',
              is_active: true,
              joined_at: new Date().toISOString(),
              preferred_language: language
            }])
            .select();
          
          if (createError) {
            throw new Error(`Failed to create participant: ${createError.message}`);
          }
          
          participantId = newParticipant[0].id;
        } else {
          participantId = participants[0].id;
        }
        
        // Insert message
        const { data: newMessage, error: messageError } = await supabase
          .from('chat_messages')
          .insert([{
            chat_room_id: roomId,
            sender_id: userId,
            content: message,
            original_language: language,
            sent_at: new Date().toISOString(),
            is_translated: false
          }])
          .select();
        
        if (messageError) {
          throw new Error(`Failed to insert message: ${messageError.message}`);
        }
        
        logDebug(`Direct database insert successful`, 'success');
        
        // Display message
        addMessageToChat(userNumber, newMessage[0], true);
        inputEl.value = '';
        
        // Also display on the other chat (simulate receiving)
        const otherUserNumber = userNumber === 1 ? 2 : 1;
        setTimeout(() => {
          addMessageToChat(otherUserNumber, newMessage[0], false);
        }, 500);
      } catch (err) {
        console.error('Error sending message directly to database:', err);
        logDebug(`Error sending message directly to database: ${err.message}`, 'error');
      }
    }
    
    function addMessageToChat(userNumber, message, isSent) {
      const messagesContainer = document.getElementById(`user${userNumber}-messages`);
      const messageEl = document.createElement('div');
      messageEl.className = `message ${isSent ? 'sent' : 'received'}`;
      messageEl.dataset.messageId = message.id;
      
      const contentEl = document.createElement('div');
      contentEl.className = 'message-content';
      contentEl.textContent = message.content;
      
      const metaEl = document.createElement('div');
      metaEl.className = 'message-meta';
      
      // Format timestamp
      const timestamp = message.sent_at ? new Date(message.sent_at).toLocaleTimeString() : new Date().toLocaleTimeString();
      metaEl.textContent = `[${message.original_language || (userNumber === 1 ? 'vi' : 'en')}] ${timestamp}`;
      
      messageEl.appendChild(contentEl);
      messageEl.appendChild(metaEl);
      messagesContainer.appendChild(messageEl);
      
      // Scroll to bottom
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Collapsible elements
    document.querySelectorAll('.collapsible').forEach(collapsible => {
      collapsible.addEventListener('click', function() {
        const content = this.nextElementSibling;
        content.classList.toggle('expanded');
        
        const icon = this.querySelector('i');
        if (content.classList.contains('expanded')) {
          icon.className = 'fas fa-chevron-down me-2';
        } else {
          icon.className = 'fas fa-chevron-right me-2';
        }
      });
    });
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      // Check if Supabase is defined
      if (typeof supabase === 'undefined') {
        // Load Supabase from CDN
        supabase = window.supabaseJs;
      }
    });
  </script>
</body>
</html>