.container {
  padding: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.backLink {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #666;
  font-size: 0.875rem;

  &:hover {
    color: #1976d2;
  }

  svg {
    stroke: currentColor;
  }
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.editButton, .deleteButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.editButton {
  background-color: #e3f2fd;
  color: #1976d2;
  text-decoration: none;
  
  &:hover {
    background-color: #bbdefb;
  }

  svg {
    stroke: currentColor;
  }
}

.deleteButton {
  background-color: #ffebee;
  color: #d32f2f;
  border: none;
  
  &:hover {
    background-color: #ffcdd2;
  }

  svg {
    stroke: currentColor;
  }
}

.roomCard {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.roomHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fafafa;
}

.roomNumber {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.statusBadge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  
  &.available {
    background-color: #e6f7e6;
    color: #2e7d32;
  }
  
  &.occupied {
    background-color: #fff3e0;
    color: #e65100;
  }
  
  &.maintenance {
    background-color: #ffebee;
    color: #c62828;
  }
  
  &.cleaning {
    background-color: #e3f2fd;
    color: #0277bd;
  }
  
  &.inactive {
    background-color: #f5f5f5;
    color: #757575;
  }
}

.detailsContainer {
  padding: 1.5rem;
}

.section {
  margin-bottom: 2rem;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.sectionTitle {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detailLabel {
  font-size: 0.75rem;
  font-weight: 500;
  color: #666;
}

.detailValue {
  font-size: 0.875rem;
}

.statusText {
  text-transform: capitalize;
  
  &.available {
    color: #2e7d32;
  }
  
  &.occupied {
    color: #e65100;
  }
  
  &.maintenance {
    color: #c62828;
  }
  
  &.cleaning {
    color: #0277bd;
  }
  
  &.inactive {
    color: #757575;
  }
}

.description {
  margin: 0.5rem 0 0 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.statusInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f9f9f9;
  border-radius: 4px;
  
  &.available {
    background-color: #f1f8e9;
  }
  
  &.occupied {
    background-color: #fff8e1;
  }
  
  &.maintenance {
    background-color: #ffebee;
  }
  
  &.cleaning {
    background-color: #e1f5fe;
  }
}

.statusDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  
  .available & {
    background-color: #4caf50;
  }
  
  .occupied & {
    background-color: #ff9800;
  }
  
  .maintenance & {
    background-color: #f44336;
  }
  
  .cleaning & {
    background-color: #2196f3;
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  flex-direction: column;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error {
  padding: 1rem;
}

.guestSection {
  margin-top: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
}

.guestHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.guestTitle {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.noGuest {
  padding: 1.5rem;
  text-align: center;
  color: #666;
}

.guestInfo {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.guestDetail {
  flex: 1;
  min-width: 200px;
}

.guestDetailLabel {
  font-size: 0.75rem;
  font-weight: 500;
  color: #666;
  margin-bottom: 0.25rem;
}

.guestDetailValue {
  font-size: 0.875rem;
}

.guestActions {
  margin-top: 1rem;
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .actions {
    width: 100%;
  }
  
  .editButton, .deleteButton {
    flex: 1;
    justify-content: center;
  }
  
  .detailsGrid {
    grid-template-columns: 1fr;
  }
  
  .roomHeader {
    flex-direction: column;
    gap: 0.5rem;
  }
}
