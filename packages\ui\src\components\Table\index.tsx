import React, { useState } from 'react';
import styles from './Table.module.scss';
import { Button } from '../Button';

// Types
export interface Column<T> {
  header: string;
  accessor: keyof T | ((item: T) => React.ReactNode);
  width?: string;
  sortable?: boolean;
}

export interface TableProps<T extends object> {
  data: T[];
  columns: Column<T>[];
  isLoading?: boolean;
  onRowClick?: (item: T) => void;
  keyExtractor?: (item: T) => string;
  pagination?: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    pageSize?: number;
  };
  emptyMessage?: string;
}

export function Table<T extends object>({
  data = [],
  columns,
  isLoading = false,
  onRowClick,
  keyExtractor = (item: any) => item.id,
  pagination,
  emptyMessage = 'No data found'
}: TableProps<T>) {
  const [sortField, setSortField] = useState<keyof T | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleSort = (accessor: any) => {
    if (typeof accessor !== 'function' && accessor !== sortField) {
      setSortField(accessor);
      setSortDirection('asc');
    } else if (sortDirection === 'asc') {
      setSortDirection('desc');
    } else {
      setSortField(null);
    }
  };

  const sortedData = React.useMemo(() => {
     if (!data || !sortField) return data || []; 

    return [...data].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (aValue === bValue) return 0;

      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      const comparison = aValue > bValue ? 1 : -1;
      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [data, sortField, sortDirection]);

  if (isLoading) {
    return (
      <div className={styles.loading}>
        <div className={styles.loadingSpinner} />
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className={styles.tableContainer}>
      <table className={styles.table}>
        <thead>
          <tr>
            {columns.map((column, index) => (
              <th 
                key={`header-${index}`}
                style={{ width: column.width }}
                className={column.sortable ? styles.sortableHeader : ''}
                onClick={() => column.sortable && typeof column.accessor !== 'function' && handleSort(column.accessor)}
              >
                <div className={styles.headerContent}>
                  {column.header}
                  {column.sortable && typeof column.accessor !== 'function' && sortField === column.accessor && (
                    <span className={`${styles.sortIcon} ${sortDirection === 'desc' ? styles.sortDesc : ''}`}>
                      ▲
                    </span>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
           {(!sortedData || sortedData.length === 0) ? (
            <tr>
              <td colSpan={columns.length} className={styles.emptyState}>
                {emptyMessage}
              </td>
            </tr>
          ) : (
            sortedData.map((item) => (
              <tr 
                key={keyExtractor(item)}
                onClick={() => onRowClick && onRowClick(item)}
                className={onRowClick ? styles.clickable : ''}
              >
                {columns.map((column, colIndex) => (
                  <td key={`cell-${colIndex}`}>
                    {typeof column.accessor === 'function'
                      ? column.accessor(item)
                      : item[column.accessor] as React.ReactNode}
                  </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </table>
      
      {pagination && pagination.totalPages > 1 && (
        <div className={styles.pagination}>
          <Button 
            size="small" 
            variant="outline"
            onClick={() => pagination.onPageChange(pagination.currentPage - 1)}
            disabled={pagination.currentPage === 1}
          >
            Previous
          </Button>
          
          <div className={styles.pageNumbers}>
            {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                className={`${styles.pageButton} ${page === pagination.currentPage ? styles.activePage : ''}`}
                onClick={() => pagination.onPageChange(page)}
              >
                {page}
              </button>
            ))}
          </div>
          
          <Button 
            size="small" 
            variant="outline"
            onClick={() => pagination.onPageChange(pagination.currentPage + 1)}
            disabled={pagination.currentPage === pagination.totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
