QR Code and Room Integration for LoaLoa Admin Portal
Overview
This document outlines the implementation of the QR Code and Room integration in the LoaLoa Admin Portal. The integration ensures proper relationships between guests, rooms, areas, and QR codes while maintaining data integrity.

Key Components
QR Code Management:

Create QR codes linked to rooms, areas, or as standalone entities
Manage QR code limits per tenant
Track QR code scans and usage
Room Management:

Track room status (available, occupied, maintenance, cleaning)
Link rooms with guests through check-in/check-out processes
Associate rooms with QR codes
Guest Management:

Check-in/check-out functionality
Guest tracking and history
Association with rooms and QR codes
Area Management:

Define common areas in the property
Link areas with QR codes for information and services
Database Constraints
check_target_consistency: Ensures QR codes maintain proper relationships with rooms and areas
Room Status Triggers: Automatically update room status during check-in/check-out
QR Code Limit Triggers: Enforce tenant QR code limits
API Endpoints
/api/qr-codes: Create and manage QR codes
/api/rooms: Manage rooms and their statuses
/api/guests: Manage guests, check-in, and check-out
/api/areas: Manage common areas in the property
User Interface
QR Code Creation Form: Create QR codes with proper validation
Room Selection Modal: Select available rooms for check-in
Guest Management: Check-in/check-out with room association
Data Reset Script
For development and testing purposes, you can use the following SQL script to reset all tenant data. This will clear all guests, rooms, QR codes, and areas, allowing you to start with a clean slate.

Copy-- Data Reset Script for LoaLoa Admin Portal
-- WARNING: This will delete all data in tenant-specific tables
-- Use only in development/testing environments

-- Start a transaction to ensure all-or-nothing execution
BEGIN;

-- Disable triggers temporarily to avoid constraint issues
SET session_replication_role = 'replica';

-- Delete records from tenant_guests table
DELETE FROM tenant_guests;

-- Delete records from tenant_qr_codes table
DELETE FROM tenant_qr_codes;

-- Delete records from tenant_rooms table
DELETE FROM tenant_rooms;

-- Delete records from tenant_areas table
DELETE FROM tenant_areas;

-- Re-enable triggers
SET session_replication_role = 'origin';

-- Optionally reset sequence generators if needed
-- ALTER SEQUENCE tenant_guests_id_seq RESTART WITH 1;
-- ALTER SEQUENCE tenant_qr_codes_id_seq RESTART WITH 1;
-- ALTER SEQUENCE tenant_rooms_id_seq RESTART WITH 1;
-- ALTER SEQUENCE tenant_areas_id_seq RESTART WITH 1;

-- If you want to reset specific tenant data only, use WHERE clause:
-- DELETE FROM tenant_guests WHERE tenant_id = 'your-tenant-id';
-- DELETE FROM tenant_qr_codes WHERE tenant_id = 'your-tenant-id';
-- DELETE FROM tenant_rooms WHERE tenant_id = 'your-tenant-id';
-- DELETE FROM tenant_areas WHERE tenant_id = 'your-tenant-id';

-- Commit the transaction
COMMIT;

-- Verify deletion
SELECT 'tenant_guests' as table_name, COUNT(*) as count FROM tenant_guests
UNION ALL
SELECT 'tenant_qr_codes', COUNT(*) FROM tenant_qr_codes
UNION ALL
SELECT 'tenant_rooms', COUNT(*) FROM tenant_rooms
UNION ALL
SELECT 'tenant_areas', COUNT(*) FROM tenant_areas;
Implementation Notes
Data Integrity:

When creating QR codes, ensure they are properly linked to rooms or areas
The check_target_consistency constraint ensures correct relationships
QR codes can be linked to rooms, areas, or be standalone
Room Status Management:

Room status changes automatically with guest check-in/check-out
Available rooms can be selected during check-in
Occupied rooms show associated guest information
Tenant-Specific Data:

All data is segregated by tenant_id
QR code limits are enforced per tenant
Access controls ensure tenants only access their own data
Best Practices
Always validate room existence before allowing check-in
Ensure proper room status transitions (available → occupied → cleaning → available)
Verify QR code target (room/area) exists before creating association
Handle edge cases like expired QR codes or deleted rooms properly