'use client';

import { defaultLanguages, Language } from './LanguageSelector';
import styles from './TranslationToggle.module.scss';

interface TranslationToggleProps {
  enabled: boolean;
  onToggle: (enabled: boolean) => void;
  guestLanguage: string;
  staffLanguage: string;
  disabled?: boolean;
  showLanguages?: boolean;
  className?: string;
}

export function TranslationToggle({
  enabled,
  onToggle,
  guestLanguage,
  staffLanguage,
  disabled = false,
  showLanguages = true,
  className = ''
}: TranslationToggleProps) {
  
  // Get language objects
  const getLanguage = (code: string): Language | undefined => {
    return defaultLanguages.find(lang => lang.code === code);
  };

  const guestLang = getLanguage(guestLanguage);
  const staffLang = getLanguage(staffLanguage);

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Language Flow Indicator */}
      {showLanguages && guestLang && staffLang && (
        <div className="flex items-center space-x-2 text-sm">
          <div className="flex items-center space-x-1">
            <span>{guestLang.flag}</span>
            <span className="font-mono text-xs">{guestLang.code.toUpperCase()}</span>
          </div>
          <div className="flex items-center space-x-1">
            <span className={`transition-transform ${enabled ? '' : 'opacity-30'}`}>
              ⇄
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <span>{staffLang.flag}</span>
            <span className="font-mono text-xs">{staffLang.code.toUpperCase()}</span>
          </div>
        </div>
      )}

      {/* Toggle Switch */}
      <div className="flex items-center space-x-2">
        <span className={`text-sm ${enabled ? 'text-gray-700' : 'text-gray-400'}`}>
          Translation
        </span>
        <button
          onClick={() => !disabled && onToggle(!enabled)}
          disabled={disabled}
          className={`
            relative inline-flex h-6 w-11 items-center rounded-full transition-colors
            focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2
            disabled:opacity-50 disabled:cursor-not-allowed
            ${enabled ? 'bg-orange-500' : 'bg-gray-200'}
          `}
          role="switch"
          aria-checked={enabled}
          aria-label="Toggle auto-translation"
        >
          <span
            className={`
              inline-block h-4 w-4 transform rounded-full bg-white transition-transform
              ${enabled ? 'translate-x-6' : 'translate-x-1'}
            `}
          />
        </button>
        <span className={`text-xs font-medium ${enabled ? 'text-green-600' : 'text-gray-500'}`}>
          {enabled ? 'ON' : 'OFF'}
        </span>
      </div>

      {/* Status Indicator */}
      {enabled && (
        <div className="flex items-center space-x-1 text-xs text-green-600">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>Auto-translate active</span>
        </div>
      )}
    </div>
  );
}

export default TranslationToggle;
