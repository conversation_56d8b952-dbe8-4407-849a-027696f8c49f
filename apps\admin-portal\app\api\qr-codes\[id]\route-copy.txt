import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Tạo Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  return createClient(supabaseUrl, supabaseKey);
};

// GET: Lấy thông tin chi tiết một QR code
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const qrCodeId = params.id;

    // Tạo Supabase client
    const supabase = createSupabaseClient();

    // Lấy thông tin QR code
    // Lưu ý: Chỉ lấy thông tin phòng nếu có room_number
    const { data, error } = await supabase
      .from('tenant_qr_codes')  // Sử dụng bảng tenant_qr_codes
      .select(`
        *
      `)
      .eq('id', qrCodeId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'QR code not found' }, { status: 404 });
      }
      console.error('Error fetching QR code:', error);
      throw error;
    }

    // Nếu QR code có room_number, lấy thông tin phòng
    let roomInfo = null;
    if (data.room_number) {
      const { data: roomData, error: roomError } = await supabase
        .from('tenant_rooms')
        .select('room_type, floor, status')
        .eq('tenant_id', data.tenant_id)
        .eq('room_number', data.room_number)
        .maybeSingle();
        
      if (!roomError && roomData) {
        roomInfo = roomData;
      }
    }

    // Lấy thông tin về số lần quét và lịt sử dùng
    const { data: scanStats, error: scanError } = await supabase
      .from('qr_code_scans')
      .select('created_at')
      .eq('qr_code_id', qrCodeId)
      .order('created_at', { ascending: false })
      .limit(10);

    // Trả về kết quả
    return NextResponse.json({
      data: {
        ...data,
        tenant_rooms: roomInfo
      },
      scan_history: scanError ? [] : scanStats || []
    });
  } catch (error) {
    console.error('Error in GET QR code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT: Cập nhật thông tin QR code
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const qrCodeId = params.id;
    const { location, description, room_number, status } = await request.json();

    // Tạo Supabase client
    const supabase = createSupabaseClient();

    // Kiểm tra QR code có tồn tại không
    const { data: existingQR, error: fetchError } = await supabase
      .from('tenant_qr_codes')  // Sử dụng bảng tenant_qr_codes
      .select('id, tenant_id')
      .eq('id', qrCodeId)
      .single();

    if (fetchError || !existingQR) {
      return NextResponse.json({ error: 'QR code not found' }, { status: 404 });
    }

    // Cập nhật thông tin QR code
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Chỉ cập nhật các trường có cung cấp
    if (location !== undefined) updateData.location = location;
    if (description !== undefined) updateData.description = description;
    if (room_number !== undefined) updateData.room_number = room_number;
    if (status !== undefined) updateData.status = status;

    const { data, error } = await supabase
      .from('tenant_qr_codes')  // Sử dụng bảng tenant_qr_codes
      .update(updateData)
      .eq('id', qrCodeId)
      .select()
      .single();

    if (error) {
      console.error('Error updating QR code:', error);
      throw error;
    }

    // Trả về kết quả
    return NextResponse.json({ data, message: 'QR code updated successfully' });
  } catch (error) {
    console.error('Error in PUT QR code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE: Xóa QR code
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const qrCodeId = params.id;

    // Tạo Supabase client
    const supabase = createSupabaseClient();

    // Kiểm tra QR code có tồn tại không
    const { data: existingQR, error: fetchError } = await supabase
      .from('tenant_qr_codes')  // Sử dụng bảng tenant_qr_codes
      .select('id, tenant_id')
      .eq('id', qrCodeId)
      .single();

    if (fetchError || !existingQR) {
      return NextResponse.json({ error: 'QR code not found' }, { status: 404 });
    }

    // Xóa QR code
    const { error } = await supabase
      .from('tenant_qr_codes')  // Sử dụng bảng tenant_qr_codes
      .delete()
      .eq('id', qrCodeId);

    if (error) {
      console.error('Error deleting QR code:', error);
      throw error;
    }

    // Lấy thông tin giới hạn QR codes mới nhất
    const { data: limitInfo } = await supabase.rpc('get_tenant_qr_code_usage', {
      tenant_id_param: existingQR.tenant_id
    });

    // Trả về kết quả
    return NextResponse.json({
      message: 'QR code deleted successfully',
      limits: limitInfo && limitInfo[0] ? limitInfo[0] : null
    });
  } catch (error) {
    console.error('Error in DELETE QR code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
