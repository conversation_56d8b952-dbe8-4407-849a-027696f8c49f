import type { Meta, StoryObj } from '@storybook/react';
import { Select } from './index';

const meta = {
  title: 'UI/Form/Select',
  component: Select,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    options: {
      control: 'object',
      description: 'Options for the select',
    },
    label: {
      control: 'text',
      description: 'Select label',
    },
    helperText: {
      control: 'text',
      description: 'Helper text displayed below the select',
    },
    errorText: {
      control: 'text',
      description: 'Error message displayed when error is true',
    },
    error: {
      control: 'boolean',
      description: 'Whether the select is in error state',
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
      description: 'Select size',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the select is disabled',
    },
    fullWidth: {
      control: 'boolean',
      description: 'Whether the select is full width',
    },
  },
} satisfies Meta<typeof Select>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample options
const sampleOptions = [
  { value: '', label: 'Choose an option' },
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
];

// Sample country options
const countryOptions = [
  { value: '', label: 'Select a country' },
  { value: 'us', label: 'United States' },
  { value: 'ca', label: 'Canada' },
  { value: 'mx', label: 'Mexico' },
  { value: 'uk', label: 'United Kingdom' },
  { value: 'fr', label: 'France' },
  { value: 'de', label: 'Germany' },
  { value: 'jp', label: 'Japan' },
  { value: 'au', label: 'Australia' },
];

// Icon component for stories
const LocationIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 8.66667C9.10457 8.66667 10 7.77124 10 6.66667C10 5.5621 9.10457 4.66667 8 4.66667C6.89543 4.66667 6 5.5621 6 6.66667C6 7.77124 6.89543 8.66667 8 8.66667Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8 14.6667C10.6667 12.0001 13.3333 9.61114 13.3333 6.66671C13.3333 3.72228 10.9428 1.33337 8 1.33337C5.05719 1.33337 2.66667 3.72228 2.66667 6.66671C2.66667 9.61114 5.33333 12.0001 8 14.6667Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export const Default: Story = {
  args: {
    label: 'Select Option',
    options: sampleOptions,
  },
};

export const WithSelectedValue: Story = {
  args: {
    label: 'Select Option',
    options: sampleOptions,
    value: 'option2',
  },
};

export const WithHelperText: Story = {
  args: {
    label: 'Select Country',
    options: countryOptions,
    helperText: 'Choose your country of residence',
  },
};

export const WithError: Story = {
  args: {
    label: 'Select Country',
    options: countryOptions,
    error: true,
    errorText: 'Please select a country',
  },
};

export const WithIcon: Story = {
  args: {
    label: 'Select Country',
    options: countryOptions,
    startIcon: <LocationIcon />,
  },
};

export const Disabled: Story = {
  args: {
    label: 'Disabled Select',
    options: sampleOptions,
    disabled: true,
  },
};

export const Small: Story = {
  args: {
    label: 'Small Select',
    options: sampleOptions,
    size: 'small',
  },
};

export const Large: Story = {
  args: {
    label: 'Large Select',
    options: sampleOptions,
    size: 'large',
  },
};

export const FullWidth: Story = {
  args: {
    label: 'Full Width Select',
    options: sampleOptions,
    fullWidth: true,
  },
};

export const SelectShowcase: Story = {
  name: 'All Select Variants',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px', maxWidth: '500px' }}>
      <Select 
        label="Default select"
        options={sampleOptions}
      />
      
      <Select 
        label="With helper text"
        options={sampleOptions}
        helperText="This is a helper text"
      />
      
      <Select 
        label="With error"
        options={sampleOptions}
        error={true}
        errorText="This field is required"
      />
      
      <Select 
        label="Disabled select"
        options={sampleOptions}
        disabled={true}
      />
      
      <Select 
        label="With start icon"
        options={countryOptions}
        startIcon={<LocationIcon />}
      />
      
      <div style={{ display: 'flex', gap: '16px' }}>
        <Select 
          label="Small size"
          options={sampleOptions}
          size="small"
        />
        
        <Select 
          label="Medium size"
          options={sampleOptions}
          size="medium"
        />
        
        <Select 
          label="Large size"
          options={sampleOptions}
          size="large"
        />
      </div>
      
      <Select 
        label="Full width select"
        options={sampleOptions}
        fullWidth={true}
      />
    </div>
  ),
};
