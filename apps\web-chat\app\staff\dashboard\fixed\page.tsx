'use client';

/**
 * Fixed Staff Dashboard - Simple Realtime like debugger
 * Uses the exact same working logic from debugger
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { createClientSupabase } from '@/lib/supabase';
import styles from '../dashboard.module.scss';

interface ChatSession {
  id: string;
  guest_name: string;
  room_number?: string;
  language: string;
  status: 'active' | 'pending' | 'waiting';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  last_message: string;
  last_message_time: string;
  unread_count: number;
  source: string;
}

interface ChatMessage {
  id: string;
  content: string;
  sender_type: 'guest' | 'staff';
  sender_name: string;
  timestamp: string;
}

interface User {
  id: string;
  name: string;
  tenant_id: string;
}

export default function FixedStaffDashboard() {
  // Core state
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeChatSessions, setActiveChatSessions] = useState<ChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [realtimeStatus, setRealtimeStatus] = useState<string>('Not connected');
  const [lastMessageId, setLastMessageId] = useState<string | null>(null);

  // Refs
  const router = useRouter();
  const supabaseRef = useRef<any>(null);
  const channelRef = useRef<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Utility functions
  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Initialize Supabase (exactly like debugger)
  const initializeSupabase = useCallback(() => {
    try {
      supabaseRef.current = createClientSupabase();
      console.log('✅ Fixed Dashboard: Supabase client initialized');
      console.log(`📡 URL: ${supabaseRef.current.supabaseUrl}`);
      console.log(`🔑 Key: ${supabaseRef.current.supabaseKey.substring(0, 20)}...`);
    } catch (err) {
      console.error(`❌ Fixed Dashboard: Failed to initialize Supabase: ${err}`);
    }
  }, []);

  // Setup realtime subscription (exactly like debugger)
  const setupRealtimeSubscription = useCallback(() => {
    if (!supabaseRef.current || !user) {
      console.log('❌ Fixed Dashboard: Supabase not initialized or no user');
      return;
    }

    if (channelRef.current) {
      console.log('🔄 Fixed Dashboard: Unsubscribing existing channel...');
      channelRef.current.unsubscribe();
    }

    console.log('🔄 Fixed Dashboard: Setting up realtime subscription...');
    console.log(`🎯 Filter: tenant_id=eq.${user.tenant_id}`);

    // Use EXACT same logic as working debugger
    channelRef.current = supabaseRef.current
      .channel('fixed-staff-messages')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'tenant_chat_messages',
          filter: `tenant_id=eq.${user.tenant_id}`
        },
        (payload: any) => {
          console.log(`🔔 Fixed Dashboard: REALTIME MESSAGE RECEIVED!`);
          console.log(`📄 Message ID: ${payload.new.id}`);
          console.log(`👤 Sender: ${payload.new.sender_type} (${payload.new.sender_name})`);
          console.log(`💬 Content: ${payload.new.content?.substring(0, 50)}...`);
          console.log(`🏢 Tenant: ${payload.new.tenant_id}`);
          console.log(`⏰ Time: ${payload.new.created_at}`);

          // Update messages if for selected session
          if (selectedSession && payload.new.chat_session_id === selectedSession) {
            const newMessage: ChatMessage = {
              id: payload.new.id,
              content: payload.new.content,
              sender_type: payload.new.sender_type,
              sender_name: payload.new.sender_name || (payload.new.sender_type === 'guest' ? 'Guest' : 'Staff'),
              timestamp: payload.new.created_at
            };

            setMessages(prev => {
              const exists = prev.some(msg => msg.id === newMessage.id);
              if (exists) return prev;
              console.log('➕ Fixed Dashboard: Adding new message to chat:', newMessage.id);
              return [...prev, newMessage];
            });

            setTimeout(scrollToBottom, 100);
          }

          // Update session list
          refreshSessionList();
        }
      )
      .subscribe((status: string) => {
        setRealtimeStatus(status);
        console.log(`📡 Fixed Dashboard: Subscription status: ${status}`);
        
        if (status === 'SUBSCRIBED') {
          console.log('✅ Fixed Dashboard: REALTIME SUBSCRIPTION ACTIVE!');
        } else if (status === 'CHANNEL_ERROR') {
          console.log('❌ Fixed Dashboard: REALTIME SUBSCRIPTION FAILED!');
        } else if (status === 'CLOSED') {
          console.log('⚠️ Fixed Dashboard: REALTIME SUBSCRIPTION CLOSED!');
        }
      });
  }, [user, selectedSession]);

  // Session list refresh
  const refreshSessionList = useCallback(async () => {
    if (!user) return;

    try {
      const response = await fetch(`/api/chat-sessions?tenant_id=${user.tenant_id}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.sessions)) {
          const transformedSessions: ChatSession[] = data.sessions.map((session: any) => ({
            id: session.id,
            guest_name: session.qr_info?.room_number ?
              `Room ${session.qr_info.room_number} Guest` :
              'Guest User',
            room_number: session.qr_info?.room_number || undefined,
            language: session.guest_language?.toUpperCase() || 'EN',
            status: session.status as 'active' | 'pending' | 'waiting',
            priority: session.priority as 'low' | 'normal' | 'high' | 'urgent',
            last_message: 'Loading...',
            last_message_time: formatTimeAgo(session.updated_at),
            unread_count: 0,
            source: session.qr_info?.location || session.reception_point?.name || 'Direct'
          }));

          setActiveChatSessions(transformedSessions);
          console.log(`✅ Fixed Dashboard: Session list refreshed (${transformedSessions.length} sessions)`);
        }
      }
    } catch (error) {
      console.error('❌ Fixed Dashboard: Error refreshing session list:', error);
    }
  }, [user]);

  // Send message
  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!selectedSession || !content.trim()) return false;

    try {
      console.log(`📤 Fixed Dashboard: Sending message: "${content}"`);
      
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: selectedSession,
          sender_type: 'staff',
          sender_name: user?.name || 'Staff',
          content: content.trim()
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Fixed Dashboard: Message sent successfully: ${data.message?.id}`);
        return true;
      }
      
      throw new Error('Failed to send message');
    } catch (err) {
      console.error('❌ Fixed Dashboard: Failed to send message:', err);
      return false;
    }
  }, [selectedSession, user]);

  // Load messages for selected session
  const loadMessages = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/messages?session_id=${sessionId}&limit=50`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.messages)) {
          setMessages(data.messages);
          const latestMessage = data.messages[data.messages.length - 1];
          if (latestMessage) {
            setLastMessageId(latestMessage.id);
          }
          setTimeout(scrollToBottom, 100);
        }
      }
    } catch (err) {
      console.error('❌ Fixed Dashboard: Failed to load messages:', err);
    }
  }, []);

  // Handle session selection
  const handleSessionSelect = useCallback((sessionId: string) => {
    setSelectedSession(sessionId);
    setLastMessageId(null);
    loadMessages(sessionId);
  }, [loadMessages]);

  // Initialize
  useEffect(() => {
    console.log('🚀 Fixed Staff Dashboard: Starting...');
    
    const token = localStorage.getItem('staff_token');
    const userData = localStorage.getItem('staff_user');

    if (!token || !userData) {
      router.push('/staff');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
      console.log('✅ Fixed Dashboard: User loaded:', parsedUser.name);
    } catch (error) {
      console.error('Error parsing user data:', error);
      router.push('/staff');
    } finally {
      setLoading(false);
    }
  }, [router]);

  // Setup when user is loaded
  useEffect(() => {
    if (user) {
      initializeSupabase();
      refreshSessionList();
      
      // Setup realtime after a short delay
      setTimeout(() => {
        setupRealtimeSubscription();
      }, 1000);
    }

    return () => {
      if (channelRef.current) {
        channelRef.current.unsubscribe();
      }
    };
  }, [user, initializeSupabase, refreshSessionList, setupRealtimeSubscription]);

  if (loading) {
    return <div className={styles.loading}>Loading Fixed Dashboard...</div>;
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h1>Fixed Staff Dashboard</h1>
        <div className={styles.connectionStatus}>
          <span className={`${styles.statusIndicator} ${realtimeStatus === 'SUBSCRIBED' ? styles.connected : styles.disconnected}`}>
            {realtimeStatus === 'SUBSCRIBED' ? '🟢 Realtime Active' : 
             realtimeStatus === 'CHANNEL_ERROR' ? '🔴 Realtime Failed' :
             realtimeStatus === 'CLOSED' ? '⚠️ Realtime Closed' :
             '🟡 Connecting...'}
          </span>
        </div>
      </div>

      <div className={styles.content}>
        {/* Session List */}
        <div className={styles.sidebar}>
          <div className={styles.sessionList}>
            {activeChatSessions.map((session) => (
              <div 
                key={session.id} 
                className={`${styles.sessionItem} ${selectedSession === session.id ? styles.selected : ''}`}
                onClick={() => handleSessionSelect(session.id)}
              >
                <div className={styles.sessionHeader}>
                  <span className={styles.guestName}>{session.guest_name}</span>
                  <span className={styles.language}>{session.language}</span>
                </div>
                <div className={styles.lastMessage}>{session.last_message}</div>
                <div className={styles.sessionMeta}>
                  <span className={styles.time}>{session.last_message_time}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Chat Area */}
        <div className={styles.chatArea}>
          {selectedSession ? (
            <>
              <div className={styles.messagesContainer}>
                {messages.map((message) => (
                  <div key={message.id} className={`${styles.message} ${styles[message.sender_type]}`}>
                    <div className={styles.messageContent}>
                      <span className={styles.senderName}>{message.sender_name}</span>
                      <p>{message.content}</p>
                      <span className={styles.timestamp}>{formatTimeAgo(message.timestamp)}</span>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
              
              <div className={styles.messageInput}>
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message..."
                  onKeyPress={async (e) => {
                    if (e.key === 'Enter' && newMessage.trim()) {
                      const success = await sendMessage(newMessage);
                      if (success) {
                        setNewMessage('');
                      }
                    }
                  }}
                />
                <button onClick={async () => {
                  if (newMessage.trim()) {
                    const success = await sendMessage(newMessage);
                    if (success) {
                      setNewMessage('');
                    }
                  }
                }}>
                  Send
                </button>
              </div>
            </>
          ) : (
            <div className={styles.noSelection}>
              <p>Select a chat session to start messaging</p>
              <p style={{ fontSize: '14px', color: '#666', marginTop: '10px' }}>
                Realtime Status: {realtimeStatus}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
