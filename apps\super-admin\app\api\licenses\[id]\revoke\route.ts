import { NextRequest, NextResponse } from 'next/server';
import { LicenseService } from '../../../../services/LicenseService';
import { withAuth } from '../../../middleware';

// Thu hồi license
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req) => {
    try {
      const licenseId = params.id;
      const { reason } = await req.json();
      
      // Validate
      if (!reason) {
        return NextResponse.json(
          { error: 'Revocation reason is required' },
          { status: 400 }
        );
      }
      
      // Thu hồi license
      const result = await LicenseService.revokeLicense(licenseId, reason);
      
      return NextResponse.json(result);
    } catch (error: any) {
      console.error('API Error:', error);
      return NextResponse.json(
        { error: error.message || 'Failed to revoke license' },
        { status: 500 }
      );
    }
  });
}
