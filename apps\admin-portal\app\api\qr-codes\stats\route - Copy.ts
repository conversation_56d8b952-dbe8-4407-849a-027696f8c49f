import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { createAdminClient } from '../../../../lib/supabase/admin';

// Function to get tenant_id from license_config.json
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET(request: NextRequest) {
  try {
    // Get tenant_id from config file
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }
    
    const supabase = createAdminClient();

    // Get total count
    const { count: totalCount, error: totalError } = await supabase
      .from('tenant_qr_codes')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenant_id);

    if (totalError) {
      console.error('Error fetching total QR codes:', totalError);
      return NextResponse.json({ 
        error: totalError.message 
      }, { status: 500 });
    }

    // Get active count
    const { count: activeCount, error: activeError } = await supabase
      .from('tenant_qr_codes')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenant_id)
      .eq('status', 'active');

    if (activeError) {
      console.error('Error fetching active QR codes:', activeError);
      return NextResponse.json({ 
        error: activeError.message 
      }, { status: 500 });
    }

    // Get total scans
    const { data: scanData, error: scanError } = await supabase
      .from('tenant_qr_codes')
      .select('scan_count')
      .eq('tenant_id', tenant_id);

    let totalScans = 0;
    if (!scanError && scanData) {
      totalScans = scanData.reduce((sum, item) => sum + (item.scan_count || 0), 0);
    }

    // Get limit information
    const { data: limitInfo } = await supabase.rpc('get_tenant_qr_code_usage', {
      tenant_id_param: tenant_id
    }).catch(() => ({ data: null }));

    // Return the stats
    return NextResponse.json({
      success: true,
      stats: {
        total: totalCount || 0,
        active: activeCount || 0,
        totalScans,
        remainingLimit: limitInfo && limitInfo[0] ? limitInfo[0].remaining : null
      }
    });
  } catch (error: any) {
    console.error('Error getting QR code stats:', error);
    return NextResponse.json({ 
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}
