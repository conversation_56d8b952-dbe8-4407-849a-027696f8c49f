import { NextRequest, NextResponse } from 'next/server';
import { LicenseService } from '../../services/LicenseService';
import { createClient } from '../../../utils/supabase/server';
import { withAuth } from '../middleware';

export async function GET(request: NextRequest) {
  console.log('GET /api/licenses - Start');
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status') || 'all';
    const search = searchParams.get('search') || '';
    
    console.log('Query params:', { page, limit, status, search });
    
    const supabase = await createClient();
    
    // Đơn giản hóa query để debug
    let query = supabase.from('licenses').select('*', { count: 'exact' });
    
    // Chỉ thêm các filter nếu cần
    if (status !== 'all' && status !== 'ALL') {
      if (status === 'ACTIVE') {
        query = query.eq('is_active', true);
      } else if (status === 'REVOKED') {
        query = query.eq('is_active', false);
      } else if (status === 'EXPIRED') {
        query = query.lt('expiry_date', new Date().toISOString());
      }
    }
    
    if (search) {
      query = query.or(`license_key.ilike.%${search}%,customer_name.ilike.%${search}%`);
    }
    
    // Phân trang
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);
    
    // Sắp xếp theo created_at giảm dần
    query = query.order('created_at', { ascending: false });
    
    // Thực thi query
    const { data, error, count } = await query;
    
    console.log('Query result:', { dataCount: data?.length || 0, error, totalCount: count });
    
    if (error) {
      console.error('Error fetching licenses:', error);
      throw error;
    }
    
    // Xử lý và format dữ liệu trả về
    const formattedData = data?.map(license => {
      const expiryDate = new Date(license.expiry_date);
      const currentDate = new Date();
      const daysRemaining = Math.ceil((expiryDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
      
      let status = 'ACTIVE';
      if (expiryDate < currentDate) {
        status = 'EXPIRED';
      } else if (license.is_active === false) {
        status = 'REVOKED';
      } else if (!license.activation_date) {
        status = 'NOT_ACTIVATED';
      } else if (daysRemaining <= 30) {
        status = 'EXPIRING_SOON';
      }
      
      return {
        ...license,
        status,
        days_remaining: daysRemaining > 0 ? daysRemaining : 0
      };
    }) || [];
    
    console.log('Formatted data:', { count: formattedData.length });
    
    return NextResponse.json({
      data: formattedData,
      meta: {
        total: count || 0,
        page,
        limit,
        pageCount: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch licenses', details: error },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  console.log('POST /api/licenses - Start');
  try {
    const { 
      customerName, 
      customerEmail, 
      customerPhone, 
      expiryDays, 
      productId, 
      isActive,
      metadata 
    } = await request.json();
    
    console.log('Request body:', { 
      customerName, 
      customerEmail, 
      customerPhone, 
      expiryDays, 
      productId, 
      isActive,
      metadata 
    });
    
    // Validate
    if (!customerName || !expiryDays) {
      return NextResponse.json(
        { error: 'Customer name and expiry days are required' },
        { status: 400 }
      );
    }
    
    const supabase = await createClient();
    
    // Tính ngày hết hạn
    const now = new Date();
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + parseInt(expiryDays.toString()));
    
    // Lấy user ID hiện tại (người tạo license)
    const { data: { user } } = await supabase.auth.getUser();
    
    // Tạo license key mới
    const licenseKey = generateLicenseKey();
    console.log('Generated license key:', licenseKey);
    
    // Chuẩn bị dữ liệu để insert
    const licenseData = {
      license_key: licenseKey,
      customer_name: customerName,
      product_id: productId || 'loaloa-hotel',
      issue_date: now.toISOString(),
      expiry_date: expiryDate.toISOString(),
      is_active: isActive === undefined ? true : isActive,
      created_at: now.toISOString(),
      updated_at: now.toISOString(),
      created_by: user?.id,
      metadata: metadata || {}
    };
    
    // Thêm customer_id nếu cần
    // TODO: Implement customer lookup logic if needed
    
    console.log('Inserting license with data:', licenseData);

    // Insert vào database
    const { data: license, error } = await supabase
      .from('licenses')
      .insert(licenseData)
      .select('*')
      .single();

    if (error) {
      console.error('Error creating license:', error);
      return NextResponse.json(
        { error: `Failed to create license: ${error.message}` },
        { status: 500 }
      );
    }

    console.log('License created successfully:', license);
    return NextResponse.json({ data: license }, { status: 201 });
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create license' },
      { status: 500 }
    );
  }
}
// Helper function để tạo license key
function generateLicenseKey() {
  // Format: LLHM-XXXXX-XXXXX-XXXXX-XXXXX
  const randomPart = () => {
    return Math.random().toString(36).substring(2, 7).toUpperCase();
  };
  
  return `LLHM-${randomPart()}-${randomPart()}-${randomPart()}-${randomPart()}`;
}