import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Lấy danh sách quy tắc định tuyến chat
export async function GET(request: NextRequest) {
  try {
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    // Lấy các query parameters
    const searchParams = request.nextUrl.searchParams;
    const ruleType = searchParams.get('rule_type') || '';
    const targetDepartment = searchParams.get('target_department') || '';
    const isActive = searchParams.get('is_active') === 'true' ? true : searchParams.get('is_active') === 'false' ? false : undefined;
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    const offset = (page - 1) * limit;

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Truy vấn danh sách quy tắc định tuyến
    let query = supabase
  .from('tenant_chat_routing_rules')
  .select(`
    *,
    target_user:target_user_id (
      id,
      tenant_users_details (
        email,
        display_name
      )
    ),
    target_reception_point:tenant_message_reception_points!tenant_chat_routing_rules_target_reception_point_id_fkey (
      id,
      name,
      code
    )
  `, { count: 'exact' })
  .eq('tenant_id', tenant_id)
  .order('priority', { ascending: false })
  .order('created_at', { ascending: false })
  .range(offset, offset + limit - 1);

    // Áp dụng các bộ lọc
    if (ruleType) query = query.eq('rule_type', ruleType);
    if (targetDepartment) query = query.eq('target_department', targetDepartment);
    if (isActive !== undefined) query = query.eq('is_active', isActive);

    // Thực hiện truy vấn
    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching chat routing rules:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({
      data,
      meta: {
        total: count || 0,
        page,
        limit,
        pageCount: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error: any) {
    console.error('Error in GET chat routing rules:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}

// POST: Tạo quy tắc định tuyến chat mới
export async function POST(request: NextRequest) {
  try {
    // Lấy dữ liệu từ request body
    const {
      rule_name,
      rule_type,
      rule_condition,
      priority,
      target_department,
      target_user_id,
      target_reception_point_id,
      is_active
    } = await request.json();

    // Kiểm tra dữ liệu bắt buộc
    if (!rule_name) {
      return NextResponse.json({ error: 'Rule name is required' }, { status: 400 });
    }

    if (!rule_type) {
      return NextResponse.json({ error: 'Rule type is required' }, { status: 400 });
    }

    if (!rule_condition || Object.keys(rule_condition).length === 0) {
      return NextResponse.json({ error: 'Rule condition is required' }, { status: 400 });
    }

    if (!target_department && !target_user_id && !target_reception_point_id) {
      return NextResponse.json(
	 { error: 'Either target department, target user or target reception point must be specified' },
        { status: 400 }
      );
    }

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Nếu có target_user_id, kiểm tra user có tồn tại không
    if (target_user_id) {
      const { data: existingUser, error: userCheckError } = await supabase
        .from('tenant_users')
        .select('id')
        .eq('id', target_user_id)
        .eq('tenant_id', tenant_id)
        .single();

      if (userCheckError || !existingUser) {
        return NextResponse.json({ error: 'Target user not found' }, { status: 404 });
      }
    }

    // Nếu có target_reception_point_id, kiểm tra reception point có tồn tại không
    if (target_reception_point_id) {
      const { data: existingPoint, error: pointCheckError } = await supabase
        .from('tenant_message_reception_points')
        .select('id')
        .eq('id', target_reception_point_id)
        .eq('tenant_id', tenant_id)
        .single();

      if (pointCheckError || !existingPoint) {
        return NextResponse.json({ error: 'Target reception point not found' }, { status: 404 });
      }
    }

    // Kiểm tra rule_type hợp lệ
    const validRuleTypes = ['qr_code', 'guest', 'time', 'language', 'custom'];
    if (!validRuleTypes.includes(rule_type)) {
      return NextResponse.json(
        { error: `Invalid rule type. Must be one of: ${validRuleTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // Tạo quy tắc mới
    const newRule = {
      tenant_id,
      rule_name,
      rule_type,
      rule_condition,
      priority: priority !== undefined ? priority : 1,
      target_department,
      target_user_id,
      target_reception_point_id,
      is_active: is_active !== undefined ? is_active : true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Lưu vào database
    const { data, error } = await supabase
  .from('tenant_chat_routing_rules')
  .insert(newRule)
  .select(`
    *,
    target_user:target_user_id (
      id,
      tenant_users_details (
        email,
        display_name
      )
    ),
    target_reception_point:tenant_message_reception_points!tenant_chat_routing_rules_target_reception_point_id_fkey (
      id,
      name,
      code
    )
  `)
  .single();

    if (error) {
      console.error('Error creating chat routing rule:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({ data, message: 'Chat routing rule created successfully' }, { status: 201 });
  } catch (error: any) {
    console.error('Error in POST chat routing rule:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}