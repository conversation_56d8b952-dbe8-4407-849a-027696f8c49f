import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '../../../../../utils/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    
    const supabase = await createClient();
    
    // Kiểm tra license có tồn tại không
    const { data: license, error: licenseError } = await supabase
      .from('licenses')
      .select('*')
      .eq('id', id)
      .single();
      
    if (licenseError || !license) {
      return NextResponse.json(
        { error: 'License not found' },
        { status: 404 }
      );
    }
    
    // Tính toán offset cho phân trang
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    
    // L<PERSON>y danh sách clone detections của license
    const { data, error, count } = await supabase
      .from('license_clones')
      .select('*', { count: 'exact' })
      .eq('license_id', id)
      .order('detection_time', { ascending: false })
      .range(from, to);
      
    if (error) {
      console.error('Error fetching license clones:', error);
      return NextResponse.json(
        { error: 'Failed to fetch license clones' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      data,
      meta: {
        total: count || 0,
        page,
        limit,
        pageCount: Math.ceil((count || 0) / limit)
      }
    });
    
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while fetching license clones' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { cloneId, status } = body;
    
    if (!cloneId || !status) {
      return NextResponse.json(
        { error: 'Clone ID and status are required' },
        { status: 400 }
      );
    }
    
    const validStatuses = ['DETECTED', 'UNDER_REVIEW', 'CONFIRMED', 'FALSE_ALARM', 'REVOKED'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status value' },
        { status: 400 }
      );
    }
    
    const supabase = await createClient();
    
    // Cập nhật trạng thái clone
    const { data: updatedClone, error } = await supabase
      .from('license_clones')
      .update({ status })
      .eq('id', cloneId)
      .eq('license_id', id)
      .select()
      .single();
      
    if (error) {
      console.error('Error updating clone status:', error);
      return NextResponse.json(
        { error: 'Failed to update clone status' },
        { status: 500 }
      );
    }
    
    // Nếu xác nhận clone, ghi log vi phạm
    if (status === 'CONFIRMED') {
      await supabase
        .from('license_activities')
        .insert({
          license_id: id,
          activity_type: 'VIOLATION',
          hardware_fingerprint: updatedClone.clone_fingerprint,
          details: {
            message: 'Clone confirmed by admin',
            original_fingerprint: updatedClone.original_fingerprint
          }
        });
    }
    
    return NextResponse.json({
      data: updatedClone,
      message: 'Clone status updated successfully'
    });
    
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while updating clone status' },
      { status: 500 }
    );
  }
}
