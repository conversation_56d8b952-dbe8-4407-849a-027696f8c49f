import React from 'react';
import Link from 'next/link';
import styles from './SuccessDialog.module.scss';

interface SuccessDialogProps {
  title: string;
  message: string;
  continueText: string;
  backText: string;
  continueHref: string;
  backHref: string;
  onClose?: () => void;
}

const SuccessDialog: React.FC<SuccessDialogProps> = ({
  title,
  message,
  continueText,
  backText,
  continueHref,
  backHref,
  onClose
}) => {
  return (
    <div className={styles.overlay}>
      <div className={styles.dialog}>
        <div className={styles.header}>
          <svg className={styles.icon} width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="24" cy="24" r="24" fill="#10B981" fillOpacity="0.1"/>
            <path d="M32 18L22 28L16 22" stroke="#10B981" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <h2 className={styles.title}>{title}</h2>
        </div>
        
        <p className={styles.message}>{message}</p>
        
        <div className={styles.actions}>
          <Link href={backHref} className={styles.backButton} onClick={onClose}>
            {backText}
          </Link>
          <Link href={continueHref} className={styles.continueButton} onClick={onClose}>
            {continueText}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SuccessDialog;
