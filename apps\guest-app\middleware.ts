import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

// Hàm này sẽ chạy trước mỗi request
export function middleware(request: NextRequest) {
  // Chỉ xử lý các request đến từ khách
  if (request.nextUrl.pathname.startsWith('/api/guest') || 
      request.nextUrl.pathname.startsWith('/qr') || 
      request.nextUrl.pathname.startsWith('/chat')) {
    
    // Kiểm tra xem đã có session_id chưa
    let sessionId = request.cookies.get('session_id')?.value;
    
    // Nếu chưa có session_id, tạo mới và đặt cookie
    if (!sessionId) {
      sessionId = uuidv4();
      
      // Tạo response mới với cookie đã thêm
      const response = NextResponse.next();
      
      // Đặt cookie với thời hạn 30 ngày
      response.cookies.set({
        name: 'session_id',
        value: sessionId,
        path: '/',
        maxAge: 60 * 60 * 24 * 30,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });
      
      return response;
    }
    
    // Nếu đã có session_id, không làm gì cả
    return NextResponse.next();
  }
  
  // Các request khác không được xử lý bởi middleware
  return NextResponse.next();
}

// Chỉ áp dụng middleware cho các đường dẫn cụ thể
export const config = {
  matcher: [
    '/api/guest/:path*',
    '/qr/:path*',
    '/chat/:path*'
  ],
};
