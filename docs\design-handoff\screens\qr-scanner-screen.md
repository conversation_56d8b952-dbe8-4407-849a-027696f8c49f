# Screen Design Handoff: QR Scanner Screen

## Overview
- **Screen Name:** QR Scanner
- **Designer:** UI Team
- **Last Updated:** 2025-04-28
- **Status:** Ready for Handoff

## Visual Design

### Mobile View
[Screenshot from Figma - QR scanner in action]

### Desktop View
[Screenshot from Figma - desktop variant with instructions]

## Layout & Components

### Camera Viewfinder
- **Size:** Full screen with overlay
- **Components:** 
  - Scanning area indicator (centered rectangle)
  - Semi-transparent overlay for non-scan area
  - Scan animation (subtle pulse)

### Header
- **Height:** 64px
- **Components:**
  - Back button
  - Title "Scan QR Code"
  - Help button

### Instructions
- **Position:** Bottom of screen
- **Components:**
  - Text instruction "Position QR code within the frame"
  - Language selection dropdown (subtle)

### Permissions State
- **Layout:** Centered card if camera permission denied
- **Components:**
  - Illustration
  - Explanatory text
  - "Enable Camera" button

## User Flows

### First-time Access
1. User sees permission request
2. Upon granting, camera activates
3. Scanning begins automatically

### Successful Scan
1. Success animation appears briefly
2. Haptic feedback (mobile)
3. Automatic redirect to appropriate screen based on QR type

### Failed/Invalid Scan
1. Error message appears
2. Option to try again
3. Help button for troubleshooting

## Responsive Behavior
- **Mobile:** Full screen camera view
- **Tablet:** Same as mobile
- **Desktop:** Centered camera view with more prominent instructions

## Accessibility
- **Alternative Flow:** Manual entry option for users who can't scan
- **Text Instructions:** Clear and concise for screen readers
- **Success/Error:** Both visual and audio indicators

## Implementation Notes
- Use native camera APIs where possible for performance
- Fall back to web APIs where native not available
- Handle permission management properly
- Pre-warm camera to reduce loading time
- QR code parsing should handle different formats
- Support for multiple QR code types (room, restaurant table, etc.)

## Animation Specifications
- **Scanner Animation:** Subtle moving line across viewfinder
- **Success Animation:** Green checkmark with brief flash
- **Error Animation:** Red X with brief flash

## Open Questions
- Should we support other code formats besides QR?
- How to handle extremely low light conditions?
- Should we add torch/flashlight option?