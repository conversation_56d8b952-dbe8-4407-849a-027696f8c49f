'use client';

import React, { useState, useEffect } from 'react';
import { Html5Qrcode } from 'html5-qrcode';
import styles from './QrScanner.module.css';

interface QrScannerProps {
  onScanSuccess: (qrCodeId: string) => void;
  onScanError?: (error: string) => void;
}

export default function QrScanner({ onScanSuccess, onScanError }: QrScannerProps) {
  const [isScanning, setIsScanning] = useState(false);
  const [scannerReady, setScannerReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [qrScanner, setQrScanner] = useState<Html5Qrcode | null>(null);

  useEffect(() => {
    // Khởi tạo scanner khi component mount
    const scanner = new Html5Qrcode('qr-scanner');
    setQrScanner(scanner);
    setScannerReady(true);

    // Dọn dẹp khi component unmount
    return () => {
      if (scanner && scanner.isScanning) {
        scanner.stop().catch(console.error);
      }
    };
  }, []);

  const startScanning = async () => {
    if (!qrScanner || !scannerReady) return;
    
    setError(null);
    setIsScanning(true);
    
    try {
      // Yêu cầu quyền truy cập camera
      const devices = await Html5Qrcode.getCameras();
      if (!devices || devices.length === 0) {
        throw new Error('No cameras found');
      }
      
      setHasPermission(true);
      
      // Bắt đầu quét với camera mặc định
      await qrScanner.start(
        { facingMode: 'environment' },
        {
          fps: 10,
          qrbox: { width: 250, height: 250 },
        },
        // Xử lý khi quét thành công
        (qrCodeMessage) => {
          // QR code thường có định dạng: "loaloa:qr:{qr_code_id}"
          const qrCodePattern = /loaloa:qr:([0-9a-f-]{36})/i;
          const match = qrCodeMessage.match(qrCodePattern);
          
          if (match && match[1]) {
            const qrCodeId = match[1];
            onScanSuccess(qrCodeId);
            stopScanning();
          } else {
            const error = 'Invalid QR Code format';
            setError(error);
            if (onScanError) onScanError(error);
          }
        },
        // Xử lý lỗi (không hiển thị)
        () => {}
      );
    } catch (err: any) {
      setIsScanning(false);
      const errorMessage = err.message || 'Failed to start scanner';
      setError(errorMessage);
      setHasPermission(false);
      if (onScanError) onScanError(errorMessage);
      console.error('QR Scanner error:', err);
    }
  };

  const stopScanning = async () => {
    if (qrScanner && qrScanner.isScanning) {
      try {
        await qrScanner.stop();
      } catch (err) {
        console.error('Error stopping scanner:', err);
      }
    }
    setIsScanning(false);
  };

  return (
    <div className={styles.container}>
      <div id="qr-scanner" className={styles.scanner}></div>
      
      {error && <div className={styles.error}>{error}</div>}
      
      {hasPermission === false && (
        <div className={styles.permissionError}>
          <p>Camera access was denied. Please grant permission to use the camera and try again.</p>
          <button className={styles.button} onClick={() => setHasPermission(null)}>
            Try again
          </button>
        </div>
      )}
      
      {!isScanning && hasPermission !== false && (
        <div className={styles.buttonContainer}>
          <button className={styles.button} onClick={startScanning}>
            Scan QR Code
          </button>
        </div>
      )}
      
      {isScanning && (
        <div className={styles.overlay}>
          <div className={styles.scanArea}></div>
          <button className={styles.stopButton} onClick={stopScanning}>
            Cancel
          </button>
        </div>
      )}
    </div>
  );
}
