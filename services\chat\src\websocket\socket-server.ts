import http from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import { verifyToken } from '../utils/jwt';
import { ConnectionData, SocketEvents } from '../types';
import { registerMessageHandlers } from './handlers/message-handlers';
import { registerRoomHandlers } from './handlers/room-handlers';
import { registerStatusHandlers } from './handlers/status-handlers';

let io: SocketIOServer;

export const initializeSocketServer = (server: HttpServer) => {
  io = new SocketIOServer(server, {
    cors: {
      origin: '*', // Trong môi trường sản xuất, hạn chế nguồn gốc
      methods: ['GET', 'POST']
    }
  });

  io.use(async (socket, next) => {
    try {
      // Lấy token từ query parameters hoặc headers
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
      
      if (!token) {
        // Cho phép kết nối không có xác thực cho người dùng tạm thời
        // Họ sẽ cần cung cấp temporaryUserId sau khi kết nối
        return next();
      }
      
      // Xác thực token
      const user = verifyToken(token);
      
      if (!user) {
        return next(new Error('Authentication failed'));
      }
      
      // Gán thông tin user vào socket
      socket.data.userId = user.userId;
      socket.data.email = user.email;
      socket.data.preferredLanguage = user.preferredLanguage || 'en';
      
      return next();
    } catch (error) {
      return next(new Error('Authentication error'));
    }
  });

  io.on(SocketEvents.CONNECT, (socket) => {
    console.log(`Client connected: ${socket.id}`);
    
    // Xử lý thiết lập kết nối
    socket.on('setup', (data: ConnectionData) => {
      // Lưu thông tin kết nối
      if (data.userId) {
        socket.data.userId = data.userId;
      }
      
      if (data.temporaryUserId) {
        socket.data.temporaryUserId = data.temporaryUserId;
      }
      
      if (!socket.data.userId && !socket.data.temporaryUserId) {
        socket.emit(SocketEvents.ERROR, {
          success: false,
          error: 'Either userId or temporaryUserId is required'
        });
        return;
      }
      
      socket.data.preferredLanguage = data.preferredLanguage || 'en';
      socket.data.deviceId = data.deviceId;
      
      // Thông báo thiết lập thành công
      socket.emit('setup_success', {
        success: true,
        data: {
          socketId: socket.id,
          userId: socket.data.userId,
          temporaryUserId: socket.data.temporaryUserId,
          preferredLanguage: socket.data.preferredLanguage
        }
      });
    });
    
    // Đăng ký các handlers cho các events
    registerMessageHandlers(io, socket);
    registerRoomHandlers(io, socket);
    registerStatusHandlers(io, socket);
    
    socket.on(SocketEvents.DISCONNECT, () => {
      console.log(`Client disconnected: ${socket.id}`);
    });
  });

  console.log('Socket.IO server initialized');
  return io;
};

export const getIO = (): SocketIOServer => {
  if (!io) {
    throw new Error('Socket.IO not initialized');
  }
  return io;
};
