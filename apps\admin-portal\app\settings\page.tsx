'use client';
import { useState } from 'react';
import Link from 'next/link';

export default function SettingsPage() {
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    
    // G<PERSON><PERSON> lập lưu cài đặt
    setTimeout(() => {
      setIsSaving(false);
      setSaveSuccess(true);
      
      // Reset thông báo thành công sau 3 giây
      setTimeout(() => setSaveSuccess(false), 3000);
    }, 1000);
  };
  
  return (
    <div className="p-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Cài đặt Hệ thống</h1>
        <Link 
          href="/dashboard" 
          className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
        >
          Quay lại Dashboard
        </Link>
      </div>
      
      {saveSuccess && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          Lưu cài đặt thành công!
        </div>
      )}
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <form onSubmit={handleSave}>
          <h2 className="text-xl font-bold mb-4">Cài đặt Cơ bản</h2>
          
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="siteName">
              Tên Hệ thống
            </label>
            <input
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700"
              id="siteName"
              type="text"
              placeholder="LoaLoa Chat System"
              defaultValue="LoaLoa Chat System"
            />
          </div>
          
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="adminEmail">
              Email Quản trị
            </label>
            <input
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700"
              id="adminEmail"
              type="email"
              placeholder="<EMAIL>"
              defaultValue="<EMAIL>"
            />
          </div>
          
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Ngôn ngữ mặc định
            </label>
            <select className="shadow border rounded w-full py-2 px-3 text-gray-700">
              <option value="vi">Tiếng Việt</option>
              <option value="en">English</option>
              <option value="ja">日本語</option>
              <option value="ko">한국어</option>
              <option value="zh">中文</option>
            </select>
          </div>
          
          <h2 className="text-xl font-bold mb-4 mt-8">Cài đặt Dịch thuật</h2>
          
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Dịch thuật Tự động
            </label>
            <div className="mt-2">
              <label className="inline-flex items-center">
                <input type="checkbox" className="form-checkbox" defaultChecked />
                <span className="ml-2">Bật dịch thuật tự động</span>
              </label>
            </div>
          </div>
          
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Ngôn ngữ được hỗ trợ
            </label>
            <div className="mt-2 grid grid-cols-2 gap-2">
              <label className="inline-flex items-center">
                <input type="checkbox" className="form-checkbox" defaultChecked />
                <span className="ml-2">Tiếng Việt</span>
              </label>
              <label className="inline-flex items-center">
                <input type="checkbox" className="form-checkbox" defaultChecked />
                <span className="ml-2">English</span>
              </label>
              <label className="inline-flex items-center">
                <input type="checkbox" className="form-checkbox" defaultChecked />
                <span className="ml-2">日本語</span>
              </label>
              <label className="inline-flex items-center">
                <input type="checkbox" className="form-checkbox" defaultChecked />
                <span className="ml-2">한국어</span>
              </label>
              <label className="inline-flex items-center">
                <input type="checkbox" className="form-checkbox" defaultChecked />
                <span className="ml-2">中文</span>
              </label>
              <label className="inline-flex items-center">
                <input type="checkbox" className="form-checkbox" />
                <span className="ml-2">Français</span>
              </label>
            </div>
          </div>
          
          <div className="flex items-center justify-end">
            <button
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              type="submit"
              disabled={isSaving}
            >
              {isSaving ? 'Đang Lưu...' : 'Lưu Cài Đặt'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
