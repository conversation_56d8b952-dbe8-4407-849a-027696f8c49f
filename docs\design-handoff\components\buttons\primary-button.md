# Component Design Handoff: Primary Button

## Overview
- **Component Name:** PrimaryButton
- **Designer:** UI Team
- **Last Updated:** 2025-04-28
- **Status:** Ready for Handoff

## Visual Design

### Desktop/Mobile View
[Screenshot from Figma - primary button in normal state]

### States
- **Default:** Blue background (#3B82F6), white text
- **Hover:** Slightly darker blue (#2563EB)
- **Active:** Darkest blue (#1D4ED8), slight scale transform (0.98)
- **Disabled:** Gray background (#D1D5DB), light gray text (#9CA3AF)
- **Loading:** Same as default but with spinner icon

## Specifications

### Layout & Spacing
- **Padding:** spacing-3 (12px) vertical, spacing-6 (24px) horizontal
- **Height:** 48px (fixed)
- **Width:** Content-based with minimum of 120px

### Typography
- **Font Family:** fontFamily.base (Roboto)
- **Font Size:** fontSize.md (16px)
- **Font Weight:** fontWeight.medium (500)
- **Line Height:** 1.5
- **Color:** White (#FFFFFF) in default/hover/active, gray (#9CA3AF) in disabled

### Colors
- **Background:**
  - Default: colors.primary (#3B82F6)
  - Hover: colors.primary-dark (#2563EB)
  - Active: colors.primary-darker (#1D4ED8)
  - Disabled: colors.gray[300] (#D1D5DB)
- **Text:**
  - Default/Hover/Active: White (#FFFFFF)
  - Disabled: colors.gray[400] (#9CA3AF)

### Other Properties
- **Border Radius:** borderRadius.md (4px)
- **Shadow:** None in default, subtle shadow on hover
- **Transition:** 150ms ease for all property changes

## Behavior
- **Interactions:** Click/tap triggers action
- **Animations:** Slight scale down (0.98) on active state
- **Transitions:** 150ms ease for background-color and transform

## Accessibility
- **Keyboard Navigation:** Focusable with tab, activated with Enter/Space
- **Focus Visible:** Blue outline when focused with keyboard
- **ARIA Attributes:** role="button" if using a div instead of button

## Implementation Notes
- Button should expand to full width on mobile screens (<768px)
- Use actual <button> element unless there's a specific reason not to
- Ensure loading state properly disables interactions

## Assets
- None required beyond the design tokens