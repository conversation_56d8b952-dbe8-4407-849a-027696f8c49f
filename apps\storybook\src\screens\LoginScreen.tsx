import React, { useState } from 'react';
import { 
  Button, 
  Card, 
  Input, 
  Heading3, 
  BodySmall, 
  Checkbox
} from '@loaloa/ui';

export interface LoginScreenProps {
  onLogin?: (email: string, password: string) => void;
  onSignup?: () => void;
  variant?: 'light' | 'dark' | 'studio';
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ 
  onLogin, 
  onSignup,
  variant = 'light'
}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);

  const handleLogin = () => {
    if (onLogin) onLogin(email, password);
  };

  // Styles
  const themeColors = {
    light: {
      background: '#F9FAFB',
      text: '#010103',
    },
    dark: {
      background: '#161616',
      text: '#EBEBEB',
    },
    studio: {
      background: '#16262E',
      text: '#EBEBEB',
    }
  };

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: themeColors[variant].background,
    padding: '16px',
    color: themeColors[variant].text
  };

  const logoStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: '24px',
    gap: '8px',
  };

  const logoIconStyle: React.CSSProperties = {
    width: '32px', 
    height: '32px', 
    backgroundColor: '#FF4D00', 
    borderRadius: '4px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontWeight: 'bold',
    fontSize: '18px'
  };

  const footerStyle: React.CSSProperties = {
    marginTop: '16px',
    textAlign: 'center',
  };

  return (
    <div style={containerStyle}>
      <div style={{ width: '100%', maxWidth: '400px' }}>
        <div style={logoStyle}>
          <div style={logoIconStyle}>L</div>
          <span style={{ fontWeight: 600, fontSize: '24px' }}>LoaLoa</span>
        </div>

        <Card>
          <div style={{ padding: '24px' }}>
            <Heading3 style={{ marginBottom: '24px', textAlign: 'center' }}>
              Login to your account
            </Heading3>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', marginBottom: '24px' }}>
              <Input
                label="Email Address"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />

              <Input
                label="Password"
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Checkbox
                  label="Remember me"
                  checked={rememberMe}
                  onChange={() => setRememberMe(!rememberMe)}
                />
                <BodySmall style={{ color: '#FF4D00', cursor: 'pointer' }}>
                  Forgot password?
                </BodySmall>
              </div>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <Button 
                variant="primary" 
                label="Login" 
                onClick={handleLogin}
                fullWidth
              />
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', margin: '8px 0' }}>
                <div style={{ flex: 1, height: '1px', backgroundColor: themeColors[variant].text, opacity: 0.2 }}></div>
                <BodySmall>OR</BodySmall>
                <div style={{ flex: 1, height: '1px', backgroundColor: themeColors[variant].text, opacity: 0.2 }}></div>
              </div>
              
              <Button 
                variant="outline" 
                label="Continue with Google" 
                onClick={() => {}}
                fullWidth
              />
            </div>
          </div>
        </Card>

        <div style={footerStyle}>
          <BodySmall>
            Don't have an account?{' '}
            <span 
              style={{ color: '#FF4D00', cursor: 'pointer', fontWeight: 500 }}
              onClick={onSignup}
            >
              Sign up
            </span>
          </BodySmall>
        </div>
      </div>
    </div>
  );
};

export default LoginScreen;
