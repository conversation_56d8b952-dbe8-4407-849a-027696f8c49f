import React from 'react';

interface SimpleButtonProps {
  /**
   * Button contents
   */
  label: string;
  /**
   * Optional click handler
   */
  onClick?: () => void;
}

export const SimpleButton: React.FC<SimpleButtonProps> = ({
  label,
  onClick,
}) => {
  const buttonStyle = {
    backgroundColor: '#FF4D00',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    padding: '8px 16px',
    cursor: 'pointer',
    fontWeight: 500,
  };

  return (
    <button
      style={buttonStyle}
      onClick={onClick}
      type="button"
    >
      {label}
    </button>
  );
};
