{"name": "@loaloa/chat-service", "version": "0.1.0", "description": "Chat microservice for LoaLoa app", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "lint": "eslint . --ext .ts", "test": "jest", "migrate": "node -r ts-node/register migrations/apply-migrations.ts"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "socket.io": "^4.7.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/cors": "^2.8.14", "@types/express": "^4.17.18", "@types/jsonwebtoken": "^9.0.3", "@types/node": "^20.8.2", "@types/uuid": "^9.0.4", "@typescript-eslint/eslint-plugin": "^6.7.5", "@typescript-eslint/parser": "^6.7.5", "eslint": "^8.51.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}