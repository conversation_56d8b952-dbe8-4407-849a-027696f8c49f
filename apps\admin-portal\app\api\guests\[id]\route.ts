import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Tạo Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// GET: Lấy thông tin chi tiết của guest
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const guestId = params.id;

    // Tạo Supabase client
    const supabase = createSupabaseClient();

    // Lấy thông tin guest
    const { data, error } = await supabase
      .from('tenant_guests')
      .select('*')
      .eq('id', guestId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // Không tìm thấy kết quả
        return NextResponse.json(
          { error: 'Guest not found' },
          { status: 404 }
        );
      }
      console.error('Error fetching guest:', error);
      throw error;
    }

    // Nếu có room_number, lấy thêm thông tin về phòng
    if (data.room_number) {
      const { data: roomData, error: roomError } = await supabase
        .from('tenant_rooms')
        .select('room_type, floor')
        .eq('tenant_id', data.tenant_id)
        .eq('room_number', data.room_number)
        .single();

      if (!roomError && roomData) {
        data.tenant_rooms = roomData;
      }
    }

    // Trả về thông tin guest
    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error in GET guest:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
// PUT: Cập nhật thông tin guest
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const guestId = params.id;
    const updateData = await request.json();
    
    // Tạo Supabase client
    const supabase = createSupabaseClient();
    
    // Kiểm tra guest có tồn tại không
    const { data: existingGuest, error: fetchError } = await supabase
      .from('tenant_guests')
      .select('id')
      .eq('id', guestId)
      .single();
    
    if (fetchError || !existingGuest) {
      return NextResponse.json(
        { error: 'Guest not found' },
        { status: 404 }
      );
    }
    
    // Cập nhật thời gian updated_at
    const dataToUpdate = {
      ...updateData,
      updated_at: new Date().toISOString()
    };
    
    // Cập nhật thông tin guest
    const { data, error } = await supabase
      .from('tenant_guests')
      .update(dataToUpdate)
      .eq('id', guestId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating guest:', error);
      throw error;
    }
    
    // Trả về thông tin guest đã cập nhật
    return NextResponse.json({
      data,
      message: 'Guest updated successfully'
    });
    
  } catch (error) {
    console.error('Error in PUT guest:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE: Xóa khách hàng
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const guestId = params.id;

    // Tạo Supabase client
    const supabase = createSupabaseClient();

    // Kiểm tra xem khách có tồn tại không
    const { data: guest, error: fetchError } = await supabase
      .from('tenant_guests')
      .select('id, is_active')
      .eq('id', guestId)
      .single();

    if (fetchError) {
      return NextResponse.json(
        { error: 'Guest not found' },
        { status: 404 }
      );
    }

    // Chỉ cho phép xóa khách đã check-out
    if (guest.is_active) {
      return NextResponse.json(
        { error: 'Cannot delete active guest. Please check-out first.' },
        { status: 400 }
      );
    }

    // Xóa khách
    const { error } = await supabase
      .from('tenant_guests')
      .delete()
      .eq('id', guestId);

    if (error) {
      console.error('Error deleting guest:', error);
      throw error;
    }

    // Trả về kết quả
    return NextResponse.json({
      message: 'Guest deleted successfully'
    });

  } catch (error) {
    console.error('Error in DELETE guest:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}