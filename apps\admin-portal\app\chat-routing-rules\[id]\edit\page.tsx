'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '../../../dashboard-layout';
import styles from './edit-rule.module.scss';
import { <PERSON><PERSON>, But<PERSON> } from '@ui';
import ChatRoutingRuleForm from '../../../components/chat-routing/ChatRoutingRuleForm';

interface EditRuleParams {
  params: {
    id: string;
  };
}

export default function EditChatRoutingRulePage({ params }: EditRuleParams) {
  const router = useRouter();
  const ruleId = params.id;
  
  const [rule, setRule] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRule = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/chat-routing-rules/${ruleId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Rule not found');
          }
          throw new Error('Failed to fetch rule details');
        }
        
        const data = await response.json();
        setRule(data.data);
      } catch (err) {
        console.error('Error fetching rule:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (ruleId) {
      fetchRule();
    }
  }, [ruleId]);

  const handleSubmit = async (formData: any) => {
    try {
      setSubmitting(true);
      setError(null);

      const response = await fetch(`/api/chat-routing-rules/${ruleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update routing rule');
      }

      // Navigate to the rule detail page on success
      router.push(`/chat-routing-rules/${ruleId}`);
    } catch (err: any) {
      console.error('Error updating rule:', err);
      setError(err.message || 'An error occurred while updating the routing rule');
      // Don't set submitting to false here, the form component will handle it
      throw err; // Re-throw to let the form component know about the error
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading rule details...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (error && !rule) {
    return (
      <DashboardLayout>
        <div className={styles.error}>
          <Alert variant="error" title="Error" closable={false}>
            {error}
          </Alert>
          <Button
            variant="secondary"
            onClick={() => router.push('/chat-routing-rules')}
          >
            Back to Routing Rules
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Link href={`/chat-routing-rules/${ruleId}`} className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M10 15.8333L4.16667 10L10 4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to Rule Details
          </Link>
          <h1 className={styles.title}>Edit Routing Rule</h1>
        </div>

        {error && (
          <Alert variant="error" title="Error" closable onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <div className={styles.formContainer}>
          {rule && (
            <ChatRoutingRuleForm 
              initialData={rule}
              onSubmit={handleSubmit}
              isSubmitting={submitting}
              isEditing={true}
            />
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
