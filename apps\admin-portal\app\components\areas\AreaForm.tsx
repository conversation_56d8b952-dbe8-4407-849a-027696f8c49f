'use client';
import { useState, FormEvent, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import styles from './AreaForm.module.scss';
import ReceptionPointDropdown from '../ReceptionPointDropdown';

interface ReceptionPoint {
  id: string;
  name: string;
  code: string;
}

interface AreaFormProps {
  initialData?: {
    id?: string;
    name: string;
    area_type: string;
    floor: string;
    location: string;
    description: string;
    staff_count: number;
    opening_hours: string;
    closing_hours: string;
    is_active: boolean;
    reception_point_id?: string; // Thêm trường reception_point_id
  };
  onSubmit: (formData: any) => Promise<void>;
  isEditing?: boolean;
}

export default function AreaForm({
  initialData,
  onSubmit,
  isEditing = false,
}: AreaFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [receptionPoints, setReceptionPoints] = useState<ReceptionPoint[]>([]);
  const [formData, setFormData] = useState({
  name: initialData?.name || '',
  area_type: initialData?.area_type || 'restaurant',
  floor: initialData?.floor || '',
  location: initialData?.location || '',
  description: initialData?.description || '',
  staff_count: initialData?.staff_count || 3,
  opening_hours: initialData?.opening_hours || '06:00', // Cập nhật thành 06:00
  closing_hours: initialData?.closing_hours || '23:00', // Cập nhật thành 23:00
  is_active: initialData?.is_active !== false, // Default to true if not specified
  reception_point_id: initialData?.reception_point_id || '',
});

  useEffect(() => {
    // Fetch reception points when component mounts
    const fetchReceptionPoints = async () => {
      try {
        const response = await fetch('/api/reception-points?is_active=true');
        if (!response.ok) {
          throw new Error('Failed to fetch reception points');
        }
        
        const data = await response.json();
        setReceptionPoints(data.data || []);
      } catch (err) {
        console.error('Error fetching reception points:', err);
        // Don't block the form if reception points can't be fetched
      }
    };
    
    fetchReceptionPoints();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) || 0 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      await onSubmit(formData);
      // Navigate back after successful submission
      router.push('/rooms-areas/areas');
    } catch (err: any) {
      setError(err.message || 'An error occurred while saving the area.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form className={styles.form} onSubmit={handleSubmit}>
      {error && <div className={styles.error}>{error}</div>}
      
      <div className={styles.formGrid}>
        <div className={styles.formGroup}>
          <label htmlFor="name" className={styles.label}>
            Tên khu vực <span className={styles.required}>*</span>
          </label>
          <input
            id="name"
            name="name"
            type="text"
            placeholder="Nhập tên khu vực (vd: Nhà hàng chính)"
            value={formData.name}
            onChange={handleChange}
            className={styles.input}
            required
          />
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="area_type" className={styles.label}>
            Loại khu vực <span className={styles.required}>*</span>
          </label>
          <select
            id="area_type"
            name="area_type"
            value={formData.area_type}
            onChange={handleChange}
            className={styles.select}
            required
          >
            <option value="restaurant">Nhà hàng</option>
            <option value="pool">Hồ bơi</option>
            <option value="spa">Spa & Wellness</option>
            <option value="lobby">Sảnh chính</option>
            <option value="gym">Phòng tập</option>
            <option value="bar">Quầy bar</option>
            <option value="conference">Phòng hội nghị</option>
            <option value="other">Khác</option>
          </select>
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="floor" className={styles.label}>
            Tầng <span className={styles.required}>*</span>
          </label>
          <input
            id="floor"
            name="floor"
            type="text"
            placeholder="Nhập tầng (vd: 1)"
            value={formData.floor}
            onChange={handleChange}
            className={styles.input}
            required
          />
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="location" className={styles.label}>
            Vị trí
          </label>
          <input
            id="location"
            name="location"
            type="text"
            placeholder="Nhập vị trí chi tiết (vd: Cánh Tây, gần thang máy)"
            value={formData.location}
            onChange={handleChange}
            className={styles.input}
          />
        </div>

        {/* Reception Point Selection */}
       <div className={styles.formGroup}>
  <label htmlFor="reception_point_id" className={styles.label}>
    Message Reception Point
  </label>
  <ReceptionPointDropdown
    value={formData.reception_point_id}
    onChange={(value) => setFormData(prev => ({ ...prev, reception_point_id: value }))}
    placeholder="-- Select Reception Point --"
    disabled={loading}
  />
  <p className={styles.helpText}>
    Select where messages from guests in this area should be routed
  </p>
</div>
        
        <div className={styles.formGroup}>
          <label htmlFor="staff_count" className={styles.label}>
            Số nhân viên
          </label>
          <input
            id="staff_count"
            name="staff_count"
            type="number"
            min="0"
            placeholder="Số nhân viên phục vụ"
            value={formData.staff_count}
            onChange={handleChange}
            className={styles.input}
          />
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="opening_hours" className={styles.label}>
            Giờ mở cửa
          </label>
          <input
            id="opening_hours"
            name="opening_hours"
            type="text"
            placeholder="Nhập giờ mở cửa (vd: 07:00)"
            value={formData.opening_hours}
            onChange={handleChange}
            className={styles.input}
          />
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="closing_hours" className={styles.label}>
            Giờ đóng cửa
          </label>
          <input
                        id="closing_hours"
            name="closing_hours"
            type="text"
            placeholder="Nhập giờ đóng cửa (vd: 22:00)"
            value={formData.closing_hours}
            onChange={handleChange}
            className={styles.input}
          />
        </div>
        
        <div className={styles.formGroupFull}>
          <label htmlFor="description" className={styles.label}>
            Mô tả
          </label>
          <textarea
            id="description"
            name="description"
            placeholder="Nhập mô tả khu vực"
            value={formData.description}
            onChange={handleChange}
            className={styles.textarea}
            rows={4}
          />
        </div>
        
        {isEditing && (
          <div className={styles.formGroupCheckbox}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="is_active"
                checked={formData.is_active}
                onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                className={styles.checkbox}
              />
              <span>Đang hoạt động</span>
            </label>
          </div>
        )}
      </div>
      
      <div className={styles.buttons}>
        <button
          type="button"
          onClick={() => router.back()}
          className={styles.cancelButton}
          disabled={loading}
        >
          Huỷ
        </button>
        <button
          type="submit"
          className={styles.submitButton}
          disabled={loading}
        >
          {loading ? 'Đang lưu...' : isEditing ? 'Cập nhật khu vực' : 'Tạo khu vực'}
        </button>
      </div>
    </form>
  );
}
