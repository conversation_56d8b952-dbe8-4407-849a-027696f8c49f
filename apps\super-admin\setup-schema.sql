-- <PERSON><PERSON><PERSON> các kiểu ENUM cần thiết
CREATE TYPE user_role AS ENUM ('super_admin', 'admin', 'user');
CREATE TYPE tenant_user_role AS ENUM ('owner', 'admin', 'member');

-- T<PERSON>o bảng tenants
CREATE TABLE IF NOT EXISTS public.tenants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  domain VARCHAR(255),
  contact_email VARCHAR(255) NOT NULL,
  contact_phone VARCHAR(50),
  address TEXT,
  logo_url TEXT,
  primary_color VARCHAR(20),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Tạo bảng tenant_licenses
CREATE TABLE IF NOT EXISTS public.tenant_licenses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
  license_type VARCHAR(50) NOT NULL,
  starts_at TIMESTAMP WITH TIME ZONE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  user_limit INT DEFAULT 10,
  payment_status VARCHAR(50),
  price DECIMAL(10, 2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Đảm bảo bảng users tồn tại và có các trường cần thiết
-- (Sử dụng ALTER TABLE thay vì CREATE TABLE vì Auth đã tạo bảng này)
-- Cần kiểm tra xem bảng users có tồn tại không
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'users') THEN
    CREATE TABLE public.users (
      id UUID PRIMARY KEY REFERENCES auth.users(id),
      email VARCHAR(255) NOT NULL,
      full_name VARCHAR(255),
      role user_role DEFAULT 'user'::user_role,
      phone VARCHAR(50),
      is_active BOOLEAN DEFAULT true,
      profile_image TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      last_login TIMESTAMP WITH TIME ZONE
    );
  ELSE
    -- Kiểm tra và thêm các cột nếu chúng chưa tồn tại
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'full_name') THEN
      ALTER TABLE public.users ADD COLUMN full_name VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'role') THEN
      ALTER TABLE public.users ADD COLUMN role user_role DEFAULT 'user'::user_role;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'phone') THEN
      ALTER TABLE public.users ADD COLUMN phone VARCHAR(50);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'is_active') THEN
      ALTER TABLE public.users ADD COLUMN is_active BOOLEAN DEFAULT true;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'profile_image') THEN
      ALTER TABLE public.users ADD COLUMN profile_image TEXT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'created_at') THEN
      ALTER TABLE public.users ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'last_login') THEN
      ALTER TABLE public.users ADD COLUMN last_login TIMESTAMP WITH TIME ZONE;
    END IF;
  END IF;
END $$;

-- Tạo bảng tenant_users để liên kết giữa users và tenants
CREATE TABLE IF NOT EXISTS public.tenant_users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  role tenant_user_role NOT NULL DEFAULT 'member'::tenant_user_role,
  is_primary_tenant BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login_at TIMESTAMP WITH TIME ZONE,
  expiry_date TIMESTAMP WITH TIME ZONE,
  permissions JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  UNIQUE(tenant_id, user_id)
);

-- Tạo view my_tenants để dễ dàng lấy danh sách tenant của user hiện tại
CREATE OR REPLACE VIEW public.my_tenants AS
SELECT
  t.id AS tenant_id,
  t.name AS tenant_name,
  t.domain AS tenant_domain,
  t.logo_url,
  t.is_active AS tenant_is_active,
  tu.role AS my_role,
  tu.is_primary_tenant,
  tu.permissions AS my_permissions,
  tu.joined_at,
  tu.last_login_at
FROM
  tenants t
  JOIN tenant_users tu ON t.id = tu.tenant_id
WHERE
  tu.user_id = auth.uid();

-- Tạo hàm để lấy tenant_id hiện tại
CREATE OR REPLACE FUNCTION public.get_current_tenant_id()
RETURNS UUID AS $$
DECLARE
  _tenant_id UUID;
BEGIN
  -- Đọc tenant_id từ cookie hoặc header
  -- Đây chỉ là ví dụ, bạn cần triển khai logic thực tế
  _tenant_id := current_setting('request.headers.x-tenant-id', true)::UUID;
  RETURN _tenant_id;
EXCEPTION
  WHEN OTHERS THEN
    RETURN NULL;
END;

$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Chèn dữ liệu mẫu cho tenant mặc định
INSERT INTO public.tenants (name, domain, contact_email, is_active)
VALUES ('Default Tenant', 'default.loaloa.app', '<EMAIL>', true)
ON CONFLICT DO NOTHING;