import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL || 'https://iwzwbrbmojvvvfstbqow.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3endicmJtb2p2dnZmc3RicW93Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMjkyNjEsImV4cCI6MjA2MTkwNTI2MX0.tyVtaSclUKC5fGh7I7Ohpm7c4FniXphYe34-cxBvo6E';

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase URL or key');
}

const supabase = createClient(supabaseUrl, supabaseKey);

export default supabase;
