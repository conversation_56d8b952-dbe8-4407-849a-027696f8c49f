.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  
  &.visible {
    opacity: 1;
    visibility: visible;
  }
}

.modalContent {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(20px);
  transition: transform 0.3s ease;
  
  &.visible {
    transform: translateY(0);
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #eee;
}

.modalTitle {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  color: #666;
  padding: 0.25rem;
  
  &:hover {
    color: #333;
  }
  
  &:disabled {
    color: #ccc;
    cursor: not-allowed;
  }
}

.modalBody {
  padding: 1.5rem;
}

.modalMessage {
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.errorMessage {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: #ffebee;
  border-radius: 4px;
  color: #d32f2f;
  margin-top: 1rem;
  
  svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    stroke: currentColor;
  }
  
  span {
    font-size: 0.875rem;
  }
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #eee;
}

@media (max-width: 768px) {
  .modalContent {
    width: 95%;
  }
  
  .modalActions {
    padding: 0.75rem 1rem;
    flex-direction: column-reverse;
  }
  
  .modalActions button {
    width: 100%;
  }
}
