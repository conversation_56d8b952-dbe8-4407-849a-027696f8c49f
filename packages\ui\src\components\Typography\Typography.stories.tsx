import type { <PERSON>a, StoryObj } from '@storybook/react';
import {
  Heading1,
  Heading2,
  Heading3,
  Heading4,
  Heading5,
  BodyRegular,
  BodyMedium,
  BodySmall,
  ButtonLabel,
  CaptionLabel,
  SmallLabel
} from './index';

// Meta cho Headings
const headingsMeta = {
  title: 'UI/Typography/Headings',
  tags: ['autodocs'],
  parameters: {
    layout: 'padded',
  },
} as const;

// Meta cho Body Text
const bodyMeta = {
  title: 'UI/Typography/Body',
  tags: ['autodocs'],
  parameters: {
    layout: 'padded',
  },
} as const;

// Meta cho Labels
const labelsMeta = {
  title: 'UI/Typography/Labels',
  tags: ['autodocs'],
  parameters: {
    layout: 'padded',
  },
} as const;

// Stories cho Headings
export default {
  ...headingsMeta,
  component: Heading1,
} satisfies Meta<typeof Heading1>;

type HeadingStory = StoryObj<typeof Heading1>;
type Heading2Story = StoryObj<typeof Heading2>;
type Heading3Story = StoryObj<typeof Heading3>;
type Heading4Story = StoryObj<typeof Heading4>;
type Heading5Story = StoryObj<typeof Heading5>;

export const H1: HeadingStory = {
  args: {
    children: 'Heading 1',
    theme: 'light',
  },
  render: (args) => (
    <div>
      <Heading1 {...args} />
    </div>
  ),
};

export const H2: Heading2Story = {
  args: {
    children: 'Heading 2',
    theme: 'light',
  },
  render: (args) => (
    <div>
      <Heading2 {...args} />
    </div>
  ),
};

export const H3: Heading3Story = {
  args: {
    children: 'Heading 3',
    theme: 'light',
  },
  render: (args) => (
    <div>
      <Heading3 {...args} />
    </div>
  ),
};

export const H4: Heading4Story = {
  args: {
    children: 'Heading 4',
    theme: 'light',
  },
  render: (args) => (
    <div>
      <Heading4 {...args} />
    </div>
  ),
};

export const H5: Heading5Story = {
  args: {
    children: 'Heading 5',
    theme: 'light',
  },
  render: (args) => (
    <div>
      <Heading5 {...args} />
    </div>
  ),
};

export const HeadingsShowcase: HeadingStory = {
  name: 'All Headings',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <Heading1>Heading 1 - Light Theme</Heading1>
        <Heading1 theme="dark" style={{ background: '#161616', padding: '8px' }}>Heading 1 - Dark Theme</Heading1>
        <Heading1 theme="studio" style={{ background: '#16262E', padding: '8px' }}>Heading 1 - Studio Theme</Heading1>
      </div>
      
      <div>
        <Heading2>Heading 2 - Light Theme</Heading2>
        <Heading2 theme="dark" style={{ background: '#161616', padding: '8px' }}>Heading 2 - Dark Theme</Heading2>
        <Heading2 theme="studio" style={{ background: '#16262E', padding: '8px' }}>Heading 2 - Studio Theme</Heading2>
      </div>
      
      <div>
        <Heading3>Heading 3 - Light Theme</Heading3>
        <Heading3 theme="dark" style={{ background: '#161616', padding: '8px' }}>Heading 3 - Dark Theme</Heading3>
        <Heading3 theme="studio" style={{ background: '#16262E', padding: '8px' }}>Heading 3 - Studio Theme</Heading3>
      </div>
      
      <div>
        <Heading4>Heading 4 - Light Theme</Heading4>
        <Heading4 theme="dark" style={{ background: '#161616', padding: '8px' }}>Heading 4 - Dark Theme</Heading4>
        <Heading4 theme="studio" style={{ background: '#16262E', padding: '8px' }}>Heading 4 - Studio Theme</Heading4>
      </div>
      
      <div>
        <Heading5>Heading 5 - Light Theme</Heading5>
        <Heading5 theme="dark" style={{ background: '#161616', padding: '8px' }}>Heading 5 - Dark Theme</Heading5>
        <Heading5 theme="studio" style={{ background: '#16262E', padding: '8px' }}>Heading 5 - Studio Theme</Heading5>
      </div>
    </div>
  ),
};

// Stories cho Body Text
export const BodyTextRegular: StoryObj<typeof BodyRegular> = {
  ...bodyMeta,
  component: BodyRegular,
  args: {
    children: 'Body Regular - The quick brown fox jumps over the lazy dog.',
    theme: 'light',
  },
};

export const BodyTextMedium: StoryObj<typeof BodyMedium> = {
  ...bodyMeta,
  component: BodyMedium,
  args: {
    children: 'Body Medium - The quick brown fox jumps over the lazy dog.',
    theme: 'light',
  },
};

export const BodyTextSmall: StoryObj<typeof BodySmall> = {
  ...bodyMeta,
  component: BodySmall,
  args: {
    children: 'Body Small - The quick brown fox jumps over the lazy dog.',
    theme: 'light',
  },
};

export const BodyTextShowcase: StoryObj<typeof BodyRegular> = {
  ...bodyMeta,
  component: BodyRegular,
  name: 'All Body Text',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <BodyRegular>Body Regular - The quick brown fox jumps over the lazy dog.</BodyRegular>
        <BodyRegular theme="dark" style={{ background: '#161616', padding: '8px' }}>
          Body Regular - The quick brown fox jumps over the lazy dog.
        </BodyRegular>
        <BodyRegular theme="studio" style={{ background: '#16262E', padding: '8px' }}>
          Body Regular - The quick brown fox jumps over the lazy dog.
        </BodyRegular>
      </div>
      
      <div>
        <BodyMedium>Body Medium - The quick brown fox jumps over the lazy dog.</BodyMedium>
        <BodyMedium theme="dark" style={{ background: '#161616', padding: '8px' }}>
          Body Medium - The quick brown fox jumps over the lazy dog.
        </BodyMedium>
        <BodyMedium theme="studio" style={{ background: '#16262E', padding: '8px' }}>
          Body Medium - The quick brown fox jumps over the lazy dog.
        </BodyMedium>
      </div>
      
      <div>
        <BodySmall>Body Small - The quick brown fox jumps over the lazy dog.</BodySmall>
        <BodySmall theme="dark" style={{ background: '#161616', padding: '8px' }}>
          Body Small - The quick brown fox jumps over the lazy dog.
        </BodySmall>
        <BodySmall theme="studio" style={{ background: '#16262E', padding: '8px' }}>
          Body Small - The quick brown fox jumps over the lazy dog.
        </BodySmall>
      </div>
      
      <div>
        <BodyRegular bold>Body Regular Bold - The quick brown fox jumps over the lazy dog.</BodyRegular>
        <BodyRegular italic>Body Regular Italic - The quick brown fox jumps over the lazy dog.</BodyRegular>
        <BodyRegular bold italic>Body Regular Bold Italic - The quick brown fox jumps over the lazy dog.</BodyRegular>
      </div>
    </div>
  ),
};

// Stories cho Labels
export const ButtonLabelStory: StoryObj<typeof ButtonLabel> = {
  ...labelsMeta,
  component: ButtonLabel,
  args: {
    children: 'BUTTON LABEL',
    theme: 'light',
  },
};

export const CaptionLabelStory: StoryObj<typeof CaptionLabel> = {
  ...labelsMeta,
  component: CaptionLabel,
  args: {
    children: 'CAPTION LABEL',
    theme: 'light',
  },
};

export const SmallLabelStory: StoryObj<typeof SmallLabel> = {
  ...labelsMeta,
  component: SmallLabel,
  args: {
    children: 'SMALL LABEL',
    theme: 'light',
  },
};

export const LabelsShowcase: StoryObj<typeof ButtonLabel> = {
  ...labelsMeta,
  component: ButtonLabel,
  name: 'All Labels',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <ButtonLabel>BUTTON LABEL</ButtonLabel>
        <div style={{ background: '#161616', padding: '8px', marginTop: '8px' }}>
          <ButtonLabel theme="dark">BUTTON LABEL</ButtonLabel>
        </div>
        <div style={{ background: '#16262E', padding: '8px', marginTop: '8px' }}>
          <ButtonLabel theme="studio">BUTTON LABEL</ButtonLabel>
        </div>
      </div>
      
      <div>
        <CaptionLabel>CAPTION LABEL</CaptionLabel>
        <div style={{ background: '#161616', padding: '8px', marginTop: '8px' }}>
          <CaptionLabel theme="dark">CAPTION LABEL</CaptionLabel>
        </div>
        <div style={{ background: '#16262E', padding: '8px', marginTop: '8px' }}>
          <CaptionLabel theme="studio">CAPTION LABEL</CaptionLabel>
        </div>
      </div>
      
      <div>
        <SmallLabel>SMALL LABEL</SmallLabel>
        <div style={{ background: '#161616', padding: '8px', marginTop: '8px' }}>
          <SmallLabel theme="dark">SMALL LABEL</SmallLabel>
        </div>
        <div style={{ background: '#16262E', padding: '8px', marginTop: '8px' }}>
          <SmallLabel theme="studio">SMALL LABEL</SmallLabel>
        </div>
      </div>
      
      <div>
        <SmallLabel uppercase={false}>Small Label (not uppercase)</SmallLabel>
      </div>
    </div>
  ),
};
