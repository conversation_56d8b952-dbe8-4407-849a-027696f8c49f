# LoaLoa Admin Portal

![LoaLoa Logo](./public/logo.png)

LoaLoa Admin Portal là công cụ quản trị dành cho hệ thống chat và dịch thuật đa ngôn ngữ LoaLoa. Admin Portal cung cấp giao diện cho quản trị viên để quản lý khách, ph<PERSON><PERSON>, mã QR, và giám sát hoạt động của hệ thống.

## Tính năng chính

### Quản lý khách hàng
- Xem danh sách khách đang lưu trú và khách đã check-out
- Tạo mới khách và check-in vào phòng
- Chỉnh sửa thông tin khách
- Check-out khách
- Kích hoạt lại khách đã check-out
- X<PERSON><PERSON> khách đã check-out

### Quản lý phòng
- Xem danh sách phòng và trạng thái
- Tạo phòng mới với các thuộc tính (số phòng, lo<PERSON><PERSON>, tầng)
- Chỉnh sửa thông tin phòng
- <PERSON> dõ<PERSON> lịch sử check-in/check-out

### Quản lý mã QR
- Tạo và quản lý mã QR cho các khu vực và phòng
- Theo dõi lượt quét mã QR
- Liên kết mã QR với khu vực cụ thể
- Tải xuống và in mã QR

### Dashboard thống kê
- Tổng quan về tình trạng phòng (trống/có khách)
- Biểu đồ phân bố khách theo phòng, thời gian
- Thống kê lượt check-in/check-out theo thời gian

### Báo cáo
- Tạo báo cáo chi tiết về hoạt động của khách
- Phân tích tương tác khách-nhân viên
- Xuất báo cáo dưới nhiều định dạng

### Super Admin (Quản trị cấp cao)
- Quản lý các tenant trong hệ thống multi-tenant
- Thiết lập giới hạn số lượng QR code và phòng cho mỗi tenant
- Giám sát tất cả các tenant từ một giao diện thống nhất

## Cấu trúc dự án

/apps/admin-portal/ 
├── app/ # Thư mục chính của ứng dụng Next.js 
│ ├── api/ # API endpoints 
│ ├── components/ # Shared components 
│ ├── dashboard/ # Dashboard pages 
│ ├── guests/ # Guest management 
│ ├── qr-codes/ # QR code management 
│ ├── rooms/ # Room management 
│ ├── reports/ # Reports 
│ ├── settings/ # Settings 
│ ├── super-admin/ # Super admin area 
│ ├── styles/ # Global styles 
│ └── utils/ # Utility functions 
├── public/ # Static files 
└── middleware.ts # Next.js middleware for authentication


## Công nghệ sử dụng

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: SCSS Modules, TailwindCSS
- **State Management**: React Context API
- **Database**: PostgreSQL (thông qua Supabase)
- **Authentication**: JWT, Supabase Auth
- **API**: REST API với Next.js API Routes
- **Hosting**: Docker (triển khai on-premise)

## Cài đặt và chạy dự án

### Yêu cầu

- Node.js 18.0.0 trở lên
- npm 7.0.0 trở lên
- Docker và Docker Compose (cho môi trường phát triển)

### Cài đặt

1. Clone repository

```bash
git clone https://github.com/your-org/loaloa.git
cd loaloa
Cài đặt dependencies
npm install
Thiết lập biến môi trường
cp .env.example .env.local
Chạy ứng dụng trong môi trường phát triển
npm run dev
Ứng dụng sẽ chạy trên http://localhost:3000

Triển khai sản phẩm
Có thể triển khai Admin Portal theo một trong các cách sau:

Sử dụng Docker Compose
cd deployments/on-premise
docker-compose up -d
Sử dụng Vercel hoặc các nền tảng tương tự
npm run build
npm run start
Giới hạn hệ thống
QR Codes: Giới hạn số lượng QR codes có thể tạo cho mỗi tenant (thiết lập bởi Super Admin)
Phòng: Giới hạn số lượng phòng có thể tạo cho mỗi tenant (thiết lập bởi Super Admin)
Hướng dẫn sử dụng
Xem tài liệu chi tiết tại docs/user-guide.md

Đóng góp
Vui lòng tham khảo CONTRIBUTING.md để biết hướng dẫn về cách đóng góp vào dự án.

Giấy phép
Phần mềm này được phân phối theo giấy phép độc quyền. Xem file LICENSE để biết thêm chi tiết.

Liên hệ
Email: <EMAIL>
Website: https://loaloa.app

Đây là kế hoạch chi tiết để triển khai tính năng giới hạn số lượng QR code và phòng cho mỗi tenant trong hệ thống LoaLoa. Với những thay đổi này, Admin Portal của bạn sẽ có khả năng quản lý giới hạn một cách hiệu quả thông qua giao diện Super Admin.


