import React, { InputHTMLAttributes } from 'react';

export interface RadioProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type'> {
  /**
   * Label for the radio button
   */
  label: string;
  /**
   * Whether the radio button is in error state
   */
  error?: boolean;
  /**
   * Helper text displayed below the radio button
   */
  helperText?: string;
}

export const Radio: React.FC<RadioProps> = ({
  id,
  label,
  error = false,
  helperText,
  style,
  className,
  ...props
}) => {
  // Generate a random ID if not provided
  const radioId = id || `radio-${Math.random().toString(36).substring(2, 9)}`;
  
  // Container style
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    marginBottom: '16px',
    ...style,
  };
  
  // Radio group style
  const radioGroupStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'flex-start',
    cursor: props.disabled ? 'not-allowed' : 'pointer',
  };
  
  // Custom radio style
  const customRadioStyle: React.CSSProperties = {
    position: 'relative',
    width: '18px',
    height: '18px',
    borderRadius: '50%',
    border: `1px solid ${error ? '#B91C1C' : props.disabled ? '#D1D5DB' : '#6B7280'}`,
    backgroundColor: props.disabled ? '#F9FAFB' : '#FFFFFF',
    marginRight: '8px',
    marginTop: '2px',
  };
  
  // Checked style (to be applied conditionally)
  const checkedStyle: React.CSSProperties = {
    borderColor: props.disabled ? '#D1D5DB' : error ? '#B91C1C' : '#FF4D00',
    borderWidth: '5px',
  };
  
  // Label style
  const labelStyle: React.CSSProperties = {
    fontSize: '14px',
    color: props.disabled ? '#9CA3AF' : error ? '#B91C1C' : '#464646',
    cursor: props.disabled ? 'not-allowed' : 'pointer',
  };
  
  // Helper text style
  const helperTextStyle: React.CSSProperties = {
    fontSize: '12px',
    marginTop: '4px',
    marginLeft: '26px',
    color: error ? '#B91C1C' : '#6B7280',
  };
  
  // Hide the native radio button but keep it accessible
  const hiddenInputStyle: React.CSSProperties = {
    position: 'absolute',
    opacity: 0,
    width: 0,
    height: 0,
  };

  return (
    <div style={containerStyle} className={className}>
      <div style={radioGroupStyle}>
        <div style={{
          ...customRadioStyle,
          ...(props.checked ? checkedStyle : {})
        }} />
        
        <input
          id={radioId}
          type="radio"
          style={hiddenInputStyle}
          {...props}
        />
        
        <label htmlFor={radioId} style={labelStyle}>
          {label}
        </label>
      </div>
      
      {helperText && <p style={helperTextStyle}>{helperText}</p>}
    </div>
  );
};

export default Radio;
