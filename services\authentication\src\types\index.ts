export interface User {
  id: string;
  email: string;
  full_name?: string;
  preferred_language: string;
  avatar_url?: string;
  phone_number?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

export interface TemporaryUser {
  id: string;
  qr_code_id: string;
  device_id?: string;
  preferred_language: string;
  created_at: string;
  expires_at: string;
  is_activated: boolean;
  room_number?: string;
  hotel_id?: string;
  metadata?: Record<string, any>;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  full_name?: string;
  preferred_language?: string;
}

export interface TokenPayload {
  userId: string;
  email: string;
  roles: string[];
}

export interface QRCodePayload {
  qr_code_id: string;
  expires_at: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}
