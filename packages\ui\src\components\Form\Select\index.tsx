import React, { SelectHTMLAttributes, ReactNode } from 'react';

export interface SelectOption {
  value: string;
  label: string;
}

export interface SelectProps extends Omit<SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  /**
   * Options for the select
   */
  options: SelectOption[];
  /**
   * Select label
   */
  label?: string;
  /**
   * Helper text displayed below the select
   */
  helperText?: string;
  /**
   * Error message displayed when error is true
   */
  errorText?: string;
  /**
   * Whether the select is in error state
   */
  error?: boolean;
  /**
   * Select size
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Icon displayed at the start of the select
   */
  startIcon?: ReactNode;
  /**
   * Whether the select is full width
   */
  fullWidth?: boolean;
  /**
   * Additional CSS properties for the select container
   */
  containerStyle?: React.CSSProperties;
}

export const Select: React.FC<SelectProps> = ({
  id,
  options,
  label,
  helperText,
  errorText,
  error = false,
  size = 'medium',
  startIcon,
  fullWidth = false,
  containerStyle,
  style,
  className,
  ...props
}) => {
  // Generate a random ID if not provided
  const selectId = id || `select-${Math.random().toString(36).substring(2, 9)}`;
  
  // Size styles
  const sizeStyles: Record<string, React.CSSProperties> = {
    small: {
      padding: '6px 12px',
      fontSize: '12px',
      height: '32px',
    },
    medium: {
      padding: '8px 16px',
      fontSize: '14px',
      height: '40px',
    },
    large: {
      padding: '12px 16px',
      fontSize: '16px',
      height: '48px',
    },
  };
  
  // Container style
  const containerBaseStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    marginBottom: '16px',
    width: fullWidth ? '100%' : 'auto',
    ...containerStyle,
  };
  
  // Label style
  const labelStyle: React.CSSProperties = {
    fontSize: '14px',
    fontWeight: 500,
    color: error ? '#B91C1C' : '#464646',
    marginBottom: '4px',
  };
  
  // Select wrapper style (for icons)
  const selectWrapperStyle: React.CSSProperties = {
    position: 'relative',
    display: 'flex',
    width: '100%',
  };
  
  // Select style
  const selectStyle: React.CSSProperties = {
    fontFamily: 'Inter, sans-serif',
    width: '100%',
    borderRadius: '4px',
    border: `1px solid ${error ? '#B91C1C' : '#D1D5DB'}`,
    outline: 'none',
    backgroundColor: props.disabled ? '#F9FAFB' : '#FFFFFF',
    color: props.disabled ? '#9CA3AF' : '#464646',
    transition: 'border-color 0.2s ease, box-shadow 0.2s ease',
    paddingLeft: startIcon ? '40px' : sizeStyles[size].padding.split(' ')[0],
    paddingRight: '28px', // Space for the dropdown arrow
    paddingTop: sizeStyles[size].padding.split(' ')[0],
    paddingBottom: sizeStyles[size].padding.split(' ')[0],
    fontSize: sizeStyles[size].fontSize,
    height: sizeStyles[size].height,
    appearance: 'none', // Remove default arrow in some browsers
    ...style,
  };
  
  // Icon style
  const iconStyle: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    transform: 'translateY(-50%)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: error ? '#B91C1C' : '#6B7280',
    pointerEvents: 'none', // Makes sure the icon doesn't interfere with select interaction
  };
  
  const startIconStyle: React.CSSProperties = {
    ...iconStyle,
    left: '12px',
  };
  
  // Dropdown arrow style
  const arrowStyle: React.CSSProperties = {
    ...iconStyle,
    right: '12px',
    pointerEvents: 'none',
  };
  
  // Helper text style
  const helperTextStyle: React.CSSProperties = {
    fontSize: '12px',
    marginTop: '4px',
    color: error ? '#B91C1C' : '#6B7280',
  };

  return (
    <div style={containerBaseStyle} className={className}>
      {label && (
        <label htmlFor={selectId} style={labelStyle}>
          {label}
        </label>
      )}
      
      <div style={selectWrapperStyle}>
        {startIcon && <span style={startIconStyle}>{startIcon}</span>}
        
        <select
          id={selectId}
          style={selectStyle}
          {...props}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        
        <span style={arrowStyle}>
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 6L8 10L12 6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </span>
      </div>
      
      {(error && errorText) ? (
        <p style={helperTextStyle}>{errorText}</p>
      ) : helperText ? (
        <p style={helperTextStyle}>{helperText}</p>
      ) : null}
    </div>
  );
};

export default Select;
