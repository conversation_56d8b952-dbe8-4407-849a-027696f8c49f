# Component Handoff: [Component Name]

## Overview
[<PERSON>ô tả tổng quan về component và mục đích sử dụng]

## Figma Link
[Link tới Figma component]

## Variants
- [Variant 1]: [<PERSON><PERSON> tả]
- [Variant 2]: [<PERSON><PERSON> tả]
- ...

## States
- Default
- Hover
- Active
- Focus
- Disabled

## Specifications

### Dimensions
- Width: [width]
- Height: [height]
- Padding: [padding]
- Margin: [margin]

### Typography
- Font: [font-family]
- Size: [font-size]
- Weight: [font-weight]
- Line Height: [line-height]

### Colors
- Background: [color token]
- Text: [color token]
- Border: [color token]
- Shadow: [shadow token]

### Interactions
- Click/Tap: [behavior]
- Hover: [visual change]
- Focus: [visual change]

## Accessibility Requirements
- [Specific accessibility considerations]
- ARIA attributes
- Keyboard navigation

## Code Snippet (Example)
```tsx
<Button variant="primary" size="md" disabled={false}>
  Button Text
</Button>
