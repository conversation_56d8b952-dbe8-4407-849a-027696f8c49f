import { Router } from 'express';
import * as chatRoomController from '../controllers/chat-room-controller';
import { authenticate, authorize } from '../middlewares/auth-middleware';

const router = Router();

// L<PERSON>y danh sách phòng chat của user
router.get('/', authenticate, chatRoomController.getUserRooms);

// Lấy thông tin phòng chat theo ID
router.get('/:id', authenticate, chatRoomController.getRoomById);

// Tạo phòng chat mới
router.post('/', authenticate, chatRoomController.createRoom);

// Cập nhật thông tin phòng chat
router.put('/:id', authenticate, chatRoomController.updateRoom);

// Lấy danh sách người tham gia trong phòng
router.get('/:id/participants', authenticate, chatRoomController.getRoomParticipants);

// Thêm người tham gia vào phòng
router.post('/:id/participants', authenticate, chatRoomController.addParticipant);

// Xóa người tham gia khỏi phòng
router.delete('/:id/participants', authenticate, chatRoomController.removeParticipant);

export default router;
