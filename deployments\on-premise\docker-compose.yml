version: '3.8'

services:
  # Database PostgreSQL
  database:
    image: postgres:15-alpine
    container_name: loaloa-database
    restart: always
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-strong_password}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Admin Portal
  admin-portal:
    build:
      context: ../../apps/admin-portal
      dockerfile: ../../deployments/on-premise/Dockerfile.admin-portal
    container_name: loaloa-admin-portal
    restart: always
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=http://supabase-api:8000
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    depends_on:
      - supabase-api

  # Chat App (Guest App)
  chat-app:
    build:
      context: ../../apps/guest-app
      dockerfile: ../../deployments/on-premise/Dockerfile.chat-app
    container_name: loaloa-chat-app
    restart: always
    ports:
      - "3001:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=http://supabase-api:8000
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    depends_on:
      - supabase-api

  # Staff Dashboard
  staff-dashboard:
    build:
      context: ../../apps/staff-dashboard
      dockerfile: ../../deployments/on-premise/Dockerfile.staff-dashboard
    container_name: loaloa-staff-dashboard
    restart: always
    ports:
      - "3002:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=http://supabase-api:8000
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    depends_on:
      - supabase-api

  # Supabase API
  supabase-api:
    image: supabase/supabase-api:latest
    container_name: loaloa-supabase-api
    restart: always
    environment:
      POSTGRES_HOST: database
      POSTGRES_PORT: 5432
      POSTGRES_DB: postgres
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-strong_password}
      JWT_SECRET: ${SUPABASE_JWT_SECRET}
      ANON_KEY: ${SUPABASE_ANON_KEY}
      SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
    ports:
      - "8000:8000"
    depends_on:
      - database

  # License Agent
  license-agent:
    build:
      context: ./license-agent
      dockerfile: Dockerfile
    container_name: loaloa-license-agent
    restart: unless-stopped
    volumes:
      - license-data:/app/data
    environment:
      - CHECK_INTERVAL=86400 # 24 giờ
      - LICENSE_SERVER_URL=${LICENSE_SERVER_URL:-https://license.loaloa.app}
    depends_on:
      - admin-portal

volumes:
  postgres-data:
  license-data:
