'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import DashboardLayout from '../../dashboard-layout';
import AreaCard from '../../components/areas/AreaCard';
import styles from './areas.module.scss';

interface Area {
  id: string;
  name: string;
  area_type: string;
  floor?: string;
  location?: string;
  description?: string;
  staff_count?: number;
  opening_hours?: string;
  closing_hours?: string;
  image_url?: string;
}

export default function AreasListPage() {
  const [areas, setAreas] = useState<Area[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [floorFilter, setFloorFilter] = useState('');
  
  useEffect(() => {
    const fetchAreas = async () => {
      try {
        setLoading(true);
        // Build query string for filters
        const queryParams = new URLSearchParams();
        if (typeFilter) queryParams.append('area_type', typeFilter);
        if (floorFilter) queryParams.append('floor', floorFilter);
        
        const response = await fetch(`/api/areas?${queryParams.toString()}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch areas');
        }
        
        const data = await response.json();
        setAreas(data.data || []);
      } catch (err: any) {
        console.error(err);
        setError(err.message || 'An error occurred while fetching areas');
      } finally {
        setLoading(false);
      }
    };
    
    fetchAreas();
  }, [typeFilter, floorFilter]);
  
  // Filter areas by search term
  const filteredAreas = areas.filter(area =>
    area.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (area.location && area.location.toLowerCase().includes(searchTerm.toLowerCase()))
  );
  
  const getAreaTypeText = (areaType: string) => {
    switch (areaType) {
      case 'restaurant':
        return 'Nhà hàng';
      case 'pool':
        return 'Hồ bơi';
      case 'spa':
        return 'Spa';
      case 'gym':
        return 'Phòng tập';
      case 'lobby':
        return 'Sảnh';
      case 'bar':
        return 'Quầy bar';
      default:
        return 'Khác';
    }
  };
  
  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <Link href="/rooms-areas" className={styles.backLink}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15.8333 10H4.16666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M9.99999 15.8334L4.16666 10.0001L9.99999 4.16675" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Dashboard
            </Link>
            <h1 className={styles.title}>Danh sách khu vực</h1>
          </div>
          <div className={styles.actions}>
            <div className={styles.viewToggle}>
              <button
                className={`${styles.viewToggleButton} ${viewMode === 'grid' ? styles.active : ''}`}
                onClick={() => setViewMode('grid')}
                title="Xem dạng lưới"
              >
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <rect x="3" y="3" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
                  <rect x="11" y="3" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
                  <rect x="3" y="11" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
                  <rect x="11" y="11" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
                </svg>
              </button>
              <button
                className={`${styles.viewToggleButton} ${viewMode === 'table' ? styles.active : ''}`}
                onClick={() => setViewMode('table')}
                title="Xem dạng bảng"
              >
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M3 5C3 3.89543 3.89543 3 5 3H15C16.1046 3 17 3.89543 17 5V15C17 16.1046 16.1046 17 15 17H5C3.89543 17 3 16.1046 3 15V5Z" stroke="currentColor" strokeWidth="1.5"/>
                  <path d="M3 8H17" stroke="currentColor" strokeWidth="1.5"/>
                  <path d="M3 12H17" stroke="currentColor" strokeWidth="1.5"/>
                  <path d="M7 8V17" stroke="currentColor" strokeWidth="1.5"/>
                </svg>
              </button>
            </div>
            <Link href="/rooms-areas/areas/create" className={styles.createButton}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M8 3.33334V12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3.33334 8H12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Thêm khu vực
            </Link>
          </div>
        </div>

        <div className={styles.filters}>
          <div className={styles.searchBox}>
            <input
              type="text"
              placeholder="Tìm kiếm khu vực..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
            <svg className={styles.searchIcon} width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M17.5 17.5L12.5 12.5M14.1667 8.33333C14.1667 11.555 11.555 14.1667 8.33333 14.1667C5.11167 14.1667 2.5 11.555 2.5 8.33333C2.5 5.11167 5.11167 2.5 8.33333 2.5C11.555 2.5 14.1667 5.11167 14.1667 8.33333Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          
          <div className={styles.filterGroup}>
            <select 
              className={styles.filterSelect}
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="">Tất cả loại khu vực</option>
              <option value="restaurant">Nhà hàng</option>
              <option value="pool">Hồ bơi</option>
              <option value="spa">Spa</option>
              <option value="gym">Phòng tập</option>
	          <option value="lobby">Sảnh</option>
              <option value="bar">Quầy bar</option>
              <option value="other">Khác</option>
            </select>
            
            <select 
              className={styles.filterSelect}
              value={floorFilter}
              onChange={(e) => setFloorFilter(e.target.value)}
            >
              <option value="">Tất cả tầng</option>
              <option value="1">Tầng 1</option>
              <option value="2">Tầng 2</option>
              <option value="3">Tầng 3</option>
              <option value="4">Tầng 4</option>
              <option value="5">Tầng 5</option>
              <option value="G">Tầng trệt</option>
              <option value="B1">Tầng hầm 1</option>
            </select>
          </div>
        </div>
        
        {loading ? (
          <div className={styles.loading}>Đang tải danh sách khu vực...</div>
        ) : error ? (
          <div className={styles.error}>
            <p>Lỗi: {error}</p>
            <button onClick={() => window.location.reload()} className={styles.retryButton}>
              Thử lại
            </button>
          </div>
        ) : filteredAreas.length === 0 ? (
          <div className={styles.emptyState}>
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="8" y1="12" x2="16" y2="12"></line>
              <line x1="8" y1="16" x2="16" y2="16"></line>
              <line x1="8" y1="8" x2="16" y2="8"></line>
            </svg>
            <h3>Không tìm thấy khu vực</h3>
            <p>
              {searchTerm || typeFilter || floorFilter
                ? 'Không có khu vực nào phù hợp với bộ lọc đã chọn.'
                : 'Chưa có khu vực nào được tạo. Hãy tạo khu vực mới để bắt đầu.'}
            </p>
            {!searchTerm && !typeFilter && !floorFilter && (
              <Link href="/rooms-areas/areas/create" className={styles.createEmptyButton}>
                Tạo khu vực mới
              </Link>
            )}
          </div>
        ) : viewMode === 'grid' ? (
          <div className={styles.areasGrid}>
            {filteredAreas.map((area) => (
              <div key={area.id} className={styles.areaCard}>
                <div className={styles.areaCardHeader}>
                  <h3 className={styles.areaName}>{area.name}</h3>
                  <span className={styles.areaType}>{getAreaTypeText(area.area_type)}</span>
                </div>
                
                {area.image_url && (
                  <div className={styles.areaImageContainer}>
                    <img src={area.image_url} alt={area.name} className={styles.areaImage} />
                  </div>
                )}
                
                <div className={styles.areaDetails}>
                  {area.location && (
                    <div className={styles.areaDetail}>
                      <span className={styles.detailLabel}>Vị trí:</span>
                      <span className={styles.detailValue}>{area.location}</span>
                    </div>
                  )}
                  
                  {area.floor && (
                    <div className={styles.areaDetail}>
                      <span className={styles.detailLabel}>Tầng:</span>
                      <span className={styles.detailValue}>{area.floor}</span>
                    </div>
                  )}
                  
                  {(area.opening_hours || area.closing_hours) && (
                    <div className={styles.areaDetail}>
                      <span className={styles.detailLabel}>Giờ hoạt động:</span>
                      <span className={styles.detailValue}>
                        {area.opening_hours || '--'} - {area.closing_hours || '--'}
                      </span>
                    </div>
                  )}
                  
                  {area.staff_count !== undefined && (
                    <div className={styles.areaDetail}>
                      <span className={styles.detailLabel}>Số nhân viên:</span>
                      <span className={styles.detailValue}>{area.staff_count}</span>
                    </div>
                  )}
                </div>
                
                <div className={styles.areaActions}>
                  <Link href={`/rooms-areas/areas/${area.id}`} className={styles.viewButton}>
                    Chi tiết
                  </Link>
                  <Link href={`/rooms-areas/areas/${area.id}/edit`} className={styles.editButton}>
                    Chỉnh sửa
                  </Link>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.tableContainer}>
            <table className={styles.tableView}>
              <thead>
                <tr>
                  <th>Tên khu vực</th>
                  <th>Loại</th>
                  <th>Vị trí</th>
                  <th>Tầng</th>
                  <th>Giờ hoạt động</th>
                  <th>Số nhân viên</th>
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody>
                {filteredAreas.map((area) => (
                  <tr key={area.id}>
                    <td>{area.name}</td>
                    <td>{getAreaTypeText(area.area_type)}</td>
                    <td>{area.location || '-'}</td>
                    <td>{area.floor || '-'}</td>
                    <td>
                      {area.opening_hours || area.closing_hours
                        ? `${area.opening_hours || '--'} - ${area.closing_hours || '--'}`
                        : '-'}
                    </td>
                    <td>{area.staff_count !== undefined ? area.staff_count : '-'}</td>
                    <td>
                      <div className={styles.tableActions}>
                        <Link href={`/rooms-areas/areas/${area.id}`} className={styles.actionLink}>
                          Chi tiết
                        </Link>
                        <Link href={`/rooms-areas/areas/${area.id}/edit`} className={styles.actionLink}>
                          Sửa
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}