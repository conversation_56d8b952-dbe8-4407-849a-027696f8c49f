.container {
  padding: 1rem;
}

.header {
  margin-bottom: 2rem;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  text-decoration: none;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  
  &:hover {
    color: #0284c7;
  }
}

.title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #334155;
  margin: 0;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-left-color: #0284c7;
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
  }
  
  p {
    color: #64748b;
  }
}

.notFound {
  text-align: center;
  padding: 4rem 2rem;
  
  h2 {
    font-size: 1.5rem;
    color: #334155;
    margin-bottom: 1rem;
  }
  
  p {
    color: #64748b;
    margin-bottom: 2rem;
  }
}

.linkButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  background-color: #0284c7;
  color: white;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #0369a1;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
