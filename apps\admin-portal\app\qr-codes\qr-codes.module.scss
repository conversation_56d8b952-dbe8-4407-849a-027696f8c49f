.container {
  padding: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #111827;
}

.description {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0 0 0;
}

.statsSection {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.filtersContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
}

.searchBar {
  flex: 1;
  min-width: 240px;
  max-width: 360px;
}

.listContainer {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .statsSection {
    grid-template-columns: 1fr;
  }
  
  .filtersContainer {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .searchBar {
    width: 100%;
    max-width: none;
  }
}
