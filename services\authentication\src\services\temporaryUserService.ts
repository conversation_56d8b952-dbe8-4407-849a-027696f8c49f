import { v4 as uuidv4 } from 'uuid';
import QRCode from 'qrcode';
import supabase from '../utils/supabase';
import { TemporaryUser, QRCodePayload } from '../types';
import { generateToken, verifyToken } from '../utils/jwt';
import { addHours } from '../utils/dateUtils';

// Get expiration time in hours from env or default to 24 hours
const TEMPORARY_USER_EXPIRATION = process.env.TEMPORARY_USER_EXPIRATION || '24h';
const expirationHours = parseInt(TEMPORARY_USER_EXPIRATION.replace('h', ''), 10) || 24;

export const createTemporaryUser = async (
  preferredLanguage: string = 'en',
  hotelId?: string,
  roomNumber?: string,
  metadata?: Record<string, any>
): Promise<{ temporaryUser: TemporaryUser; qrCodeUrl: string } | null> => {
  try {
    // Generate unique QR code ID
    const qrCodeId = uuidv4();
    
    // Set expiration date
    const expiresAt = addHours(new Date(), expirationHours);
    
    // Create temporary user in database
    const { data, error } = await supabase
      .from('temporary_users')
      .insert([{
        qr_code_id: qrCodeId,
        preferred_language: preferredLanguage,
        expires_at: expiresAt.toISOString(),
        is_activated: false,
        room_number: roomNumber,
        hotel_id: hotelId,
        metadata: metadata || {}
      }])
      .select()
      .single();

    if (error) {
      console.error('Create temporary user error:', error);
      return null;
    }
    
    if (!data) {
      console.error('No data returned from temporary user creation');
      return null;
    }
    
    // Create QR code payload with minimal data
    const qrCodePayload: QRCodePayload = {
      qr_code_id: qrCodeId,
      expires_at: expiresAt.toISOString()
    };
    
    // Generate JWT for the QR code - shorter lived than the record itself
    const qrToken = generateToken(qrCodePayload as any, '12h');
    
    // Base URL for QR activation - in production this would be your app's URL
    const baseUrl = process.env.QR_ACTIVATION_URL || 'https://loaloa.app/activate';
    const qrUrl = `${baseUrl}/${qrToken}`;
    
    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(qrUrl);
    
    return {
      temporaryUser: data as TemporaryUser,
      qrCodeUrl
    };
  } catch (error) {
    console.error('Create temporary user with QR error:', error);
    return null;
  }
};

export const activateTemporaryUser = async (
  qrToken: string, 
  deviceId: string
): Promise<TemporaryUser | null> => {
  try {
    // Verify the QR token
    const payload = verifyToken(qrToken) as QRCodePayload;
    
    if (!payload || !payload.qr_code_id) {
      return null;
    }
    
    // Get the temporary user from database
    const { data, error } = await supabase
      .from('temporary_users')
      .select('*')
      .eq('qr_code_id', payload.qr_code_id)
      .eq('is_activated', false)
      .single();
    
    if (error || !data) {
      return null;
    }
    
    // Check if expired
    const now = new Date();
    const expiresAt = new Date(data.expires_at);
    
    if (now > expiresAt) {
      return null;
    }
    
    // Activate the temporary user
    const { data: updatedUser, error: updateError } = await supabase
      .from('temporary_users')
      .update({
        device_id: deviceId,
        is_activated: true
      })
      .eq('id', data.id)
      .select()
      .single();
    
    if (updateError) {
      return null;
    }
    
    return updatedUser as TemporaryUser;
  } catch (error) {
    console.error('Activate temporary user error:', error);
    return null;
  }
};

export const getTemporaryUserByDeviceId = async (deviceId: string): Promise<TemporaryUser | null> => {
  try {
    // Get temporary user with given device ID that isn't expired
    const now = new Date().toISOString();
    
    const { data, error } = await supabase
      .from('temporary_users')
      .select('*')
      .eq('device_id', deviceId)
      .eq('is_activated', true)
      .gt('expires_at', now)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();
    
    if (error || !data) {
      return null;
    }
    
    return data as TemporaryUser;
  } catch (error) {
    console.error('Get temporary user error:', error);
    return null;
  }
};

export const convertToPermamentUser = async (
  temporaryUserId: string,
  userData: {
    email: string;
    password: string;
    full_name?: string;
  }
): Promise<boolean> => {
  try {
    // Get the temporary user
    const { data: tempUser, error } = await supabase
      .from('temporary_users')
      .select('*')
      .eq('id', temporaryUserId)
      .single();
    
    if (error || !tempUser) {
      return false;
    }
    
    // Create a permanent user
    // Note: In a real application, you would use your existing registration service
    // instead of duplicating code here
    
    // For demo purposes, we're returning true
    // In a real app, you'd create the user and transfer data/history from the temp user
    return true;
  } catch (error) {
    console.error('Convert to permanent user error:', error);
    return false;
  }
};
