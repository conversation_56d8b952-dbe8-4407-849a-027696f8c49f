import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabase } from '@/lib/supabase'
import * as fs from 'fs'
import * as path from 'path'

// ✅ UUID generator function
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ code: string }> }
) {
  try {
    const resolvedParams = await params
    const code = resolvedParams.code
    const { searchParams } = new URL(request.url)
    const deviceId = searchParams.get('device') || generateDeviceId(request)
    const preferredLanguage = searchParams.get('lang') || 'en'

    console.log('🔍 QR Scan Request:', { code, deviceId, preferredLanguage })

    // 1. Get tenant ID from license config file
    const tenantId = await getCurrentTenantIdFromFile()
    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant not found. Please activate your license.' },
        { status: 404 }
      )
    }

    console.log('✅ Tenant ID found:', tenantId)

    const supabase = createServerSupabase()

    // 2. Find QR code in database with enhanced fields
    const { data: qrCode, error: qrError } = await supabase
      .from('tenant_qr_codes')
      .select(`
        id,
        tenant_id,
        code_value,
        location,
        description,
        room_number,
        status,
        target_type,
        target_id,
        scan_count,
        qr_type_id,
        custom_action,
        reception_point_id,
        is_active,
        name,
        qr_type,
        target_department,
        last_scanned_at
      `)
      .eq('tenant_id', tenantId)
      .eq('code_value', code)
      .eq('is_active', true)
      .single()

    if (qrError || !qrCode) {
      console.error('❌ QR Code not found:', qrError)
      return NextResponse.json(
        { error: 'QR code not found or inactive' },
        { status: 404 }
      )
    }

    console.log('✅ QR Code found:', qrCode)

    // 3. Check if temporary user already exists for this QR code
    const { data: existingUser, error: existingUserError } = await supabase
      .from('temporary_users')
      .select('*')
      .eq('qr_code_id', code)
      .eq('device_id', deviceId)
      .maybeSingle()

    let tempUser, tempUserId, sessionToken

    if (existingUser && !existingUserError) {
      // User already exists, check if still valid
      const now = new Date()
      const expiresAt = new Date(existingUser.expires_at)

      if (now < expiresAt) {
        // Existing user is still valid, reuse it
        console.log('♻️ Reusing existing temporary user:', existingUser.id)
        tempUser = existingUser
        tempUserId = existingUser.id
        sessionToken = `session-${Date.now()}-${Math.random().toString(36).substr(2, 12)}`
      } else {
        // Existing user expired, delete and create new
        console.log('⏰ Existing user expired, deleting...')
        await supabase
          .from('temporary_users')
          .delete()
          .eq('id', existingUser.id)

        tempUser = null // Will create new below
      }
    } else {
      // Check if there's any existing user for this QR code (regardless of device)
      const { data: anyExistingUser, error: anyUserError } = await supabase
        .from('temporary_users')
        .select('*')
        .eq('qr_code_id', code)
        .maybeSingle()

      if (anyExistingUser && !anyUserError) {
        // Reuse existing user but update device_id
        console.log('♻️ Reusing existing user for QR code:', anyExistingUser.id)
        await supabase
          .from('temporary_users')
          .update({ device_id: deviceId })
          .eq('id', anyExistingUser.id)

        tempUser = { ...anyExistingUser, device_id: deviceId }
        tempUserId = anyExistingUser.id
        sessionToken = `session-${Date.now()}-${Math.random().toString(36).substr(2, 12)}`
      } else {
        tempUser = null // Will create new below
      }
    }

    // 4. Create new temporary user if needed
    if (!tempUser) {
      tempUserId = generateUUID()
      sessionToken = `session-${Date.now()}-${Math.random().toString(36).substr(2, 12)}`
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

      console.log('🆔 Creating new temp user ID:', tempUserId)

      const tempUserData = {
        id: tempUserId,
        qr_code_id: code,
        device_id: deviceId,
        preferred_language: preferredLanguage,
        room_number: qrCode.room_number,
        hotel_id: qrCode.tenant_id,
        expires_at: expiresAt.toISOString(),
        is_activated: true,
        metadata: {
          qr_location: qrCode.location,
          qr_description: qrCode.description,
          scan_timestamp: new Date().toISOString(),
          display_name: generateGuestDisplayName(qrCode)
        }
      }

      console.log('📝 Temp user data to insert:', tempUserData)

      const { data: newTempUser, error: tempUserError } = await supabase
        .from('temporary_users')
        .insert(tempUserData)
        .select()
        .single()

      if (tempUserError) {
        console.error('❌ Error creating temporary user:', tempUserError)
        return NextResponse.json(
          { error: 'Failed to create user session: ' + tempUserError.message },
          { status: 500 }
        )
      }

      tempUser = newTempUser
      console.log('✅ New temporary user created:', tempUser)
    }

    // 5. Update QR code scan count and last scanned timestamp
    await supabase
      .from('tenant_qr_codes')
      .update({
        scan_count: (qrCode.scan_count || 0) + 1,
        last_scanned_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', qrCode.id)

    // 6. Generate display name based on QR type
    const displayName = generateGuestDisplayName(qrCode)

    const response = {
      success: true,
      qr_code: {
        id: qrCode.id,
        location: qrCode.location,
        room_number: qrCode.room_number,
        tenant_id: qrCode.tenant_id,
        display_name: displayName
      },
      temporary_user: {
        id: tempUserId,
        session_token: sessionToken,
        preferred_language: preferredLanguage,
        expires_at: tempUser.expires_at,
        display_name: displayName,
        is_existing: !!existingUser // Flag to indicate if user was reused
      },
      redirect_url: `/chat/new?temp_user=${tempUserId}&lang=${preferredLanguage}&qr=${code}&token=${sessionToken}`
    }

    console.log('🚀 QR scan successful response:', response)
    return NextResponse.json(response)

  } catch (error) {
    console.error('💥 Error processing QR scan:', error)
    return NextResponse.json(
      { error: 'Internal server error: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    )
  }
}

// Helper functions remain the same...
async function getCurrentTenantIdFromFile(): Promise<string | null> {
  try {
    const possiblePaths = [
      path.resolve(process.cwd(), 'license_config.json'),
      path.resolve(process.cwd(), '../../license_config.json'),
    ]

    for (const configPath of possiblePaths) {
      if (fs.existsSync(configPath)) {
        const fileContent = fs.readFileSync(configPath, 'utf8')
        if (fileContent.trim()) {
          const config = JSON.parse(fileContent)
          if (config.licenseKey && config.tenant_id) {
            return config.tenant_id
          }
        }
      }
    }
    return null
  } catch (error) {
    console.error('Error reading license config:', error)
    return null
  }
}

function generateDeviceId(request: NextRequest): string {
  const userAgent = request.headers.get('user-agent') || ''
  const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
  const deviceString = `${userAgent}-${ip}-${Date.now()}`
  return Buffer.from(deviceString).toString('base64').substr(0, 32)
}

function generateGuestDisplayName(qrCode: any): string {
  // Priority: room_number > name > location > description
  if (qrCode.room_number) {
    return `Room ${qrCode.room_number}`
  }
  if (qrCode.name) {
    return qrCode.name
  }
  if (qrCode.location) {
    return qrCode.location
  }
  if (qrCode.description) {
    return qrCode.description
  }
  return 'Guest Area'
}
