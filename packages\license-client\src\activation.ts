import axios from 'axios';
import { LicenseActivationRequest, LicenseActivationResponse } from './types';
import { generateHardwareFingerprint } from './fingerprint';

/**
 * License activation service
 */
export class LicenseActivationService {
  private apiUrl: string;
  
  /**
   * Constructor
   * @param apiUrl The URL of the license service API
   */
  constructor(apiUrl: string) {
    this.apiUrl = apiUrl;
  }
  
  /**
   * Activate a license
   * @param licenseKey The license key
   * @param customerName The customer name
   * @param customerEmail The customer email
   * @param useAutoFingerprint Whether to automatically generate hardware fingerprint
   * @param customFingerprint Optional custom hardware fingerprint
   * @returns Promise with activation response
   */
  async activateLicense(
    licenseKey: string,
    customerName: string,
    customerEmail: string,
    useAutoFingerprint: boolean = true,
    customFingerprint?: string
  ): Promise<LicenseActivationResponse> {
    try {
      // Generate hardware fingerprint if auto is enabled
      const hardwareFingerprint = useAutoFingerprint
        ? generateHardwareFingerprint()
        : customFingerprint || '';
      
      if (!hardwareFingerprint) {
        throw new Error('Hardware fingerprint is required for activation');
      }
      
      const request: LicenseActivationRequest = {
        license_key: licenseKey,
        customer_name: customerName,
        customer_email: customerEmail,
        hardware_fingerprint: hardwareFingerprint
      };
      
      const response = await axios.post<LicenseActivationResponse>(
        `${this.apiUrl}/license-check?action=activate`,
        request
      );
      
      return response.data;
    } catch (error: any) {
      console.error('License activation failed:', error);
      return {
        success: false,
        message: 'License activation failed',
        error: error.message || 'Unknown error'
      };
    }
  }
  
  /**
   * Verify email activation
   * @param activationId The activation ID received from email
   * @param token The verification token received from email
   * @returns Promise with activation response
   */
  async verifyEmailActivation(
    activationId: string,
    token: string
  ): Promise<LicenseActivationResponse> {
    try {
      const response = await axios.post<LicenseActivationResponse>(
        `${this.apiUrl}/license-check/verify`,
        {
          activation_id: activationId,
          token: token
        }
      );
      
      return response.data;
    } catch (error: any) {
      console.error('Email verification failed:', error);
      return {
        success: false,
        message: 'Email verification failed',
        error: error.message || 'Unknown error'
      };
    }
  }
}
