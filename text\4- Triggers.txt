[{"table_name": "tenant_areas", "trigger_name": "set_tenant_areas_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_updated_at_column()", "trigger_comment": null}, {"table_name": "tenant_chat_routing_rules", "trigger_name": "update_tenant_chat_routing_rules_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_tenant_chat_routing_rules_updated_at()", "trigger_comment": "Tự động cập nhật trường updated_at khi có thay đổi"}, {"table_name": "tenant_chat_session_assignments", "trigger_name": "update_chat_session_status_on_assignment", "event_manipulation": "UPDATE", "action_timing": "AFTER", "action_statement": "EXECUTE FUNCTION update_chat_session_status_on_assignment()", "trigger_comment": "Tự động cập nhật trạng thái phiên chat khi phân công thay đổi"}, {"table_name": "tenant_chat_session_assignments", "trigger_name": "update_tenant_chat_session_assignments_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_tenant_chat_session_assignments_updated_at()", "trigger_comment": "Tự động cập nhật trường updated_at khi có thay đổi"}, {"table_name": "tenant_guests", "trigger_name": "after_insert_guest", "event_manipulation": "INSERT", "action_timing": "AFTER", "action_statement": "EXECUTE FUNCTION handle_new_guest()", "trigger_comment": null}, {"table_name": "tenant_guests", "trigger_name": "after_update_room_guest", "event_manipulation": "UPDATE", "action_timing": "AFTER", "action_statement": "EXECUTE FUNCTION handle_guest_room_change()", "trigger_comment": null}, {"table_name": "tenant_guests", "trigger_name": "check_room_before_checkin", "event_manipulation": "INSERT", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION check_room_availability()", "trigger_comment": "Validates room availability before guest check-in"}, {"table_name": "tenant_guests", "trigger_name": "check_room_before_checkin", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION check_room_availability()", "trigger_comment": "Validates room availability before guest check-in"}, {"table_name": "tenant_guests", "trigger_name": "update_room_on_checkout", "event_manipulation": "UPDATE", "action_timing": "AFTER", "action_statement": "EXECUTE FUNCTION update_room_status_on_checkout()", "trigger_comment": "Updates room status when guest checks out"}, {"table_name": "tenant_guests", "trigger_name": "validate_guest_room_trigger", "event_manipulation": "INSERT", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION validate_guest_room()", "trigger_comment": "<PERSON><PERSON><PERSON> b<PERSON>o kh<PERSON>ch chỉ được check-in vào phòng có thật"}, {"table_name": "tenant_guests", "trigger_name": "validate_guest_room_trigger", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION validate_guest_room()", "trigger_comment": "<PERSON><PERSON><PERSON> b<PERSON>o kh<PERSON>ch chỉ được check-in vào phòng có thật"}, {"table_name": "tenant_message_reception_points", "trigger_name": "update_tenant_message_reception_points_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_tenant_message_reception_points_updated_at()", "trigger_comment": "Tự động cập nhật trường updated_at khi có thay đổi"}, {"table_name": "tenant_message_reception_points", "trigger_name": "update_tenant_message_reception_points_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_tenant_message_reception_points_updated_at()", "trigger_comment": "Tự động cập nhật trường updated_at khi có thay đổi"}, {"table_name": "tenant_qr_codes", "trigger_name": "validate_qr_code_target_trigger", "event_manipulation": "INSERT", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION validate_qr_code_target()", "trigger_comment": "Ensures QR codes can only be linked to existing rooms or areas"}, {"table_name": "tenant_qr_codes", "trigger_name": "validate_qr_code_target_trigger", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION validate_qr_code_target()", "trigger_comment": "Ensures QR codes can only be linked to existing rooms or areas"}, {"table_name": "tenant_qr_code_scans", "trigger_name": "increment_qr_code_scan_count", "event_manipulation": "INSERT", "action_timing": "AFTER", "action_statement": "EXECUTE FUNCTION increment_qr_code_scan_count()", "trigger_comment": "Tự động tăng số lượt quét cho QR code"}, {"table_name": "tenant_qr_code_types", "trigger_name": "update_tenant_qr_code_types_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_tenant_qr_code_types_updated_at()", "trigger_comment": "Tự động cập nhật trường updated_at khi có thay đổi"}, {"table_name": "tenant_rooms", "trigger_name": "check_room_limit_trigger", "event_manipulation": "INSERT", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION check_room_limit_trigger()", "trigger_comment": null}, {"table_name": "tenant_rooms", "trigger_name": "prevent_delete_room_with_guests", "event_manipulation": "DELETE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION prevent_room_deletion_with_guests()", "trigger_comment": "Prevents deletion of rooms with active guests"}, {"table_name": "tenant_rooms", "trigger_name": "set_tenant_rooms_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_updated_at_column()", "trigger_comment": null}, {"table_name": "tenant_staff_assignments", "trigger_name": "update_tenant_staff_assignments_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_tenant_staff_assignments_updated_at()", "trigger_comment": "Tự động cập nhật trường updated_at khi có thay đổi"}, {"table_name": "tenant_translation_settings", "trigger_name": "trigger_update_translation_settings_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_updated_at_column()", "trigger_comment": null}, {"table_name": "tenant_translation_settings", "trigger_name": "update_tenant_translation_settings_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_tenant_translation_settings_updated_at()", "trigger_comment": "Tự động cập nhật trường updated_at khi có thay đổi"}, {"table_name": "tenant_typing_status", "trigger_name": "trigger_update_typing_status_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_typing_status_updated_at()", "trigger_comment": null}, {"table_name": "tenant_users", "trigger_name": "log_tenant_users_delete", "event_manipulation": "DELETE", "action_timing": "AFTER", "action_statement": "EXECUTE FUNCTION log_tenant_users_changes()", "trigger_comment": "Ghi log khi xóa người dùng khỏi tenant"}, {"table_name": "tenant_users", "trigger_name": "log_tenant_users_insert", "event_manipulation": "INSERT", "action_timing": "AFTER", "action_statement": "EXECUTE FUNCTION log_tenant_users_changes()", "trigger_comment": "Ghi log khi thêm mới người dùng vào tenant"}, {"table_name": "tenant_users", "trigger_name": "log_tenant_users_update", "event_manipulation": "UPDATE", "action_timing": "AFTER", "action_statement": "EXECUTE FUNCTION log_tenant_users_changes()", "trigger_comment": "Ghi log khi cập nhật thông tin người dùng trong tenant"}, {"table_name": "tenant_users", "trigger_name": "update_tenant_users_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_tenant_users_updated_at()", "trigger_comment": null}, {"table_name": "tenant_users_details", "trigger_name": "update_tenant_users_details_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_tenant_users_details_updated_at()", "trigger_comment": "Tự động cập nhật trường updated_at khi có thay đổi"}, {"table_name": "tenant_voice_messages", "trigger_name": "trigger_update_voice_messages_updated_at", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_updated_at_column()", "trigger_comment": null}, {"table_name": "tenant_web_sessions", "trigger_name": "trigger_update_web_session_activity", "event_manipulation": "UPDATE", "action_timing": "BEFORE", "action_statement": "EXECUTE FUNCTION update_session_activity()", "trigger_comment": null}]