import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { createAdminClient } from '../../../lib/supabase/admin';

// Function to get tenant_id from license_config.json
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const areaType = searchParams.get('area_type');
    const floor = searchParams.get('floor');
    const isActive = searchParams.get('is_active');
    const limitStr = searchParams.get('limit');
    const limit = limitStr ? parseInt(limitStr) : undefined;
    const reception_point_id = searchParams.get('reception_point_id') || '';

    // Get tenant_id from config file
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }
    
    const supabase = createAdminClient();

    // Query areas by tenant_id
    let query = supabase
      .from('tenant_areas')
      .select('*', { count: 'exact' })
      .eq('tenant_id', tenant_id);

    // Apply filters
    if (areaType) {
      query = query.eq('area_type', areaType);
    }
    if (floor) {
      query = query.eq('floor', floor);
    }
    if (isActive === 'true') {
      query = query.eq('is_active', true);
    } else if (isActive === 'false') {
      query = query.eq('is_active', false);
    }
    if (reception_point_id) {
      query = query.eq('reception_point_id', reception_point_id);
    }
    // Apply limit if provided
    if (limit) {
      query = query.limit(limit);
    }

    const { data, error, count } = await query;
    
    if (error) {
      console.error('Error fetching areas:', error);
      return NextResponse.json({ 
        error: error.message 
      }, { status: 500 });
    }

    return NextResponse.json({
      data: data || [],
      count: count || 0
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ 
      data: [],
      count: 0
    });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    // Get tenant_id from config file
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    const supabase = createAdminClient();
    
    const { 
      name, 
      area_type, 
      floor, 
      location, 
      description, 
      staff_count, 
      opening_hours, 
      closing_hours, 
      image_url,
      reception_point_id // Thêm reception_point_id
    } = body;

    // Check if area with same name already exists
    const { data: existingArea, error: checkError } = await supabase
      .from('tenant_areas')
      .select('id')
      .eq('name', name)
      .eq('tenant_id', tenant_id)
      .maybeSingle();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing area:', checkError);
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    if (existingArea) {
      return NextResponse.json({ error: `Area with name "${name}" already exists` }, { status: 400 });
    }

    // Kiểm tra reception_point_id nếu được cung cấp
    if (reception_point_id) {
      const { data: receptionPoint, error: rpError } = await supabase
        .from('tenant_message_reception_points')
        .select('id')
        .eq('id', reception_point_id)
        .eq('tenant_id', tenant_id)
        .single();

      if (rpError || !receptionPoint) {
        return NextResponse.json({ error: 'Reception point không tồn tại hoặc không thuộc tenant này' }, { status: 400 });
      }
    }

    // Create new area
    const { data, error } = await supabase
      .from('tenant_areas')
      .insert([
        { 
          tenant_id, 
          name, 
          area_type, 
          floor, 
         location: location || 'No location inputed',
  	description: description || 'Empty',
  	staff_count: staff_count !== undefined ? staff_count : 3, // Đảm bảo mặc định là 0 nếu không có giá trị
  	opening_hours: opening_hours || '06:00',
  	closing_hours: closing_hours || '23:00',
  	is_active: true,
 	 reception_point_id: reception_point_id || null // Đảm bảo null nếu không có giá trị
        }
      ])
      .select();

    if (error) {
      console.error('Error creating area:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data: data[0] }, { status: 201 });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
