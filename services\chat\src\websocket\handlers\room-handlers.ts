import { Server as SocketIOServer, Socket } from 'socket.io';
import { SocketEvents, SocketResponse } from '../../types';
import * as chatRoomService from '../../services/chat-room-service';

export const registerRoomHandlers = (io: SocketIOServer, socket: Socket) => {
  // Handler cho event JOIN_ROOM
  socket.on(SocketEvents.JOIN_ROOM, async (roomId: string, callback?: (response: SocketResponse) => void) => {
    try {
      const userId = socket.data.userId;
      const temporaryUserId = socket.data.temporaryUserId;
      
      if (!userId && !temporaryUserId) {
        const response: SocketResponse = {
          success: false,
          error: 'Authentication required'
        };
        if (callback) callback(response);
        return;
      }
      
      // Kiểm tra quyền tham gia phòng chat
      const canJoin = await chatRoomService.canJoinRoom(roomId, userId, temporaryUserId);
      if (!canJoin) {
        const response: SocketResponse = {
          success: false,
          error: 'You do not have permission to join this room'
        };
        if (callback) callback(response);
        return;
      }
      
      // Tham gia socket room
      await socket.join(roomId);
      
      // Lấy thông tin phòng chat
      const room = await chatRoomService.getRoomById(roomId);
      if (!room) {
        const response: SocketResponse = {
          success: false,
          error: 'Room not found'
        };
        if (callback) callback(response);
        return;
      }
      
      // Thông báo cho người dùng đã tham gia thành công
      const response: SocketResponse = {
        success: true,
        data: { room }
      };
      if (callback) callback(response);
      
      // Thông báo cho người dùng khác trong phòng
      socket.to(roomId).emit(SocketEvents.ROOM_JOINED, {
        userId: userId || 'guest',
        temporaryUserId,
        socketId: socket.id,
        timestamp: new Date().toISOString()
      });
      
      console.log(`User ${userId || temporaryUserId} joined room ${roomId}`);
    } catch (error) {
      console.error('Error in JOIN_ROOM handler:', error);
      const response: SocketResponse = {
        success: false,
        error: 'Failed to join room'
      };
      if (callback) callback(response);
    }
  });
  
  // Handler cho event LEAVE_ROOM
  socket.on(SocketEvents.LEAVE_ROOM, async (roomId: string, callback?: (response: SocketResponse) => void) => {
    try {
      await socket.leave(roomId);
      
      const userId = socket.data.userId;
      const temporaryUserId = socket.data.temporaryUserId;
      
      // Thông báo cho người dùng đã rời phòng thành công
      const response: SocketResponse = {
        success: true,
        data: { roomId }
      };
      if (callback) callback(response);
      
      // Thông báo cho người dùng khác trong phòng
      socket.to(roomId).emit(SocketEvents.ROOM_LEFT, {
        userId: userId || 'guest',
        temporaryUserId,
        socketId: socket.id,
        timestamp: new Date().toISOString()
      });
      
      console.log(`User ${userId || temporaryUserId} left room ${roomId}`);
    } catch (error) {
      console.error('Error in LEAVE_ROOM handler:', error);
      const response: SocketResponse = {
        success: false,
        error: 'Failed to leave room'
      };
      if (callback) callback(response);
    }
  });
};
