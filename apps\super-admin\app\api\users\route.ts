import { NextRequest, NextResponse } from 'next/server';
import { UserService, UserFilterOptions, PaginationOptions } from '../../services/UserService';

export async function GET(request: NextRequest) {
  try {
    // Lấy query parameters
    const searchParams = request.nextUrl.searchParams;
    
    // Xử lý pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    
    // Xử lý filters
    const status = searchParams.get('status') as 'active' | 'inactive' | 'all' || 'all';
    const role = searchParams.get('role') || undefined;
    const tenant_id = searchParams.get('tenant_id') || undefined;
    const search = searchParams.get('search') || undefined;
    
    const pagination: PaginationOptions = { page, limit };
    const filter: UserFilterOptions = { status, role, tenant_id, search };
    
    // Gọi service để lấy dữ liệu
    const { data, count } = await UserService.getUsers(pagination, filter);
    
    // Trả về kết quả
    return NextResponse.json({
      data,
      meta: {
        total: count,
        page,
        limit,
        pageCount: Math.ceil(count / limit)
      }
    });
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const userData = await request.json();
    
    // Validate dữ liệu
    if (!userData.email || !userData.full_name) {
      return NextResponse.json(
        { error: 'Email and full name are required' },
        { status: 400 }
      );
    }
    
    // Tạo user mới
    const newUser = await UserService.createUser(userData);
    
    // Trả về kết quả
    return NextResponse.json({ data: newUser }, { status: 201 });
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create user' },
      { status: 500 }
    );
  }
}
