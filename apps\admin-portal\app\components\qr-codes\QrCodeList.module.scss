.container {
  width: 100%;
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  
  th {
    text-align: left;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }
  
  td {
    padding: 12px 16px;
    font-size: 14px;
    border-bottom: 1px solid #e5e7eb;
    color: #1f2937;
  }
  
  tr:last-child td {
    border-bottom: none;
  }
  
  tr:hover {
    background-color: #f9fafb;
  }
}

.nameCell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.qrPreview {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.nameText {
  display: flex;
  flex-direction: column;
}

.name {
  font-weight: 500;
  color: #111827;
}

.description {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.actionCell {
  white-space: nowrap;
}

.actionBadge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  
  &.chat {
    background-color: #e0f2fe;
    color: #0369a1;
  }
  
  &.info {
    background-color: #f3e8ff;
    color: #7e22ce;
  }
  
  &.service {
    background-color: #dcfce7;
    color: #15803d;
  }
  
  &.feedback {
    background-color: #fef3c7;
    color: #92400e;
  }
}

.statusBadge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  
  &.active {
    background-color: #dcfce7;
    color: #15803d;
  }
  
  &.inactive {
    background-color: #f3f4f6;
    color: #6b7280;
  }
}

.actionsCell {
  display: flex;
  gap: 8px;
  white-space: nowrap;
}

.viewButton,
.editButton,
.deleteButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  background: none;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #f3f4f6;
  }
  
  svg {
    width: 14px;
    height: 14px;
    color: #4b5563;
  }
}

.editButton:hover {
  border-color: #93c5fd;
  background-color: #eff6ff;
  
  svg {
    color: #2563eb;
  }
}

.deleteButton {
  border: 1px solid #e5e7eb;
  padding: 0;
  
  &:hover {
    border-color: #fca5a5;
    background-color: #fee2e2;
    
    svg {
      color: #dc2626;
    }
  }
}

.loadingContainer,
.emptyContainer,
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.loadingContainer {
  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

.emptyContainer {
  .emptyIcon {
    margin-bottom: 16px;
  }
  
  .emptyTitle {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px;
    color: #111827;
  }
  
  .emptyText {
    font-size: 14px;
    color: #6b7280;
    max-width: 300px;
    margin: 0 0 24px;
  }
}

.errorContainer {
  .errorText {
    color: #dc2626;
    margin-bottom: 16px;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}
.tableContainer {
  width: 100%;
  overflow-x: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f1f5f9;
  }
  
  th {
    background-color: #f8fafc;
    color: #64748b;
    font-weight: 500;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    
    &:first-child {
      border-top-left-radius: 8px;
    }
    
    &:last-child {
      border-top-right-radius: 8px;
    }
  }
  
  tr:last-child td {
    border-bottom: none;
  }
}

.nameCell {
  max-width: 200px;
}

.nameLink {
  color: #0369a1;
  font-weight: 500;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

.description {
  margin-top: 4px;
  font-size: 0.875rem;
  color: #64748b;
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  
  &.active {
    background-color: #dcfce7;
    color: #16a34a;
  }
  
  &.inactive {
    background-color: #fef3c7;
    color: #d97706;
  }
}

.actionsCell {
  white-space: nowrap;
}

.actions {
  display: flex;
  gap: 8px;
}

.editButton, .deleteButton {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  
  &:focus {
    outline: none;
  }
}

.editButton {
  background-color: #eff6ff;
  color: #2563eb;
  border: none;
  text-decoration: none;
  
  &:hover {
    background-color: #dbeafe;
  }
}

.deleteButton {
  background-color: #fee2e2;
  color: #ef4444;
  border: none;
  
  &:hover {
    background-color: #fecaca;
  }
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  .spinner {
    width: 40px;
    height: 40px;
    margin-bottom: 16px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #0284c7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  p {
    color: #64748b;
    font-size: 1rem;
  }
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  svg {
    color: #ef4444;
    margin-bottom: 16px;
  }
  
  p {
    color: #64748b;
    margin-bottom: 16px;
  }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  svg {
    color: #94a3b8;
    margin-bottom: 16px;
  }
  
  h3 {
    color: #334155;
    margin-bottom: 8px;
  }
  
  p {
    color: #64748b;
    text-align: center;
    margin-bottom: 24px;
  }
}

.createButton, .retryButton {
  padding: 8px 16px;
  background-color: #0284c7;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #0369a1;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
}

.paginationButton {
  padding: 8px 16px;
  border: 1px solid #cbd5e1;
  background-color: #fff;
  color: #0284c7;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  
  &:disabled {
    color: #cbd5e1;
    cursor: not-allowed;
  }
  
  &:not(:disabled):hover {
    background-color: #f1f5f9;
  }
}

.pageInfo {
  margin: 0 16px;
  font-size: 0.875rem;
  color: #64748b;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Styles for Reception Point display
.receptionPointBadge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  background-color: #f0f9ff;
  color: #0369a1;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  
  &::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-right: 0.5rem;
    background-color: #0ea5e9;
    border-radius: 50%;
  }
}

.noReceptionPoint {
  color: #64748b;
  font-size: 0.875rem;
  font-style: italic;
}
