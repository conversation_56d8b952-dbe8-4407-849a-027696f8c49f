.container {
  padding: 1rem;
}

.header {
  margin-bottom: 1.5rem;
}

.backLink {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #666;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;

  &:hover {
    color: #1976d2;
  }

  svg {
    stroke: currentColor;
  }
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.formContainer {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  flex-direction: column;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error {
  padding: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
