'use client';
import { useState } from 'react';
import styles from './ReceptionPointsList.module.scss';

interface ReceptionPoint {
  id: string;
  name: string;
  code: string;
  description?: string;
  icon_url?: string;
  is_active: boolean;
  priority: number;
}

interface UserReceptionPoint {
  id: string;
  reception_point: ReceptionPoint;
  is_primary: boolean;
}

interface ReceptionPointsListProps {
  points: UserReceptionPoint[];
  loading?: boolean;
  error?: string | null;
  onSetPrimary: (linkId: string) => void;
  onRemove: (linkId: string) => void;
}

export default function ReceptionPointsList({
  points,
  loading = false,
  error = null,
  onSetPrimary,
  onRemove,
}: ReceptionPointsListProps) {
  const [confirmingRemove, setConfirmingRemove] = useState<string | null>(null);

  const handleRemoveClick = (id: string) => {
    setConfirmingRemove(id);
  };

  const handleCancelRemove = () => {
    setConfirmingRemove(null);
  };

  const handleConfirmRemove = (id: string) => {
    onRemove(id);
    setConfirmingRemove(null);
  };

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading reception points...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.error}>
        <p>{error}</p>
        <button className={styles.retryButton} onClick={() => window.location.reload()}>
          Retry
        </button>
      </div>
    );
  }

  if (!points || points.length === 0) {
    return (
      <div className={styles.empty}>
        <p>This user is not assigned to any message reception points.</p>
        <p className={styles.emptySubtext}>
          Assign to reception points to allow this user to receive and handle messages.
        </p>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {points.map((point) => (
        <div 
          key={point.id}
          className={`${styles.pointItem} ${point.is_primary ? styles.primary : ''}`}
        >
          <div className={styles.pointIcon}>
            {point.reception_point.icon_url ? (
              <img src={point.reception_point.icon_url} alt={point.reception_point.name} />
            ) : (
              <div className={styles.defaultIcon}>
                {point.reception_point.name[0]}
              </div>
            )}
          </div>
          
          <div className={styles.pointInfo}>
            <div className={styles.pointHeader}>
              <h3 className={styles.pointName}>
                {point.reception_point.name}
                {point.is_primary && (
                  <span className={styles.primaryBadge}>Primary</span>
                )}
              </h3>
              <span className={`${styles.statusBadge} ${point.reception_point.is_active ? styles.active : styles.inactive}`}>
                {point.reception_point.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
            
            <div className={styles.pointCode}>
              Code: <span>{point.reception_point.code}</span>
            </div>
            
            {point.reception_point.description && (
              <div className={styles.pointDescription}>
                {point.reception_point.description}
              </div>
            )}
            
            <div className={styles.pointActions}>
              {!point.is_primary && (
                <button
                  className={styles.setPrimaryButton}
                  onClick={() => onSetPrimary(point.id)}
                >
                  Set as Primary
                </button>
              )}
              
              {confirmingRemove === point.id ? (
                <div className={styles.confirmActions}>
                  <span>Are you sure?</span>
                  <button
                    className={styles.confirmButton}
                    onClick={() => handleConfirmRemove(point.id)}
                  >
                    Yes
                  </button>
                  <button
                    className={styles.cancelButton}
                    onClick={handleCancelRemove}
                  >
                    No
                  </button>
                </div>
              ) : (
                <button
                  className={styles.removeButton}
                  onClick={() => handleRemoveClick(point.id)}
                >
                  Remove
                </button>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
