'use client';

import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';

interface Tenant {
  id: string;
  name: string;
}

interface TenantContextType {
  tenant: Tenant | null;
  isLoading: boolean;
  setTenant: (tenant: Tenant) => void;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export function TenantProvider({ children }: { children: ReactNode }) {
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Lấy thông tin tenant từ license config hoặc API
    const fetchTenant = async () => {
      try {
        // Thử lấy từ localStorage trước (nếu đã lưu trước đó)
        const storedTenant = localStorage.getItem('currentTenant');
        if (storedTenant) {
          setTenant(JSON.parse(storedTenant));
          setIsLoading(false);
          return;
        }

        // Nếu không có trong localStorage, gọi API
        const response = await fetch('/api/license/tenant-info');
        if (!response.ok) {
          throw new Error('Failed to fetch tenant info');
        }

        const data = await response.json();
        const tenantInfo = {
          id: data.tenant_id || "f49430a8-486c-4ed8-8e4f-edbdafc8842f", // Grand Melia Resort tenant ID
          name: data.name || "Grand Melia"
        };

        // Lưu vào state và localStorage
        setTenant(tenantInfo);
        localStorage.setItem('currentTenant', JSON.stringify(tenantInfo));
      } catch (error) {
        console.error('Error fetching tenant:', error);
        // Fallback to default tenant
        const defaultTenant = {
          id: "f49430a8-486c-4ed8-8e4f-edbdafc8842f",
          name: "Grand Melia"
        };
        setTenant(defaultTenant);
        localStorage.setItem('currentTenant', JSON.stringify(defaultTenant));
      } finally {
        setIsLoading(false);
      }
    };

    fetchTenant();
  }, []);

  const value = {
    tenant,
    isLoading,
    setTenant: (newTenant: Tenant) => {
      setTenant(newTenant);
      localStorage.setItem('currentTenant', JSON.stringify(newTenant));
    }
  };

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}
