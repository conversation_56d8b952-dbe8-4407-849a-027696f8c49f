import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// PATCH: Cập nhật một liên kết (priority, is_primary)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { linkId: string } }
) {
  try {
    const linkId = params.linkId;
    const updateData = await request.json();
    
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }
    
    // Tạo Supabase client
    const supabase = createAdminClient(cookies());
    
    // Kiểm tra liên kết có tồn tại không
    const { data: link, error: checkError } = await supabase
      .from('tenant_user_reception_points')
      .select('id, tenant_id, tenant_user_id, reception_point_id')
      .eq('id', linkId)
      .single();
      
    if (checkError) {
      return NextResponse.json({ error: 'Link not found' }, { status: 404 });
    }
    
    if (link.tenant_id !== tenant_id) {
      return NextResponse.json({ error: 'Unauthorized access' }, { status: 403 });
    }
    
    // Prepare update data
    const dataToUpdate: { priority?: number; is_primary?: boolean } = {};
    
    if (typeof updateData.priority === 'number') {
      dataToUpdate.priority = Math.max(1, Math.min(100, updateData.priority));
    }
    
    if (typeof updateData.is_primary === 'boolean' && updateData.is_primary) {
      // If setting as primary, first reset all others
      await supabase
        .from('tenant_user_reception_points')
        .update({ is_primary: false })
        .eq('tenant_user_id', link.tenant_user_id)
        .neq('id', linkId);
      
      dataToUpdate.is_primary = true;
    }
    
    // Update the link
    const { data, error: updateError } = await supabase
      .from('tenant_user_reception_points')
      .update(dataToUpdate)
      .eq('id', linkId)
      .select()
      .single();
      
    if (updateError) {
      console.error('Error updating link:', updateError);
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }
    
    return NextResponse.json({ data, message: 'Link updated successfully' });
  } catch (error: any) {
    console.error('Error in PATCH link:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}

// DELETE: Xóa một liên kết
export async function DELETE(
  request: NextRequest,
  { params }: { params: { linkId: string } }
) {
  try {
    const linkId = params.linkId;
    
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }
    
    // Tạo Supabase client
    const supabase = createAdminClient(cookies());
    
     // Kiểm tra liên kết có tồn tại không và lấy thông tin
    const { data: link, error: checkError } = await supabase
      .from('tenant_user_reception_points')
      .select('id, tenant_id, tenant_user_id, is_primary')
      .eq('id', linkId)
      .single();
      
    if (checkError) {
      return NextResponse.json({ error: 'Link not found' }, { status: 404 });
    }
    
    if (link.tenant_id !== tenant_id) {
      return NextResponse.json({ error: 'Unauthorized access' }, { status: 403 });
    }
    
    // Xóa liên kết
    const { error: deleteError } = await supabase
      .from('tenant_user_reception_points')
      .delete()
      .eq('id', linkId);
      
    if (deleteError) {
      console.error('Error deleting link:', deleteError);
      return NextResponse.json({ error: deleteError.message }, { status: 500 });
    }
    
    // Nếu liên kết bị xóa là primary, cần cập nhật liên kết khác thành primary (nếu có)
    if (link.is_primary) {
      const { data: otherLinks, error: fetchError } = await supabase
        .from('tenant_user_reception_points')
        .select('id')
        .eq('tenant_user_id', link.tenant_user_id)
        .order('priority', { ascending: false })
        .limit(1);
        
      if (!fetchError && otherLinks && otherLinks.length > 0) {
        // Cập nhật liên kết có priority cao nhất thành primary
        await supabase
          .from('tenant_user_reception_points')
          .update({ is_primary: true })
          .eq('id', otherLinks[0].id);
      }
    }
    
    return NextResponse.json({ message: 'Link deleted successfully' });
  } catch (error: any) {
    console.error('Error in DELETE link:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}