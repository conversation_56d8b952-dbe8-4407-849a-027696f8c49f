/**
 * License configuration for web chat and admin portal
 */
export interface LicenseConfig {
  licenseKey: string;
  customerName: string;
  email: string;
  tenant_id: string;
  activated_at?: string;
  last_check?: string;
}

/**
 * License validation result for apps
 */
export interface LicenseValidationResult {
  success: boolean;
  licenseKey?: string;
  customerName?: string;
  tenant_id?: string;
  product_id?: string;
  issueDate?: string;
  expiryDate?: string;
  isActive?: boolean;
  error?: string;
  daysRemaining?: number;
}

/**
 * Tenant context for multi-tenant apps
 */
export interface TenantContext {
  tenant_id: string;
  license_key: string;
  customer_name: string;
  is_active: boolean;
  expires_at?: string;
}
