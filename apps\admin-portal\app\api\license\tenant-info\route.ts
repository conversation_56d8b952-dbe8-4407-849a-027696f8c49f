import { createClient } from '../../../../lib/supabase/server';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    // Đọc file license_config.json để lấy thông tin tenant
    const configPath = path.resolve('./license_config.json');
    let licenseConfig;
    
    try {
      // Đọc file cấu hình nếu tồn tại
      if (fs.existsSync(configPath)) {
        const fileContent = fs.readFileSync(configPath, 'utf8');
        licenseConfig = JSON.parse(fileContent);
        console.log('License config loaded:', licenseConfig);
      }
    } catch (error) {
      console.error('Error reading license config:', error);
    }
    
    // Kết nối Supabase để lấy thông tin tenant
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Lấy thông tin tenant dựa trên license key
    if (licenseConfig?.licenseKey) {
      const { data: license, error } = await supabase
        .from('licenses')
        .select('id, customer_name, tenant_id, product_id')
        .eq('license_key', licenseConfig.licenseKey)
        .single();
        
      if (!error && license) {
        // Nếu có tenant_id trong license, lấy thông tin chi tiết của tenant
        if (license.tenant_id) {
          const { data: tenant } = await supabase
            .from('tenants')
            .select('id, name')
            .eq('id', license.tenant_id)
            .single();
            
          if (tenant) {
            return NextResponse.json({
              tenant_id: tenant.id,
              name: tenant.name
            });
          }
        }
        
        // Nếu không có tenant_id trong license, tìm tenant với tên khách hàng
        const { data: tenant } = await supabase
          .from('tenants')
          .select('id, name')
          .eq('name', license.customer_name)
          .single();
          
        if (tenant) {
          return NextResponse.json({
            tenant_id: tenant.id,
            name: tenant.name
          });
        }
      }
    }
    
    // Fallback: Lấy Grand Melia Resort tenant
    const { data: defaultTenant } = await supabase
      .from('tenants')
      .select('id, name')
      .eq('name', 'Grand Melia')
      .single();
    
    if (defaultTenant) {
      return NextResponse.json({
        tenant_id: defaultTenant.id,
        name: defaultTenant.name
      });
    }
    
    // Fallback cuối cùng: hardcode tenant ID của Grand Melia
    return NextResponse.json({
      tenant_id: "35ceaef2-4b9e-4d12-b4c1-32cfe0ac43da",
      name: "Default Tenant"
    });
  } catch (error) {
    console.error('Error getting tenant info:', error);
    return NextResponse.json({
      tenant_id: "35ceaef2-4b9e-4d12-b4c1-32cfe0ac43da",
      name: "Default Tenant"
    });
  }
}
