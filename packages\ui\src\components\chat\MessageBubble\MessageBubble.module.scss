@import '../../../styles/variables';

.container {
  display: flex;
  flex-direction: column;
  max-width: 70%;
  margin-bottom: 8px;
}

.sender {
  align-self: flex-end;
  align-items: flex-end;
  
  .bubble {
    background-color: var(--color-primary, #FF4D00);
    border-radius: 12px 12px 4px 12px;
    color: white;
  }
}

.receiver {
  align-self: flex-start;
  align-items: flex-start;
  
  .bubble {
    background-color: var(--color-secondary, #EBEBEB);
    border-radius: 12px 12px 12px 4px;
    color: var(--color-text, #010103);
  }
}

.bubble {
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  word-break: break-word;
}

.content {
  white-space: pre-wrap;
}

.footer {
  display: flex;
  align-items: center;
  margin-top: 4px;
  font-size: 0.75rem;
  color: var(--color-gray, #7D8491);
  gap: 4px;
}

.time {
  font-size: 0.75rem;
}

.status {
  display: flex;
  align-items: center;
  
  &.sending {
    color: var(--color-gray, #7D8491);
  }
  
  &.sent {
    color: var(--color-gray, #7D8491);
  }
  
  &.delivered {
    color: var(--color-gray, #7D8491);
  }
  
  &.read {
    color: var(--color-primary, #FF4D00);
  }
}
