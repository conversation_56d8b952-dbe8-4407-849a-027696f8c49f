[{"table_name": "temporary_users", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": null}, {"table_name": "temporary_users", "column_name": "qr_code_id", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "temporary_users", "column_name": "device_id", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "temporary_users", "column_name": "preferred_language", "data_type": "character varying", "character_maximum_length": 50, "column_default": "'en'::character varying", "is_nullable": "YES", "column_comment": null}, {"table_name": "temporary_users", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": null}, {"table_name": "temporary_users", "column_name": "expires_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "temporary_users", "column_name": "is_activated", "data_type": "boolean", "character_maximum_length": null, "column_default": "false", "is_nullable": "YES", "column_comment": null}, {"table_name": "temporary_users", "column_name": "room_number", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "temporary_users", "column_name": "hotel_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "temporary_users", "column_name": "metadata", "data_type": "jsonb", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_areas", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "uuid_generate_v4()", "is_nullable": "NO", "column_comment": "ID duy nhất của khu vực"}, {"table_name": "tenant_areas", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của tenant sở hữu khu vực này"}, {"table_name": "tenant_areas", "column_name": "name", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON><PERSON> khu vực"}, {"table_name": "tenant_areas", "column_name": "area_type", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON><PERSON> khu vực (nhà hàng, hồ bơi, spa, ...)"}, {"table_name": "tenant_areas", "column_name": "floor", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> c<PERSON>a khu vực"}, {"table_name": "tenant_areas", "column_name": "location", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> trí chi tiết của khu vực"}, {"table_name": "tenant_areas", "column_name": "description", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> tả chi tiết về khu vực"}, {"table_name": "tenant_areas", "column_name": "qr_code_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của mã QR gắn với khu vực này"}, {"table_name": "tenant_areas", "column_name": "staff_count", "data_type": "integer", "character_maximum_length": null, "column_default": "0", "is_nullable": "YES", "column_comment": "Số lượng nhân viên phục vụ tại khu vực"}, {"table_name": "tenant_areas", "column_name": "opening_hours", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "YES", "column_comment": "Giờ mở cửa"}, {"table_name": "tenant_areas", "column_name": "closing_hours", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> đ<PERSON>g c<PERSON>a"}, {"table_name": "tenant_areas", "column_name": "image_url", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "YES", "column_comment": "URL ảnh đại diện của khu vực"}, {"table_name": "tenant_areas", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "Tr<PERSON>ng thái hoạt động của khu vực"}, {"table_name": "tenant_areas", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON>o bản ghi"}, {"table_name": "tenant_areas", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> gian cập nh<PERSON>t bản ghi gần nhất"}, {"table_name": "tenant_areas", "column_name": "reception_point_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của điểm nhận tin nhắn đư<PERSON><PERSON> gán cho khu vực này"}, {"table_name": "tenant_chat_messages", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "uuid_generate_v4()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_chat_messages", "column_name": "chat_session_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_chat_messages", "column_name": "sender_type", "data_type": "character varying", "character_maximum_length": 20, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_chat_messages", "column_name": "sender_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_chat_messages", "column_name": "content", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_chat_messages", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_chat_messages", "column_name": "is_translated", "data_type": "boolean", "character_maximum_length": null, "column_default": "false", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_chat_messages", "column_name": "original_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> ngữ của nội dung gốc (xác định tự động hoặc từ thiết lập người dùng)"}, {"table_name": "tenant_chat_messages", "column_name": "metadata", "data_type": "jsonb", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_chat_messages", "column_name": "original_content", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON>ội dung gốc không thay đổi do người dùng nhập"}, {"table_name": "tenant_chat_messages", "column_name": "translated_content", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON>ội dung đã dị<PERSON> sang ngôn ngữ đích"}, {"table_name": "tenant_chat_messages", "column_name": "translated_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> ng<PERSON> đích của bản dịch"}, {"table_name": "tenant_chat_messages", "column_name": "translation_provider", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> vụ được sử dụng để dịch (Google, Azure, v.v.)"}, {"table_name": "tenant_chat_messages", "column_name": "translation_confidence", "data_type": "numeric", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> <PERSON> cậy củ<PERSON> bản <PERSON> (0.0-1.0)"}, {"table_name": "tenant_chat_messages", "column_name": "show_translation", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "<PERSON><PERSON> xác đ<PERSON> có hiển thị bản dịch cho người nhận hay không"}, {"table_name": "tenant_chat_routing_rules", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": "<PERSON> duy nhất của quy tắc"}, {"table_name": "tenant_chat_routing_rules", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của tenant"}, {"table_name": "tenant_chat_routing_rules", "column_name": "rule_name", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON><PERSON> quy tắc định tuyến"}, {"table_name": "tenant_chat_routing_rules", "column_name": "rule_type", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON><PERSON> quy tắc: qr_code, guest, time, language, custom"}, {"table_name": "tenant_chat_routing_rules", "column_name": "rule_condition", "data_type": "jsonb", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON><PERSON><PERSON> kiện áp dụng quy tắc, dạng JSON tùy thuộc vào rule_type"}, {"table_name": "tenant_chat_routing_rules", "column_name": "target_department", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> phận đích để định tuyến"}, {"table_name": "tenant_chat_routing_rules", "column_name": "target_user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của nhân viên cụ thể để định tuyến"}, {"table_name": "tenant_chat_routing_rules", "column_name": "priority", "data_type": "integer", "character_maximum_length": null, "column_default": "1", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> độ ưu tiên của quy tắc"}, {"table_name": "tenant_chat_routing_rules", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "Tr<PERSON><PERSON> thái kích ho<PERSON>t của quy tắc"}, {"table_name": "tenant_chat_routing_rules", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm tạo bản ghi"}, {"table_name": "tenant_chat_routing_rules", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm cập nhật gần nhất"}, {"table_name": "tenant_chat_routing_rules", "column_name": "target_reception_point_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> nhận tin nhắn đích khi quy tắc này khớp"}, {"table_name": "tenant_chat_routing_rules", "column_name": "view_order", "data_type": "integer", "character_maximum_length": null, "column_default": "0", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> tự hiển thị của quy tắc định tuyến trong giao diện"}, {"table_name": "tenant_chat_session_assignments", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": "ID duy nhất của phân công"}, {"table_name": "tenant_chat_session_assignments", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của tenant"}, {"table_name": "tenant_chat_session_assignments", "column_name": "chat_session_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID c<PERSON>a phiên chat"}, {"table_name": "tenant_chat_session_assignments", "column_name": "assigned_user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của nhân viên đ<PERSON><PERSON> gán"}, {"table_name": "tenant_chat_session_assignments", "column_name": "assignment_status", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "NO", "column_comment": "Trạng thái phân công: assigned, accepted, transferred, closed"}, {"table_name": "tenant_chat_session_assignments", "column_name": "assigned_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON>h<PERSON><PERSON> điểm gán phiên chat"}, {"table_name": "tenant_chat_session_assignments", "column_name": "accepted_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "Th<PERSON><PERSON> điểm nhân viên tiếp nhận phiên chat"}, {"table_name": "tenant_chat_session_assignments", "column_name": "closed_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON>h<PERSON><PERSON> điểm kết thúc phiên chat"}, {"table_name": "tenant_chat_session_assignments", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm tạo bản ghi"}, {"table_name": "tenant_chat_session_assignments", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm cập nhật gần nhất"}, {"table_name": "tenant_chat_sessions", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "uuid_generate_v4()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_chat_sessions", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_chat_sessions", "column_name": "guest_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_chat_sessions", "column_name": "status", "data_type": "character varying", "character_maximum_length": 50, "column_default": "'active'::character varying", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_chat_sessions", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_chat_sessions", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_chat_sessions", "column_name": "ended_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_chat_sessions", "column_name": "source_qr_code_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "QR code được sử dụng để bắt đầu phiên chat (nếu có)"}, {"table_name": "tenant_chat_sessions", "column_name": "guest_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON>n ngữ của khách trong phiên chat này"}, {"table_name": "tenant_chat_sessions", "column_name": "priority", "data_type": "character varying", "character_maximum_length": 20, "column_default": "'normal'::character varying", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> độ ưu tiên của phiên chat: low, normal, high, urgent"}, {"table_name": "tenant_chat_sessions", "column_name": "staff_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON>n ngữ của nhân viên trong phiên chat này"}, {"table_name": "tenant_chat_sessions", "column_name": "auto_translate", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "<PERSON><PERSON> xác định có tự động dịch trong phiên chat này không"}, {"table_name": "tenant_chat_sessions", "column_name": "source_type", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> nguồn bắt đầu phiên chat (qr_code, direct, guest_app, etc.)"}, {"table_name": "tenant_chat_sessions", "column_name": "source_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của nguồn bắt đầu phiên chat (tù<PERSON> thuộc vào source_type)"}, {"table_name": "tenant_chat_sessions", "column_name": "reception_point_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> nhận tin nhắn của phiên chat này"}, {"table_name": "tenant_guests", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "uuid_generate_v4()", "is_nullable": "NO", "column_comment": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h của bảng tenant_guests"}, {"table_name": "tenant_guests", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của tenant (khách sạn/resort) mà khách đang lưu trú"}, {"table_name": "tenant_guests", "column_name": "full_name", "data_type": "character varying", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> và tên đ<PERSON>y đủ của khách"}, {"table_name": "tenant_guests", "column_name": "email", "data_type": "character varying", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "Địa chỉ email của khách"}, {"table_name": "tenant_guests", "column_name": "phone", "data_type": "character varying", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> điện thoại liên hệ của khách"}, {"table_name": "tenant_guests", "column_name": "room_number", "data_type": "character varying", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "S<PERSON> phòng mà khách đang lưu trú, li<PERSON><PERSON> kết với tenant_rooms.room_number"}, {"table_name": "tenant_guests", "column_name": "check_in", "data_type": "timestamp without time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "Thời gian kh<PERSON>ch check-in"}, {"table_name": "tenant_guests", "column_name": "check_out", "data_type": "timestamp without time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON>h<PERSON><PERSON> g<PERSON> check-out, NULL nếu ch<PERSON>a check-out"}, {"table_name": "tenant_guests", "column_name": "qr_code_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của mã QR riêng của khách nếu có"}, {"table_name": "tenant_guests", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "Trạng thái của khách: TRUE nếu đang lưu trú, FALSE nếu đã check-out"}, {"table_name": "tenant_guests", "column_name": "created_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_guests", "column_name": "updated_at", "data_type": "timestamp without time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_guests", "column_name": "device_id", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "YES", "column_comment": "ID định danh thiết bị củ<PERSON> kh<PERSON>ch hàng, dùng để xác định khách gi<PERSON><PERSON> các lần sử dụng app"}, {"table_name": "tenant_guests", "column_name": "is_profile_completed", "data_type": "boolean", "character_maximum_length": null, "column_default": "false", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> dấu khách đã hoàn thành cập nhật thông tin cá nhân hay chưa"}, {"table_name": "tenant_guests", "column_name": "preferred_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> ng<PERSON> <PERSON> thích của kh<PERSON>ch"}, {"table_name": "tenant_message_reception_points", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_message_reception_points", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_message_reception_points", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_message_reception_points", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_message_reception_points", "column_name": "name", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON>n hiển thị của điểm nhận tin nhắn"}, {"table_name": "tenant_message_reception_points", "column_name": "name", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON>n hiển thị của điểm nhận tin nhắn"}, {"table_name": "tenant_message_reception_points", "column_name": "code", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON> định danh cho điểm nhận tin nhắn, dùng trong các phép dẫn chiếu"}, {"table_name": "tenant_message_reception_points", "column_name": "code", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON> định danh cho điểm nhận tin nhắn, dùng trong các phép dẫn chiếu"}, {"table_name": "tenant_message_reception_points", "column_name": "description", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> tả chi tiết về điểm nhận tin nhắn"}, {"table_name": "tenant_message_reception_points", "column_name": "description", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> tả chi tiết về điểm nhận tin nhắn"}, {"table_name": "tenant_message_reception_points", "column_name": "icon_url", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "URL biểu tư<PERSON> (nếu có) của điểm nhận tin nhắn"}, {"table_name": "tenant_message_reception_points", "column_name": "icon_url", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "URL biểu tư<PERSON> (nếu có) của điểm nhận tin nhắn"}, {"table_name": "tenant_message_reception_points", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "Tr<PERSON>ng thái hoạt động của điểm nhận tin nhắn"}, {"table_name": "tenant_message_reception_points", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "Tr<PERSON>ng thái hoạt động của điểm nhận tin nhắn"}, {"table_name": "tenant_message_reception_points", "column_name": "priority", "data_type": "integer", "character_maximum_length": null, "column_default": "1", "is_nullable": "YES", "column_comment": "<PERSON><PERSON> <PERSON>u tiên để sắp xếp các điểm nhận tin nhắn"}, {"table_name": "tenant_message_reception_points", "column_name": "priority", "data_type": "integer", "character_maximum_length": null, "column_default": "1", "is_nullable": "YES", "column_comment": "<PERSON><PERSON> <PERSON>u tiên để sắp xếp các điểm nhận tin nhắn"}, {"table_name": "tenant_message_reception_points", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_message_reception_points", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_message_reception_points", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_message_reception_points", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_message_reception_points", "column_name": "view_order", "data_type": "integer", "character_maximum_length": null, "column_default": "0", "is_nullable": "YES", "column_comment": "Thứ tự hiển thị của điểm nhận tin nhắn trong giao diện"}, {"table_name": "tenant_message_reception_points", "column_name": "view_order", "data_type": "integer", "character_maximum_length": null, "column_default": "0", "is_nullable": "YES", "column_comment": "Thứ tự hiển thị của điểm nhận tin nhắn trong giao diện"}, {"table_name": "tenant_qr_codes", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "uuid_generate_v4()", "is_nullable": "NO", "column_comment": "ID duy nhất của mã QR code"}, {"table_name": "tenant_qr_codes", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của tenant sở hữu mã QR code"}, {"table_name": "tenant_qr_codes", "column_name": "code_value", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON><PERSON> trị của mã QR, dùng để xác định khi đ<PERSON><PERSON><PERSON> quét"}, {"table_name": "tenant_qr_codes", "column_name": "description", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON>ô tả về mã QR và mục đích sử dụng"}, {"table_name": "tenant_qr_codes", "column_name": "location", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "YES", "column_comment": "Vị trí vật lý của mã QR trong khuôn viên"}, {"table_name": "tenant_qr_codes", "column_name": "status", "data_type": "character varying", "character_maximum_length": 50, "column_default": "'active'::character varying", "is_nullable": "YES", "column_comment": "Tr<PERSON><PERSON> thái hoạt động của mã QR (active, inactive)"}, {"table_name": "tenant_qr_codes", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "YES", "column_comment": "Thời điểm tạo mã QR code"}, {"table_name": "tenant_qr_codes", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "YES", "column_comment": "Thời gian cập nhật QR code gần nhất"}, {"table_name": "tenant_qr_codes", "column_name": "last_used", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "Thời gian QR code đ<PERSON><PERSON><PERSON> quét gần nhất"}, {"table_name": "tenant_qr_codes", "column_name": "code_type", "data_type": "USER-DEFINED", "character_maximum_length": null, "column_default": "'TENANT'::qr_code_type", "is_nullable": "YES", "column_comment": "Loại QR code - USER: QR code thuộc về người dùng cụ thể (mỗi user có 2 QR code), TENANT: QR code thuộc về tenant và tính vào giới hạn của tenant"}, {"table_name": "tenant_qr_codes", "column_name": "counts_against_limit", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "Indicates whether this QR code counts against the tenant QR code limit"}, {"table_name": "tenant_qr_codes", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của người dùng sở hữu QR code này. NULL nếu QR code thuộc về tenant."}, {"table_name": "tenant_qr_codes", "column_name": "qr_type", "data_type": "USER-DEFINED", "character_maximum_length": null, "column_default": "'TENANT'::qr_code_type", "is_nullable": "YES", "column_comment": "Type of QR code: TENANT (managed by tenant), USER (for specific users), TEMPORARY"}, {"table_name": "tenant_qr_codes", "column_name": "room_number", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": "Số phòng liên kết với mã QR, nếu target_type là room"}, {"table_name": "tenant_qr_codes", "column_name": "scan_count", "data_type": "integer", "character_maximum_length": null, "column_default": "0", "is_nullable": "YES", "column_comment": "<PERSON><PERSON> lượt quét mã QR này, tăng mỗi khi có người quét mã"}, {"table_name": "tenant_qr_codes", "column_name": "target_type", "data_type": "character varying", "character_maximum_length": 20, "column_default": null, "is_nullable": "YES", "column_comment": "Type of target linked to QR code (room, area, guest, general)"}, {"table_name": "tenant_qr_codes", "column_name": "target_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID of the target linked to QR code"}, {"table_name": "tenant_qr_codes", "column_name": "qr_type_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của loại QR code"}, {"table_name": "tenant_qr_codes", "column_name": "custom_action", "data_type": "jsonb", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> động tùy chỉnh khi quét QR (nếu có)"}, {"table_name": "tenant_qr_codes", "column_name": "target_department", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON>ộ phận nhận tin nhắn khi quét QR này"}, {"table_name": "tenant_qr_codes", "column_name": "last_scanned_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm quét gần nhất"}, {"table_name": "tenant_qr_codes", "column_name": "reception_point_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của điểm tiếp nhận tin nhắn liên kết với mã QR code"}, {"table_name": "tenant_qr_codes", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "Trạng thái hoạt động của mã QR code"}, {"table_name": "tenant_qr_codes", "column_name": "name", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_qr_code_scans", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": "<PERSON> duy nh<PERSON>t c<PERSON><PERSON> quét"}, {"table_name": "tenant_qr_code_scans", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của tenant"}, {"table_name": "tenant_qr_code_scans", "column_name": "qr_code_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của QR code đ<PERSON><PERSON><PERSON> quét"}, {"table_name": "tenant_qr_code_scans", "column_name": "guest_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của khách lưu trú (nếu có)"}, {"table_name": "tenant_qr_code_scans", "column_name": "session_id", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "YES", "column_comment": "ID phiên của khách vãng lai"}, {"table_name": "tenant_qr_code_scans", "column_name": "device_info", "data_type": "jsonb", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> tin về thiết bị quét, đ<PERSON><PERSON> dạng JSON"}, {"table_name": "tenant_qr_code_scans", "column_name": "ip_address", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": "Địa chỉ IP của ngư<PERSON>i quét"}, {"table_name": "tenant_qr_code_scans", "column_name": "user_agent", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "User agent c<PERSON><PERSON> trình <PERSON>/thiết bị"}, {"table_name": "tenant_qr_code_scans", "column_name": "location", "data_type": "jsonb", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> tr<PERSON> đ<PERSON><PERSON> l<PERSON> (n<PERSON><PERSON> có), <PERSON><PERSON><PERSON> dạng JSON"}, {"table_name": "tenant_qr_code_scans", "column_name": "language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> ngữ của người dùng"}, {"table_name": "tenant_qr_code_scans", "column_name": "scan_time", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm quét"}, {"table_name": "tenant_qr_code_scans", "column_name": "action_taken", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> động đ<PERSON><PERSON><PERSON> thực hiện sau khi quét"}, {"table_name": "tenant_qr_code_scans", "column_name": "metadata", "data_type": "jsonb", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> li<PERSON> b<PERSON> sung, <PERSON><PERSON><PERSON> dạng JSON"}, {"table_name": "tenant_qr_code_scans", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_qr_code_types", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": "ID duy nhất của loại QR code"}, {"table_name": "tenant_qr_code_types", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của tenant"}, {"table_name": "tenant_qr_code_types", "column_name": "name", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "NO", "column_comment": "Tên loại QR code"}, {"table_name": "tenant_qr_code_types", "column_name": "description", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "Mô tả về loại QR code"}, {"table_name": "tenant_qr_code_types", "column_name": "default_action", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON><PERSON> động mặc định khi quét: chat, info, service, feedback"}, {"table_name": "tenant_qr_code_types", "column_name": "icon_url", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "URL của biểu tượng đại di<PERSON>n"}, {"table_name": "tenant_qr_code_types", "column_name": "color_code", "data_type": "character varying", "character_maximum_length": 20, "column_default": null, "is_nullable": "YES", "column_comment": "Mã màu cho loại QR code"}, {"table_name": "tenant_qr_code_types", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm tạo bản ghi"}, {"table_name": "tenant_qr_code_types", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm cập nhật gần nhất"}, {"table_name": "tenant_qr_code_types", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_rooms", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "uuid_generate_v4()", "is_nullable": "NO", "column_comment": "ID duy nhất của phòng"}, {"table_name": "tenant_rooms", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của tenant sở hữu phòng này"}, {"table_name": "tenant_rooms", "column_name": "room_number", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "NO", "column_comment": "Số phòng hoặc ký hiệu phòng"}, {"table_name": "tenant_rooms", "column_name": "room_type", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> phòng (đơn, đôi, suite, ...)"}, {"table_name": "tenant_rooms", "column_name": "floor", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> của phòng"}, {"table_name": "tenant_rooms", "column_name": "status", "data_type": "character varying", "character_maximum_length": 50, "column_default": "'available'::character varying", "is_nullable": "YES", "column_comment": "Trạng thái phòng (available, occupied, maintenance)"}, {"table_name": "tenant_rooms", "column_name": "last_checkin", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_rooms", "column_name": "last_checkout", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_rooms", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_rooms", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "now()", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_rooms", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "Trạng thái hoạt động của phòng (true: đang hoạt động, false: không hoạt động)"}, {"table_name": "tenant_rooms", "column_name": "room_category", "data_type": "character varying", "character_maximum_length": 50, "column_default": "'Standard'::character varying", "is_nullable": "YES", "column_comment": "<PERSON><PERSON> loại phòng (Standard, Deluxe, VIP, ...)"}, {"table_name": "tenant_rooms", "column_name": "qr_code_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của mã QR gắn với phòng này"}, {"table_name": "tenant_rooms", "column_name": "image_url", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "YES", "column_comment": "URL ảnh đại diện của phòng"}, {"table_name": "tenant_rooms", "column_name": "description", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "Detailed description of the room"}, {"table_name": "tenant_rooms", "column_name": "reception_point_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của điểm nhận tin nhắn đư<PERSON><PERSON> gán cho phòng này"}, {"table_name": "tenant_staff_assignments", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": "ID duy nhất của phân công"}, {"table_name": "tenant_staff_assignments", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của tenant"}, {"table_name": "tenant_staff_assignments", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của nhân viên đ<PERSON><PERSON><PERSON> phân công"}, {"table_name": "tenant_staff_assignments", "column_name": "assignment_type", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "NO", "column_comment": "Loại phân công: area, room_type, department"}, {"table_name": "tenant_staff_assignments", "column_name": "resource_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của tài nguyên (area_id hoặc room_type_id tùy thuộc vào assignment_type)"}, {"table_name": "tenant_staff_assignments", "column_name": "department", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": "Bộ phận: reception, restaurant, spa, etc."}, {"table_name": "tenant_staff_assignments", "column_name": "priority", "data_type": "integer", "character_maximum_length": null, "column_default": "1", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> độ ưu tiên của phân công"}, {"table_name": "tenant_staff_assignments", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "Trạng thái kích ho<PERSON>t của phân công"}, {"table_name": "tenant_staff_assignments", "column_name": "working_hours", "data_type": "jsonb", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> làm vi<PERSON>c của n<PERSON> viên, dạng JSON: {start_hour: 9, end_hour: 17, days: [\"mon\", \"tue\", \"wed\", \"thu\", \"fri\"]}"}, {"table_name": "tenant_staff_assignments", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm tạo bản ghi"}, {"table_name": "tenant_staff_assignments", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm cập nhật gần nhất"}, {"table_name": "tenant_staff_assignments", "column_name": "reception_point_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> nhận tin nhắn mà nhân viên đ<PERSON><PERSON><PERSON> phân công phụ trách"}, {"table_name": "tenant_translation_cache", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_translation_cache", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_translation_cache", "column_name": "source_text", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_translation_cache", "column_name": "source_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_translation_cache", "column_name": "target_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_translation_cache", "column_name": "translated_text", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_translation_cache", "column_name": "provider", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_translation_cache", "column_name": "confidence", "data_type": "numeric", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_translation_cache", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_translation_cache", "column_name": "used_count", "data_type": "integer", "character_maximum_length": null, "column_default": "1", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_translation_cache", "column_name": "last_used_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_translation_cache", "column_name": "source_text_hash", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> trị băm của source_text dùng cho việc tìm kiếm nhanh"}, {"table_name": "tenant_translation_settings", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": "<PERSON> duy nhất của thiết lập"}, {"table_name": "tenant_translation_settings", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của tenant"}, {"table_name": "tenant_translation_settings", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của nhân viên (NULL nếu là thiết lập tenant-wide)"}, {"table_name": "tenant_translation_settings", "column_name": "guest_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID của khách lưu trú (NULL nếu không phải thiết lập cho khách cụ thể)"}, {"table_name": "tenant_translation_settings", "column_name": "session_id", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "YES", "column_comment": "ID phiên của khách vãng lai"}, {"table_name": "tenant_translation_settings", "column_name": "default_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON><PERSON> ngữ mặc định của người dùng"}, {"table_name": "tenant_translation_settings", "column_name": "auto_translate", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "T<PERSON> động dịch hay không"}, {"table_name": "tenant_translation_settings", "column_name": "target_languages", "data_type": "ARRAY", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> ngôn ngữ muốn d<PERSON> sang"}, {"table_name": "tenant_translation_settings", "column_name": "display_mode", "data_type": "character varying", "character_maximum_length": 20, "column_default": "'parallel'::character varying", "is_nullable": "YES", "column_comment": "<PERSON><PERSON> độ hiển thị: parallel, original_first, translation_first, translation_only"}, {"table_name": "tenant_translation_settings", "column_name": "preferred_provider", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> v<PERSON> dịch <PERSON> th<PERSON>ch"}, {"table_name": "tenant_translation_settings", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm tạo bản ghi"}, {"table_name": "tenant_translation_settings", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm cập nhật gần nhất"}, {"table_name": "tenant_translation_settings", "column_name": "provider", "data_type": "character varying", "character_maximum_length": 50, "column_default": "'google'::character varying", "is_nullable": "NO", "column_comment": "Nhà cung cấp dịch thuật: google, azure, aws"}, {"table_name": "tenant_translation_settings", "column_name": "api_key_encrypted", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "API key đã mã hóa của nhà cung cấp dịch thu<PERSON>t"}, {"table_name": "tenant_translation_settings", "column_name": "supported_languages", "data_type": "jsonb", "character_maximum_length": null, "column_default": "'[]'::jsonb", "is_nullable": "NO", "column_comment": "<PERSON><PERSON> s<PERSON>ch ngôn ngữ được hỗ trợ dạng JSON"}, {"table_name": "tenant_translation_settings", "column_name": "default_guest_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": "'en'::character varying", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> ngữ mặc định cho khách"}, {"table_name": "tenant_translation_settings", "column_name": "default_staff_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": "'en'::character varying", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> ngữ mặc định cho nhân viên"}, {"table_name": "tenant_translation_settings", "column_name": "auto_detect_language", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "<PERSON><PERSON> tự động phát hiện ngôn ngữ không"}, {"table_name": "tenant_translation_settings", "column_name": "translation_enabled", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_typing_status", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_typing_status", "column_name": "session_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID c<PERSON>a phiên chat"}, {"table_name": "tenant_typing_status", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của ng<PERSON><PERSON><PERSON> dù<PERSON> (guest hoặc staff)"}, {"table_name": "tenant_typing_status", "column_name": "user_type", "data_type": "character varying", "character_maximum_length": 20, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON><PERSON> dùng: guest hoặc staff"}, {"table_name": "tenant_typing_status", "column_name": "is_typing", "data_type": "boolean", "character_maximum_length": null, "column_default": "false", "is_nullable": "NO", "column_comment": "Tr<PERSON>ng thái có đang gõ tin nhắn không"}, {"table_name": "tenant_typing_status", "column_name": "last_typing_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON>h<PERSON><PERSON> điểm gõ tin nhắn gần nhất"}, {"table_name": "tenant_typing_status", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_typing_status", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_typing_status", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_users", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": "ID duy nhất của mỗi bản ghi"}, {"table_name": "tenant_users", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của tenant mà người dùng th<PERSON> về"}, {"table_name": "tenant_users", "column_name": "user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của người dùng trong bảng users"}, {"table_name": "tenant_users", "column_name": "is_primary_tenant", "data_type": "boolean", "character_maximum_length": null, "column_default": "false", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> đ<PERSON> đ<PERSON> có phải là tenant ch<PERSON>h của người dùng"}, {"table_name": "tenant_users", "column_name": "is_active", "data_type": "boolean", "character_maximum_length": null, "column_default": "true", "is_nullable": "YES", "column_comment": "Tr<PERSON>ng thái hoạt động của người dùng"}, {"table_name": "tenant_users", "column_name": "joined_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON>h<PERSON><PERSON> điểm người dùng tham gia tenant"}, {"table_name": "tenant_users", "column_name": "last_login_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON>h<PERSON><PERSON> điểm đăng nhập gần nhất"}, {"table_name": "tenant_users", "column_name": "expiry_date", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> hế<PERSON> h<PERSON> (n<PERSON><PERSON> c<PERSON>)"}, {"table_name": "tenant_users", "column_name": "permissions", "data_type": "jsonb", "character_maximum_length": null, "column_default": "'{}'::jsonb", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> quyền đ<PERSON><PERSON><PERSON> gán cho ng<PERSON>ờ<PERSON> dùng (JSON)"}, {"table_name": "tenant_users", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm tạo bản ghi"}, {"table_name": "tenant_users", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm cập nhật gần nhất"}, {"table_name": "tenant_users", "column_name": "metadata", "data_type": "jsonb", "character_maximum_length": null, "column_default": "'{}'::jsonb", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> thông tin bổ sung về người dùng (JSON)"}, {"table_name": "tenant_users", "column_name": "role", "data_type": "USER-DEFINED", "character_maximum_length": null, "column_default": "'user'::tenant_user_role", "is_nullable": "NO", "column_comment": "<PERSON><PERSON> trò người dùng: admin, manager, user"}, {"table_name": "tenant_users_details", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": "<PERSON> duy nh<PERSON>t c<PERSON>a bản <PERSON>hi"}, {"table_name": "tenant_users_details", "column_name": "tenant_user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID của tenant_user li<PERSON><PERSON> k<PERSON>t"}, {"table_name": "tenant_users_details", "column_name": "email", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON>ail của ng<PERSON>ời dùng"}, {"table_name": "tenant_users_details", "column_name": "display_name", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> hiển thị của người dùng"}, {"table_name": "tenant_users_details", "column_name": "avatar_url", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "URL của avatar ngư<PERSON>i dùng"}, {"table_name": "tenant_users_details", "column_name": "phone", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> điện thoại của người dùng"}, {"table_name": "tenant_users_details", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm tạo bản ghi"}, {"table_name": "tenant_users_details", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON><PERSON> điểm cập nhật gần nhất"}, {"table_name": "tenant_users_details", "column_name": "preferred_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> ng<PERSON> <PERSON>a thích của nhân viên"}, {"table_name": "tenant_users_details", "column_name": "department", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "YES", "column_comment": "Phòng ban/bộ phận mà người dùng thuộc về"}, {"table_name": "tenant_users_details", "column_name": "title", "data_type": "character varying", "character_maximum_length": 100, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> danh/vị trí của người dùng"}, {"table_name": "tenant_voice_messages", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_voice_messages", "column_name": "message_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_voice_messages", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_voice_messages", "column_name": "voice_file_url", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "URL file audio đã upload"}, {"table_name": "tenant_voice_messages", "column_name": "voice_file_size", "data_type": "integer", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_voice_messages", "column_name": "duration_seconds", "data_type": "integer", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "Thời lượng audio tính bằng giây"}, {"table_name": "tenant_voice_messages", "column_name": "mime_type", "data_type": "character varying", "character_maximum_length": 100, "column_default": "'audio/webm'::character varying", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_voice_messages", "column_name": "transcription", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> bản đư<PERSON><PERSON> chuyển đổi từ giọng nói"}, {"table_name": "tenant_voice_messages", "column_name": "transcription_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_voice_messages", "column_name": "transcription_confidence", "data_type": "numeric", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> <PERSON> cậy của transcription (0.0-1.0)"}, {"table_name": "tenant_voice_messages", "column_name": "is_transcribed", "data_type": "boolean", "character_maximum_length": null, "column_default": "false", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_voice_messages", "column_name": "transcription_provider", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_voice_messages", "column_name": "uploaded_by", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": "ID người upload (guest hoặc staff)"}, {"table_name": "tenant_voice_messages", "column_name": "processing_status", "data_type": "character varying", "character_maximum_length": 20, "column_default": "'pending'::character varying", "is_nullable": "YES", "column_comment": "Tr<PERSON>ng thái xử lý: pending, processing, completed, failed"}, {"table_name": "tenant_voice_messages", "column_name": "error_message", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_voice_messages", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_voice_messages", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_web_sessions", "column_name": "id", "data_type": "uuid", "character_maximum_length": null, "column_default": "gen_random_uuid()", "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_web_sessions", "column_name": "session_token", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "NO", "column_comment": "<PERSON><PERSON> định danh phiên duy nhất"}, {"table_name": "tenant_web_sessions", "column_name": "tenant_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_web_sessions", "column_name": "guest_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID khách hàng đã checkin (nếu có)"}, {"table_name": "tenant_web_sessions", "column_name": "temporary_user_id", "data_type": "uuid", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "ID người dùng tạm thời (từ QR scan)"}, {"table_name": "tenant_web_sessions", "column_name": "user_agent", "data_type": "text", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_web_sessions", "column_name": "ip_address", "data_type": "inet", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_web_sessions", "column_name": "device_fingerprint", "data_type": "character varying", "character_maximum_length": 255, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON><PERSON> vân tay thiết bị để nhận dạng"}, {"table_name": "tenant_web_sessions", "column_name": "browser_info", "data_type": "jsonb", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "Thông tin trình duyệt dạng JSON"}, {"table_name": "tenant_web_sessions", "column_name": "screen_resolution", "data_type": "character varying", "character_maximum_length": 20, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_web_sessions", "column_name": "timezone", "data_type": "character varying", "character_maximum_length": 50, "column_default": null, "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_web_sessions", "column_name": "preferred_language", "data_type": "character varying", "character_maximum_length": 10, "column_default": "'en'::character varying", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_web_sessions", "column_name": "is_mobile", "data_type": "boolean", "character_maximum_length": null, "column_default": "false", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_web_sessions", "column_name": "last_activity", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": "<PERSON>h<PERSON><PERSON> điểm hoạt động cuối cùng"}, {"table_name": "tenant_web_sessions", "column_name": "session_data", "data_type": "jsonb", "character_maximum_length": null, "column_default": null, "is_nullable": "YES", "column_comment": "<PERSON><PERSON> liệu phiên làm việc tùy chỉnh"}, {"table_name": "tenant_web_sessions", "column_name": "expires_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": null, "is_nullable": "NO", "column_comment": null}, {"table_name": "tenant_web_sessions", "column_name": "created_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": null}, {"table_name": "tenant_web_sessions", "column_name": "updated_at", "data_type": "timestamp with time zone", "character_maximum_length": null, "column_default": "CURRENT_TIMESTAMP", "is_nullable": "YES", "column_comment": null}]