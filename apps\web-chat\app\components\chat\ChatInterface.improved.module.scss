// Enhanced Chat Interface - CSS Modules Compatible Version
// No global CSS variables, using SCSS variables instead

// SCSS Variables for theming
$chat-primary: #f97316;
$chat-primary-dark: #ea580c;
$chat-primary-light: #fed7aa;
$chat-secondary: #6b7280;
$chat-background: #ffffff;
$chat-surface: #f8fafc;
$chat-surface-hover: #f1f5f9;
$chat-border: #e5e7eb;
$chat-border-light: #f3f4f6;
$chat-text: #111827;
$chat-text-light: #6b7280;
$chat-text-muted: #9ca3af;
$chat-success: #10b981;
$chat-success-light: #dcfce7;
$chat-error: #ef4444;
$chat-error-light: #fef2f2;
$chat-warning: #f59e0b;
$chat-warning-light: #fef3c7;
$chat-info: #3b82f6;
$chat-info-light: #dbeafe;
$chat-radius: 0.75rem;
$chat-radius-sm: 0.5rem;
$chat-radius-lg: 1rem;
$chat-radius-xl: 1.5rem;
$chat-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
$chat-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$chat-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
$chat-shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
$chat-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
$chat-transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$chat-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);

// Enhanced Chat Interface
.chatInterface {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 100vh;
  background: linear-gradient(135deg, $chat-surface 0%, $chat-background 100%);
  border-radius: $chat-radius-lg;
  overflow: hidden;
  box-shadow: $chat-shadow-xl;
  position: relative;
  
  // Glass morphism effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    z-index: 1;
  }
}

// Enhanced Error Banner
.errorBanner {
  background: $chat-error-light;
  border-left: 4px solid $chat-error;
  padding: 1rem 1.5rem;
  animation: slideDown 0.4s $chat-spring;
  position: relative;
  
  &::before {
    content: '⚠️';
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.25rem;
    animation: pulse 2s infinite;
  }
  
  .errorContent {
    margin-left: 2rem;
    color: $chat-error;
    font-weight: 500;
  }
}

// Enhanced Messages Area
.messagesArea {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  background: linear-gradient(to bottom, 
    rgba(248, 250, 252, 0.8) 0%, 
    rgba(255, 255, 255, 0.9) 50%,
    rgba(248, 250, 252, 0.8) 100%
  );
  scroll-behavior: smooth;
  position: relative;
  
  // Enhanced scrollbar
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.5);
    border-radius: 4px;
    margin: 0.5rem 0;
  }
  
  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, $chat-primary-light, $chat-primary);
    border-radius: 4px;
    transition: $chat-transition;
    
    &:hover {
      background: linear-gradient(180deg, $chat-primary, $chat-primary-dark);
    }
  }
  
  // Scroll fade effect
  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 20px;
    pointer-events: none;
    z-index: 2;
  }
  
  &::before {
    top: 0;
    background: linear-gradient(to bottom, $chat-surface, transparent);
  }
  
  &::after {
    bottom: 0;
    background: linear-gradient(to top, $chat-surface, transparent);
  }
}

// Enhanced Welcome Message
.welcomeMessage {
  text-align: center;
  padding: 3rem 1rem;
  animation: fadeInUp 0.8s $chat-spring;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, $chat-primary-light 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0.3;
    z-index: -1;
    animation: pulse 3s infinite;
  }
  
  .icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 4px 8px rgba(249, 115, 22, 0.3));
  }
  
  .title {
    font-size: 1.5rem;
    font-weight: 700;
    color: $chat-text;
    margin-bottom: 0.75rem;
    background: linear-gradient(135deg, $chat-primary, $chat-primary-dark);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .subtitle {
    color: $chat-text-light;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
  }
  
  .badges {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
  }
  
  .badge {
    padding: 0.5rem 1rem;
    border-radius: $chat-radius-xl;
    font-size: 0.875rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: $chat-transition;
    cursor: default;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: $chat-shadow-md;
    }
    
    &.connected {
      background: linear-gradient(135deg, $chat-success-light, rgba(16, 185, 129, 0.1));
      color: $chat-success;
      border-color: $chat-success;
    }
    
    &.language {
      background: linear-gradient(135deg, $chat-info-light, rgba(59, 130, 246, 0.1));
      color: $chat-info;
      border-color: $chat-info;
    }
    
    &.translation {
      background: linear-gradient(135deg, $chat-warning-light, rgba(245, 158, 11, 0.1));
      color: $chat-warning;
      border-color: $chat-warning;
    }
  }
}

// Enhanced Messages List
.messagesList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem 0;
  position: relative;
  z-index: 1;
}

// Enhanced Message Container
.messageContainer {
  display: flex;
  animation: messageSlideIn 0.4s $chat-spring;
  position: relative;
  
  &.guest {
    justify-content: flex-end;
    
    .messageBubble {
      animation-delay: 0.1s;
    }
  }
  
  &.staff {
    justify-content: flex-start;
    
    .messageBubble {
      animation-delay: 0.05s;
    }
  }
  
  // Message hover effect
  &:hover {
    .messageBubble {
      transform: translateY(-1px);
      box-shadow: $chat-shadow-md;
    }
    
    .messageTime {
      opacity: 1;
    }
  }
}

// Enhanced Message Bubble
.messageBubble {
  max-width: 22rem;
  padding: 1rem 1.25rem;
  border-radius: $chat-radius-lg;
  position: relative;
  word-wrap: break-word;
  transition: $chat-transition;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  @media (min-width: 1024px) {
    max-width: 28rem;
  }
  
  &.guest {
    background: linear-gradient(135deg, $chat-primary 0%, $chat-primary-dark 100%);
    color: white;
    border-bottom-right-radius: 0.375rem;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
    
    // Enhanced tail
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: -10px;
      width: 0;
      height: 0;
      border: 10px solid transparent;
      border-top-color: $chat-primary-dark;
      border-left-color: $chat-primary-dark;
      filter: drop-shadow(2px 2px 4px rgba(249, 115, 22, 0.2));
    }
  }
  
  &.staff {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    color: $chat-text;
    border-bottom-left-radius: 0.375rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid $chat-border-light;
    
    // Enhanced tail
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -10px;
      width: 0;
      height: 0;
      border: 10px solid transparent;
      border-top-color: rgba(248, 250, 252, 0.9);
      border-right-color: rgba(248, 250, 252, 0.9);
      filter: drop-shadow(-2px 2px 4px rgba(0, 0, 0, 0.05));
    }
  }
}

// Enhanced Message Content
.messageContent {
  font-size: 0.9rem;
  line-height: 1.5;
  position: relative;

  // Text selection styling
  &::selection {
    background: rgba(255, 255, 255, 0.3);
  }
}

// Enhanced Original Content (for translations)
.originalContent {
  font-size: 0.8rem;
  margin-top: 0.5rem;
  font-style: italic;
  opacity: 0.8;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.1);
  border-radius: $chat-radius-sm;
  border-left: 3px solid rgba(255, 255, 255, 0.3);

  &::before {
    content: '📝 ';
    opacity: 0.7;
  }
}

// Enhanced Typing Indicator
.typingIndicator {
  display: flex;
  justify-content: flex-start;
  animation: messageSlideIn 0.3s $chat-spring;

  .bubble {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    color: $chat-text;
    max-width: 20rem;
    padding: 1rem 1.25rem;
    border-radius: $chat-radius-lg;
    border-bottom-left-radius: 0.375rem;
    box-shadow: $chat-shadow;
    border: 1px solid $chat-border-light;
    backdrop-filter: blur(10px);
    position: relative;

    // Tail for typing indicator
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -10px;
      width: 0;
      height: 0;
      border: 10px solid transparent;
      border-top-color: rgba(248, 250, 252, 0.95);
      border-right-color: rgba(248, 250, 252, 0.95);
    }

    .dots {
      display: flex;
      align-items: center;
      gap: 0.375rem;

      .dot {
        width: 0.5rem;
        height: 0.5rem;
        background: $chat-primary;
        border-radius: 50%;

        &:nth-child(1) {
          animation: typingDot 1.4s infinite;
        }

        &:nth-child(2) {
          animation: typingDot 1.4s infinite 0.2s;
        }

        &:nth-child(3) {
          animation: typingDot 1.4s infinite 0.4s;
        }
      }
    }

    .text {
      font-size: 0.8rem;
      color: $chat-text-light;
      margin-left: 0.75rem;
      font-weight: 500;
    }
  }
}

// Enhanced Input Area
.inputArea {
  border-top: 1px solid $chat-border;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20px);
  position: relative;

  // Glass effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  }
}

// Enhanced Input Container
.inputContainer {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
  position: relative;
}

// Enhanced Message Input
.messageInput {
  flex: 1;
  border: 2px solid $chat-border;
  border-radius: $chat-radius-lg;
  padding: 1rem 1.25rem;
  font-size: 0.9rem;
  line-height: 1.4;
  resize: none;
  transition: $chat-transition;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  min-height: 2.5rem;
  max-height: 8rem;

  &:focus {
    outline: none;
    border-color: $chat-primary;
    box-shadow: 0 0 0 4px rgba(249, 115, 22, 0.1);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
  }

  &:disabled {
    background: rgba(249, 250, 251, 0.8);
    cursor: not-allowed;
    opacity: 0.6;
  }

  &::placeholder {
    color: $chat-text-muted;
    font-style: italic;
  }
}

// Enhanced Send Button
.sendButton {
  background: linear-gradient(135deg, $chat-primary 0%, $chat-primary-dark 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: $chat-radius-lg;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  cursor: pointer;
  transition: $chat-transition;
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
  position: relative;
  overflow: hidden;

  // Ripple effect
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: width 0.3s, height 0.3s, top 0.3s, left 0.3s;
    transform: translate(-50%, -50%);
  }

  &:active::before {
    width: 300px;
    height: 300px;
  }

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, $chat-primary-dark 0%, #dc2626 100%);
    box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4);
    transform: translateY(-2px);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(249, 115, 22, 0.3);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    background: $chat-text-muted;
  }

  .spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .text {
    font-weight: 600;
    letter-spacing: 0.025em;
  }

  .icon {
    font-size: 1.1rem;
    transition: $chat-transition;
  }

  &:hover .icon {
    transform: translateX(2px);
  }
}

// Enhanced Status Bar
.statusBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  font-size: 0.75rem;
  color: $chat-text-light;
  padding: 0.75rem 1rem;
  background: rgba(248, 250, 252, 0.5);
  border-radius: $chat-radius;
  backdrop-filter: blur(10px);
}

.statusLeft {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.connectionStatus {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-weight: 500;

  .indicator {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    position: relative;

    &.connected {
      background: $chat-success;

      &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: $chat-success;
        opacity: 0.3;
        animation: pulse 2s infinite;
      }
    }

    &.disconnected {
      background: $chat-error;
      animation: blink 1s infinite;
    }
  }

  &.connected {
    color: $chat-success;
  }

  &.disconnected {
    color: $chat-error;
  }
}

.syncStatus {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: $chat-info;
  font-weight: 500;

  &::before {
    content: '🔄';
    animation: rotate 2s linear infinite;
  }
}

// Enhanced Animations
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes typingDot {
  0%, 60%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.3);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate {
  to {
    transform: rotate(360deg);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

// Enhanced Responsive Design
@media (max-width: 768px) {
  .chatInterface {
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }

  .messagesArea {
    padding: 1rem;

    &::before,
    &::after {
      height: 15px;
    }
  }

  .welcomeMessage {
    padding: 2rem 1rem;

    .icon {
      font-size: 3rem;
    }

    .title {
      font-size: 1.25rem;
    }

    .subtitle {
      font-size: 1rem;
    }

    .badges {
      gap: 0.75rem;
    }

    .badge {
      padding: 0.375rem 0.75rem;
      font-size: 0.8rem;
    }
  }

  .messageBubble {
    max-width: 18rem;
    padding: 0.875rem 1rem;

    &.guest,
    &.staff {
      &::after {
        border-width: 8px;
      }
    }
  }

  .inputArea {
    padding: 1rem;
  }

  .inputContainer {
    gap: 0.75rem;
  }

  .messageInput {
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
  }

  .sendButton {
    padding: 0.875rem 1rem;

    .text {
      display: none;
    }

    .icon {
      font-size: 1.25rem;
    }
  }

  .statusBar {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
    padding: 0.5rem 0.75rem;
  }

  .statusLeft {
    gap: 0.75rem;
  }
}
