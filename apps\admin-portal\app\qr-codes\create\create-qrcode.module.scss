.container {
  padding: 24px;
}

.header {
  margin-bottom: 24px;
  
  .title {
    font-size: 24px;
    font-weight: 600;
    margin-top: 8px;
    margin-bottom: 0;
  }
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #4b5563;
  text-decoration: none;
  font-size: 14px;
  
  &:hover {
    color: #1f2937;
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
}

.error {
  background-color: #fee2e2;
  border-left: 4px solid #dc2626;
  padding: 12px 16px;
  margin-bottom: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #b91c1c;
  
  svg {
    flex-shrink: 0;
    color: #dc2626;
  }
}

.successContainer {
  display: flex;
  justify-content: center;
  padding: 24px 0;
}

.successContent {
  max-width: 500px;
  text-align: center;
}

.successIcon {
  margin: 0 auto 24px;
}

.successTitle {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px;
}

.successMessage {
  color: #6b7280;
  margin: 0 0 24px;
}

.qrCodePreview {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  img {
    margin-bottom: 16px;
  }
}

.qrCodeDetails {
  text-align: center;
}

.qrCodeName {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.qrCodeDescription {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 16px;
}

.qrCodeDownload {
  margin-top: 16px;
}

.downloadButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #eff6ff;
  color: #2563eb;
  border-radius: 6px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  
  &:hover {
    background-color: #dbeafe;
  }
}

.successActions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  
  button {
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .viewButton {
    background-color: #2563eb;
    color: white;
    border: none;
    
    &:hover {
      background-color: #1d4ed8;
    }
  }
  
  .createButton {
    background-color: white;
    color: #2563eb;
    border: 1px solid #2563eb;
    
    &:hover {
      background-color: #eff6ff;
    }
  }
  
  .backToListButton {
    background-color: white;
    color: #4b5563;
    border: 1px solid #d1d5db;
    
    &:hover {
      background-color: #f3f4f6;
    }
  }
}

@media (max-width: 640px) {
  .qrCodePreview {
    flex-direction: column;
    gap: 16px;
    
    img {
      max-width: 160px;
    }
  }
  
  .successActions {
    flex-direction: column;
    width: 100%;
    
    button {
      width: 100%;
    }
  }
}
