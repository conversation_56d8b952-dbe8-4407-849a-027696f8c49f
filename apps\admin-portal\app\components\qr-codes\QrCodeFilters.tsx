import React from 'react';
import styles from './QrCodeFilters.module.scss';

interface QrType {
  id: string;
  name: string;
  default_action: string;
}

interface QrCodeFiltersProps {
  types: QrType[];
  onFilterChange: (filterType: string, value: string) => void;
}

export default function QrCodeFilters({ types, onFilterChange }: QrCodeFiltersProps) {
  return (
    <div className={styles.container}>
      <div className={styles.filterGroup}>
        <label htmlFor="type-filter">Type</label>
        <select 
          id="type-filter"
          onChange={(e) => onFilterChange('type', e.target.value)}
          className={styles.select}
        >
          <option value="">All Types</option>
          {types.map((type) => (
            <option key={type.id} value={type.id}>
              {type.name}
            </option>
          ))}
        </select>
      </div>
      
      <div className={styles.filterGroup}>
        <label htmlFor="location-filter">Location</label>
        <select 
          id="location-filter"
          onChange={(e) => onFilterChange('location', e.target.value)}
          className={styles.select}
        >
          <option value="">All Locations</option>
          <option value="room">Rooms Only</option>
          <option value="area">Areas Only</option>
          <option value="none">No Location</option>
        </select>
      </div>
      
      <div className={styles.filterGroup}>
        <label htmlFor="status-filter">Status</label>
        <select 
          id="status-filter"
          onChange={(e) => onFilterChange('status', e.target.value)}
          className={styles.select}
        >
          <option value="">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>
    </div>
  );
}
