<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Authentication API Test</title>
  <style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    .card { border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin-bottom: 20px; }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; }
    input { width: 100%; padding: 8px; box-sizing: border-box; }
    button { padding: 10px; background: #4CAF50; color: white; border: none; cursor: pointer; }
    button:hover { background: #45a049; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto; }
    .error { color: red; }
    .success { color: green; }
  </style>
</head>
<body>
  <h1>Authentication API Test</h1>
  
  <div class="card">
    <h2>1. Register</h2>
    <div class="form-group">
      <label>Email:</label>
      <input type="email" id="registerEmail" />
    </div>
    <div class="form-group">
      <label>Password:</label>
      <input type="password" id="registerPassword" />
    </div>
    <div class="form-group">
      <label>Full Name:</label>
      <input type="text" id="registerName" />
    </div>
    <button id="registerBtn">Register</button>
    <div id="registerResult"></div>
  </div>
  
  <div class="card">
    <h2>2. Login</h2>
    <div class="form-group">
      <label>Email:</label>
      <input type="email" id="loginEmail" />
    </div>
    <div class="form-group">
      <label>Password:</label>
      <input type="password" id="loginPassword" />
    </div>
    <button id="loginBtn">Login</button>
    <div id="loginResult"></div>
  </div>
  
  <div class="card">
    <h2>3. Get Profile</h2>
    <button id="profileBtn">Get Profile</button>
    <div id="profileResult"></div>
  </div>
  
  <div class="card">
    <h2>4. Create Temporary User with QR</h2>
    <div class="form-group">
      <label>Language:</label>
      <input type="text" id="tempLang" value="en" />
    </div>
    <button id="createTempUserBtn">Create Temporary User</button>
    <div id="qrCodeResult"></div>
    <div id="tempUserResult"></div>
  </div>

  <script>
    const API_URL = 'http://localhost:3001/api';
    let accessToken = localStorage.getItem('accessToken') || '';
    let refreshToken = localStorage.getItem('refreshToken') || '';
    
    // Register form
    document.getElementById('registerBtn').addEventListener('click', async () => {
      const email = document.getElementById('registerEmail').value;
      const password = document.getElementById('registerPassword').value;
      const fullName = document.getElementById('registerName').value;
      
      try {
        const response = await fetch(`${API_URL}/auth/register`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, password, full_name: fullName })
        });
        
        const data = await response.json();
        
        const resultDiv = document.getElementById('registerResult');
        if (data.success) {
          resultDiv.innerHTML = `
            <p class="success">Registration successful!</p>
            <pre>${JSON.stringify(data.data, null, 2)}</pre>
          `;
        } else {
          resultDiv.innerHTML = `<p class="error">Error: ${data.message}</p>`;
        }
      } catch (error) {
        document.getElementById('registerResult').innerHTML = `<p class="error">Error: ${error.message}</p>`;
      }
    });
    
    // Login form
    document.getElementById('loginBtn').addEventListener('click', async () => {
      const email = document.getElementById('loginEmail').value;
      const password = document.getElementById('loginPassword').value;
      
      try {
        const response = await fetch(`${API_URL}/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, password })
        });
        
        const data = await response.json();
        
        const resultDiv = document.getElementById('loginResult');
        if (data.success) {
          accessToken = data.data.access_token;
          refreshToken = data.data.refresh_token;
          
          // Store tokens
          localStorage.setItem('accessToken', accessToken);
          localStorage.setItem('refreshToken', refreshToken);
          
          resultDiv.innerHTML = `
            <p class="success">Login successful!</p>
            <pre>${JSON.stringify({ 
              access_token: data.data.access_token.substring(0, 20) + '...',
              refresh_token: data.data.refresh_token.substring(0, 20) + '...',
              expires_in: data.data.expires_in
            }, null, 2)}</pre>
          `;
        } else {
          resultDiv.innerHTML = `<p class="error">Error: ${data.message}</p>`;
        }
      } catch (error) {
        document.getElementById('loginResult').innerHTML = `<p class="error">Error: ${error.message}</p>`;
      }
    });
    
    // Get profile
    document.getElementById('profileBtn').addEventListener('click', async () => {
      if (!accessToken) {
        document.getElementById('profileResult').innerHTML = `<p class="error">Error: Please login first</p>`;
        return;
      }
      
      try {
        const response = await fetch(`${API_URL}/auth/profile`, {
          method: 'GET',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
          }
        });
        
        const data = await response.json();
        
        const resultDiv = document.getElementById('profileResult');
        if (data.success) {
          resultDiv.innerHTML = `
            <p class="success">Profile retrieved!</p>
            <pre>${JSON.stringify(data.data, null, 2)}</pre>
          `;
        } else {
          resultDiv.innerHTML = `<p class="error">Error: ${data.message}</p>`;
        }
      } catch (error) {
        document.getElementById('profileResult').innerHTML = `<p class="error">Error: ${error.message}</p>`;
      }
    });
    
    // Create temporary user
document.getElementById('createTempUserBtn').addEventListener('click', async () => {
  if (!accessToken) {
    document.getElementById('tempUserResult').innerHTML = `<p class="error">Error: Please login first</p>`;
    return;
  }
  
  const lang = document.getElementById('tempLang').value || 'en';
  
  try {
    const response = await fetch(`${API_URL}/temporary-users`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        preferred_language: lang,
        room_number: '101',
        metadata: { source: 'test_html' }
      })
    });
    
    const data = await response.json();
    
    const resultDiv = document.getElementById('tempUserResult');
    const qrDiv = document.getElementById('qrCodeResult');
    
    if (data.success) {
      resultDiv.innerHTML = `
        <p class="success">Temporary user created!</p>
        <pre>${JSON.stringify(data.data.temporary_user, null, 2)}</pre>
      `;
      
      // Display QR code
      qrDiv.innerHTML = `<img src="${data.data.qr_code_url}" alt="QR Code" style="max-width: 300px;" />`;
    } else {
      // Check if it's a permission issue
      if (response.status === 403) {
        resultDiv.innerHTML = `
          <p class="error">Error: ${data.message}</p>
          <p>You likely don't have admin or staff role. Please update your role in the database.</p>
        `;
      } else {
        resultDiv.innerHTML = `<p class="error">Error: ${data.message}</p>`;
      }
    }
  } catch (error) {
    document.getElementById('tempUserResult').innerHTML = `<p class="error">Error: ${error.message}</p>`;
  }
});

  </script>
</body>
</html>
