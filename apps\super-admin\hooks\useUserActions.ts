import { useState } from 'react';
import toast from 'react-hot-toast';
import { User } from '@/services/UserService';
export function useUserActions() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
const resetPassword = async (id: string, email: string): Promise<boolean> => {
  setIsLoading(true);
  setError(null);
  
  const promise = fetch(`/api/users/${id}/reset-password`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email }),
  }).then(async (response) => {
    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.error || 'Failed to reset password');
    }
    
    return true;
  });
  
  return toast.promise(
    promise,
    {
      loading: 'Sending password reset link...',
      success: 'Password reset link sent successfully',
      error: (err) => `Error: ${err.message}`,
    }
  ).catch((err) => {
    setError(err.message || 'An error occurred');
    return false;
  }).finally(() => {
    setIsLoading(false);
  });
};

  // Tạo user mới
  const createUser = async (userData: Partial<User>): Promise<User | null> => {
  setIsLoading(true);
  setError(null);
  
  const promise = fetch('/api/users', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(userData),
  }).then(async (response) => {
    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.error || 'Failed to create user');
    }
    
    const { data } = await response.json();
    return data;
  });
  
  return toast.promise(
    promise,
    {
      loading: 'Creating user...',
      success: 'User created successfully',
      error: (err) => `Error: ${err.message}`,
    }
  ).catch((err) => {
    setError(err.message || 'An error occurred');
    return null;
  }).finally(() => {
    setIsLoading(false);
  });
};
  
  // Cập nhật user
  const updateUser = async (id: string, userData: Partial<User>): Promise<User | null> => {
  setIsLoading(true);
  setError(null);
  
  const promise = fetch(`/api/users/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(userData),
  }).then(async (response) => {
    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.error || 'Failed to update user');
    }
    
    const { data } = await response.json();
    return data;
  });
  
  return toast.promise(
    promise,
    {
      loading: 'Updating user...',
      success: 'User updated successfully',
      error: (err) => `Error: ${err.message}`,
    }
  ).catch((err) => {
    setError(err.message || 'An error occurred');
    return null;
  }).finally(() => {
    setIsLoading(false);
  });
};
  
  // Vô hiệu hóa user
 const deactivateUser = async (id: string): Promise<boolean> => {
  setIsLoading(true);
  setError(null);
  
  const promise = fetch(`/api/users/${id}`, {
    method: 'DELETE',
  }).then(async (response) => {
    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.error || 'Failed to deactivate user');
    }
    
    return true;
  });
  
  return toast.promise(
    promise,
    {
      loading: 'Deactivating user...',
      success: 'User deactivated successfully',
      error: (err) => `Error: ${err.message}`,
    }
  ).catch((err) => {
    setError(err.message || 'An error occurred');
    return false;
  }).finally(() => {
    setIsLoading(false);
  });
};
  // Kích hoạt user
  const activateUser = async (id: string): Promise<boolean> => {
  setIsLoading(true);
  setError(null);
  
  const promise = fetch(`/api/users/${id}/activate`, {
    method: 'POST',
  }).then(async (response) => {
    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.error || 'Failed to activate user');
    }
    
    return true;
  });
  
  return toast.promise(
    promise,
    {
      loading: 'Activating user...',
      success: 'User activated successfully',
      error: (err) => `Error: ${err.message}`,
    }
  ).catch((err) => {
    setError(err.message || 'An error occurred');
    return false;
  }).finally(() => {
    setIsLoading(false);
  });
};
  
  return {
    createUser,
    updateUser,
    deactivateUser,
    activateUser,
    isLoading,
    error,
  };
}
