import React, { ReactNode } from 'react';

export interface CardProps {
  /**
   * Card content
   */
  children: ReactNode;
  /**
   * Card variant
   */
  variant?: 'basic' | 'highlight';
  /**
   * Optional heading/title for the card
   */
  title?: string;
  /**
   * Optional footer content
   */
  footer?: ReactNode;
  /**
   * Optional click handler
   */
  onClick?: () => void;
  /**
   * Width of the card (default: 100%)
   */
  width?: string;
  /**
   * Additional CSS properties
   */
  style?: React.CSSProperties;
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'basic',
  title,
  footer,
  onClick,
  width = '100%',
  style,
  ...props
}) => {
  // Theme colors based on the design
  const colors = {
    basic: {
      background: '#FFFFFF',
      border: '#EBEBEB',
      title: '#010103',
      text: '#464646',
    },
    highlight: {
      background: '#FFFFFF',
      border: '#FF4D00',
      title: '#010103',
      text: '#464646',
    },
  };

  // Base styles
  const cardStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    borderRadius: '8px',
    backgroundColor: colors[variant].background,
    border: `1px solid ${colors[variant].border}`,
    boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
    overflow: 'hidden',
    width,
    ...style,
  };

  const headerStyle: React.CSSProperties = {
    padding: '16px',
    borderBottom: title ? `1px solid ${colors[variant].border}` : 'none',
  };

  const bodyStyle: React.CSSProperties = {
    padding: '16px',
    flex: 1,
  };

  const footerStyle: React.CSSProperties = {
    padding: '16px',
    borderTop: footer ? `1px solid ${colors[variant].border}` : 'none',
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '8px',
  };

  const titleStyle: React.CSSProperties = {
    margin: 0,
    fontSize: '18px',
    fontWeight: 600,
    color: colors[variant].title,
  };

  // Highlight style
  if (variant === 'highlight') {
    cardStyle.borderTop = `4px solid ${colors.highlight.border}`;
  }

  return (
    <div 
      style={cardStyle} 
      onClick={onClick} 
      role={onClick ? "button" : undefined}
      tabIndex={onClick ? 0 : undefined}
      {...props}
    >
      {title && (
        <div style={headerStyle}>
          <h3 style={titleStyle}>{title}</h3>
        </div>
      )}
      <div style={bodyStyle}>
        {children}
      </div>
      {footer && (
        <div style={footerStyle}>
          {footer}
        </div>
      )}
    </div>
  );
};

export default Card;
