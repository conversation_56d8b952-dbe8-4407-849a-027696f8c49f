@import '../../../styles/variables';

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px 12px;
  overflow-y: auto;
  background-color: var(--color-background, white);
}

.messageGroup {
  margin-bottom: 16px;
}

.dateHeader {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16px 0;
  
  span {
    background-color: var(--color-gray-100, #E1E1E1);
    color: var(--color-dark, #464646);
    font-size: 0.75rem;
    padding: 4px 12px;
    border-radius: 12px;
  }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-gray, #7D8491);
  font-size: 0.875rem;
  text-align: center;
  padding: 2rem;
}
