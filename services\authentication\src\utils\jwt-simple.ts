import jwt from 'jsonwebtoken';

// Sử dụng một secret key c<PERSON> định cho kiểm thử
const SECRET_KEY = 'test_secret_key_for_jwt';

export interface TokenPayload {
  userId: string;
  email: string;
  roles: string[];
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

// Hàm đơn giản để tạo token
export function createToken(payload: any): string {
  return jwt.sign(payload, SECRET_KEY);
}

// Hàm đơn giản để xác thực token
export function verifyToken(token: string): any {
  try {
    return jwt.verify(token, SECRET_KEY);
  } catch (error) {
    return null;
  }
}

// Hàm test
export function testJWT(): boolean {
  const testPayload = {
    userId: '123',
    email: '<EMAIL>',
    roles: ['user']
  };
  
  try {
    // Tạo token
    const token = createToken(testPayload);
    console.log('Token đã tạo:', token);
    
    // <PERSON><PERSON><PERSON> thực token
    const decoded = verifyToken(token);
    console.log('Nội dung đã giải mã:', decoded);
    
    return true;
  } catch (error) {
    console.error('Lỗi khi test JWT:', error);
    return false;
  }
}
