<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Test for LoaLoa</title>
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- QR Code Generation Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
    <!-- QR Code Scanner Library -->
    <script src="https://cdn.jsdelivr.net/npm/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
    <!-- UUID Generation -->
    <script src="https://cdn.jsdelivr.net/npm/uuid@9.0.0/dist/index.min.js"></script>
    
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        
        h1, h2, h3 {
            color: #2c3e50;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .tab-btn {
            padding: 10px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: #7f8c8d;
            position: relative;
            transition: all 0.3s;
        }
        
        .tab-btn.active {
            color: #3498db;
        }
        
        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: #3498db;
        }
        
        .tab-panel {
            display: none;
        }
        
        .tab-panel.active {
            display: block;
            animation: fadeIn 0.5s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background-color: #3498db;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn-secondary {
            background-color: #95a5a6;
        }
        
        .btn:hover {
            background-color: #2980b9;
        }
        
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
        }
        
        .status-pending {
            background-color: #ffeaa7;
            color: #d35400;
        }
        
        .status-success {
            background-color: #55efc4;
            color: #27ae60;
        }
        
        .status-error {
            background-color: #ff7675;
            color: #c0392b;
        }
        
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .qr-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }
        
        #qrcode {
            margin-bottom: 20px;
        }
        
        .qr-url {
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 14px;
        }
        
        .qr-info {
            margin-top: 10px;
            color: #7f8c8d;
            font-size: 14px;
        }
        
        #qr-reader {
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px;
            border-radius: 5px;
            color: white;
            z-index: 1000;
            animation: slideIn 0.3s, fadeOut 0.5s 2.5s;
            animation-fill-mode: forwards;
            max-width: 300px;
        }
        
        .notification-success {
            background-color: #2ecc71;
        }
        
        .notification-error {
            background-color: #e74c3c;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
        
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        
        .debug-console {
            margin-top: 20px;
            padding: 15px;
            background-color: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .debug-line {
            margin-bottom: 5px;
            word-wrap: break-word;
        }
        
        .debug-error {
            color: #ff7675;
        }
        
        .debug-success {
            color: #55efc4;
        }
        
        .debug-info {
            color: #74b9ff;
        }
        
        .section-title {
            border-left: 4px solid #3498db;
            padding-left: 10px;
            margin-top: 30px;
        }
        
        .admin-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
        
        .qr-reader-result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        .hidden {
            display: none;
        }
        
        .qr-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .qr-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .qr-card-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .qr-card-body {
            padding: 15px;
            text-align: center;
        }
        
        .qr-card-footer {
            padding: 15px;
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
        }
        
        .connection-status {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .connected {
            background-color: #2ecc71;
        }
        
        .disconnected {
            background-color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LoaLoa QR Code Test</h1>
        
        <!-- Supabase Connection Card -->
        <div class="card">
            <div class="card-header">
                <h3>Supabase Connection</h3>
                <span class="status status-pending" id="connection-status">Pending</span>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label for="supabase-url">Supabase URL</label>
                    <input type="text" id="supabase-url" value="https://iwzwbrbmojvvvfstbqow.supabase.co">
                </div>
                <div class="form-group">
                    <label for="supabase-key">Supabase Anon Key</label>
                    <input type="text" id="supabase-key" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3endicmJtb2p2dnZmc3RicW93Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMjkyNjEsImV4cCI6MjA2MTkwNTI2MX0.tyVtaSclUKC5fGh7I7Ohpm7c4FniXphYe34-cxBvo6E">
                </div>
                <div class="form-group">
                    <label for="service-role-key">Service Role Key (for admin operations)</label>
                    <input type="password" id="service-role-key" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3endicmJtb2p2dnZmc3RicW93Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjMyOTI2MSwiZXhwIjoyMDYxOTA1MjYxfQ.qMq8C34LescZuPeuSxredqdsjxsK6YBmkEDKsvzV7mQ">
                </div>
                <button id="connect-btn" class="btn">Connect to Supabase</button>
            </div>
        </div>
        
        <!-- Authentication Card -->
        <div class="card">
            <div class="card-header">
                <h3>Authentication</h3>
                <span class="status status-pending" id="auth-status">Not Logged In</span>
            </div>
            <div class="card-body">
                <div id="login-form">
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" placeholder="your password">
                    </div>
                    <button id="login-btn" class="btn">Login</button>
                    <button id="signup-btn" class="btn btn-secondary">Sign Up</button>
                </div>
                <div id="user-info" class="hidden">
                    <p><strong>User ID:</strong> <span id="user-id"></span></p>
                    <p><strong>Email:</strong> <span id="user-email"></span></p>
                    <button id="logout-btn" class="btn btn-secondary">Logout</button>
                </div>
            </div>
        </div>
        
        <!-- Main Content Tabs -->
        <div class="tabs">
            <button class="tab-btn active" data-tab="personal-qr">Personal QR</button>
            <button class="tab-btn" data-tab="scan-qr">Scan QR</button>
            <button class="tab-btn" data-tab="admin">Admin Tools</button>
            <button class="tab-btn" data-tab="debug">Debug Console</button>
        </div>
        
        <!-- Personal QR Panel -->
        <div class="tab-panel active" id="personal-qr-panel">
            <h2 class="section-title">Your Personal QR Code</h2>
            <p>This QR code can be scanned by other users to start a chat with you.</p>
            
            <div id="personal-qr-container" class="qr-container">
                <div class="loading">Connect to Supabase and login to view your QR code</div>
            </div>
            
            <button id="generate-personal-qr" class="btn" disabled>Generate Personal QR</button>
        </div>
        
        <!-- Scan QR Panel -->
        <div class="tab-panel" id="scan-qr-panel">
            <h2 class="section-title">Scan QR Code</h2>
            <p>Use your camera to scan a QR code from another user or location.</p>
            
            <div id="qr-reader"></div>
            <div id="qr-reader-result" class="qr-reader-result hidden">
                <h3>Scan Result</h3>
                <div id="result-content"></div>
            </div>
            
            <button id="start-scanner" class="btn">Start Scanner</button>
            <button id="stop-scanner" class="btn btn-secondary" disabled>Stop Scanner</button>
        </div>
        
        <!-- Admin Panel -->
        <div class="tab-panel" id="admin-panel">
            <h2 class="section-title">Admin Tools</h2>
            <p>Create and manage QR codes for locations.</p>
            
            <div id="admin-auth-check" class="admin-section">
                <h3>Admin Authentication</h3>
                <p>You need admin privileges to access these tools.</p>
                <div id="admin-status">Checking admin status...</div>
            </div>
            
            <div id="admin-tools" class="hidden">
                <!-- Location Selection -->
                <div class="form-group">
                    <label for="location-select">Select Location</label>
                    <select id="location-select">
                        <option value="">-- Select a location --</option>
                    </select>
                </div>
                
                <!-- Create QR Code Form -->
                <div class="card">
                    <div class="card-header">
                        <h3>Create Location QR Code</h3>
                    </div>
                    <div class="card-body">
                        <form id="create-qr-form">
                            <div class="form-group">
                                <label for="qr-title">QR Title</label>
                                <input type="text" id="qr-title" placeholder="e.g., Reception Support">
                            </div>
                            <div class="form-group">
                                <label for="qr-description">Description</label>
                                <textarea id="qr-description" placeholder="e.g., Scan this code to chat with reception"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="qr-expire">Expiration Date (Optional)</label>
                                <input type="date" id="qr-expire">
                            </div>
                            <div class="form-group">
                                <label for="qr-limit">Usage Limit (Optional)</label>
                                <input type="number" id="qr-limit" min="1">
                            </div>
                            <button type="submit" class="btn">Create QR Code</button>
                        </form>
                    </div>
                </div>
                
                <!-- Location QR Codes -->
                <h3 class="section-title">Location QR Codes</h3>
                <div id="location-qr-grid" class="qr-grid">
                    <div class="loading">Select a location to view QR codes</div>
                </div>
            </div>
        </div>
        
        <!-- Debug Panel -->
        <div class="tab-panel" id="debug-panel">
            <h2 class="section-title">Debug Console</h2>
            <div class="debug-console" id="debug-console">
                <div class="debug-line debug-info">Debug console initialized</div>
            </div>
            <button id="clear-debug" class="btn btn-secondary">Clear Console</button>
        </div>
    </div>

    <script>
        // Global variables
        let supabaseClient = null;
        let serviceClient = null;
        let html5QrScanner = null;
        
        // DOM References
        const connectionStatus = document.getElementById('connection-status');
        const authStatus = document.getElementById('auth-status');
        const debugConsole = document.getElementById('debug-console');
        
        // Debug logging function
        function logDebug(message, type = 'info') {
            const line = document.createElement('div');
            line.className = `debug-line debug-${type}`;
            const timestamp = new Date().toLocaleTimeString();
            line.textContent = `[${timestamp}] ${message}`;
            debugConsole.appendChild(line);
            debugConsole.scrollTop = debugConsole.scrollHeight;
        }
        
        // Show notification
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            // Remove notification after 3 seconds
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
        
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all tabs
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                
                // Hide all panels
                document.querySelectorAll('.tab-panel').forEach(panel => {
                    panel.classList.remove('active');
                });
                
                // Add active class to clicked tab
                button.classList.add('active');
                
                // Show corresponding panel
                const tabId = button.getAttribute('data-tab');
                document.getElementById(`${tabId}-panel`).classList.add('active');
                
                // Special handling for scanner tab
                if (tabId === 'scan-qr' && html5QrScanner) {
                    // Resume scanner if it was already initialized
                    document.getElementById('start-scanner').click();
                }
            });
        });
        
        // Connect to Supabase
        document.getElementById('connect-btn').addEventListener('click', async () => {
            const supabaseUrl = document.getElementById('supabase-url').value;
            const supabaseKey = document.getElementById('supabase-key').value;
            const serviceRoleKey = document.getElementById('service-role-key').value;
            
            if (!supabaseUrl || !supabaseKey) {
                showNotification('Please enter Supabase URL and key', 'error');
                return;
            }
            
            try {
                logDebug(`Connecting to Supabase at ${supabaseUrl}...`);
                
                // Initialize Supabase client
                supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
                
                // If service role key is provided, create admin client
                if (serviceRoleKey) {
                    serviceClient = supabase.createClient(supabaseUrl, serviceRoleKey);
                    logDebug('Service role client initialized', 'success');
                }
                
                // Test connection
                const { data: testData, error: testError } = await supabaseClient
                    .from('qr_codes')
                    .select('*', { count: 'exact', head: true });
                    
                if (testError && testError.code !== 'PGRST116') {
                    // PGRST116 means no rows found, which is fine for a connection test
                    throw testError;
                }
                
                // Update connection status
                connectionStatus.className = 'status status-success';
                connectionStatus.textContent = 'Connected';
                
                // Enable personal QR button
                document.getElementById('generate-personal-qr').disabled = false;
                
                // Check if user is already logged in
                const { data: { session }, error: sessionError } = await supabaseClient.auth.getSession();
                if (session) {
                    handleLoggedInUser(session.user);
                }
                
                showNotification('Successfully connected to Supabase');
                logDebug('Supabase connection successful', 'success');
                
                // Initialize admin section
                checkAdminStatus();
                
            } catch (error) {
                connectionStatus.className = 'status status-error';
                connectionStatus.textContent = 'Error';
                
                showNotification(`Connection failed: ${error.message}`, 'error');
                logDebug(`Supabase connection failed: ${error.message}`, 'error');
            }
        });
        
        // Authentication: Login
        document.getElementById('login-btn').addEventListener('click', async () => {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showNotification('Please enter email and password', 'error');
                return;
            }
            
            try {
                logDebug(`Attempting to login with email: ${email}`);
                
                const { data, error } = await supabaseClient.auth.signInWithPassword({
                    email,
                    password
                });
                
                if (error) throw error;
                
                logDebug('Login successful', 'success');
                showNotification('Login successful');
                
                // Handle logged in user
                handleLoggedInUser(data.user);
                
            } catch (error) {
                logDebug(`Login failed: ${error.message}`, 'error');
                showNotification(`Login failed: ${error.message}`, 'error');
            }
        });
        
        // Authentication: Sign Up
        document.getElementById('signup-btn').addEventListener('click', async () => {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showNotification('Please enter email and password', 'error');
                return;
            }
            
            try {
                logDebug(`Attempting to sign up with email: ${email}`);
                
                const { data, error } = await supabaseClient.auth.signUp({
                    email,
                    password
                });
                
                if (error) throw error;
                
                logDebug('Sign up successful', 'success');
                showNotification('Sign up successful. Check your email for confirmation.');
                
            } catch (error) {
                logDebug(`Sign up failed: ${error.message}`, 'error');
                showNotification(`Sign up failed: ${error.message}`, 'error');
            }
        });
        
        // Authentication: Logout
        document.getElementById('logout-btn').addEventListener('click', async () => {
            try {
                await supabaseClient.auth.signOut();
                
                // Update UI
                document.getElementById('login-form').classList.remove('hidden');
                document.getElementById('user-info').classList.add('hidden');
                authStatus.className = 'status status-pending';
                authStatus.textContent = 'Not Logged In';
                
                // Clear personal QR code
                document.getElementById('personal-qr-container').innerHTML = 
                    '<div class="loading">Login to view your QR code</div>';
                
                logDebug('Logged out successfully', 'success');
                showNotification('Logged out successfully');
                
            } catch (error) {
                logDebug(`Logout failed: ${error.message}`, 'error');
                showNotification(`Logout failed: ${error.message}`, 'error');
            }
        });
        
        // Handle logged in user
        function handleLoggedInUser(user) {
            // Update auth status
            authStatus.className = 'status status-success';
            authStatus.textContent = 'Logged In';
            
            // Update user info
            document.getElementById('user-id').textContent = user.id;
            document.getElementById('user-email').textContent = user.email;
            
            // Show user info, hide login form
            document.getElementById('login-form').classList.add('hidden');
            document.getElementById('user-info').classList.remove('hidden');
            
            // Generate personal QR code if we're on that tab
            if (document.getElementById('personal-qr-panel').classList.contains('active')) {
                generatePersonalQR(user.id);
            }
            
            // Check admin status
            checkAdminStatus();
        }
        
        // Generate Personal QR Code
        document.getElementById('generate-personal-qr').addEventListener('click', async () => {
            try {
                const { data: { session } } = await supabaseClient.auth.getSession();
                if (!session) {
                    showNotification('Please login first', 'error');
                    return;
                }
                
                generatePersonalQR(session.user.id);
            } catch (error) {
                logDebug(`Error generating QR: ${error.message}`, 'error');
                showNotification(`Error: ${error.message}`, 'error');
            }
        });
        
        // Function to generate personal QR code
        async function generatePersonalQR(userId) {
            const container = document.getElementById('personal-qr-container');
            container.innerHTML = '<div class="loading">Generating your QR code...</div>';
            
            try {
                logDebug('Checking for existing personal QR code');
                
                // Check if user already has a QR code
                const { data: existingQR, error: qrError } = await supabaseClient
                    .from('qr_codes')
                    .select('*')
                    .eq('qr_type', 'user')
                    .eq('reference_id', userId)
                    .maybeSingle();
                
                let qrCode;
                
                // If no QR code exists, create one
                if (!existingQR && (!qrError || qrError.code === 'PGRST116')) {
                    logDebug('No existing QR code found, creating new one');
                    
                    // Generate a short code
                    const shortCode = 'u_' + userId.substring(0, 8);
                    
                    // Create new QR code
                    const { data: newQR, error: createError } = await supabaseClient
                        .from('qr_codes')
                        .insert([{
                            qr_type: 'user',
                            title: 'Personal QR Code',
                            description: 'Scan to start a chat with me',
                            owner_id: userId,
                            reference_id: userId,
                            short_code: shortCode,
                            is_dynamic: true,
                            access_type: 'public',
                            usage_count: 0
                        }])
                        .select()
                        .single();
                    
                    if (createError) throw createError;
                    qrCode = newQR;
                    
                    logDebug('Created new personal QR code', 'success');
                } else if (qrError && qrError.code !== 'PGRST116') {
                    throw qrError;
                } else {
                    qrCode = existingQR;
                    logDebug('Found existing personal QR code', 'success');
                }
                
                // Display QR code
                displayQRCode(qrCode, container);
                
            } catch (error) {
                container.innerHTML = '<div class="error">Error generating QR code</div>';
                logDebug(`Error: ${error.message}`, 'error');
                showNotification(`Error: ${error.message}`, 'error');
            }
        }
        
        // Display QR code in container
     function displayQRCode(qrCode, container) {
    // Create QR URL
    const appUrl = window.location.origin;
    const qrUrl = `${appUrl}/qr/${qrCode.short_code}`;
    
    // Clear container
    container.innerHTML = '';
    
    // Create QR code element
    const qrElement = document.createElement('div');
    qrElement.id = 'qrcode';
    container.appendChild(qrElement);
    
    // Generate QR code - sử dụng cú pháp đơn giản nhất
    try {
        // Xóa QR code cũ nếu có
        qrElement.innerHTML = '';
        
        // Tạo QR code mới
        new QRCode(qrElement, qrUrl);
        logDebug('QR code generated successfully', 'success');
    } catch (err) {
        logDebug(`QR code generation error: ${err.message}`, 'error');
        container.innerHTML = '<div class="error">Error generating QR code</div>';
    }
logDebug('QR code generated successfully', 'success');
            
            // Add URL
            const urlElement = document.createElement('div');
            urlElement.className = 'qr-url';
            urlElement.textContent = qrUrl;
            container.appendChild(urlElement);
			
 // Add download button
            const downloadButton = document.createElement('button');
            downloadButton.className = 'btn';
            downloadButton.textContent = 'Download QR Code';
            downloadButton.onclick = function() {
    try {
        // Lấy canvas từ QR code đã tạo
        const canvas = qrElement.querySelector('canvas');
        if (!canvas) {
            logDebug('Canvas element not found in QR code', 'error');
            return;
        }
        
        const dataUrl = canvas.toDataURL('image/png');
        
        const downloadLink = document.createElement('a');
        downloadLink.href = dataUrl;
        downloadLink.download = `qrcode-${qrCode.short_code}.png`;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        
        logDebug('QR code downloaded');
    } catch (err) {
        logDebug(`Error downloading QR code: ${err.message}`, 'error');
        showNotification('Error downloading QR code', 'error');
    }
};
            container.appendChild(downloadButton);
            
            // Add stats
            const statsElement = document.createElement('div');
            statsElement.className = 'qr-info';
            statsElement.textContent = `Scanned ${qrCode.usage_count || 0} times`;
            container.appendChild(statsElement);
        }
        
        // Start QR Code Scanner
        document.getElementById('start-scanner').addEventListener('click', () => {
            if (!supabaseClient) {
                showNotification('Please connect to Supabase first', 'error');
                return;
            }
            
            const qrReader = document.getElementById('qr-reader');
            
            if (html5QrScanner) {
                // Resume existing scanner
                html5QrScanner.resume();
                document.getElementById('start-scanner').disabled = true;
                document.getElementById('stop-scanner').disabled = false;
                logDebug('QR scanner resumed');
                return;
            }
            
            // Initialize scanner
            logDebug('Initializing QR scanner');
            html5QrScanner = new Html5Qrcode('qr-reader');
            
            const qrCodeSuccessCallback = async (decodedText, decodedResult) => {
                // Stop scanning
                html5QrScanner.pause();
                document.getElementById('start-scanner').disabled = false;
                document.getElementById('stop-scanner').disabled = true;
                
                logDebug(`QR code detected: ${decodedText}`);
                
                // Process QR code
                await processQRCode(decodedText);
            };
            
            const config = { 
                fps: 10,
                qrbox: 250,
                aspectRatio: 1.0
            };
            
            html5QrScanner.start(
                { facingMode: "environment" },
                config,
                qrCodeSuccessCallback
            ).catch((err) => {
                logDebug(`QR scanner error: ${err}`, 'error');
                showNotification(`Scanner error: ${err}`, 'error');
            });
            
            document.getElementById('start-scanner').disabled = true;
            document.getElementById('stop-scanner').disabled = false;
        });
        
        // Stop QR Code Scanner
        document.getElementById('stop-scanner').addEventListener('click', () => {
            if (html5QrScanner) {
                html5QrScanner.pause();
                document.getElementById('start-scanner').disabled = false;
                document.getElementById('stop-scanner').disabled = true;
                logDebug('QR scanner paused');
            }
        });
        
        // Process scanned QR code
        async function processQRCode(qrData) {
            const resultContainer = document.getElementById('qr-reader-result');
            const resultContent = document.getElementById('result-content');
            
            resultContainer.classList.remove('hidden');
            resultContent.innerHTML = '<div class="loading">Processing QR code...</div>';
            
            try {
                // Check if QR code is valid
                if (!qrData.includes('/qr/')) {
                    throw new Error('Invalid QR code format');
                }
                
                // Extract short code
                const shortCode = qrData.split('/qr/')[1];
                logDebug(`Processing QR code with short code: ${shortCode}`);
                
                // Get QR code details
                const { data: qrCode, error: qrError } = await supabaseClient
                    .from('qr_codes')
                    .select('*')
                    .eq('short_code', shortCode)
                    .single();
                
                if (qrError) {
                    throw new Error('QR code not found in database');
                }
                
                // Check if QR code is active
                if (!qrCode.is_active) {
                    throw new Error('This QR code is inactive');
                }
                
                // Check if QR code is expired
                if (qrCode.expire_at && new Date(qrCode.expire_at) < new Date()) {
                    throw new Error('This QR code has expired');
                }
                
                // Check if usage limit reached
                if (qrCode.usage_limit && qrCode.usage_count >= qrCode.usage_limit) {
                    throw new Error('Usage limit for this QR code has been reached');
                }
                
                // Check if user is logged in
                const { data: { session } } = await supabaseClient.auth.getSession();
                if (!session) {
                    resultContent.innerHTML = `
                        <div class="card">
                            <div class="card-header">
                                <h3>Login Required</h3>
                            </div>
                            <div class="card-body">
                                <p>You need to be logged in to process this QR code.</p>
                                <p>Please login and try again.</p>
                            </div>
                        </div>
                    `;
                    logDebug('Login required to process QR code', 'info');
                    return;
                }
                
                // Process based on QR type
                switch (qrCode.qr_type) {
                    case 'user':
                        await processUserQR(qrCode, session.user);
                        break;
                    
                    case 'location':
                        await processLocationQR(qrCode, session.user);
                        break;
                    
                    default:
                        throw new Error('Unknown QR code type');
                }
                
            } catch (error) {
                resultContent.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h3>Error</h3>
                        </div>
                        <div class="card-body">
                            <p>Error: ${error.message}</p>
                        </div>
                    </div>
                `;
                logDebug(`Error processing QR code: ${error.message}`, 'error');
            }
        }
        
        // Process User QR code
        async function processUserQR(qrCode, currentUser) {
            const resultContent = document.getElementById('result-content');
            
            try {
                // Get QR owner's information
                const { data: ownerData, error: ownerError } = await supabaseClient
                    .from('auth.users')
                    .select('email')
                    .eq('id', qrCode.reference_id)
                    .single();
                
                if (ownerError) {
                    throw new Error('Could not find user information');
                }
                
                // Check if trying to scan own QR code
                if (qrCode.reference_id === currentUser.id) {
                    resultContent.innerHTML = `
                        <div class="card">
                            <div class="card-header">
                                <h3>That's Your QR Code!</h3>
                            </div>
                            <div class="card-body">
                                <p>You scanned your own QR code. To chat with someone, have them scan your QR code instead.</p>
                            </div>
                        </div>
                    `;
                    logDebug('User scanned their own QR code', 'info');
                    return;
                }
                
                // Record the scan
                await recordQRScan(qrCode.id, currentUser.id);
                
                // Create chat room between users
                const { data: roomData, error: roomError } = await supabaseClient
                    .from('chat_rooms')
                    .insert([{
                        name: `Chat via QR code`,
                        room_type: 'direct',
                        created_by: currentUser.id,
                        metadata: {
                            created_via: 'qr_scan',
                            qr_code_id: qrCode.id
                        }
                    }])
                    .select()
                    .single();
                
                if (roomError) {
                    throw new Error('Could not create chat room');
                }
                
                // Add participants to the room
                const participantsPromises = [
                    // Add current user
                    supabaseClient
                        .from('chat_participants')
                        .insert([{
                            chat_room_id: roomData.id,
                            user_id: currentUser.id,
                            participant_role: 'member',
                            is_active: true
                        }]),
                    // Add QR code owner
                    supabaseClient
                        .from('chat_participants')
                        .insert([{
                            chat_room_id: roomData.id,
                            user_id: qrCode.reference_id,
                            participant_role: 'member',
                            is_active: true
                        }])
                ];
                
                await Promise.all(participantsPromises);
                
                // Show success message
                resultContent.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h3>Chat Room Created!</h3>
                        </div>
                        <div class="card-body">
                            <p>You've successfully connected with ${ownerData.email}.</p>
                            <p>A new chat room has been created. In a real app, you would now be redirected to the chat room.</p>
                            <p><strong>Room ID:</strong> ${roomData.id}</p>
                        </div>
                    </div>
                `;
                
                logDebug(`Chat room created successfully with ID: ${roomData.id}`, 'success');
                showNotification('Chat room created successfully');
                
            } catch (error) {
                throw new Error(`Failed to process user QR code: ${error.message}`);
            }
        }
        
        // Process Location QR code
        async function processLocationQR(qrCode, currentUser) {
            const resultContent = document.getElementById('result-content');
            
            try {
                // Get location information
                const { data: locationData, error: locationError } = await supabaseClient
                    .from('locations')
                    .select('name, description, location_type')
                    .eq('id', qrCode.reference_id)
                    .single();
                
                if (locationError) {
                    throw new Error('Could not find location information');
                }
                
                // Record the scan
                await recordQRScan(qrCode.id, currentUser.id);
                
                // Create support chat room
                const roomName = `${locationData.name} Support`;
                const { data: roomData, error: roomError } = await supabaseClient
                    .from('chat_rooms')
                    .insert([{
                        name: roomName,
                        room_type: 'support',
                        created_by: currentUser.id,
                        organization_id: qrCode.organization_id,
                        metadata: {
                            created_via: 'location_qr_scan',
                            qr_code_id: qrCode.id,
                            location_id: qrCode.reference_id,
                            location_name: locationData.name,
                            location_type: locationData.location_type
                        }
                    }])
                    .select()
                    .single();
                
                if (roomError) {
                    throw new Error('Could not create support chat room');
                }
                
                // Add current user as participant
                await supabaseClient
                    .from('chat_participants')
                    .insert([{
                        chat_room_id: roomData.id,
                        user_id: currentUser.id,
                        participant_role: 'member',
                        is_active: true
                    }]);
                
                // TODO: In a real app, we would add service staff as participants
                // based on their roles and responsibilities for this location
                
                // Show success message
                resultContent.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h3>Support Chat Created!</h3>
                        </div>
                        <div class="card-body">
                            <p>You've successfully connected with ${locationData.name} support.</p>
                            <p>A new support chat room has been created. In a real app, you would now be redirected to the chat room.</p>
                            <p><strong>Room ID:</strong> ${roomData.id}</p>
                        </div>
                    </div>
                `;
                
                logDebug(`Support chat room created successfully with ID: ${roomData.id}`, 'success');
                showNotification('Support chat room created successfully');
                
            } catch (error) {
                throw new Error(`Failed to process location QR code: ${error.message}`);
            }
        }
        
        // Record QR code scan
        async function recordQRScan(qrCodeId, userId) {
            try {
                // Get or generate anonymous ID
                let anonymousId = localStorage.getItem('anonymousId');
                if (!anonymousId) {
                    anonymousId = uuidv4();
                    localStorage.setItem('anonymousId', anonymousId);
                }
                
                // Record scan in qr_code_scans table
                const { error: scanError } = await supabaseClient
                    .from('qr_code_scans')
                    .insert([{
                        qr_code_id: qrCodeId,
                        scanned_by_user_id: userId,
                        scanned_by_anonymous_id: anonymousId,
                        ip_address: null, // Can't get client IP in browser
                        user_agent: navigator.userAgent,
                        device_info: {
                            platform: navigator.platform,
                            language: navigator.language,
                            userAgent: navigator.userAgent,
                            screenWidth: window.screen.width,
                            screenHeight: window.screen.height
                        }
                    }]);
                
                if (scanError) throw scanError;
                
                // Increment usage count in qr_codes table
                const { error: updateError } = await supabaseClient
                    .from('qr_codes')
                    .update({ usage_count: Number(qrCode.usage_count || 0) + 1 })
                    .eq('id', qrCodeId);
                
                if (updateError) throw updateError;
                
                logDebug('QR scan recorded successfully', 'success');
                
            } catch (error) {
                logDebug(`Failed to record QR scan: ${error.message}`, 'error');
                // Continue anyway - this shouldn't block the main functionality
            }
        }
        
        // Check if user has admin privileges
        async function checkAdminStatus() {
            if (!supabaseClient) return;
            
            const adminContainer = document.getElementById('admin-auth-check');
            const adminStatus = document.getElementById('admin-status');
            const adminTools = document.getElementById('admin-tools');
            
            try {
                // Check if user is logged in
                const { data: { session } } = await supabaseClient.auth.getSession();
                if (!session) {
                    adminStatus.innerHTML = '<div class="status status-error">Not logged in</div>';
                    return;
                }
                
                adminStatus.innerHTML = '<div class="loading">Checking admin privileges...</div>';
                
                // In a real app, we would check if the user is an admin in their organization
                // For this demo, we'll just check if they're logged in and a service client is available
                if (serviceClient) {
                    // Show admin tools
                    adminStatus.innerHTML = '<div class="status status-success">Admin access granted</div>';
                    adminTools.classList.remove('hidden');
                    
                    // Load locations
                    loadLocations();
                } else {
                    adminStatus.innerHTML = '<div class="status status-error">Not an admin or service role key not provided</div>';
                }
                
            } catch (error) {
                adminStatus.innerHTML = `<div class="status status-error">Error: ${error.message}</div>`;
                logDebug(`Admin check error: ${error.message}`, 'error');
            }
        }
        
        // Load locations for admin
        async function loadLocations() {
            if (!serviceClient) return;
            
            const locationSelect = document.getElementById('location-select');
            
            try {
                logDebug('Loading locations');
                
                const { data: locations, error } = await serviceClient
                    .from('locations')
                    .select('id, name, location_type')
                    .order('name', { ascending: true });
                
                if (error) throw error;
                
                // Clear select options
                locationSelect.innerHTML = '<option value="">-- Select a location --</option>';
                
                if (locations && locations.length > 0) {
                    locations.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.id;
                        option.textContent = `${location.name} (${location.location_type})`;
                        locationSelect.appendChild(option);
                    });
                    
                    logDebug(`Loaded ${locations.length} locations`, 'success');
                } else {
                    // If no locations exist, create a test location
                    logDebug('No locations found, creating test location');
                    
                    const { data: newLocation, error: createError } = await serviceClient
                        .from('locations')
                        .insert([{
                            name: 'Test Hotel',
                            description: 'A test location for QR code testing',
                            location_type: 'hotel',
                            is_active: true
                        }])
                        .select()
                        .single();
                    
                    if (createError) throw createError;
                    
                    // Add the new location to the select
                    const option = document.createElement('option');
                    option.value = newLocation.id;
                    option.textContent = `${newLocation.name} (${newLocation.location_type})`;
                    locationSelect.appendChild(option);
                    
                    logDebug('Created test location', 'success');
                }
                
            } catch (error) {
                logDebug(`Error loading locations: ${error.message}`, 'error');
                showNotification(`Error loading locations: ${error.message}`, 'error');
            }
        }
        
        // Location change handler
        document.getElementById('location-select').addEventListener('change', function() {
            const locationId = this.value;
            if (locationId) {
                loadLocationQRCodes(locationId);
            } else {
                document.getElementById('location-qr-grid').innerHTML = 
                    '<div class="loading">Select a location to view QR codes</div>';
            }
        });
        
        // Create QR code form submission
        document.getElementById('create-qr-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!serviceClient) {
                showNotification('Admin access required', 'error');
                return;
            }
            
            const locationId = document.getElementById('location-select').value;
            if (!locationId) {
                showNotification('Please select a location', 'error');
                return;
            }
            
            const title = document.getElementById('qr-title').value;
            const description = document.getElementById('qr-description').value;
            const expireDate = document.getElementById('qr-expire').value || null;
            const usageLimit = document.getElementById('qr-limit').value || null;
            
            if (!title) {
                showNotification('Please enter a title for the QR code', 'error');
                return;
            }
            
            try {
                logDebug(`Creating QR code for location ${locationId}`);
                
                // Get user session
                const { data: { session } } = await supabaseClient.auth.getSession();
                if (!session) {
                    throw new Error('You need to be logged in');
                }
                
                // Generate short code
                const shortCode = `l_${locationId.substring(0, 8)}`;
                
                // Create QR code
                const { data: qrCode, error } = await serviceClient
                    .from('qr_codes')
                    .insert([{
                        qr_type: 'location',
                        title: title,
                        description: description,
                        short_code: shortCode,
                        owner_id: session.user.id,
                        reference_id: locationId,
                        is_active: true,
                        is_dynamic: true,
                        access_type: 'public',
                        expire_at: expireDate,
                        usage_limit: usageLimit ? parseInt(usageLimit) : null,
                        usage_count: 0
                    }])
                    .select()
                    .single();
                
                if (error) throw error;
                
                logDebug('QR code created successfully', 'success');
                showNotification('QR code created successfully');
                
                // Reset form
                this.reset();
                
                // Reload QR codes
                loadLocationQRCodes(locationId);
                
            } catch (error) {
                logDebug(`Error creating QR code: ${error.message}`, 'error');
                showNotification(`Error: ${error.message}`, 'error');
            }
        });
        
        // Load QR codes for a location
        async function loadLocationQRCodes(locationId) {
            if (!serviceClient) return;
            
            const qrGrid = document.getElementById('location-qr-grid');
            qrGrid.innerHTML = '<div class="loading">Loading QR codes...</div>';
            
            try {
                logDebug(`Loading QR codes for location ${locationId}`);
                
                const { data: qrCodes, error } = await serviceClient
                    .from('qr_codes')
                    .select('*')
                    .eq('qr_type', 'location')
                    .eq('reference_id', locationId)
                    .order('created_at', { ascending: false });
                
                if (error) throw error;
                
                if (!qrCodes || qrCodes.length === 0) {
                    qrGrid.innerHTML = '<div class="loading">No QR codes found for this location</div>';
                    return;
                }
                
                // Clear grid
                qrGrid.innerHTML = '';
                
                // Add each QR code to the grid
                qrCodes.forEach(qr => {
                    displayLocationQRCode(qr, qrGrid);
                });
                
                logDebug(`Loaded ${qrCodes.length} QR codes`, 'success');
                
            } catch (error) {
                qrGrid.innerHTML = `<div class="error">Error loading QR codes: ${error.message}</div>`;
                logDebug(`Error loading QR codes: ${error.message}`, 'error');
            }
        }
        
        // Display location QR code in grid
        function displayLocationQRCode(qrCode, container) {
            // Create QR URL
            const appUrl = window.location.origin;
            const qrUrl = `${appUrl}/qr/${qrCode.short_code}`;
            
            // Create card
            const card = document.createElement('div');
            card.className = 'qr-card';
            
            // Determine status
            let status = qrCode.is_active ? 'Active' : 'Inactive';
            let statusClass = qrCode.is_active ? 'status-success' : 'status-error';
            
            if (qrCode.expire_at && new Date(qrCode.expire_at) < new Date()) {
                status = 'Expired';
                statusClass = 'status-error';
            } else if (qrCode.usage_limit && qrCode.usage_count >= qrCode.usage_limit) {
                status = 'Limit Reached';
                statusClass = 'status-error';
            }
            
            // Set card content
            card.innerHTML = `
                <div class="qr-card-header">
                    <h3>${qrCode.title}</h3>
                    <span class="status ${statusClass}">${status}</span>
                </div>
                <div class="qr-card-body">
                    <div id="qr-${qrCode.id}" class="qr-image"></div>
                    <div class="qr-info">
                        <p>${qrCode.description || 'No description'}</p>
                        <p>Created: ${new Date(qrCode.created_at).toLocaleDateString()}</p>
                        <p>Scans: ${qrCode.usage_count || 0}</p>
                        ${qrCode.usage_limit ? `<p>Limit: ${qrCode.usage_count || 0}/${qrCode.usage_limit}</p>` : ''}
                        ${qrCode.expire_at ? `<p>Expires: ${new Date(qrCode.expire_at).toLocaleDateString()}</p>` : ''}
                    </div>
                </div>
                <div class="qr-card-footer">
                    <button class="btn download-qr" data-id="${qrCode.id}">Download</button>
                    <button class="btn ${qrCode.is_active ? 'btn-secondary' : ''} toggle-qr" data-id="${qrCode.id}" data-active="${qrCode.is_active}">
                        ${qrCode.is_active ? 'Deactivate' : 'Activate'}
                    </button>
                </div>
            `;
            
            // Add to container
            container.appendChild(card);
            
            // Generate QR code
           try {
        // Xóa QR code cũ nếu có
        const qrElement = card.querySelector(`#qr-${qrCode.id}`);
        qrElement.innerHTML = '';
        
        // Tạo QR code mới với cú pháp đơn giản
        new QRCode(qrElement, qrUrl);
    } catch (err) {
        logDebug(`QR code generation error: ${err.message}`, 'error');
    }
            
            // Add download event
            card.querySelector('.download-qr').addEventListener('click', function() {
    try {
        // Lấy canvas từ QR code đã tạo
        const canvas = card.querySelector(`#qr-${qrCode.id} canvas`);
        if (!canvas) {
            logDebug('Canvas element not found in QR code', 'error');
            return;
        }
        
        const dataUrl = canvas.toDataURL('image/png');
        
        const downloadLink = document.createElement('a');
        downloadLink.href = dataUrl;
        downloadLink.download = `location-qr-${qrCode.id}.png`;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        
        logDebug(`Downloaded QR code for ${qrCode.title}`);
    } catch (err) {
        logDebug(`Error downloading QR code: ${err.message}`, 'error');
        showNotification('Error downloading QR code', 'error');
    }
});
            
            // Add toggle event
            card.querySelector('.toggle-qr').addEventListener('click', async function() {
                const qrId = this.getAttribute('data-id');
                const isActive = this.getAttribute('data-active') === 'true';
                
                try {
                    // Update QR code status
                    const { error } = await serviceClient
                        .from('qr_codes')
                        .update({ is_active: !isActive })
                        .eq('id', qrId);
                    
                    if (error) throw error;
                    
                    // Update UI
                    this.setAttribute('data-active', !isActive);
                    this.textContent = !isActive ? 'Deactivate' : 'Activate';
                    this.className = `btn ${!isActive ? 'btn-secondary' : ''} toggle-qr`;
                    
                    // Update status
                    const statusElement = card.querySelector('.status');
                    statusElement.className = `status ${!isActive ? 'status-success' : 'status-error'}`;
                    statusElement.textContent = !isActive ? 'Active' : 'Inactive';
                    
                    logDebug(`QR code ${!isActive ? 'activated' : 'deactivated'}`, 'success');
                    showNotification(`QR code ${!isActive ? 'activated' : 'deactivated'}`);
                    
                } catch (error) {
                    logDebug(`Error toggling QR code: ${error.message}`, 'error');
                    showNotification(`Error: ${error.message}`, 'error');
                }
            });
        }
        
        // Clear debug console
        document.getElementById('clear-debug').addEventListener('click', function() {
            debugConsole.innerHTML = '';
            logDebug('Debug console cleared');
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logDebug('TestQRCode.html loaded', 'info');
            
            // Check URL parameters for test data
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('test') && urlParams.has('url') && urlParams.has('key')) {
                document.getElementById('supabase-url').value = urlParams.get('url');
                document.getElementById('supabase-key').value = urlParams.get('key');
                if (urlParams.has('service')) {
                    document.getElementById('service-role-key').value = urlParams.get('service');
                }
                document.getElementById('connect-btn').click();
            }
        });
    </script>
</body>
</html>