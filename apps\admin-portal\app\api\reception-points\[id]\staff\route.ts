import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const receptionPointId = params.id;
    
    // Lấy tenant_id từ file config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json(
        { error: 'Tenant ID not found. Please activate your license.' },
        { status: 400 }
      );
    }
    
    // Tạo Supabase client
    const supabase = createAdminClient(cookies());
    
    // Kiểm tra reception point có tồn tại không
    const { data: receptionPoint, error: rptError } = await supabase
      .from('tenant_message_reception_points')
      .select('id')
      .eq('id', receptionPointId)
      .eq('tenant_id', tenant_id)
      .single();
      
    if (rptError) {
      if (rptError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Reception point not found' }, { status: 404 });
      }
      return NextResponse.json(
        { error: 'Error checking reception point' },
        { status: 500 }
      );
    }
    
    // Lấy tất cả nhân viên thuộc tenant này
    const { data: allUsers, error: usersError } = await supabase
      .from('tenant_users')
      .select(`
        id,
        user_id,
        tenant_users_details (
          email,
          display_name,
          avatar_url
        )
      `)
      .eq('tenant_id', tenant_id)
      .in('role', ['user', 'manager', 'admin']);
      
    if (usersError) {
      console.error('Error fetching users:', usersError);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }
    
    // Lấy các liên kết giữa user và reception point đang xét
    const { data: assignments, error: assignError } = await supabase
      .from('tenant_user_reception_points')
      .select(`
        id,
        tenant_user_id,
        priority,
        is_primary
      `)
      .eq('reception_point_id', receptionPointId);
      
    if (assignError) {
      console.error('Error fetching assignments:', assignError);
      return NextResponse.json(
        { error: 'Failed to fetch assignments' },
        { status: 500 }
      );
    }
    
    // Map kết quả để biết mỗi user có được assign hay không
    const staffList = allUsers.map(user => {
      const assignment = assignments?.find(a => a.tenant_user_id === user.id);
      return {
        id: user.id,
        user_id: user.user_id,
        display_name: user.tenant_users_details?.display_name || 'Unknown',
        email: user.tenant_users_details?.email || 'No email',
        avatar_url: user.tenant_users_details?.avatar_url || null,
        is_assigned: !!assignment,
        priority: assignment?.priority,
        is_primary: assignment?.is_primary,
        assignment_id: assignment?.id
      };
    });
    
    return NextResponse.json({ data: staffList });
  } catch (error: any) {
    console.error('Error in GET reception point staff:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
