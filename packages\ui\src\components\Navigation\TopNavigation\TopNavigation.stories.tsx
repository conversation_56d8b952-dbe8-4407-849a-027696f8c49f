import type { Meta, StoryObj } from '@storybook/react';
import { TopNavigation } from './index';
import { Button } from '../../Button';

const meta = {
  title: 'UI/Navigation/TopNavigation',
  component: TopNavigation,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['light', 'dark', 'studio'],
      description: 'Theme variant',
    },
    shadow: {
      control: 'boolean',
      description: 'Optional shadow',
    },
    sticky: {
      control: 'boolean',
      description: 'Whether to make the navigation sticky',
    },
    logo: {
      control: { disable: true },
      description: 'Logo component or text',
    },
    links: {
      control: { disable: true },
      description: 'Navigation links',
    },
    rightItems: {
      control: { disable: true },
      description: 'Additional items to display on the right side',
    },
  },
} satisfies Meta<typeof TopNavigation>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample icons for stories
const HomeIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 6L8 1.33333L14 6V13.3333C14 13.687 13.8595 14.0261 13.6095 14.2761C13.3594 14.5262 13.0203 14.6667 12.6667 14.6667H3.33333C2.97971 14.6667 2.64057 14.5262 2.39052 14.2761C2.14048 14.0261 2 13.687 2 13.3333V6Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6 14.6667V8H10V14.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const AboutIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 14.6667C11.6819 14.6667 14.6667 11.6819 14.6667 8.00004C14.6667 4.31814 11.6819 1.33337 8 1.33337C4.3181 1.33337 1.33334 4.31814 1.33334 8.00004C1.33334 11.6819 4.3181 14.6667 8 14.6667Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.06 6.00001C6.21674 5.55446 6.52728 5.17875 6.9393 4.93944C7.35132 4.70012 7.83582 4.61264 8.30772 4.69249C8.77962 4.77234 9.20673 5.01436 9.51839 5.3757C9.83005 5.73703 10.0056 6.19436 10.0067 6.66668C10.0067 8.00001 8.00666 8.66668 8.00666 8.66668" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M8 11.3334H8.00667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Default logo for stories
const Logo = () => (
  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
    <div style={{ 
      width: '32px', 
      height: '32px', 
      backgroundColor: '#FF4D00', 
      borderRadius: '4px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      fontWeight: 'bold',
      fontSize: '18px'
    }}>
      L
    </div>
    <span style={{ fontWeight: 600, fontSize: '18px' }}>LoaLoa</span>
  </div>
);

// Sample links for stories
const links = [
  { label: 'Home', href: '#', active: true, icon: <HomeIcon /> },
  { label: 'Features', href: '#', active: false },
  { label: 'About', href: '#', active: false, icon: <AboutIcon /> },
  { label: 'Contact', href: '#', active: false },
];

// Sample right items for stories
const rightItems = (
  <>
    <Button variant="outline" size="small" label="Login" />
    <Button variant="primary" size="small" label="Sign Up" />
  </>
);

export const Light: Story = {
  args: {
    variant: 'light',
    logo: <Logo />,
    links: links,
    rightItems: rightItems,
    shadow: true,
    sticky: false,
  },
};

export const Dark: Story = {
  args: {
    variant: 'dark',
    logo: <Logo />,
    links: links,
    rightItems: rightItems,
    shadow: true,
    sticky: false,
  },
  parameters: {
    backgrounds: { default: 'dark' },
  },
};

export const Studio: Story = {
  args: {
    variant: 'studio',
    logo: <Logo />,
    links: links,
    rightItems: rightItems,
    shadow: true,
    sticky: false,
  },
  parameters: {
    backgrounds: { default: 'dark' },
  },
};

export const WithoutIcons: Story = {
  args: {
    variant: 'light',
    logo: <Logo />,
    links: links.map(link => ({ ...link, icon: undefined })),
    rightItems: rightItems,
    shadow: true,
    sticky: false,
  },
};

export const WithoutRightItems: Story = {
  args: {
    variant: 'light',
    logo: <Logo />,
    links: links,
    shadow: true,
    sticky: false,
  },
};

export const WithoutShadow: Story = {
  args: {
    variant: 'light',
    logo: <Logo />,
    links: links,
    rightItems: rightItems,
    shadow: false,
    sticky: false,
  },
};

export const Sticky: Story = {
  args: {
    variant: 'light',
    logo: <Logo />,
    links: links,
    rightItems: rightItems,
    shadow: true,
    sticky: true,
  },
};

export const WithSimpleLogo: Story = {
  args: {
    variant: 'light',
    logo: "LoaLoa",
    links: links,
    rightItems: rightItems,
    shadow: true,
    sticky: false,
  },
};

export const MobileView: Story = {
  args: {
    variant: 'light',
    logo: <Logo />,
    links: links,
    rightItems: rightItems,
    shadow: true,
    sticky: false,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
};
