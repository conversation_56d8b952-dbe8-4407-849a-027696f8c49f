import { License } from './types';

/**
 * Calculate days remaining for a license
 * @param license The license object
 * @returns Number of days remaining
 */
export const calculateDaysRemaining = (license: License): number => {
  if (!license.expiry_date) {
    return 0;
  }
  
  const expiryDate = new Date(license.expiry_date);
  const currentDate = new Date();
  
  // Calculate difference in milliseconds
  const diffMs = expiryDate.getTime() - currentDate.getTime();
  
  // Convert to days and round
  const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
  
  // Return 0 if negative
  return Math.max(0, diffDays);
};

/**
 * Format a date string to a human-readable format
 * @param dateString ISO date string
 * @returns Formatted date string
 */
export const formatDate = (dateString?: string): string => {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (e) {
    return 'Invalid Date';
  }
};

/**
 * Validate a license key format
 * @param licenseKey The license key to validate
 * @returns Whether the license key format is valid
 */
export const isValidLicenseKeyFormat = (licenseKey: string): boolean => {
  // Example format: LLHM-12345-ABCDE-67890-FGHIJ
  const licenseKeyPattern = /^[A-Z]{4}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/;
  return licenseKeyPattern.test(licenseKey);
};
