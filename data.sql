SET session_replication_role = replica;

--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.8

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: audit_log_entries; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."audit_log_entries" ("instance_id", "id", "payload", "created_at", "ip_address") FROM stdin;
********-0000-0000-0000-********0000	859643e7-a0c3-477e-ad97-899feade5db3	{"action":"user_confirmation_requested","actor_id":"ce66af4f-1e75-4cea-929c-e90a42ae5cf7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user","traits":{"provider":"email"}}	2025-05-12 03:59:44.741936+00	
********-0000-0000-0000-********0000	9fe48020-7b8f-4ef4-880a-9d08273a5b19	{"action":"user_signedup","actor_id":"ce66af4f-1e75-4cea-929c-e90a42ae5cf7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team"}	2025-05-12 06:36:37.394821+00	
********-0000-0000-0000-********0000	82840a58-b137-4340-a095-302156db3ca2	{"action":"login","actor_id":"ce66af4f-1e75-4cea-929c-e90a42ae5cf7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-05-12 07:00:06.426924+00	
********-0000-0000-0000-********0000	b7dab131-1378-4151-bf0f-2a5fd85da840	{"action":"logout","actor_id":"ce66af4f-1e75-4cea-929c-e90a42ae5cf7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account"}	2025-05-12 07:03:07.540853+00	
********-0000-0000-0000-********0000	6d9e70b0-f5d3-4023-a910-9bc6de340eec	{"action":"login","actor_id":"ce66af4f-1e75-4cea-929c-e90a42ae5cf7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}	2025-05-12 07:03:13.405335+00	
********-0000-0000-0000-********0000	2af61eed-8a78-4a4a-a301-b0b3aa9fdf1b	{"action":"token_refreshed","actor_id":"ce66af4f-1e75-4cea-929c-e90a42ae5cf7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-05-12 08:01:27.360877+00	
********-0000-0000-0000-********0000	c71b3eec-e551-4a30-ba78-ce38a11c740d	{"action":"token_revoked","actor_id":"ce66af4f-1e75-4cea-929c-e90a42ae5cf7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-05-12 08:01:27.367269+00	
********-0000-0000-0000-********0000	13694a19-ee02-4f78-bfed-3debd9972132	{"action":"token_refreshed","actor_id":"ce66af4f-1e75-4cea-929c-e90a42ae5cf7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-05-13 01:45:08.958495+00	
********-0000-0000-0000-********0000	1f4df024-cc3b-49ab-977e-93e6eb268efe	{"action":"token_revoked","actor_id":"ce66af4f-1e75-4cea-929c-e90a42ae5cf7","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}	2025-05-13 01:45:08.965748+00	
\.


--
-- Data for Name: flow_state; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."flow_state" ("id", "user_id", "auth_code", "code_challenge_method", "code_challenge", "provider_type", "provider_access_token", "provider_refresh_token", "created_at", "updated_at", "authentication_method", "auth_code_issued_at") FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."users" ("instance_id", "id", "aud", "role", "email", "encrypted_password", "email_confirmed_at", "invited_at", "confirmation_token", "confirmation_sent_at", "recovery_token", "recovery_sent_at", "email_change_token_new", "email_change", "email_change_sent_at", "last_sign_in_at", "raw_app_meta_data", "raw_user_meta_data", "is_super_admin", "created_at", "updated_at", "phone", "phone_confirmed_at", "phone_change", "phone_change_token", "phone_change_sent_at", "email_change_token_current", "email_change_confirm_status", "banned_until", "reauthentication_token", "reauthentication_sent_at", "is_sso_user", "deleted_at", "is_anonymous") FROM stdin;
\N	7dd52d59-990b-4ddc-a829-bae7ee4627b9	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": "Test User", "preferred_language": "en"}	\N	2025-05-04 05:18:22.566727+00	2025-05-04 05:18:22.566727+00	\N	\N			\N		0	\N		\N	f	\N	f
\N	0059a3c0-fb9f-42a5-99fa-8400f4a11823	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": "Test name", "preferred_language": "en"}	\N	2025-05-04 05:29:16.674439+00	2025-05-04 05:29:16.674439+00	\N	\N			\N		0	\N		\N	f	\N	f
\N	973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": "Admin User", "preferred_language": "en"}	\N	2025-05-04 05:33:33.617892+00	2025-05-04 05:33:33.617892+00	\N	\N			\N		0	\N		\N	f	\N	f
\N	60fdb8b3-9430-4bfd-9429-5765b6adcf75	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": "Admin User", "preferred_language": "en"}	\N	2025-05-04 05:37:45.471247+00	2025-05-04 05:37:45.471247+00	\N	\N			\N		0	\N		\N	f	\N	f
\N	9983779e-0d4f-486b-ab5d-0878a4f7f868	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": "Admin User", "preferred_language": "en"}	\N	2025-05-04 05:46:16.513782+00	2025-05-04 05:46:16.513782+00	\N	\N			\N		0	\N		\N	f	\N	f
\N	0b58bbf2-4ae5-4ba1-9f98-6a78ee8ee133	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": "Test", "preferred_language": "en"}	\N	2025-05-04 05:47:37.239442+00	2025-05-04 05:47:37.239442+00	\N	\N			\N		0	\N		\N	f	\N	f
\N	d162c96f-4bac-4ee7-9cda-47be04b83ff7	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": "Admin Test User", "preferred_language": "en"}	\N	2025-05-04 05:52:50.636874+00	2025-05-04 05:52:50.636874+00	\N	\N			\N		0	\N		\N	f	\N	f
\N	1000eaac-6a55-422b-8014-1d256768f35a	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": "Admin Test User", "preferred_language": "en"}	\N	2025-05-04 06:08:44.794348+00	2025-05-04 06:08:44.794348+00	\N	\N			\N		0	\N		\N	f	\N	f
\N	c2675670-1e64-4070-8f3b-ffc9eb65771c	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": "Admin Test User", "preferred_language": "en"}	\N	2025-05-04 06:17:54.409008+00	2025-05-04 06:17:54.409008+00	\N	\N			\N		0	\N		\N	f	\N	f
\N	3e82b7d1-7048-4c52-8a33-5091166f2abd	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": "Admin Test User", "preferred_language": "en"}	\N	2025-05-04 06:18:03.023795+00	2025-05-04 06:18:03.023795+00	\N	\N			\N		0	\N		\N	f	\N	f
\N	49bd458c-45b6-4791-a3af-0ddf710d3a25	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": "Son Nguyen", "preferred_language": "en"}	\N	2025-05-04 06:19:12.216322+00	2025-05-04 06:19:12.216322+00	\N	\N			\N		0	\N		\N	f	\N	f
\N	a98f13ae-9a46-4dc9-9fa3-6f040627f650	\N	\N	<EMAIL>	\N	2025-05-08 04:46:16.773152+00	\N	\N	\N	\N	\N	\N	\N	\N	\N	\N	{"full_name": null, "preferred_language": "en"}	\N	2025-05-06 02:51:35.070459+00	2025-05-06 02:51:35.070459+00	\N	\N			\N		0	\N		\N	f	\N	f
********-0000-0000-0000-********0000	ce66af4f-1e75-4cea-929c-e90a42ae5cf7	authenticated	authenticated	<EMAIL>	$2a$10$/juggAyzXRv1Luj9d20.nu3k/3LFvf2roAiT2nOzS/pquB1LrxJ8u	2025-05-12 06:36:37.4019+00	\N		2025-05-12 03:59:44.751566+00		\N			\N	2025-05-12 07:03:13.406047+00	{"provider": "email", "providers": ["email"]}	{"sub": "ce66af4f-1e75-4cea-929c-e90a42ae5cf7", "email": "<EMAIL>", "email_verified": true, "phone_verified": false}	\N	2025-05-12 03:59:44.694638+00	2025-05-13 01:45:08.986008+00	\N	\N			\N		0	\N		\N	f	\N	f
\.


--
-- Data for Name: identities; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."identities" ("provider_id", "user_id", "identity_data", "provider", "last_sign_in_at", "created_at", "updated_at", "id") FROM stdin;
ce66af4f-1e75-4cea-929c-e90a42ae5cf7	ce66af4f-1e75-4cea-929c-e90a42ae5cf7	{"sub": "ce66af4f-1e75-4cea-929c-e90a42ae5cf7", "email": "<EMAIL>", "email_verified": true, "phone_verified": false}	email	2025-05-12 03:59:44.728873+00	2025-05-12 03:59:44.73025+00	2025-05-12 03:59:44.73025+00	b400679b-9867-42cf-96da-56c5ce2c7cda
\.


--
-- Data for Name: instances; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."instances" ("id", "uuid", "raw_base_config", "created_at", "updated_at") FROM stdin;
\.


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."sessions" ("id", "user_id", "created_at", "updated_at", "factor_id", "aal", "not_after", "refreshed_at", "user_agent", "ip", "tag") FROM stdin;
*************-4d52-8a2f-bc5dec0c7611	ce66af4f-1e75-4cea-929c-e90a42ae5cf7	2025-05-12 07:03:13.406122+00	2025-05-13 01:45:08.997082+00	\N	aal1	\N	2025-05-13 01:45:08.996983	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********	***************	\N
\.


--
-- Data for Name: mfa_amr_claims; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."mfa_amr_claims" ("session_id", "created_at", "updated_at", "authentication_method", "id") FROM stdin;
*************-4d52-8a2f-bc5dec0c7611	2025-05-12 07:03:13.409452+00	2025-05-12 07:03:13.409452+00	password	540bc9ff-b46a-4de8-a11d-9a1da3ca7f6f
\.


--
-- Data for Name: mfa_factors; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."mfa_factors" ("id", "user_id", "friendly_name", "factor_type", "status", "created_at", "updated_at", "secret", "phone", "last_challenged_at", "web_authn_credential", "web_authn_aaguid") FROM stdin;
\.


--
-- Data for Name: mfa_challenges; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."mfa_challenges" ("id", "factor_id", "created_at", "verified_at", "ip_address", "otp_code", "web_authn_session_data") FROM stdin;
\.


--
-- Data for Name: one_time_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."one_time_tokens" ("id", "user_id", "token_type", "token_hash", "relates_to", "created_at", "updated_at") FROM stdin;
\.


--
-- Data for Name: refresh_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."refresh_tokens" ("instance_id", "id", "token", "user_id", "revoked", "created_at", "updated_at", "parent", "session_id") FROM stdin;
********-0000-0000-0000-********0000	3	56gcmccjhce5	ce66af4f-1e75-4cea-929c-e90a42ae5cf7	t	2025-05-12 07:03:13.407003+00	2025-05-12 08:01:27.367886+00	\N	*************-4d52-8a2f-bc5dec0c7611
********-0000-0000-0000-********0000	4	3sqqgxz3itkq	ce66af4f-1e75-4cea-929c-e90a42ae5cf7	t	2025-05-12 08:01:27.371052+00	2025-05-13 01:45:08.966683+00	56gcmccjhce5	*************-4d52-8a2f-bc5dec0c7611
********-0000-0000-0000-********0000	5	vjcbbeocoqux	ce66af4f-1e75-4cea-929c-e90a42ae5cf7	f	2025-05-13 01:45:08.98014+00	2025-05-13 01:45:08.98014+00	3sqqgxz3itkq	*************-4d52-8a2f-bc5dec0c7611
\.


--
-- Data for Name: sso_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."sso_providers" ("id", "resource_id", "created_at", "updated_at") FROM stdin;
\.


--
-- Data for Name: saml_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."saml_providers" ("id", "sso_provider_id", "entity_id", "metadata_xml", "metadata_url", "attribute_mapping", "created_at", "updated_at", "name_id_format") FROM stdin;
\.


--
-- Data for Name: saml_relay_states; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."saml_relay_states" ("id", "sso_provider_id", "request_id", "for_email", "redirect_to", "created_at", "updated_at", "flow_state_id") FROM stdin;
\.


--
-- Data for Name: sso_domains; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

COPY "auth"."sso_domains" ("id", "sso_provider_id", "domain", "created_at", "updated_at") FROM stdin;
\.


--
-- Data for Name: tenants; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."tenants" ("id", "name", "domain", "contact_email", "contact_phone", "address", "logo_url", "primary_color", "is_active", "created_at", "updated_at", "metadata") FROM stdin;
\.


--
-- Data for Name: organizations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."organizations" ("id", "name", "description", "plan_type", "is_active", "created_at", "updated_at", "metadata", "tenant_id") FROM stdin;
78f1663f-1e6a-4249-a24e-fc1c776ffd98	Default Organization	default-organization	standard	t	2025-05-08 04:46:16.773152+00	2025-05-08 04:46:16.773152+00	\N	\N
6a15559f-67c8-4623-90ef-c6267cd42a20	LoaLoa Hotel Group	Organization for testing QR code functionality	free	t	2025-05-12 07:23:33.775447+00	2025-05-12 07:23:33.775447+00	\N	\N
\.


--
-- Data for Name: chat_rooms; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."chat_rooms" ("id", "name", "description", "created_at", "updated_at", "is_active", "room_type", "metadata", "last_activity_at", "organization_id", "tenant_id") FROM stdin;
5877c2de-1042-4173-b02d-30941aad655b	Dịch vụ phòng	Phòng chat cho dịch vụ phòng	2025-05-04 07:11:20.26281+00	2025-05-08 05:16:51.620283+00	t	support	{"organization_id": "78f1663f-1e6a-4249-a24e-fc1c776ffd98"}	2025-05-08 03:28:55.443189+00	78f1663f-1e6a-4249-a24e-fc1c776ffd98	\N
954247d6-1e11-4b21-9cbb-a877846d374b	Nhà hàng	Phòng chat cho dịch vụ nhà hàng	2025-05-04 07:11:20.26281+00	2025-05-08 05:16:51.620283+00	t	support	{"organization_id": "78f1663f-1e6a-4249-a24e-fc1c776ffd98"}	2025-05-08 03:28:55.443189+00	78f1663f-1e6a-4249-a24e-fc1c776ffd98	\N
c446a124-b7f1-4bbd-aa5a-e2ad0fd984d6	Chat chung	Phòng chat chung cho mọi người	2025-05-04 07:11:20.26281+00	2025-05-08 05:16:51.620283+00	t	general	{"organization_id": "78f1663f-1e6a-4249-a24e-fc1c776ffd98"}	2025-05-08 03:28:55.443189+00	78f1663f-1e6a-4249-a24e-fc1c776ffd98	\N
9fab8d63-e4fd-435b-bd57-3f63a9be7952	Phòng hỗ trợ lễ tân	Phòng chat để liên hệ với lễ tân	2025-05-04 08:12:48.687967+00	2025-05-08 05:16:51.620283+00	t	support	{"organization_id": "78f1663f-1e6a-4249-a24e-fc1c776ffd98"}	2025-05-08 03:28:55.443189+00	78f1663f-1e6a-4249-a24e-fc1c776ffd98	\N
48223543-debf-4fc9-b27c-580940d60dc3	Phòng hỗ trợ dịch vụ phòng	Phòng chat để yêu cầu dịch vụ phòng	2025-05-04 08:12:48.687967+00	2025-05-08 05:16:51.620283+00	t	support	{"organization_id": "78f1663f-1e6a-4249-a24e-fc1c776ffd98"}	2025-05-08 03:28:55.443189+00	78f1663f-1e6a-4249-a24e-fc1c776ffd98	\N
44f2b4ea-3534-4e35-8d97-dd9ad94c2b5b	Chat chung	Phòng chat chung cho mọi người	2025-05-04 08:12:48.687967+00	2025-05-08 05:16:51.620283+00	t	general	{"organization_id": "78f1663f-1e6a-4249-a24e-fc1c776ffd98"}	2025-05-08 03:28:55.443189+00	78f1663f-1e6a-4249-a24e-fc1c776ffd98	\N
c636ea40-1d87-4982-a6a3-86fa0805e258	Lễ tân	Phòng chat cho dịch vụ lễ tân	2025-05-04 07:11:20.26281+00	2025-05-09 05:53:38.721124+00	t	support	{"organization_id": "78f1663f-1e6a-4249-a24e-fc1c776ffd98"}	2025-05-09 05:53:37.913+00	78f1663f-1e6a-4249-a24e-fc1c776ffd98	\N
\.


--
-- Data for Name: temporary_users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."temporary_users" ("id", "qr_code_id", "device_id", "preferred_language", "created_at", "expires_at", "is_activated", "room_number", "hotel_id", "metadata") FROM stdin;
2cff5a0b-5873-4afb-a62c-f3461c66530e	3361ce30-2211-430f-bc54-f4dbee93bc5c	\N	vi	2025-05-04 06:22:30.380939+00	2025-05-05 06:22:29.033+00	f	101	\N	{"source": "test"}
\.


--
-- Data for Name: chat_participants; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."chat_participants" ("id", "chat_room_id", "user_id", "temporary_user_id", "joined_at", "left_at", "is_active", "participant_role", "preferred_language", "last_read_at", "is_muted", "notification_preferences", "display_name", "user_id_orphaned") FROM stdin;
ec87f46a-4602-4b97-b1b5-0556e8800375	c636ea40-1d87-4982-a6a3-86fa0805e258	7dd52d59-990b-4ddc-a829-bae7ee4627b9	\N	2025-05-08 09:38:21.649703+00	\N	t	member	en	2025-05-08 09:38:21.649703+00	f	{"all": true}	\N	f
397b355d-aec0-4e98-8340-ad18660962fe	c636ea40-1d87-4982-a6a3-86fa0805e258	49bd458c-45b6-4791-a3af-0ddf710d3a25	\N	2025-05-08 08:33:47.070166+00	\N	t	member	en	2025-05-08 08:33:47.070166+00	f	{"all": true}	\N	f
918975b6-36b9-4263-a0e5-54ebfd2b74fa	5877c2de-1042-4173-b02d-30941aad655b	0059a3c0-fb9f-42a5-99fa-8400f4a11823	\N	2025-05-08 09:38:27.659406+00	\N	t	member	en	2025-05-08 09:38:27.659406+00	f	{"all": true}	\N	f
43e117ad-c04d-40f6-9a8a-101448df6b31	c636ea40-1d87-4982-a6a3-86fa0805e258	a98f13ae-9a46-4dc9-9fa3-6f040627f650	\N	2025-05-08 08:33:55.079774+00	\N	t	member	en	2025-05-08 08:33:55.079774+00	f	{"all": true}	\N	f
035ff8af-a3c5-400d-83e8-1e11b222d3ad	c636ea40-1d87-4982-a6a3-86fa0805e258	973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	\N	2025-05-08 10:06:50.82701+00	\N	t	member	en	2025-05-08 10:06:50.82701+00	f	{"all": true}	\N	f
1cc2d09a-2232-49b8-9ecb-0b958d8b7448	c636ea40-1d87-4982-a6a3-86fa0805e258	0059a3c0-fb9f-42a5-99fa-8400f4a11823	\N	2025-05-08 09:38:24.612908+00	\N	t	member	en	2025-05-08 09:38:24.612908+00	f	{"all": true}	\N	f
\.


--
-- Data for Name: chat_messages; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."chat_messages" ("id", "chat_room_id", "sender_id", "temporary_sender_id", "content", "original_language", "sent_at", "is_translated", "metadata", "participant_id", "content_type", "reply_to", "updated_at", "deleted_at", "sender_id_orphaned", "read_at") FROM stdin;
13c9b7d6-b5f1-41d9-a67a-18cea89c064b	c636ea40-1d87-4982-a6a3-86fa0805e258	49bd458c-45b6-4791-a3af-0ddf710d3a25	\N	Tin nhắn test	vi	2025-05-09 04:04:15.182864+00	t	\N	ec87f46a-4602-4b97-b1b5-0556e8800375	text	\N	2025-05-09 04:04:15.182864+00	\N	f	\N
bdf410c1-1de6-4ab4-83f0-b848baf59841	c636ea40-1d87-4982-a6a3-86fa0805e258	973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	\N	Test message 2025-05-09T04:09:35.159Z	en	2025-05-09 04:09:35.159+00	f	\N	\N	text	\N	2025-05-09 04:09:36.057799+00	\N	f	\N
0dca8ed1-e978-44c1-9e94-8e70ab67f4bd	c636ea40-1d87-4982-a6a3-86fa0805e258	49bd458c-45b6-4791-a3af-0ddf710d3a25	\N	Tin nhắn test sau khi làm sạch cấu trúc DB	vi	2025-05-09 04:20:35.352466+00	t	\N	ec87f46a-4602-4b97-b1b5-0556e8800375	text	\N	2025-05-09 04:20:35.352466+00	\N	f	\N
fadfc198-a20a-4a2d-9b3b-33ce17d49f69	c636ea40-1d87-4982-a6a3-86fa0805e258	973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	\N	Test message 2025-05-09T04:21:14.332Z	en	2025-05-09 04:21:14.332+00	f	\N	\N	text	\N	2025-05-09 04:21:15.212894+00	\N	f	\N
dd672554-9872-42bc-bbf5-747d3bb3e1e9	c636ea40-1d87-4982-a6a3-86fa0805e258	973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	\N	Test message 2025-05-13T09:26:01.428Z	en	2025-05-13 09:26:01.429+00	f	\N	\N	text	\N	2025-05-13 09:26:02.399135+00	\N	f	\N
61b056c8-2296-4a90-93fe-37a824e5b2c3	c636ea40-1d87-4982-a6a3-86fa0805e258	973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	\N	Test message 2025-05-09T04:30:44.612Z	en	2025-05-09 04:30:44.612+00	f	\N	\N	text	\N	2025-05-09 04:30:45.490303+00	\N	f	\N
6d8e4dd3-fa22-4db2-8920-18b0e375db08	c636ea40-1d87-4982-a6a3-86fa0805e258	49bd458c-45b6-4791-a3af-0ddf710d3a25	\N	Chào bạn	vi	2025-05-09 04:32:04.238+00	t	\N	\N	text	\N	2025-05-09 04:32:05.054256+00	\N	f	\N
0688eef8-e634-48c2-92b6-566359997a85	c636ea40-1d87-4982-a6a3-86fa0805e258	a98f13ae-9a46-4dc9-9fa3-6f040627f650	\N	Hello, how are you?	en	2025-05-09 04:32:17.932+00	f	\N	\N	text	\N	2025-05-09 04:32:18.81748+00	\N	f	\N
c8d6112e-ca41-4e17-a736-674aec66b29f	c636ea40-1d87-4982-a6a3-86fa0805e258	49bd458c-45b6-4791-a3af-0ddf710d3a25	\N	Tôi khỏe	vi	2025-05-09 05:53:36.161+00	t	\N	\N	text	\N	2025-05-09 05:53:37.050499+00	\N	f	\N
8d35e57c-ec5c-45c3-9998-ec54e13a9f25	c636ea40-1d87-4982-a6a3-86fa0805e258	a98f13ae-9a46-4dc9-9fa3-6f040627f650	\N	Bạn thế nào	en	2025-05-09 05:53:37.913+00	f	\N	\N	text	\N	2025-05-09 05:53:38.721124+00	\N	f	\N
\.


--
-- Data for Name: hotels; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."hotels" ("id", "name", "address", "city", "country", "postal_code", "phone", "email", "website", "timezone", "check_in_time", "check_out_time", "created_at", "updated_at", "is_active", "metadata", "organization_id", "tenant_id") FROM stdin;
\.


--
-- Data for Name: hotel_rooms; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."hotel_rooms" ("id", "hotel_id", "room_number", "room_type", "floor", "capacity", "is_occupied", "status", "created_at", "updated_at") FROM stdin;
\.


--
-- Data for Name: locations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."locations" ("id", "organization_id", "name", "description", "location_type", "address", "coordinates", "floor", "room_number", "parent_location_id", "contact_info", "metadata", "is_active", "created_at", "updated_at") FROM stdin;
173c4c83-e515-4561-bcab-dea2dfd0b883	6a15559f-67c8-4623-90ef-c6267cd42a20	LoaLoa Grand Hotel	Our flagship hotel with 5-star services	hotel	\N	\N	\N	\N	\N	{}	{}	t	2025-05-12 07:24:35.583373+00	2025-05-12 07:24:35.583373+00
33542041-623d-4a75-bb8d-9bcc3507ef23	6a15559f-67c8-4623-90ef-c6267cd42a20	LoaLoa Resort & Spa	Beachside resort with spa facilities	resort	\N	\N	\N	\N	\N	{}	{}	t	2025-05-12 07:24:35.583373+00	2025-05-12 07:24:35.583373+00
7ca844d7-906c-46e7-8ca7-41a06e49cc12	6a15559f-67c8-4623-90ef-c6267cd42a20	LoaLoa Business Center	Conference and meeting facilities	business_center	\N	\N	\N	\N	\N	{}	{}	t	2025-05-12 07:24:35.583373+00	2025-05-12 07:24:35.583373+00
\.


--
-- Data for Name: message_attachments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."message_attachments" ("id", "message_id", "file_url", "file_name", "file_type", "file_size", "thumbnail_url", "created_at") FROM stdin;
\.


--
-- Data for Name: message_drafts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."message_drafts" ("id", "user_id", "chat_room_id", "participant_id", "content", "content_type", "attachments", "original_language", "created_at", "updated_at") FROM stdin;
\.


--
-- Data for Name: message_reactions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."message_reactions" ("id", "message_id", "participant_id", "reaction", "created_at") FROM stdin;
\.


--
-- Data for Name: message_read_status; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."message_read_status" ("id", "message_id", "user_id", "participant_id", "read_at") FROM stdin;
99c1b4ec-0548-432b-b14c-70d321cf0f76	c8d6112e-ca41-4e17-a736-674aec66b29f	49bd458c-45b6-4791-a3af-0ddf710d3a25	\N	2025-05-09 05:53:36.161+00
0f252056-cbe2-46a2-9f31-1ed7b01976e7	8d35e57c-ec5c-45c3-9998-ec54e13a9f25	a98f13ae-9a46-4dc9-9fa3-6f040627f650	\N	2025-05-09 05:53:37.913+00
8a2ee80e-08e4-42f5-ac29-7643626debba	dd672554-9872-42bc-bbf5-747d3bb3e1e9	973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	\N	2025-05-13 09:26:01.429+00
\.


--
-- Data for Name: message_status; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."message_status" ("id", "message_id", "participant_id", "is_delivered", "is_read", "delivered_at", "read_at") FROM stdin;
\.


--
-- Data for Name: message_translations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."message_translations" ("id", "message_id", "language", "translated_content", "created_at", "updated_at", "translation_provider", "is_automatic", "source_table") FROM stdin;
360a3ef4-1c54-4d81-a133-f73093510a6d	13c9b7d6-b5f1-41d9-a67a-18cea89c064b	en	Tin nhắn test	2025-05-09 04:04:15.182864+00	2025-05-09 04:04:15.182864+00	auto	t	unified
a134449a-545e-445b-bc56-f5b50cf5f088	0dca8ed1-e978-44c1-9e94-8e70ab67f4bd	en	Tin nhắn test sau khi làm sạch cấu trúc DB	2025-05-09 04:20:35.352466+00	2025-05-09 04:20:35.352466+00	auto	t	unified
b5253bd6-f7c6-488a-b60e-f7196e6186ef	0dca8ed1-e978-44c1-9e94-8e70ab67f4bd	en	Tin nhắn test sau khi làm sạch cấu trúc DB	2025-05-09 04:20:35.352466+00	2025-05-09 04:20:35.352466+00	auto	t	unified
94c2bb8b-c3ba-47bd-83f4-3e1b883d814b	6d8e4dd3-fa22-4db2-8920-18b0e375db08	en	Chào bạn	2025-05-09 04:32:05.054256+00	2025-05-09 04:32:05.054256+00	auto	t	unified
f6a29832-4f98-48f1-905b-9e107b3bd4e7	6d8e4dd3-fa22-4db2-8920-18b0e375db08	en	Chào bạn	2025-05-09 04:32:05.054256+00	2025-05-09 04:32:05.054256+00	auto	t	unified
5766e12c-680c-4a3b-bd24-4c4c434e437d	c8d6112e-ca41-4e17-a736-674aec66b29f	en	Tôi khỏe	2025-05-09 05:53:37.050499+00	2025-05-09 05:53:37.050499+00	auto	t	unified
05e4f621-253b-4855-909b-9d1469781c57	c8d6112e-ca41-4e17-a736-674aec66b29f	en	Tôi khỏe	2025-05-09 05:53:37.050499+00	2025-05-09 05:53:37.050499+00	auto	t	unified
\.


--
-- Data for Name: organization_members; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."organization_members" ("id", "organization_id", "user_id", "role", "is_primary", "joined_at", "metadata") FROM stdin;
8e754422-8e3c-4f50-95a8-c22f48273950	78f1663f-1e6a-4249-a24e-fc1c776ffd98	7dd52d59-990b-4ddc-a829-bae7ee4627b9	admin	f	2025-05-08 04:46:16.773152+00	\N
50d546aa-637b-469c-b6cb-ff35ccc793fc	78f1663f-1e6a-4249-a24e-fc1c776ffd98	0059a3c0-fb9f-42a5-99fa-8400f4a11823	admin	f	2025-05-08 04:46:16.773152+00	\N
e6310c86-6da8-47f6-bc4a-59356e6faf5d	78f1663f-1e6a-4249-a24e-fc1c776ffd98	973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	admin	f	2025-05-08 04:46:16.773152+00	\N
81d005e9-25c6-493b-956a-b1733e7d5bef	78f1663f-1e6a-4249-a24e-fc1c776ffd98	60fdb8b3-9430-4bfd-9429-5765b6adcf75	admin	f	2025-05-08 04:46:16.773152+00	\N
53e6ee85-4e84-4cf2-aeb4-e900de2aaeb5	78f1663f-1e6a-4249-a24e-fc1c776ffd98	9983779e-0d4f-486b-ab5d-0878a4f7f868	admin	f	2025-05-08 04:46:16.773152+00	\N
557f0127-e4b0-4bdd-b584-d07401559e88	78f1663f-1e6a-4249-a24e-fc1c776ffd98	0b58bbf2-4ae5-4ba1-9f98-6a78ee8ee133	admin	f	2025-05-08 04:46:16.773152+00	\N
6ffa2421-a589-4653-bf95-ac51e03a54f4	78f1663f-1e6a-4249-a24e-fc1c776ffd98	d162c96f-4bac-4ee7-9cda-47be04b83ff7	admin	f	2025-05-08 04:46:16.773152+00	\N
3b74ce76-4b33-45a5-9420-6998206368b2	78f1663f-1e6a-4249-a24e-fc1c776ffd98	1000eaac-6a55-422b-8014-1d256768f35a	admin	f	2025-05-08 04:46:16.773152+00	\N
31c1b3ba-c866-435c-9f39-1b5d3746ac49	78f1663f-1e6a-4249-a24e-fc1c776ffd98	c2675670-1e64-4070-8f3b-ffc9eb65771c	admin	f	2025-05-08 04:46:16.773152+00	\N
50398379-b11f-4fcc-a5bf-dff82eac2923	78f1663f-1e6a-4249-a24e-fc1c776ffd98	3e82b7d1-7048-4c52-8a33-5091166f2abd	admin	f	2025-05-08 04:46:16.773152+00	\N
cef4705a-8263-4aeb-96b4-f6c7bf8aa57e	78f1663f-1e6a-4249-a24e-fc1c776ffd98	49bd458c-45b6-4791-a3af-0ddf710d3a25	admin	f	2025-05-08 04:46:16.773152+00	\N
9b954c4e-84bb-43c5-bcab-3330606b1264	78f1663f-1e6a-4249-a24e-fc1c776ffd98	a98f13ae-9a46-4dc9-9fa3-6f040627f650	admin	f	2025-05-08 04:46:16.773152+00	\N
\.


--
-- Data for Name: organization_roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."organization_roles" ("id", "organization_id", "name", "description", "created_at", "updated_at") FROM stdin;
\.


--
-- Data for Name: organization_member_roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."organization_member_roles" ("id", "organization_member_id", "organization_role_id", "created_at") FROM stdin;
\.


--
-- Data for Name: permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."permissions" ("id", "name", "description", "resource", "action", "created_at") FROM stdin;
6c077245-6223-4023-8f67-6cd5c1e6fe51	user:read	Can read user information	user	read	2025-05-04 05:17:19.499084+00
161807fa-1f01-433d-9aba-d0a4aee0704f	user:create	Can create users	user	create	2025-05-04 05:17:19.499084+00
5946ee04-1f97-4d8f-8d18-a2dbd427eb6f	user:update	Can update user information	user	update	2025-05-04 05:17:19.499084+00
66e90f6d-aa6f-4784-aeab-b7753e376f07	user:delete	Can delete users	user	delete	2025-05-04 05:17:19.499084+00
cc31e841-ff10-4bc8-a531-be39225ea665	chat:read	Can read chats	chat	read	2025-05-04 05:17:19.499084+00
89f4c3a4-e320-4cd0-bd42-f3cc10709dd4	chat:create	Can create chat messages	chat	create	2025-05-04 05:17:19.499084+00
4f1e3e84-20e5-4bad-807d-9c80204a47c9	chat:manage	Can manage chats	chat	manage	2025-05-04 05:17:19.499084+00
4eda0b9e-4816-4f37-a6c6-259766970f67	staff:dashboard	Can access staff dashboard	dashboard	access	2025-05-04 05:17:19.499084+00
f801fd94-612f-44a1-90df-ece33895099c	admin:dashboard	Can access admin dashboard	dashboard	admin	2025-05-04 05:17:19.499084+00
\.


--
-- Data for Name: organization_role_permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."organization_role_permissions" ("id", "organization_role_id", "permission_id", "created_at") FROM stdin;
\.


--
-- Data for Name: orphaned_user_roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."orphaned_user_roles" ("id", "user_id", "role_id", "created_at", "notes") FROM stdin;
\.


--
-- Data for Name: qr_codes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."qr_codes" ("id", "qr_type", "title", "description", "owner_id", "organization_id", "reference_id", "metadata", "usage_limit", "usage_count", "expire_at", "is_active", "is_dynamic", "access_type", "short_code", "created_at", "updated_at", "tenant_id") FROM stdin;
0e01dff6-a82c-43c0-b0d1-f34db4e5b0e0	user	<EMAIL>	Personal QR code for chat	ce66af4f-1e75-4cea-929c-e90a42ae5cf7	\N	ce66af4f-1e75-4cea-929c-e90a42ae5cf7	{"email": "<EMAIL>", "created_at": "2025-05-12 03:59:44.694274+00"}	\N	0	\N	t	t	public	u_4f132f88	2025-05-12 03:59:44.694274+00	2025-05-12 03:59:44.694274+00	\N
1fd44da6-289a-4505-92b4-61d231b44237	location	Lobby	Front Officer	ce66af4f-1e75-4cea-929c-e90a42ae5cf7	\N	7ca844d7-906c-46e7-8ca7-41a06e49cc12	{}	\N	0	2025-05-12 00:00:00+00	t	t	public	l_7ca844d7	2025-05-12 07:36:42.348653+00	2025-05-12 07:36:42.348653+00	\N
c2744cb4-5e03-4ed4-bf41-1d1da72b9144	location	Lobby1	Front Desk	ce66af4f-1e75-4cea-929c-e90a42ae5cf7	\N	173c4c83-e515-4561-bcab-dea2dfd0b883	{}	100	0	2025-05-13 00:00:00+00	t	t	public	l_173c4c83	2025-05-12 07:37:30.675486+00	2025-05-12 07:37:30.675486+00	\N
b1283702-d8e9-41cd-a88f-854ecc54a532	location	Lobby 3	Lobby	ce66af4f-1e75-4cea-929c-e90a42ae5cf7	\N	33542041-623d-4a75-bb8d-9bcc3507ef23	{}	10	0	2025-05-13 00:00:00+00	t	t	public	l_33542041	2025-05-12 07:52:29.08427+00	2025-05-12 07:52:29.08427+00	\N
\.


--
-- Data for Name: qr_code_scans; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."qr_code_scans" ("id", "qr_code_id", "scanned_by_user_id", "scanned_by_anonymous_id", "scanned_at", "ip_address", "user_agent", "device_info", "location_info", "action_taken", "action_result", "session_id", "duration") FROM stdin;
\.


--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."roles" ("id", "name", "description", "created_at") FROM stdin;
174b9d26-8127-4af6-90ba-5b3275223e67	admin	Administrator with full access	2025-05-04 05:17:19.499084+00
5da7dc3c-a4bb-4b50-ace3-8160dfafe250	staff	Hotel staff member	2025-05-04 05:17:19.499084+00
34760d52-bbc9-49dd-a6f8-b306fd0dfe22	guest	Hotel guest	2025-05-04 05:17:19.499084+00
efda94c7-d1a1-4531-8625-864b54c2c4e9	temporary_guest	Temporary guest account	2025-05-04 05:17:19.499084+00
\.


--
-- Data for Name: role_permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."role_permissions" ("id", "role_id", "permission_id", "created_at") FROM stdin;
\.


--
-- Data for Name: tenant_databases; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."tenant_databases" ("id", "tenant_id", "schema_name", "status", "connection_string", "created_at", "updated_at", "last_backup_at", "error_message", "resource_usage", "metadata") FROM stdin;
\.


--
-- Data for Name: tenant_licenses; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."tenant_licenses" ("id", "tenant_id", "license_type", "user_limit", "storage_limit", "features", "starts_at", "expires_at", "is_active", "last_payment_date", "payment_status", "payment_method", "billing_cycle", "price", "currency", "created_at", "updated_at", "metadata") FROM stdin;
\.


--
-- Data for Name: tenant_users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."tenant_users" ("id", "tenant_id", "user_id", "role", "is_primary_tenant", "is_active", "joined_at", "last_login_at", "expiry_date", "permissions", "created_at", "updated_at", "metadata") FROM stdin;
\.


--
-- Data for Name: translation_cache; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."translation_cache" ("id", "source_text", "source_language", "target_language", "translated_text", "created_at", "last_used_at", "use_count") FROM stdin;
\.


--
-- Data for Name: user_orphans; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."user_orphans" ("id", "email", "full_name", "preferred_language", "avatar_url", "phone_number", "created_at", "updated_at", "metadata", "notes") FROM stdin;
7dd52d59-990b-4ddc-a829-bae7ee4627b9	<EMAIL>	Test User	en	\N	\N	2025-05-04 05:18:22.566727+00	2025-05-04 05:18:22.566727+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
0059a3c0-fb9f-42a5-99fa-8400f4a11823	<EMAIL>	Test name	en	\N	\N	2025-05-04 05:29:16.674439+00	2025-05-04 05:29:16.674439+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	<EMAIL>	Admin User	en	\N	\N	2025-05-04 05:33:33.617892+00	2025-05-04 05:33:33.617892+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
60fdb8b3-9430-4bfd-9429-5765b6adcf75	<EMAIL>	Admin User	en	\N	\N	2025-05-04 05:37:45.471247+00	2025-05-04 05:37:45.471247+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
9983779e-0d4f-486b-ab5d-0878a4f7f868	<EMAIL>	Admin User	en	\N	\N	2025-05-04 05:46:16.513782+00	2025-05-04 05:46:16.513782+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
0b58bbf2-4ae5-4ba1-9f98-6a78ee8ee133	<EMAIL>	Test	en	\N	\N	2025-05-04 05:47:37.239442+00	2025-05-04 05:47:37.239442+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
d162c96f-4bac-4ee7-9cda-47be04b83ff7	<EMAIL>	Admin Test User	en	\N	\N	2025-05-04 05:52:50.636874+00	2025-05-04 05:52:50.636874+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
1000eaac-6a55-422b-8014-1d256768f35a	<EMAIL>	Admin Test User	en	\N	\N	2025-05-04 06:08:44.794348+00	2025-05-04 06:08:44.794348+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
c2675670-1e64-4070-8f3b-ffc9eb65771c	<EMAIL>	Admin Test User	en	\N	\N	2025-05-04 06:17:54.409008+00	2025-05-04 06:17:54.409008+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
3e82b7d1-7048-4c52-8a33-5091166f2abd	<EMAIL>	Admin Test User	en	\N	\N	2025-05-04 06:18:03.023795+00	2025-05-04 06:18:03.023795+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
49bd458c-45b6-4791-a3af-0ddf710d3a25	<EMAIL>	Son Nguyen	en	\N	\N	2025-05-04 06:19:12.216322+00	2025-05-04 06:19:12.216322+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
a98f13ae-9a46-4dc9-9fa3-6f040627f650	<EMAIL>	\N	en	\N	\N	2025-05-06 02:51:35.070459+00	2025-05-06 02:51:35.070459+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	User không có tương ứng trong auth.users
\.


--
-- Data for Name: user_presence; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."user_presence" ("id", "user_id", "status", "last_active_at", "current_room_id", "device_info", "ip_address", "updated_at") FROM stdin;
\.


--
-- Data for Name: user_profiles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."user_profiles" ("id", "full_name", "preferred_language", "avatar_url", "phone_number", "created_at", "updated_at", "metadata", "last_active_at") FROM stdin;
7dd52d59-990b-4ddc-a829-bae7ee4627b9	Test User	en	\N	\N	2025-05-04 05:18:22.566727+00	2025-05-04 05:18:22.566727+00	\N	\N
0059a3c0-fb9f-42a5-99fa-8400f4a11823	Test name	en	\N	\N	2025-05-04 05:29:16.674439+00	2025-05-04 05:29:16.674439+00	\N	\N
973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	Admin User	en	\N	\N	2025-05-04 05:33:33.617892+00	2025-05-04 05:33:33.617892+00	\N	\N
60fdb8b3-9430-4bfd-9429-5765b6adcf75	Admin User	en	\N	\N	2025-05-04 05:37:45.471247+00	2025-05-04 05:37:45.471247+00	\N	\N
9983779e-0d4f-486b-ab5d-0878a4f7f868	Admin User	en	\N	\N	2025-05-04 05:46:16.513782+00	2025-05-04 05:46:16.513782+00	\N	\N
0b58bbf2-4ae5-4ba1-9f98-6a78ee8ee133	Test	en	\N	\N	2025-05-04 05:47:37.239442+00	2025-05-04 05:47:37.239442+00	\N	\N
d162c96f-4bac-4ee7-9cda-47be04b83ff7	Admin Test User	en	\N	\N	2025-05-04 05:52:50.636874+00	2025-05-04 05:52:50.636874+00	\N	\N
1000eaac-6a55-422b-8014-1d256768f35a	Admin Test User	en	\N	\N	2025-05-04 06:08:44.794348+00	2025-05-04 06:08:44.794348+00	\N	\N
c2675670-1e64-4070-8f3b-ffc9eb65771c	Admin Test User	en	\N	\N	2025-05-04 06:17:54.409008+00	2025-05-04 06:17:54.409008+00	\N	\N
3e82b7d1-7048-4c52-8a33-5091166f2abd	Admin Test User	en	\N	\N	2025-05-04 06:18:03.023795+00	2025-05-04 06:18:03.023795+00	\N	\N
49bd458c-45b6-4791-a3af-0ddf710d3a25	Son Nguyen	en	\N	\N	2025-05-04 06:19:12.216322+00	2025-05-04 06:19:12.216322+00	\N	\N
a98f13ae-9a46-4dc9-9fa3-6f040627f650	\N	en	\N	\N	2025-05-06 02:51:35.070459+00	2025-05-06 02:51:35.070459+00	\N	\N
ce66af4f-1e75-4cea-929c-e90a42ae5cf7		en	\N	\N	2025-05-12 03:59:44.694274+00	2025-05-12 03:59:44.694274+00	\N	\N
\.


--
-- Data for Name: user_profiles_temp; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."user_profiles_temp" ("id", "full_name", "preferred_language", "avatar_url", "phone_number", "created_at", "updated_at", "metadata", "last_active_at", "source_table") FROM stdin;
7dd52d59-990b-4ddc-a829-bae7ee4627b9	Test User	en	\N	\N	2025-05-04 05:18:22.566727+00	2025-05-04 05:18:22.566727+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
0059a3c0-fb9f-42a5-99fa-8400f4a11823	Test name	en	\N	\N	2025-05-04 05:29:16.674439+00	2025-05-04 05:29:16.674439+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	Admin User	en	\N	\N	2025-05-04 05:33:33.617892+00	2025-05-04 05:33:33.617892+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
60fdb8b3-9430-4bfd-9429-5765b6adcf75	Admin User	en	\N	\N	2025-05-04 05:37:45.471247+00	2025-05-04 05:37:45.471247+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
9983779e-0d4f-486b-ab5d-0878a4f7f868	Admin User	en	\N	\N	2025-05-04 05:46:16.513782+00	2025-05-04 05:46:16.513782+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
0b58bbf2-4ae5-4ba1-9f98-6a78ee8ee133	Test	en	\N	\N	2025-05-04 05:47:37.239442+00	2025-05-04 05:47:37.239442+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
d162c96f-4bac-4ee7-9cda-47be04b83ff7	Admin Test User	en	\N	\N	2025-05-04 05:52:50.636874+00	2025-05-04 05:52:50.636874+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
1000eaac-6a55-422b-8014-1d256768f35a	Admin Test User	en	\N	\N	2025-05-04 06:08:44.794348+00	2025-05-04 06:08:44.794348+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
c2675670-1e64-4070-8f3b-ffc9eb65771c	Admin Test User	en	\N	\N	2025-05-04 06:17:54.409008+00	2025-05-04 06:17:54.409008+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
3e82b7d1-7048-4c52-8a33-5091166f2abd	Admin Test User	en	\N	\N	2025-05-04 06:18:03.023795+00	2025-05-04 06:18:03.023795+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
49bd458c-45b6-4791-a3af-0ddf710d3a25	Son Nguyen	en	\N	\N	2025-05-04 06:19:12.216322+00	2025-05-04 06:19:12.216322+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
a98f13ae-9a46-4dc9-9fa3-6f040627f650	\N	en	\N	\N	2025-05-06 02:51:35.070459+00	2025-05-06 02:51:35.070459+00	{"email": "<EMAIL>", "is_active": true, "is_verified": false}	\N	public.users
\.


--
-- Data for Name: user_roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."user_roles" ("id", "user_id", "role_id", "created_at") FROM stdin;
3301b5a3-2e6c-4b7a-a54f-84c1dd7ba9f4	7dd52d59-990b-4ddc-a829-bae7ee4627b9	34760d52-bbc9-49dd-a6f8-b306fd0dfe22	2025-05-04 05:18:22.963138+00
aea97d85-9dd5-4ee1-ab48-0d71420dec77	0059a3c0-fb9f-42a5-99fa-8400f4a11823	34760d52-bbc9-49dd-a6f8-b306fd0dfe22	2025-05-04 05:29:17.041694+00
40d816c5-9d3e-44d3-93ab-65d5f726f198	973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	174b9d26-8127-4af6-90ba-5b3275223e67	2025-05-04 05:37:30.682167+00
5c7a9c71-9915-42b4-82ba-db9b08219aee	60fdb8b3-9430-4bfd-9429-5765b6adcf75	34760d52-bbc9-49dd-a6f8-b306fd0dfe22	2025-05-04 05:37:45.823047+00
f2e71178-4164-4ff8-846e-b757774f1714	9983779e-0d4f-486b-ab5d-0878a4f7f868	34760d52-bbc9-49dd-a6f8-b306fd0dfe22	2025-05-04 05:46:16.870372+00
e36c63e4-438e-4fb0-9ce4-d3793570ab8a	0b58bbf2-4ae5-4ba1-9f98-6a78ee8ee133	34760d52-bbc9-49dd-a6f8-b306fd0dfe22	2025-05-04 05:47:37.591128+00
2b8b8391-076c-41b2-bf64-2fef6e632122	d162c96f-4bac-4ee7-9cda-47be04b83ff7	34760d52-bbc9-49dd-a6f8-b306fd0dfe22	2025-05-04 05:52:50.963969+00
e5065fcf-2054-4811-8735-1060a9ac973d	1000eaac-6a55-422b-8014-1d256768f35a	34760d52-bbc9-49dd-a6f8-b306fd0dfe22	2025-05-04 06:08:45.059398+00
d052563c-7bd2-4238-8ea1-a05852862e98	c2675670-1e64-4070-8f3b-ffc9eb65771c	34760d52-bbc9-49dd-a6f8-b306fd0dfe22	2025-05-04 06:17:54.737141+00
b613bbbb-b668-489d-a3dd-70ce9c42565d	3e82b7d1-7048-4c52-8a33-5091166f2abd	34760d52-bbc9-49dd-a6f8-b306fd0dfe22	2025-05-04 06:18:03.363958+00
59a96ceb-2a39-4289-970b-6f157423309b	49bd458c-45b6-4791-a3af-0ddf710d3a25	34760d52-bbc9-49dd-a6f8-b306fd0dfe22	2025-05-04 06:19:12.496452+00
00f20627-cb57-4b67-8c75-bb23d7fa5c3c	49bd458c-45b6-4791-a3af-0ddf710d3a25	174b9d26-8127-4af6-90ba-5b3275223e67	2025-05-04 06:21:49.712009+00
1a1fbde0-454a-42c0-a2e2-6efa57cdee5c	a98f13ae-9a46-4dc9-9fa3-6f040627f650	34760d52-bbc9-49dd-a6f8-b306fd0dfe22	2025-05-06 02:51:35.495918+00
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY "public"."users" ("id", "email", "password_hash", "full_name", "preferred_language", "avatar_url", "phone_number", "is_active", "is_verified", "created_at", "updated_at") FROM stdin;
7dd52d59-990b-4ddc-a829-bae7ee4627b9	<EMAIL>	$2b$10$qH76x0h78LA7GtGFU4HnTOHD07TWRgXwK2BCbg/dNG8HOSrBMUTW2	Test User	en	\N	\N	t	f	2025-05-04 05:18:22.566727+00	2025-05-04 05:18:22.566727+00
0059a3c0-fb9f-42a5-99fa-8400f4a11823	<EMAIL>	$2b$10$yZqFq.eW3Bqfv3IwO6nDue8Vr57PlDzKpuhmLMefqepppGu7LOpTW	Test name	en	\N	\N	t	f	2025-05-04 05:29:16.674439+00	2025-05-04 05:29:16.674439+00
973c8e99-9b06-437a-9ab5-e4bdf2aa4b53	<EMAIL>	$2b$10$RyL481dQNVf5NDWaWKyOQuFXyIWT4DNa1IKLKTYqzoGD36Zxg1.26	Admin User	en	\N	\N	t	f	2025-05-04 05:33:33.617892+00	2025-05-04 05:33:33.617892+00
60fdb8b3-9430-4bfd-9429-5765b6adcf75	<EMAIL>	$2b$10$QBQtipG33AvlUAP7vXql9.wqjH9jFBJ7kH5oy08hAQ7a8uHuZvOsS	Admin User	en	\N	\N	t	f	2025-05-04 05:37:45.471247+00	2025-05-04 05:37:45.471247+00
9983779e-0d4f-486b-ab5d-0878a4f7f868	<EMAIL>	$2b$10$sGNRHZnQoOW4jsybZVXpneCGiXCMRlQxGxidqdZXUezG2lTpwt/ni	Admin User	en	\N	\N	t	f	2025-05-04 05:46:16.513782+00	2025-05-04 05:46:16.513782+00
0b58bbf2-4ae5-4ba1-9f98-6a78ee8ee133	<EMAIL>	$2b$10$gG1RXKg/IJSytSx/XlQ6.ett4UOh8H8bQraHioPLt7n3/8sXdlbyG	Test	en	\N	\N	t	f	2025-05-04 05:47:37.239442+00	2025-05-04 05:47:37.239442+00
d162c96f-4bac-4ee7-9cda-47be04b83ff7	<EMAIL>	$2b$10$zt0vI72RQKh4TEzCLff0T.gnzCMnGtM0r7rGd0ytXWnQLPvVc2z2K	Admin Test User	en	\N	\N	t	f	2025-05-04 05:52:50.636874+00	2025-05-04 05:52:50.636874+00
1000eaac-6a55-422b-8014-1d256768f35a	<EMAIL>	$2b$10$qX3f98QJGN0a/DXTIEJBrueJI7EzZLoRNx0z8ISCS8bGl3SyLv28a	Admin Test User	en	\N	\N	t	f	2025-05-04 06:08:44.794348+00	2025-05-04 06:08:44.794348+00
c2675670-1e64-4070-8f3b-ffc9eb65771c	<EMAIL>	$2b$10$NgAj1RSCCjzxWjGcxUKrrO8S7kZmpcBLOVk949k26vIHBbqyNwE6.	Admin Test User	en	\N	\N	t	f	2025-05-04 06:17:54.409008+00	2025-05-04 06:17:54.409008+00
3e82b7d1-7048-4c52-8a33-5091166f2abd	<EMAIL>	$2b$10$qqRUBqK5DLpCfZ4Ysm4JP.k4m18bHe7lq0RLtTQay5HJMijtkfo4e	Admin Test User	en	\N	\N	t	f	2025-05-04 06:18:03.023795+00	2025-05-04 06:18:03.023795+00
49bd458c-45b6-4791-a3af-0ddf710d3a25	<EMAIL>	$2b$10$zstgfS1ratY2mHwbyQ4i/uVgsDjg9zMXGLtbV3S/jbyfRYgjqS3.u	Son Nguyen	en	\N	\N	t	f	2025-05-04 06:19:12.216322+00	2025-05-04 06:19:12.216322+00
a98f13ae-9a46-4dc9-9fa3-6f040627f650	<EMAIL>	$2b$10$vtSayGFucZkHPH5.15aukuNeV11xKFUGV5ywHhq5.jnPkui9TTKAi	\N	en	\N	\N	t	f	2025-05-06 02:51:35.070459+00	2025-05-06 02:51:35.070459+00
\.


--
-- Data for Name: buckets; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY "storage"."buckets" ("id", "name", "owner", "created_at", "updated_at", "public", "avif_autodetection", "file_size_limit", "allowed_mime_types", "owner_id") FROM stdin;
\.


--
-- Data for Name: objects; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY "storage"."objects" ("id", "bucket_id", "name", "owner", "created_at", "updated_at", "last_accessed_at", "metadata", "version", "owner_id", "user_metadata") FROM stdin;
\.


--
-- Data for Name: s3_multipart_uploads; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY "storage"."s3_multipart_uploads" ("id", "in_progress_size", "upload_signature", "bucket_id", "key", "version", "owner_id", "created_at", "user_metadata") FROM stdin;
\.


--
-- Data for Name: s3_multipart_uploads_parts; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

COPY "storage"."s3_multipart_uploads_parts" ("id", "upload_id", "size", "part_number", "bucket_id", "key", "etag", "owner_id", "version", "created_at") FROM stdin;
\.


--
-- Data for Name: secrets; Type: TABLE DATA; Schema: vault; Owner: supabase_admin
--

COPY "vault"."secrets" ("id", "name", "description", "secret", "key_id", "nonce", "created_at", "updated_at") FROM stdin;
\.


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE SET; Schema: auth; Owner: supabase_auth_admin
--

SELECT pg_catalog.setval('"auth"."refresh_tokens_id_seq"', 5, true);


--
-- PostgreSQL database dump complete
--

RESET ALL;
