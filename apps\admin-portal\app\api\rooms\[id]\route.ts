import { createClient } from '../../../../lib/supabase/server';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Hàm đọc tenant_id từ file license_config.json
function getTenantIdFromConfig() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Lấy tenant_id từ config file
    const tenant_id = getTenantIdFromConfig();
    
    if (!tenant_id) {
      return NextResponse.json({
        error: 'Không thể tìm thấy Tenant ID. Vui lòng kích hoạt license.'
      }, { status: 400 });
    }
    
    // Truy vấn phòng theo ID và tenant_id
    const { data, error } = await supabase
      .from('tenant_rooms')
      .select('*')
      .eq('id', params.id)
      .eq('tenant_id', tenant_id) // Thêm điều kiện lọc theo tenant_id
      .single();
    
    if (error) {
      console.error('Error fetching room:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    if (!data) {
      return NextResponse.json({ error: 'Room not found' }, { status: 404 });
    }
    
    // Lấy thông tin khách nếu có
    const { data: guests, error: guestError } = await supabase
      .from('tenant_guests')
      .select('id, full_name, email, phone, check_in, check_out, is_active')
      .eq('room_number', data.room_number)
      .eq('tenant_id', tenant_id)
      .eq('is_active', true);
    
    // Trả về dữ liệu phòng kèm thông tin khách
    return NextResponse.json({ 
      data: {
        ...data,
        tenant_guests: guests || []
      } 
    });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    const body = await request.json();
    
    // Lấy tenant_id từ config file
    const tenant_id = getTenantIdFromConfig();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Không thể tìm thấy Tenant ID. Vui lòng kích hoạt license.' }, { status: 400 });
    }

    const { 
      room_number, 
      room_type, 
      floor, 
      room_category,
      description,
      image_url,
      status,
      is_active,
      reception_point_id  // Thêm reception_point_id
    } = body;

    // Kiểm tra phòng tồn tại và thuộc tenant này
    const { data: existingRoom, error: checkError } = await supabase
      .from('tenant_rooms')
      .select('id')
      .eq('id', params.id)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError || !existingRoom) {
      return NextResponse.json({ error: 'Phòng không tồn tại hoặc không thuộc tenant này' }, { status: 404 });
    }

    // Kiểm tra reception_point_id nếu được cung cấp
    if (reception_point_id) {
      const { data: receptionPoint, error: rpError } = await supabase
        .from('tenant_message_reception_points')
        .select('id')
        .eq('id', reception_point_id)
        .eq('tenant_id', tenant_id)
        .single();

      if (rpError || !receptionPoint) {
        return NextResponse.json({ error: 'Reception point không tồn tại hoặc không thuộc tenant này' }, { status: 400 });
      }
    }

    // Cập nhật phòng
    const { data, error } = await supabase
      .from('tenant_rooms')
      .update({
        room_number,
        room_type,
        floor,
        room_category,
        description,
        image_url,
        status,
        is_active,
        reception_point_id: reception_point_id || null // Thêm trường reception_point_id
      })
      .eq('id', params.id)
      .eq('tenant_id', tenant_id)
      .select();

    if (error) {
      console.error('Error updating room:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data: data[0] });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Lấy tenant_id từ config file
    const tenant_id = getTenantIdFromConfig();
    
    if (!tenant_id) {
      return NextResponse.json({
        error: 'Không thể tìm thấy Tenant ID. Vui lòng kích hoạt license.'
      }, { status: 400 });
    }
    
    // Kiểm tra phòng tồn tại và thuộc tenant này
    const { data: existingRoom, error: checkError } = await supabase
      .from('tenant_rooms')
      .select('id')
      .eq('id', params.id)
      .eq('tenant_id', tenant_id)
      .single();
    
    if (checkError || !existingRoom) {
      return NextResponse.json({
        error: 'Phòng không tồn tại hoặc không thuộc tenant này'
      }, { status: 404 });
    }
    
    // Xóa phòng
    const { error } = await supabase
      .from('tenant_rooms')
      .delete()
      .eq('id', params.id)
      .eq('tenant_id', tenant_id); // Thêm điều kiện tenant_id để đảm bảo an toàn
    
    if (error) {
      console.error('Error deleting room:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
