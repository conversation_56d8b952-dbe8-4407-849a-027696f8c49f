.licensesContainer {
  padding: 24px;
  
  .pageHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .titleSection {
      .pageTitle {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }
      
      .pageDescription {
        font-size: 14px;
        color: #666;
        margin: 0;
      }
    }
    
    .actions {
      display: flex;
      gap: 12px;
    }
  }
  
  .filtersContainer {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    
    .searchBar {
      flex: 1;
      
      .searchInput {
        width: 100%;
        height: 40px;
        padding: 0 12px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        font-size: 14px;
        
        &:focus {
          outline: none;
          border-color: #2563eb;
        }
      }
    }
    
    .filters {
      display: flex;
      gap: 12px;
      
      .filterSelect {
        height: 40px;
        padding: 0 12px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        font-size: 14px;
        min-width: 150px;
        
        &:focus {
          outline: none;
          border-color: #2563eb;
        }
      }
    }
  }
  
  .statsCards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 24px;
    
    .statCard {
      padding: 16px;
      
      .statTitle {
        font-size: 14px;
        color: #666;
        margin: 0 0 8px 0;
      }
      
      .statValue {
        font-size: 24px;
        font-weight: 600;
        margin: 0;
      }
      
      .statFooter {
        font-size: 12px;
        color: #666;
        margin-top: 8px;
      }
    }
  }
  
  .tableCard {
    overflow: hidden;
  }
  
  .licenseKeyCell {
    display: flex;
    flex-direction: column;
    
    .licenseLink {
      color: #2563eb;
      font-weight: 500;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    .customerName {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }
  }
  
  .expiryCell {
    .daysRemaining {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }
  }
  
  .actions {
    display: flex;
    gap: 8px;
    align-items: center;
    
    .alertBadge {
      background-color: #ef4444;
      color: white;
      font-size: 12px;
      padding: 2px 6px;
      border-radius: 10px;
    }
  }
  
  .errorMessage {
    background-color: #fef2f2;
    color: #b91c1c;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 24px;
  }
}