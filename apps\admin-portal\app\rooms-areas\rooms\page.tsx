'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import DashboardLayout from '../../dashboard-layout';
import RoomCard from '../../components/rooms/RoomCard';
import styles from './rooms.module.scss';

interface Room {
  id: string;
  room_number: string;
  room_type: string;
  room_category: string;
  floor: string;
  status: 'available' | 'occupied' | 'maintenance' | 'cleaning';
  tenant_guests?: {
    id: string;
    full_name: string;
    check_in: string;
    check_out: string | null;
  }[];
}

export default function RoomsListPage() {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [floorFilter, setFloorFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  
  useEffect(() => {
    const fetchRooms = async () => {
      try {
        setLoading(true);
        // Build query string for filters
        const queryParams = new URLSearchParams();
        if (categoryFilter) queryParams.append('category', categoryFilter);
        if (floorFilter) queryParams.append('floor', floorFilter);
        if (statusFilter) queryParams.append('status', statusFilter);
        
        const response = await fetch(`/api/rooms?${queryParams.toString()}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch rooms');
        }
        
        const data = await response.json();
        setRooms(data.data || []);
      } catch (err: any) {
        console.error(err);
        setError(err.message || 'An error occurred while fetching rooms');
      } finally {
        setLoading(false);
      }
    };
    
    fetchRooms();
  }, [categoryFilter, floorFilter, statusFilter]);
  
  // Filter rooms by search term
  const filteredRooms = rooms.filter(room =>
    room.room_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (room.tenant_guests?.[0]?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()))
  );
  
  const getStatusText = (status: string) => {
    switch (status) {
      case 'available':
        return 'Trống';
      case 'occupied':
        return 'Có khách';
      case 'maintenance':
        return 'Bảo trì';
      case 'cleaning':
        return 'Đang dọn';
      default:
        return '';
    }
  };
  
  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <Link href="/rooms-areas" className={styles.backLink}>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15.8333 10H4.16666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M9.99999 15.8334L4.16666 10.0001L9.99999 4.16675" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Dashboard
            </Link>
            <h1 className={styles.title}>Danh sách phòng</h1>
          </div>
          <div className={styles.actions}>
            <div className={styles.viewToggle}>
              <button
                className={`${styles.viewToggleButton} ${viewMode === 'grid' ? styles.active : ''}`}
                onClick={() => setViewMode('grid')}
                title="Xem dạng lưới"
              >
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <rect x="3" y="3" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
                  <rect x="11" y="3" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
                  <rect x="3" y="11" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
                  <rect x="11" y="11" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5"/>
                </svg>
              </button>
              <button
                className={`${styles.viewToggleButton} ${viewMode === 'table' ? styles.active : ''}`}
                onClick={() => setViewMode('table')}
                title="Xem dạng bảng"
              >
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M3 5C3 3.89543 3.89543 3 5 3H15C16.1046 3 17 3.89543 17 5V15C17 16.1046 16.1046 17 15 17H5C3.89543 17 3 16.1046 3 15V5Z" stroke="currentColor" strokeWidth="1.5"/>
                  <path d="M3 8H17" stroke="currentColor" strokeWidth="1.5"/>
                  <path d="M3 12H17" stroke="currentColor" strokeWidth="1.5"/>
                  <path d="M7 8V17" stroke="currentColor" strokeWidth="1.5"/>
                </svg>
              </button>
            </div>
            <Link href="/rooms-areas/rooms/create" className={styles.createButton}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M8 3.33334V12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3.33334 8H12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Thêm phòng
            </Link>
          </div>
        </div>

        <div className={styles.filters}>
          <div className={styles.searchBox}>
            <input
              type="text"
              placeholder="Tìm kiếm phòng..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
            <svg className={styles.searchIcon} width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M17.5 17.5L12.5 12.5M14.1667 8.33333C14.1667 11.555 11.555 14.1667 8.33333 14.1667C5.11167 14.1667 2.5 11.555 2.5 8.33333C2.5 5.11167 5.11167 2.5 8.33333 2.5C11.555 2.5 14.1667 5.11167 14.1667 8.33333Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          
          <div className={styles.filterGroup}>
            <select 
              className={styles.filterSelect}
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              <option value="">Tất cả loại phòng</option>
              <option value="Standard">Phòng Standard</option>
              <option value="Deluxe">Phòng Deluxe</option>
              <option value="Suite">Phòng Suite</option>
              <option value="Family">Phòng Family</option>
            </select>
            
            <select 
              className={styles.filterSelect}
              value={floorFilter}
              onChange={(e) => setFloorFilter(e.target.value)}
            >
              <option value="">Tất cả tầng</option>
              <option value="1">Tầng 1</option>
              <option value="2">Tầng 2</option>
              <option value="3">Tầng 3</option>
              <option value="4">Tầng 4</option>
              <option value="5">Tầng 5</option>
            </select>
            
            <select 
              className={styles.filterSelect}
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">Tất cả trạng thái</option>
              <option value="available">Trống</option>
              <option value="occupied">Có khách</option>
              <option value="maintenance">Bảo trì</option>
              <option value="cleaning">Đang dọn</option>
            </select>
          </div>
        </div>
        
        {loading ? (
          <div className={styles.loading}>Đang tải danh sách phòng...</div>
        ) : error ? (
          <div className={styles.error}>
            <p>Lỗi: {error}</p>
            <button onClick={() => window.location.reload()} className={styles.retryButton}>
              Thử lại
            </button>
          </div>
        ) : filteredRooms.length === 0 ? (
          <div className={styles.emptyState}>
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="8" y1="12" x2="16" y2="12"></line>
              <line x1="8" y1="16" x2="16" y2="16"></line>
              <line x1="8" y1="8" x2="16" y2="8"></line>
            </svg>
            <h3>Không tìm thấy phòng</h3>
            <p>
              {searchTerm || categoryFilter || floorFilter || statusFilter
                ? 'Không có phòng nào phù hợp với bộ lọc đã chọn.'
                : 'Chưa có phòng nào được tạo. Hãy tạo phòng mới để bắt đầu.'}
            </p>
            {!searchTerm && !categoryFilter && !floorFilter && !statusFilter && (
              <Link href="/rooms-areas/rooms/create" className={styles.createEmptyButton}>
                Tạo phòng mới
              </Link>
            )}
          </div>
        ) : viewMode === 'grid' ? (
          <div className={styles.roomsGrid}>
            {filteredRooms.map((room) => (
              <RoomCard
                key={room.id}
                id={room.id}
                roomNumber={room.room_number}
                roomType={room.room_type}
                roomCategory={room.room_category || room.room_type}
                floor={room.floor}
                status={room.status || 'available'}
                guest={room.tenant_guests?.[0]}
              />
            ))}
          </div>
        ) : (
          <div className={styles.tableContainer}>
            <table className={styles.tableView}>
              <thead>
                <tr>
                  <th>Số phòng</th>
                  <th>Loại phòng</th>
                  <th>Tầng</th>
                  <th>Trạng thái</th>
                  <th>Khách</th>
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody>
                {filteredRooms.map((room) => (
                  <tr key={room.id}>
                    <td>{room.room_number}</td>
                    <td>{room.room_type} - {room.room_category || room.room_type}</td>
                    <td>{room.floor}</td>
                    <td>
                      <span className={`${styles.statusBadge} ${styles[room.status || 'available']}`}>
                        {getStatusText(room.status || 'available')}
                      </span>
                    </td>
                    <td>{room.tenant_guests?.[0]?.full_name || '-'}</td>
                    <td>
                      <div className={styles.tableActions}>
                        <Link href={`/rooms-areas/rooms/${room.id}`} className={styles.actionLink}>
                          Chi tiết
                        </Link>
                        <Link href={`/rooms-areas/rooms/${room.id}/edit`} className={styles.actionLink}>
                          Sửa
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}