.homepage {
  min-height: 100vh;
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
  padding: 2rem 1rem;
}

.container {
  max-width: 4xl;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  animation: slideDown 0.6s ease-out;
  
  .logo {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: bounce 2s infinite;
  }
  
  .title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ea580c;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #f97316, #ea580c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
  
  .subtitle {
    font-size: 1.125rem;
    color: #7c2d12;
    max-width: 32rem;
    margin: 0 auto;
    line-height: 1.6;
    
    @media (max-width: 768px) {
      font-size: 1rem;
    }
  }
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.featureCard {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  animation: slideUp 0.6s ease-out;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f97316, #ea580c);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(249, 115, 22, 0.15);
    
    &::before {
      transform: scaleX(1);
    }
    
    .icon {
      transform: scale(1.1);
    }
  }
  
  .icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
  }
  
  .title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
  }
  
  .description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
  
  .features {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.25rem 0;
      color: #4b5563;
      font-size: 0.875rem;
      
      &::before {
        content: '✓';
        color: #10b981;
        font-weight: 600;
      }
    }
  }
}

.ctaSection {
  text-align: center;
  animation: slideUp 0.8s ease-out;
  
  .title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    
    @media (max-width: 768px) {
      font-size: 1.5rem;
    }
  }
  
  .description {
    font-size: 1rem;
    color: #6b7280;
    max-width: 28rem;
    margin: 0 auto 2rem;
    line-height: 1.6;
  }
}

.buttonGroup {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.demoButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  text-decoration: none;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px rgba(249, 115, 22, 0.3);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
    
    &::before {
      left: 100%;
    }
  }
  
  &:active {
    transform: translateY(0);
  }
}

.infoButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  background: white;
  color: #374151;
  text-decoration: none;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #f97316;
    color: #f97316;
    transform: translateY(-2px);
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
  }
}

.footer {
  margin-top: 4rem;
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(234, 88, 12, 0.1);
  animation: fadeIn 1s ease-out;
  
  .footerContent {
    color: #7c2d12;
    font-size: 0.875rem;
    line-height: 1.6;
    
    .links {
      display: flex;
      justify-content: center;
      gap: 2rem;
      margin-top: 1rem;
      flex-wrap: wrap;
      
      @media (max-width: 640px) {
        gap: 1rem;
      }
      
      a {
        color: #ea580c;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
        
        &:hover {
          color: #dc2626;
        }
      }
    }
  }
}

// Animations
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-30px);
  }
  70% {
    transform: translateY(-15px);
  }
  90% {
    transform: translateY(-4px);
  }
}

// Responsive design
@media (max-width: 640px) {
  .homepage {
    padding: 1rem 0.5rem;
  }
  
  .header {
    margin-bottom: 2rem;
    
    .logo {
      font-size: 2.5rem;
    }
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .featureCard {
    padding: 1.5rem;
  }
  
  .buttonGroup {
    flex-direction: column;
    align-items: center;
    
    .demoButton,
    .infoButton {
      width: 100%;
      max-width: 20rem;
      justify-content: center;
    }
  }
}
