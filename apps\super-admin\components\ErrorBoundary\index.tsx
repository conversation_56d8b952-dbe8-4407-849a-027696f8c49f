'use client';
import React, { Component, ErrorInfo, ReactNode } from 'react';
import styles from './styles.module.scss';
import { Button } from '@loaloa/ui';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export default class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    // Ở đây có thể gửi lỗi đến service logging của bạn
  }

  render(): ReactNode {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className={styles.errorContainer}>
          <div className={styles.errorContent}>
            <h2>Something went wrong</h2>
            <p>We're sorry, but we encountered an error while loading this page.</p>
            {this.state.error && (
              <div className={styles.errorDetails}>
                <p className={styles.errorMessage}>{this.state.error.message}</p>
              </div>
            )}
            <div className={styles.actions}>
              <Button 
                variant="primary"
                label="Reload Page" 
                onClick={() => window.location.reload()} 
              />
              <Button 
                variant="outline"
                label="Go to Dashboard" 
                onClick={() => window.location.href = '/'} 
              />
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
