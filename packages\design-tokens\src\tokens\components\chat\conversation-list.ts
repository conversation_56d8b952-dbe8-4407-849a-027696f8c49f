import { spacing } from '../../primitives/spacing';
import { borders } from '../../primitives/borders';
import { typography } from '../../primitives/typography';

export const conversationListTokens = {
  // Item
  item: {
    padding: `${spacing[3]} ${spacing[4]}`,
    height: '72px',
    borderRadius: borders.radii.md
  },
  
  // Typography
  title: {
    fontSize: typography.fontSizes.base,
    fontWeight: typography.fontWeights.medium
  },
  
  lastMessage: {
    fontSize: typography.fontSizes.sm,
    maxLines: 1,
    lineHeight: typography.lineHeights.tight
  },
  
  // Avatar
  avatar: {
    size: '48px',
    marginRight: spacing[3]
  },
  
  // Badge
  badge: {
    size: '22px',
    fontSize: typography.fontSizes.xs
  },
  
  // Time
  time: {
    fontSize: typography.fontSizes.xs
  }
};
