import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Lấy user ID từ params
    const userId = params.id;

    // Lấy tenant_id từ file config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json(
        { error: 'Tenant ID not found. Please activate your license.' },
        { status: 400 }
      );
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Lấy thông tin user từ tenant_users kèm theo tenant_users_details
    const { data: user, error } = await supabase
      .from('tenant_users')
      .select(`
        id,
        user_id,
        role,
        is_active,
        joined_at,
        last_login_at,
        expiry_date,
        tenant_users_details (
          id,
          email,
          display_name,
          avatar_url,
          phone,
	  department,
          title,        
          preferred_language
        )
      `)
      .eq('id', userId)
      .eq('tenant_id', tenant_id)
      .single();

    // Xử lý lỗi nếu có
    if (error) {
      console.error('Error fetching user:', error);
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      return NextResponse.json(
        { error: 'Error fetching user details' },
        { status: 500 }
      );
    }

    // Trả về dữ liệu user
    return NextResponse.json({ data: user });
  } catch (error: any) {
    console.error('Error in GET user:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Lấy user ID từ params
    const userId = params.id;
    const updateData = await request.json();

    // Lấy tenant_id từ file config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json(
        { error: 'Tenant ID not found. Please activate your license.' },
        { status: 400 }
      );
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra xem user có tồn tại không
    const { data: existingUser, error: checkError } = await supabase
      .from('tenant_users')
      .select('id')
      .eq('id', userId)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError) {
      console.error('Error checking user:', checkError);
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      return NextResponse.json(
        { error: 'Error checking user' },
        { status: 500 }
      );
    }

    // Cập nhật thông tin cơ bản của user
    const userUpdateData: any = {};
    if (updateData.role) userUpdateData.role = updateData.role;
    if (typeof updateData.is_active !== 'undefined')
      userUpdateData.is_active = updateData.is_active;

    // Cập nhật tenant_users nếu có dữ liệu
    if (Object.keys(userUpdateData).length > 0) {
      const { error: updateError } = await supabase
        .from('tenant_users')
        .update(userUpdateData)
        .eq('id', userId)
        .eq('tenant_id', tenant_id);

      if (updateError) {
        console.error('Error updating user:', updateError);
        return NextResponse.json(
          { error: 'Error updating user' },
          { status: 500 }
        );
      }
    }

    // Cập nhật thông tin chi tiết của user
    const detailsUpdateData: any = {};
if (updateData.display_name) detailsUpdateData.display_name = updateData.display_name;
if (updateData.phone) detailsUpdateData.phone = updateData.phone;
if (updateData.department !== undefined) detailsUpdateData.department = updateData.department;
if (updateData.title !== undefined) detailsUpdateData.title = updateData.title;
if (updateData.preferred_language) detailsUpdateData.preferred_language = updateData.preferred_language;

    // Cập nhật tenant_users_details nếu có dữ liệu
    if (Object.keys(detailsUpdateData).length > 0) {
      const { error: detailsUpdateError } = await supabase
        .from('tenant_users_details')
        .update(detailsUpdateData)
        .eq('tenant_user_id', userId);

      if (detailsUpdateError) {
        console.error('Error updating user details:', detailsUpdateError);
        return NextResponse.json(
          { error: 'Error updating user details' },
          { status: 500 }
        );
      }
    }

    // Lấy thông tin user đã cập nhật
    const { data: updatedUser, error: fetchError } = await supabase
      .from('tenant_users')
      .select(`
        id,
        role,
        is_active,
        joined_at,
        last_login_at,
        expiry_date,
        tenant_users_details (
          id,
          email,
          display_name,
          avatar_url,
	  department,
          title,
          phone,       
          preferred_language
        )
      `)
      .eq('id', userId)
      .eq('tenant_id', tenant_id)
      .single();

    if (fetchError) {
      console.error('Error fetching updated user:', fetchError);
      return NextResponse.json(
        { error: 'User updated but unable to fetch details' },
        { status: 206 }
      );
    }

    // Trả về dữ liệu user đã cập nhật
    return NextResponse.json({
      data: updatedUser,
      message: 'User updated successfully',
    });
  } catch (error: any) {
    console.error('Error in PATCH user:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Lấy user ID từ params
    const userId = params.id;

    // Lấy tenant_id từ file config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json(
        { error: 'Tenant ID not found. Please activate your license.' },
        { status: 400 }
      );
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra xem user có tồn tại không
    const { data: existingUser, error: checkError } = await supabase
      .from('tenant_users')
      .select('id')
      .eq('id', userId)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      return NextResponse.json(
        { error: 'Error checking user' },
        { status: 500 }
      );
    }

    // Xóa user
    const { error: deleteError } = await supabase
      .from('tenant_users')
      .delete()
      .eq('id', userId)
      .eq('tenant_id', tenant_id);

    if (deleteError) {
      console.error('Error deleting user:', deleteError);
      return NextResponse.json(
        { error: 'Error deleting user' },
        { status: 500 }
      );
    }

    // Trả về kết quả thành công
    return NextResponse.json({ message: 'User deleted successfully' });
  } catch (error: any) {
    console.error('Error in DELETE user:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
