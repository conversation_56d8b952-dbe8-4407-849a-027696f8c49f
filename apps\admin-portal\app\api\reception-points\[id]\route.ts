import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Lấy chi tiết điểm nhận tin nhắn theo ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const pointId = params.id;

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Truy vấn chi tiết điểm nhận tin nhắn
    const { data, error } = await supabase
      .from('tenant_message_reception_points')
      .select('*')
      .eq('id', pointId)
      .eq('tenant_id', tenant_id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Reception point not found' }, { status: 404 });
      }
      console.error('Error fetching reception point:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Error in GET reception point:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}

// PUT: Cập nhật điểm nhận tin nhắn
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const pointId = params.id;
    const updateData = await request.json();

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra điểm nhận tin nhắn có tồn tại không
    const { data: existingPoint, error: checkError } = await supabase
      .from('tenant_message_reception_points')
      .select('id, code')
      .eq('id', pointId)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Reception point not found' }, { status: 404 });
      }
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    // Nếu cập nhật code, kiểm tra xem code mới có trùng với code của điểm khác không
    if (updateData.code && updateData.code !== existingPoint.code) {
      const { data: existingCode, error: codeError } = await supabase
        .from('tenant_message_reception_points')
        .select('id')
        .eq('tenant_id', tenant_id)
        .eq('code', updateData.code)
        .neq('id', pointId)
        .maybeSingle();

      if (codeError && codeError.code !== 'PGRST116') {
        console.error('Error checking code uniqueness:', codeError);
        return NextResponse.json({ error: codeError.message }, { status: 500 });
      }

      if (existingCode) {
        return NextResponse.json({ error: `Reception point with code "${updateData.code}" already exists` }, { status: 409 });
      }
    }

    // Chuẩn bị dữ liệu cập nhật
    const pointUpdateData: any = {
      updated_at: new Date().toISOString()
    };

    // Chỉ cập nhật các trường có cung cấp
    if (updateData.name !== undefined) pointUpdateData.name = updateData.name;
    if (updateData.code !== undefined) pointUpdateData.code = updateData.code;
    if (updateData.description !== undefined) pointUpdateData.description = updateData.description;
    if (updateData.icon_url !== undefined) pointUpdateData.icon_url = updateData.icon_url;
    if (updateData.priority !== undefined) pointUpdateData.priority = updateData.priority;
    if (updateData.is_active !== undefined) pointUpdateData.is_active = updateData.is_active;

    // Cập nhật điểm nhận tin nhắn
    const { data, error } = await supabase
      .from('tenant_message_reception_points')
      .update(pointUpdateData)
      .eq('id', pointId)
      .eq('tenant_id', tenant_id)
      .select()
      .single();

    if (error) {
      console.error('Error updating reception point:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({ data, message: 'Reception point updated successfully' });
  } catch (error: any) {
    console.error('Error in PUT reception point:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}

// DELETE: Xóa điểm nhận tin nhắn
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const pointId = params.id;

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra điểm nhận tin nhắn có tồn tại không
    const { data: existingPoint, error: checkError } = await supabase
      .from('tenant_message_reception_points')
      .select('id')
      .eq('id', pointId)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Reception point not found' }, { status: 404 });
      }
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    // Xóa điểm nhận tin nhắn
    const { error } = await supabase
      .from('tenant_message_reception_points')
      .delete()
      .eq('id', pointId)
      .eq('tenant_id', tenant_id);

    if (error) {
      console.error('Error deleting reception point:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({ message: 'Reception point deleted successfully' });
  } catch (error: any) {
    console.error('Error in DELETE reception point:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}
