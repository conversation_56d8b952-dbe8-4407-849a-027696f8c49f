'use client';
import React, { createContext, useContext, useState, useEffect } from 'react';
import styles from './styles.module.scss';

// Tạo context cho LoadingIndicator
interface LoadingContextType {
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
}

const LoadingContext = createContext<LoadingContextType>({
  isLoading: false,
  setLoading: () => {},
});

// Hook để sử dụng LoadingIndicator
export const useLoadingIndicator = () => useContext(LoadingContext);

// Provider component
export const LoadingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingRequests, setLoadingRequests] = useState(0);

  useEffect(() => {
    setIsLoading(loadingRequests > 0);
  }, [loadingRequests]);

  const setLoading = (loading: boolean) => {
    setLoadingRequests(prev => loading ? prev + 1 : Math.max(0, prev - 1));
  };

  return (
    <LoadingContext.Provider value={{ isLoading, setLoading }}>
      {children}
      {isLoading && <LoadingIndicator />}
    </LoadingContext.Provider>
  );
};

// Loading Indicator component
const LoadingIndicator: React.FC = () => {
  return (
    <div className={styles.loadingContainer}>
      <div className={styles.spinner}>
        <div className={styles.bounce1}></div>
        <div className={styles.bounce2}></div>
        <div className={styles.bounce3}></div>
      </div>
    </div>
  );
};
