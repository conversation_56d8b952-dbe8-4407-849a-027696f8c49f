.dashboard {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  
  @media (max-width: 1200px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
}

.statCard {
  min-height: 160px;
}

.recentTenantsCard {
  width: 100%;
}

.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  
  h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.tenantCell {
  display: flex;
  flex-direction: column;
}

.tenantName {
  font-weight: 500;
  color: var(--color-text);
  
  &:hover {
    color: var(--color-primary);
  }
}

.tenantDomain {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.planBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  
  &.free {
    background-color: var(--color-background-secondary);
    color: var(--color-text-secondary);
  }
  
  &.basic {
    background-color: #E3F2FD;
    color: #1565C0;
  }
  
  &.premium {
    background-color: #FFF8E1;
    color: #F57F17;
  }
  
  &.enterprise {
    background-color: #E8F5E9;
    color: #2E7D32;
  }
}

.quickActions {
  margin-top: 8px;
}

.sectionTitle {
  margin-bottom: 16px;
  font-size: 1.25rem;
  font-weight: 600;
}

.actionButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  
  @media (max-width: 640px) {
    flex-direction: column;
  }
}

