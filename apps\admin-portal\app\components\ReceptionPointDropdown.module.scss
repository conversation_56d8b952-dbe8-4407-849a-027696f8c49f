.container {
  width: 100%;
}

.dropdown {
  display: block;
  width: 100%;
  padding: 0.75rem;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  
  &:focus {
    border-color: #1976d2;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
  }
  
  &.error {
    border-color: #f44336;
  }
  
  &:disabled {
    background-color: #e9ecef;
    opacity: 1;
  }
}

.errorMessage {
  color: #f44336;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}
