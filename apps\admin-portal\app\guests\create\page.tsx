'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Alert } from '@ui';
import styles from './guestForm.module.scss';
import RoomSelectionModal from '../../components/modals/RoomSelectionModal';

export default function CreateGuestPage() {
  const router = useRouter();
  const [guestData, setGuestData] = useState({
    full_name: '',
    email: '',
    phone: '',
    room_number: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tenantId, setTenantId] = useState<string>('');
  const [showRoomModal, setShowRoomModal] = useState(false);

  // Fetch tenant_id
  useEffect(() => {
    const fetchTenantId = async () => {
      try {
        const response = await fetch('/api/tenants');
        const result = await response.json();
        if (response.ok && result.data && result.data.length > 0) {
          setTenantId(result.data[0].id);
        } else {
          console.error('No tenants found or API returned empty result');
        }
      } catch (err) {
        console.error('Error fetching tenant ID:', err);
      }
    };

    fetchTenantId();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setGuestData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleOpenRoomModal = () => {
    setShowRoomModal(true);
  };

  const handleRoomSelection = (roomNumber: string) => {
    setGuestData(prev => ({
      ...prev,
      room_number: roomNumber
    }));
    setShowRoomModal(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!guestData.full_name) {
      setError('Please enter the guest name');
      return;
    }
    if (!tenantId) {
      setError('Could not find tenant information. Please try again later.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const payload = {
        ...guestData,
        tenant_id: tenantId,
      };

      console.log('Sending payload:', payload); // Debug log
      const response = await fetch('/api/guests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || 'Could not create new guest');
      }

      // Navigate to guest detail page
      router.push(`/guests/${result.data.id}`);
    } catch (err) {
      console.error('Error creating guest:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while creating the guest');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.pageHeader}>
        <div className={styles.titleSection}>
          <Link href="/guests" className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.33333 5.83331L4.16666 9.99998L8.33333 14.1666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to Guest List
          </Link>
          <h1 className={styles.pageTitle}>Create New Guest</h1>
        </div>
      </div>

      {error && (
        <Alert 
          variant="error" 
          title="Error" 
          closable 
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      <div className={styles.formCard}>
        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="full_name" className={styles.label}>
              Full Name <span className={styles.required}>*</span>
            </label>
            <input
              type="text"
              id="full_name"
              name="full_name"
              value={guestData.full_name}
              onChange={handleChange}
              className={styles.input}
              placeholder="Enter guest's full name"
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="email" className={styles.label}>
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={guestData.email}
              onChange={handleChange}
              className={styles.input}
              placeholder="Enter email address"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="phone" className={styles.label}>
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={guestData.phone}
              onChange={handleChange}
              className={styles.input}
              placeholder="Enter phone number"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="room_number" className={styles.label}>
              Room
            </label>
            <div className={styles.roomSelection}>
              <input
                type="text"
                id="room_number"
                name="room_number"
                value={guestData.room_number}
                className={styles.input}
                placeholder="No room selected"
                readOnly
              />
              <button 
                type="button" 
                className={styles.roomSelectButton}
                onClick={handleOpenRoomModal}
              >
                Select Room
              </button>
            </div>
            <p className={styles.helpText}>
              Select a room if you want to check-in the guest immediately. If left empty, the guest will be created but not checked-in.
            </p>
          </div>

          <div className={styles.formActions}>
            <Link href="/guests" className={styles.cancelButton}>
              Cancel
            </Link>
            <button
              type="submit"
              className={styles.submitButton}
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Guest'}
            </button>
          </div>
        </form>
      </div>

      {/* Room Selection Modal */}
      <RoomSelectionModal
        isOpen={showRoomModal}
        onClose={() => setShowRoomModal(false)}
        onConfirm={handleRoomSelection}
        title="Select Room for Guest"
      />
    </div>
  );
}
