-- Bảng phòng chat
CREATE TABLE IF NOT EXISTS chat_rooms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255),
  description TEXT,
  room_type VARCHAR(50) NOT NULL DEFAULT 'general', -- general, support, private, group
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  hotel_id UUID, -- <PERSON><PERSON><PERSON> kết với khách sạn nếu có
  room_number VARCHAR(50), -- <PERSON><PERSON> phòng khách sạn nếu có
  metadata JSONB -- Thông tin bổ sung, có thể chứa các thuộc tính tùy chỉnh
);

-- Bảng người tham gia phòng chat
CREATE TABLE IF NOT EXISTS chat_participants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_room_id UUID NOT NULL REFERENCES chat_rooms(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  temporary_user_id UUID REFERENCES temporary_users(id) ON DELETE SET NULL,
  participant_role VARCHAR(50) NOT NULL DEFAULT 'member', -- member, admin, moderator
  is_active BOOLEAN DEFAULT true,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  left_at TIMESTAMP WITH TIME ZONE, -- NULL nếu vẫn đang tham gia
  display_name VARCHAR(255), -- Tên hiển thị tùy chỉnh trong phòng chat
  preferred_language VARCHAR(10), -- Ngôn ngữ ưu tiên cho người tham gia
  last_read_message_id UUID, -- ID của tin nhắn được đọc cuối cùng
  metadata JSONB, -- Thông tin bổ sung
  CHECK (user_id IS NOT NULL OR temporary_user_id IS NOT NULL) -- Ít nhất một trong hai cần có giá trị
);

-- Bảng tin nhắn chat (đã cập nhật để tham chiếu đến chat_participants đã tồn tại)
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_room_id UUID NOT NULL REFERENCES chat_rooms(id) ON DELETE CASCADE,
  participant_id UUID NOT NULL REFERENCES chat_participants(id) ON DELETE CASCADE,
  content TEXT NOT NULL, -- Nội dung gốc của tin nhắn
  content_type VARCHAR(50) DEFAULT 'text', -- text, image, audio, video, file, system
  original_language VARCHAR(10) NOT NULL, -- Mã ngôn ngữ của nội dung gốc
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  edited_at TIMESTAMP WITH TIME ZONE, -- Thời điểm chỉnh sửa gần nhất, NULL nếu chưa chỉnh sửa
  is_delivered BOOLEAN DEFAULT false, -- Đã gửi đến server
  metadata JSONB -- Thông tin bổ sung, có thể chứa URL của đa phương tiện, thông tin tệp...
);

-- Bảng trạng thái đọc tin nhắn
CREATE TABLE IF NOT EXISTS message_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
  participant_id UUID NOT NULL REFERENCES chat_participants(id) ON DELETE CASCADE,
  is_delivered BOOLEAN DEFAULT false, -- Đã gửi đến thiết bị người dùng
  is_read BOOLEAN DEFAULT false, -- Đã được đọc
  delivered_at TIMESTAMP WITH TIME ZONE, -- Thời điểm gửi đến thiết bị người dùng
  read_at TIMESTAMP WITH TIME ZONE, -- Thời điểm đọc
  UNIQUE(message_id, participant_id) -- Mỗi cặp tin nhắn-người tham gia chỉ có một bản ghi trạng thái
);

-- Bảng dịch tin nhắn
CREATE TABLE IF NOT EXISTS message_translations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
  language VARCHAR(10) NOT NULL, -- Mã ngôn ngữ của bản dịch
  translated_content TEXT NOT NULL, -- Nội dung đã dịch
  is_machine_translation BOOLEAN DEFAULT true, -- Đánh dấu là dịch máy hay dịch người
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(message_id, language) -- Mỗi tin nhắn chỉ có một bản dịch cho mỗi ngôn ngữ
);

-- Bảng đính kèm tập tin
CREATE TABLE IF NOT EXISTS message_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
  file_url TEXT NOT NULL, -- URL lưu trữ tập tin
  file_name VARCHAR(255) NOT NULL, -- Tên tập tin gốc
  file_type VARCHAR(100), -- MIME type của tập tin
  file_size INTEGER, -- Kích thước tập tin (bytes)
  thumbnail_url TEXT, -- URL của hình thu nhỏ (nếu có)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Bảng phản ứng với tin nhắn (emoji reactions)
CREATE TABLE IF NOT EXISTS message_reactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
  participant_id UUID NOT NULL REFERENCES chat_participants(id) ON DELETE CASCADE,
  reaction VARCHAR(50) NOT NULL, -- Emoji hoặc mã reaction
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(message_id, participant_id, reaction) -- Mỗi người chỉ có thể thêm một reaction cụ thể cho mỗi tin nhắn
);

-- Tạo trigger để tự động cập nhật trường updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;

$$ LANGUAGE plpgsql;

-- Áp dụng trigger cho các bảng cần updated_at
CREATE TRIGGER update_chat_rooms_updated_at
BEFORE UPDATE ON chat_rooms
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_message_translations_updated_at
BEFORE UPDATE ON message_translations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Indexes để tối ưu hiệu suất
CREATE INDEX IF NOT EXISTS idx_chat_participants_room_id ON chat_participants(chat_room_id);
CREATE INDEX IF NOT EXISTS idx_chat_participants_user_id ON chat_participants(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_chat_participants_temp_user_id ON chat_participants(temporary_user_id) WHERE temporary_user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_chat_messages_room_id ON chat_messages(chat_room_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_participant_id ON chat_messages(participant_id);
CREATE INDEX IF NOT EXISTS idx_message_translations_message_id ON message_translations(message_id);
CREATE INDEX IF NOT EXISTS idx_message_status_message_id ON message_status(message_id);
CREATE INDEX IF NOT EXISTS idx_message_status_participant_id ON message_status(participant_id);
CREATE INDEX IF NOT EXISTS idx_message_attachments_message_id ON message_attachments(message_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sent_at ON chat_messages(sent_at);
