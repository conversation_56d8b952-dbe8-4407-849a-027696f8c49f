import { Request, Response } from 'express';
import * as authService from '../services/authService';
import { verifyToken } from '../utils/jwt';

export const register = async (req: Request, res: Response) => {
  try {
    const { email, password, full_name, preferred_language } = req.body;
    
    console.log('<PERSON><PERSON> xử lý đăng ký cho email:', email);
    
    const user = await authService.register({
      email,
      password,
      full_name,
      preferred_language
    });
    
    if (!user) {
      console.error('Đăng ký thất bại: service trả về null');
      return res.status(400).json({ 
        success: false, 
        message: 'Registration failed' 
      });
    }
    
    console.log('<PERSON><PERSON><PERSON> ký thành công cho email:', email);
    return res.status(201).json({
      success: true,
      data: user
    });
  } catch (error: any) {
    // Log chi tiết lỗi
    console.error('Lỗi chi tiết khi đăng ký:', error);
    console.error('Loại lỗi:', typeof error);
    console.error('Message:', error.message);
    console.error('Stack:', error.stack);
    
    // Kiểm tra chi tiết nếu là lỗi từ Supabase
    if (error.code) {
      console.error('Mã lỗi:', error.code);
    }
    
    if (error.details) {
      console.error('Chi tiết lỗi:', error.details);
    }
    
    // Handle duplicate email error
    if (error.message?.includes('duplicate') || error.message?.includes('unique constraint')) {
      return res.status(409).json({
        success: false,
        message: 'Email already exists'
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      debug: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};


export const login = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;
    
    const tokens = await authService.login({ email, password });
    
    if (!tokens) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
    
    return res.json({
      success: true,
      data: tokens
    });
  } catch (error) {
    console.error('Login controller error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getProfile = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }
    
    const user = await authService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    return res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get profile controller error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const updateProfile = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }
    
    // Only allow specific fields to be updated
    const { full_name, preferred_language, phone_number, avatar_url } = req.body;
    
    const updatedUser = await authService.updateUser(userId, {
      full_name,
      preferred_language,
      phone_number,
      avatar_url
    });
    
    if (!updatedUser) {
      return res.status(400).json({
        success: false,
        message: 'Failed to update profile'
      });
    }
    
    return res.json({
      success: true,
      data: updatedUser
    });
  } catch (error) {
    console.error('Update profile controller error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const changePassword = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized'
      });
    }
    
    const { current_password, new_password } = req.body;
    
    if (!current_password || !new_password) {
      return res.status(400).json({
        success: false,
        message: 'Current password and new password are required'
      });
    }
    
    const success = await authService.changePassword(
      userId,
      current_password,
      new_password
    );
    
    if (!success) {
      return res.status(400).json({
        success: false,
        message: 'Failed to change password. Check your current password.'
      });
    }
    
    return res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Change password controller error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const forgotPassword = async (req: Request, res: Response) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }
    
    const success = await authService.forgotPassword(email);
    
    // Always return success to prevent email enumeration attacks
    return res.json({
      success: true,
      message: 'If the email exists, a password reset link has been sent'
    });
  } catch (error) {
    console.error('Forgot password controller error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const refreshToken = async (req: Request, res: Response) => {
  try {
    const { refresh_token } = req.body;
    
    if (!refresh_token) {
      return res.status(400).json({
        success: false,
        message: 'Refresh token is required'
      });
    }
    
    const payload = verifyToken(refresh_token);
    
    if (!payload) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired refresh token'
      });
    }
    
    // Get current user roles - they might have changed
    const roles = await authService.getUserRoles(payload.userId);
    
    const tokenPayload = {
      userId: payload.userId,
      email: payload.email,
      roles
    };
    
    const tokens = authService.generateAuthTokens(tokenPayload);
    
    return res.json({
      success: true,
      data: tokens
    });
  } catch (error) {
    console.error('Refresh token controller error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const verifyEmail = async (req: Request, res: Response) => {
  try {
    const { userId, token } = req.params;
    
    if (!userId || !token) {
      return res.status(400).json({
        success: false,
        message: 'User ID and verification token are required'
      });
    }
    
    const success = await authService.verifyEmail(userId, token);
    
    if (!success) {
      return res.status(400).json({
        success: false,
        message: 'Email verification failed'
      });
    }
    
    return res.json({
      success: true,
      message: 'Email verified successfully'
    });
  } catch (error) {
    console.error('Verify email controller error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
