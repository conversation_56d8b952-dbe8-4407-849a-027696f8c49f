# Quy trình <PERSON> nhật Design Tokens

## Khi nào cần cập nhật tokens
- Thay đổi color palette
- Thay đổi typography scale
- Thay đổi spacing system
- Thay đổi border radius, shadows, animations

## Quy trình cập nhật

### 1. Designer x<PERSON><PERSON> định thay đổi
- Cập nhật Figma design system
- Tạo document mô tả những thay đổi
- Làm việc với developer để xác định impact

### 2. Developer cập nhật design tokens
- Fork branch mới `design/update-tokens-[date]`
- C<PERSON><PERSON> nhật các files trong `packages/design-tokens/src/tokens`
- Viết unit tests để đảm bảo tokens hoạt động đúng
- Build package và kiểm tra exports

### 3. Kiểm tra tác động
- Chạy visual regression tests
- Test trên các component ảnh hưởng
- Tạo screenshot so sánh trước/sau

### 4. Review và merge
- Tạo pull request
- Designer review thay đổi
- Developer review code
- Sa<PERSON> khi approval, merge vào main branch

### 5. Thông báo team
- Tạo changelog ghi rõ những thay đổi
- Thông báo trong meeting
- Update documentation
