'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './QRCodeCard.module.scss';

interface QRCodeCardProps {
  id: string;
  location: string;
  description?: string;
  roomNumber?: string;
  status?: string;
  scanCount?: number;
  lastScan?: string;
  createdAt: string;
  code: string; // QR code value
}

export default function QRCodeCard({ 
  id,
  location,
  description,
  roomNumber,
  status = 'active',
  scanCount = 0,
  lastScan,
  createdAt,
  code
}: QRCodeCardProps) {
  const [qrImageUrl, setQrImageUrl] = useState<string>('');
  const [loading, setLoading] = useState(true);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  // Fetch QR code image
  useEffect(() => {
    const fetchQRCode = async () => {
      try {
        const response = await fetch(`/api/qr-codes/${id}/download?format=json`);
        const data = await response.json();
        if (data.data_url) {
          setQrImageUrl(data.data_url);
        }
      } catch (error) {
        console.error('Error fetching QR code image:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchQRCode();
  }, [id]);

  // Handle download click
  const handleDownload = () => {
    window.open(`/api/qr-codes/${id}/download?size=800`, '_blank');
  };

  return (
    <div className={styles.card}>
      <div className={styles.qrSection}>
        {loading ? (
          <div className={styles.placeholder}>
            <div className={styles.spinner}></div>
          </div>
        ) : (
          qrImageUrl && (
            <div className={styles.qrWrapper}>
              <img src={qrImageUrl} alt={`QR Code for ${location}`} className={styles.qrImage} />
              <div className={styles.overlay}>
                <button className={styles.downloadButton} onClick={handleDownload}>
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M10 12.5L6.5 9H9V3H11V9H13.5L10 12.5Z" fill="currentColor"/>
                    <path d="M17 14H3V10H1V14C1 15.1 1.9 16 3 16H17C18.1 16 19 15.1 19 14V10H17V14Z" fill="currentColor"/>
                  </svg>
                </button>
              </div>
            </div>
          )
        )}
      </div>
      
      <div className={styles.content}>
        <Link href={`/qr-codes/${id}`} className={styles.title}>
          {location}
        </Link>
        
        {description && (
          <p className={styles.description}>{description}</p>
        )}
        
        <div className={styles.meta}>
          {roomNumber && (
            <div className={styles.metaItem}>
              <span className={styles.metaLabel}>Phòng:</span>
              <span className={styles.metaValue}>{roomNumber}</span>
            </div>
          )}
          
          <div className={styles.metaItem}>
            <span className={styles.metaLabel}>Trạng thái:</span>
            <span className={`${styles.status} ${status === 'active' ? styles.active : styles.inactive}`}>
              {status === 'active' ? 'Đang hoạt động' : 'Không hoạt động'}
            </span>
          </div>
          
          <div className={styles.metaItem}>
            <span className={styles.metaLabel}>Lượt quét:</span>
            <span className={styles.metaValue}>{scanCount}</span>
          </div>
          
          <div className={styles.metaItem}>
            <span className={styles.metaLabel}>Ngày tạo:</span>
            <span className={styles.metaValue}>{formatDate(createdAt)}</span>
          </div>
          
          {lastScan && (
            <div className={styles.metaItem}>
              <span className={styles.metaLabel}>Quét gần đây:</span>
              <span className={styles.metaValue}>{formatDate(lastScan)}</span>
            </div>
          )}
        </div>
      </div>
      
      <div className={styles.actions}>
        <Link href={`/qr-codes/${id}`} className={styles.viewButton}>
          Chi tiết
        </Link>
        <Link href={`/qr-codes/${id}/edit`} className={styles.editButton}>
          Chỉnh sửa
        </Link>
      </div>
    </div>
  );
}
