import React from 'react';
import Icon, { IconProps } from '../Icon';

export const ParkingIcon: React.FC<Omit<IconProps, 'icon'>> = (props) => {
  return (
    <Icon
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M13 3H6v18h4v-6h3c3.31 0 6-2.69 6-6s-2.69-6-6-6zm0 10H10V7h3c1.1 0 2 .9 2 2s-.9 2-2 2z"
      />
    </Icon>
  );
};

export default ParkingIcon;
