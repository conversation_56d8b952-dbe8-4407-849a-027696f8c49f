.container {
  padding: 16px 0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  @media (max-width: 640px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

.headerLeft {
  display: flex;
  align-items: center;
}

.backLink {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  text-decoration: none;
  margin-right: 16px;
  padding: 6px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f3f4f6;
    color: #111827;
  }
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  
  @media (min-width: 768px) {
    font-size: 1.75rem;
  }
}

.actions {
  display: flex;
  gap: 12px;
  
  @media (max-width: 640px) {
    width: 100%;
    justify-content: space-between;
  }
}

.viewToggle {
  display: flex;
  background-color: #f3f4f6;
  border-radius: 6px;
  overflow: hidden;
}

.viewToggleButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  
  &.active {
    background-color: #e5e7eb;
    color: #111827;
  }
  
  &:hover:not(.active) {
    background-color: #e5e7eb;
  }
}

.createButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #0ea5e9;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  text-decoration: none;
  transition: background-color 0.2s;
  height: 40px;
  
  svg {
    width: 16px;
    height: 16px;
    stroke-width: 2px;
  }
  
  &:hover {
    background-color: #0284c7;
  }
}

.filters {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
  
  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.searchBox {
  position: relative;
  width: 100%;
  
  @media (min-width: 768px) {
    max-width: 320px;
  }
}

.searchInput {
  width: 100%;
  padding: 10px 16px 10px 40px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #374151;
  
  &:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
}

.searchIcon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.filterGroup {
  display: flex;
  gap: 12px;
  
  @media (max-width: 767px) {
    flex-wrap: wrap;
  }
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #374151;
  background-color: white;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
  }
}

.roomsGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 16px;
  
  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @media (min-width: 1280px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1rem;
  color: #6b7280;
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  
  p {
    color: #ef4444;
    margin-bottom: 16px;
  }
}

.retryButton {
  padding: 8px 16px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  
  &:hover {
    background-color: #dc2626;
  }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  
  svg {
    color: #d1d5db;
    margin-bottom: 16px;
  }
  
  h3 {
    font-size: 1.25rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    max-width: 400px;
    margin-bottom: 16px;
  }
}

.createEmptyButton {
  display: inline-flex;
  padding: 8px 16px;
  background-color: #0ea5e9;
  color: white;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  
  &:hover {
    background-color: #0284c7;
  }
}

.tableContainer {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tableView {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  
  th, td {
    padding: 12px 16px;
    text-align: left;
  }
  
  th {
    background-color: #f9fafb;
    font-weight: 500;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
  }
  
  td {
    border-bottom: 1px solid #f3f4f6;
  }
  
  tr:last-child td {
    border-bottom: none;
  }
  
  tbody tr {
    &:hover {
      background-color: #f9fafb;
    }
  }
}

.statusBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  
  &.available {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
  }
  
  &.occupied {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
  }
  
  &.maintenance {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }
  
  &.cleaning {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }
}

.tableActions {
  display: flex;
  gap: 8px;
}

.actionLink {
  color: #0ea5e9;
  text-decoration: none;
  font-size: 0.875rem;
  
  &:hover {
    text-decoration: underline;
  }
}