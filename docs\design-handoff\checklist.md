# Checklist Triển khai Design

## Components
- [ ] Sử dụng đúng color tokens (không hard-coded colors)
- [ ] Typography đúng theo design system
- [ ] Spacing tuân theo quy định
- [ ] Border radius nhất quán
- [ ] Shadows và effects chính xác
- [ ] States đầy đủ (default, hover, active, focus, disabled)
- [ ] Responsive behavior đúng spec

## Animations
- [ ] Timing function phù hợp (ease, ease-in, ease-out, etc.)
- [ ] Duration thích hợp
- [ ] Không gây khó chịu cho người dùng
- [ ] Tuân theo tokens animation

## Accessibility
- [ ] Text contrast đạt chuẩn WCAG (4.5:1 cho text thường, 3:1 cho text lớn)
- [ ] Interactive elements đủ lớn (tối thiểu 44x44px theo WCAG)
- [ ] Focus indicator rõ ràng
- [ ] Semantic HTML được sử dụng phù hợp
- [ ] Keyboard navigation hoạt động tốt

## Internationalization
- [ ] Text có thể mở rộng khi dài hơn
- [ ] Layout không vỡ với text dài
- [ ] RTL layout được hỗ trợ (nếu cần)

## Cross-platform
- [ ] Web: Hiển thị đúng trên các trình duyệt chính (Chrome, Safari, Firefox, Edge)
- [ ] Mobile: Hiển thị đúng trên iOS và Android
- [ ] Tablet/Desktop: Responsive design phù hợp
