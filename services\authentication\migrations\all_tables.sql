-- Bảng users (đã tạo)
-- Bảng roles (đã tạo)  
-- Bảng permissions (đã tạo)
-- Bảng role_permissions (đã tạo)
-- Bảng user_roles (đã tạo)
-- Bảng temporary_users (đã tạo)

-- B<PERSON><PERSON> cho Chat Service
CREATE TABLE IF NOT EXISTS chat_rooms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255),
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  room_type VARCHAR(50) DEFAULT 'general', -- general, support, private
  metadata JSONB
);

CREATE TABLE IF NOT EXISTS chat_participants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_room_id UUID NOT NULL REFERENCES chat_rooms(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  temporary_user_id UUID REFERENCES temporary_users(id) ON DELETE SET NULL,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  left_at TIMESTAMP WITH TIME ZONE,
  role VARCHAR(50) DEFAULT 'member', -- member, admin, moderator
  is_active BOOLEAN DEFAULT true,
  CHECK (user_id IS NOT NULL OR temporary_user_id IS NOT NULL) -- Ít nhất một trong hai cần có giá trị
);

CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_room_id UUID NOT NULL REFERENCES chat_rooms(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES users(id) ON DELETE SET NULL,
  temporary_sender_id UUID REFERENCES temporary_users(id) ON DELETE SET NULL,
  content TEXT NOT NULL,
  original_language VARCHAR(10),
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  is_translated BOOLEAN DEFAULT false,
  metadata JSONB,
  CHECK (sender_id IS NOT NULL OR temporary_sender_id IS NOT NULL) -- Ít nhất một trong hai cần có giá trị
);

CREATE TABLE IF NOT EXISTS message_translations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
  language VARCHAR(10) NOT NULL,
  translated_content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(message_id, language)
);

-- Bảng cho Translation Service
CREATE TABLE IF NOT EXISTS translation_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source_text TEXT NOT NULL,
  source_language VARCHAR(10) NOT NULL,
  target_language VARCHAR(10) NOT NULL,
  translated_text TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  last_used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  use_count INTEGER DEFAULT 1,
  UNIQUE(source_text, source_language, target_language)
);

-- Bảng cho QR Code Service
CREATE TABLE IF NOT EXISTS qr_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code_value VARCHAR(100) UNIQUE NOT NULL,
  type VARCHAR(50) NOT NULL, -- check-in, room, restaurant, etc.
  location_id VARCHAR(100),
  room_number VARCHAR(20),
  hotel_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB
);

CREATE TABLE IF NOT EXISTS qr_code_scans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  qr_code_id UUID NOT NULL REFERENCES qr_codes(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  temporary_user_id UUID REFERENCES temporary_users(id) ON DELETE SET NULL,
  scanned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  device_info JSONB,
  ip_address VARCHAR(45),
  CHECK (user_id IS NOT NULL OR temporary_user_id IS NOT NULL)
);

-- Bảng cho Hotel Service (nếu cần)
CREATE TABLE IF NOT EXISTS hotels (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  address TEXT,
  city VARCHAR(100),
  country VARCHAR(100),
  postal_code VARCHAR(20),
  phone VARCHAR(50),
  email VARCHAR(255),
  website VARCHAR(255),
  timezone VARCHAR(50),
  check_in_time TIME,
  check_out_time TIME,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB
);

CREATE TABLE IF NOT EXISTS hotel_rooms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  hotel_id UUID NOT NULL REFERENCES hotels(id) ON DELETE CASCADE,
  room_number VARCHAR(20) NOT NULL,
  room_type VARCHAR(50), -- single, double, suite, etc.
  floor VARCHAR(10),
  capacity INTEGER,
  is_occupied BOOLEAN DEFAULT false,
  status VARCHAR(50) DEFAULT 'available', -- available, occupied, maintenance, cleaning
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(hotel_id, room_number)
);

-- Cập nhật trigger cho các bảng cần updated_at tự động
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;

$$ LANGUAGE plpgsql;

-- Thêm trigger cho các bảng mới
CREATE TRIGGER update_chat_rooms_updated_at
BEFORE UPDATE ON chat_rooms
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_hotels_updated_at
BEFORE UPDATE ON hotels
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_hotel_rooms_updated_at
BEFORE UPDATE ON hotel_rooms
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Thêm RLS cho bảo mật
-- Cho phép mọi người đọc danh sách chat_rooms
ALTER TABLE chat_rooms ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Public chat rooms are viewable by everyone"
  ON chat_rooms FOR SELECT
  USING (room_type = 'general');

-- Thêm RLS cho message
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Chat participants can view messages"
  ON chat_messages FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM chat_participants
    WHERE chat_participants.chat_room_id = chat_messages.chat_room_id
    AND (chat_participants.user_id = auth.uid() OR chat_participants.temporary_user_id IS NOT NULL)
  ));
