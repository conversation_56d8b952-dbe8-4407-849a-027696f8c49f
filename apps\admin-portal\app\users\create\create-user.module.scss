.container {
  padding: 24px;
}

.header {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #111827;
    margin: 8px 0 0 0;
  }
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #4b5563;
  text-decoration: none;
  padding: 6px 12px;
  border-radius: 4px;
  width: fit-content;
  transition: all 0.2s;
  
  &:hover {
    background-color: #f3f4f6;
  }
}

.formContainer {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 24px;
  max-width: 640px;
}

.formGroup {
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
  }
  
  input, select {
    width: 100%;
    padding: 10px 14px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 15px;
    
    &:focus {
      border-color: #2563eb;
      outline: none;
      box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
    }
    
    &:disabled {
      background-color: #f9f9f9;
      cursor: not-allowed;
    }
  }
  
  select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M8 12l-6-6h12l-6 6z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 14px center;
    background-size: 12px;
  }
}

.checkboxGroup {
  display: flex;
  align-items: center;
  gap: 10px;
  
  input {
    width: auto;
    margin: 0;
  }
  
  label {
    margin-bottom: 0;
  }
}

.inputError {
  border-color: #dc2626 !important;
  
  &:focus {
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2) !important;
  }
}

.errorText {
  color: #dc2626;
  font-size: 14px;
  margin-top: 4px;
  margin-bottom: 0;
}

.errorMessage {
  background-color: #fee2e2;
  border-left: 4px solid #dc2626;
  color: #b91c1c;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 24px;
  
  p {
    margin: 0;
  }
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
}

.required {
  color: #dc2626;
}

.successContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.successCard {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 32px;
  width: 100%;
  max-width: 480px;
  text-align: center;
}

.successIcon {
  margin: 0 auto 24px;
  display: flex;
  justify-content: center;
}

.successTitle {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px;
}

.successDesc {
  color: #6b7280;
  margin: 0 0 24px;
}

.successActions {
  display: flex;
  gap: 12px;
  justify-content: center;
  
  @media (max-width: 480px) {
    flex-direction: column;
  }
}
.selectedPointsContainer {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;

  h3 {
    margin-top: 0;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    color: #334155;
  }
}

.pointsList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pointItem {
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background-color: #fff;
  border-radius: 0.375rem;
  border: 1px solid #e2e8f0;
}

.pointInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.pointName {
  font-weight: 500;
  color: #334155;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.primaryBadge {
  background-color: #0ea5e9;
  color: white;
  font-size: 0.75rem;
  padding: 0.125rem 0.5rem;
  border-radius: 1rem;
}

.pointActions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.priorityControl {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  label {
    font-size: 0.875rem;
    color: #64748b;
  }

  input {
    width: 4rem;
    padding: 0.25rem 0.5rem;
    border: 1px solid #cbd5e1;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }
}

.setPrimaryBtn {
  background-color: transparent;
  border: 1px solid #0ea5e9;
  color: #0ea5e9;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #0ea5e9;
    color: white;
  }
}

.removePointBtn {
  background-color: transparent;
  border: 1px solid #ef4444;
  color: #ef4444;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #ef4444;
    color: white;
  }
}

.helpText {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #64748b;
}