import React, { useState } from 'react';
import { 
  TopNavigation, 
  BottomNavigation,
  Card, 
  Button, 
  Heading4,
  Heading5,
  BodyRegular, 
  BodySmall,
  Badge,
  Input 
} from '@loaloa/ui';
import { 
  HomeIcon, 
  SearchIcon, 
  SettingsIcon,
  NotificationIcon,
  ArrowIcon,
  StarIcon,
  LocationIcon
} from '@loaloa/ui';

// Mock hotel data
const mockHotels = [
  {
    id: 1,
    name: 'Sunset Resort & Spa',
    location: 'Phuket, Thailand',
    rating: 4.8,
    reviews: 352,
    price: 199,
    currency: '$',
    image: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
    tags: ['Beach', 'Luxury']
  },
  {
    id: 2,
    name: 'City Central Hotel',
    location: 'Bangkok, Thailand',
    rating: 4.5,
    reviews: 215,
    price: 120,
    currency: '$',
    image: 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
    tags: ['City Center', 'Business']
  },
  {
    id: 3,
    name: 'Mountain View Lodge',
    location: 'Chiang Mai, Thailand',
    rating: 4.7,
    reviews: 187,
    price: 150,
    currency: '$',
    image: 'https://images.unsplash.com/photo-1540541338287-41700207dee6?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
    tags: ['Mountain', 'Nature']
  },
  {
    id: 4,
    name: 'Riverside Retreat',
    location: 'Krabi, Thailand',
    rating: 4.6,
    reviews: 124,
    price: 180,
    currency: '$',
    image: 'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
    tags: ['River', 'Peaceful']
  }
];

export interface HotelListingScreenProps {
  onSelectHotel?: (hotelId: number) => void;
  variant?: 'light' | 'dark' | 'studio';
}

export const HotelListingScreen: React.FC<HotelListingScreenProps> = ({ 
  onSelectHotel,
  variant = 'light'
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeNavItem, setActiveNavItem] = useState('explore');
  
  // Filter hotels based on search query
  const filteredHotels = mockHotels.filter(hotel => 
    hotel.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
    hotel.location.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Styles
  const themeColors = {
    light: {
      background: '#F9FAFB',
      text: '#010103',
      secondaryText: '#464646',
      border: '#EBEBEB'
    },
    dark: {
      background: '#161616',
      text: '#EBEBEB',
      secondaryText: '#ABABAB',
      border: '#2E2E2E'
    },
    studio: {
      background: '#16262E',
      text: '#EBEBEB',
      secondaryText: '#ABABAB',
      border: '#2E4756'
    }
  };

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    minHeight: '100vh',
    backgroundColor: themeColors[variant].background,
    color: themeColors[variant].text
  };

  const contentStyle: React.CSSProperties = {
    padding: '16px',
    paddingBottom: '80px', // Space for bottom navigation
    flex: 1
  };

  const headerStyle: React.CSSProperties = {
    marginBottom: '24px'
  };

  const searchContainerStyle: React.CSSProperties = {
    marginBottom: '24px'
  };

  const hotelListStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px'
  };

  const hotelCardStyle: React.CSSProperties = {
    padding: 0,
    overflow: 'hidden'
  };

  const hotelImageStyle: React.CSSProperties = {
    width: '100%',
    height: '180px',
    objectFit: 'cover',
    borderTopLeftRadius: '8px',
    borderTopRightRadius: '8px'
  };

  const hotelContentStyle: React.CSSProperties = {
    padding: '16px'
  };

  const hotelHeaderStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '8px'
  };

  const locationStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    marginBottom: '8px'
  };

  const ratingStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '4px'
  };

  const tagsContainerStyle: React.CSSProperties = {
    display: 'flex',
    gap: '8px',
    marginTop: '12px'
  };

  const footerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: '16px',
    paddingTop: '16px',
    borderTop: `1px solid ${themeColors[variant].border}`
  };

  const priceStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column'
  };

  const Logo = (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <div style={{ 
        width: '32px', 
        height: '32px', 
        backgroundColor: '#FF4D00', 
        borderRadius: '4px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontWeight: 'bold',
        fontSize: '18px'
      }}>L</div>
      <span>LoaLoa</span>
    </div>
  );

  const RightItems = (
    <Button 
      variant="outline"
      size="small"
      icon={<NotificationIcon size="small" />}
      label=""
    />
  );
  
  // Bottom navigation items
  const navItems = [
    { label: 'Explore', href: '#', icon: <HomeIcon />, active: activeNavItem === 'explore' },
    { label: 'Search', href: '#', icon: <SearchIcon />, active: activeNavItem === 'search' },
    { label: 'Settings', href: '#', icon: <SettingsIcon />, active: activeNavItem === 'settings' }
  ];

  return (
    <div style={containerStyle}>
      <TopNavigation
        logo={Logo}
        links={[
          { label: 'Hotels', href: '#', active: true },
          { label: 'Flights', href: '#' },
          { label: 'Cars', href: '#' }
        ]}
        rightItems={RightItems}
        variant={variant}
      />
      
      <div style={contentStyle}>
        <div style={headerStyle}>
          <Heading4>Find your perfect stay</Heading4>
          <BodyRegular style={{ color: themeColors[variant].secondaryText }}>
            Explore our curated selection of top-rated hotels
          </BodyRegular>
        </div>

        <div style={searchContainerStyle}>
          <Input
            placeholder="Search by hotel name or location"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            startIcon={<SearchIcon size="small" />}
          />
        </div>

        <div style={hotelListStyle}>
          {filteredHotels.length > 0 ? (
            filteredHotels.map(hotel => (
              <Card key={hotel.id} style={hotelCardStyle}>
                <img 
                  src={hotel.image} 
                  alt={hotel.name}
                  style={hotelImageStyle}
                />
                <div style={hotelContentStyle}>
                  <div style={hotelHeaderStyle}>
                    <Heading5>{hotel.name}</Heading5>
                    <div style={ratingStyle}>
                      <StarIcon size="small" color="#FFD700" />
                      <BodyRegular style={{ fontWeight: 600 }}>{hotel.rating}</BodyRegular>
                      <BodySmall style={{ color: themeColors[variant].secondaryText }}>
                        ({hotel.reviews})
                      </BodySmall>
                    </div>
                  </div>
                  
                  <div style={locationStyle}>
                    <LocationIcon size="small" />
                    <BodySmall style={{ color: themeColors[variant].secondaryText }}>
                      {hotel.location}
                    </BodySmall>
                  </div>

                  <div style={tagsContainerStyle}>
                    {hotel.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" label={tag} size="small" />
                    ))}
                  </div>

                  <div style={footerStyle}>
                    <div style={priceStyle}>
                      <Heading5>{hotel.currency}{hotel.price}</Heading5>
                      <BodySmall style={{ color: themeColors[variant].secondaryText }}>per night</BodySmall>
                    </div>
                    <Button 
                      variant="primary"
                      size="small"
                      label="View Details"
                      onClick={() => onSelectHotel && onSelectHotel(hotel.id)}
                    />
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <Card>
              <div style={{ padding: '24px', textAlign: 'center' }}>
                <BodyRegular>No hotels found matching your search.</BodyRegular>
              </div>
            </Card>
          )}
        </div>
      </div>

      <BottomNavigation
        items={navItems}
        variant={variant}
      />
    </div>
  );
};

export default HotelListingScreen;
