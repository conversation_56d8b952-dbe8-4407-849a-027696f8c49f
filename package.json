{"name": "loaloa", "version": "0.1.0", "private": true, "packageManager": "pnpm@8.15.4", "scripts": {"prepare": "husky", "build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "clean": "turbo clean", "test": "turbo test", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "admin": "cd apps/admin-portal && pnpm dev", "super": "cd apps/super-admin && pnpm dev", "web": "cd apps/web-chat && pnpm dev"}, "workspaces": ["apps/*", "packages/*", "packages/license-client"], "dependencies": {"@loaloa/design-tokens": "workspace:*"}, "devDependencies": {"@storybook/addon-actions": "^8.6.12", "@storybook/addon-controls": "^8.6.12", "@storybook/addon-docs": "^8.6.12", "@storybook/addon-styling-webpack": "^1.0.1", "@types/node": "^20.11.5", "autoprefixer": "^10.4.21", "axios": "1.6.7", "css-loader": "^7.1.2", "husky": "^8.0.0", "postcss": "^8.5.3", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.1.6", "prettier": "^3.0.0", "sass": "^1.87.0", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "turbo": "^1.13.0", "typescript": "^5.4.5"}}