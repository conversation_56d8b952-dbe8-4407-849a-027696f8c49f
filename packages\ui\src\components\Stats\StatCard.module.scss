@import '../../styles/variables';

.statCard {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  &.primary {
    border-top: 3px solid var(--color-primary, #FF4D00);
  }
  
  &.success {
    border-top: 3px solid var(--color-success, #2E7D32);
  }
  
  &.warning {
    border-top: 3px solid var(--color-warning, #F57C00);
  }
  
  &.info {
    border-top: 3px solid var(--color-info, #0288D1);
  }
  
  &.danger {
    border-top: 3px solid var(--color-error, #D32F2F);
  }
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-secondary, #7D8491);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.icon {
  color: var(--color-text-secondary, #7D8491);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.value {
  font-size: 1.75rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.75rem;
  color: var(--color-text, #010103);
}

.change {
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  
  &.positive {
    color: var(--color-success, #2E7D32);
  }
  
  &.negative {
    color: var(--color-error, #D32F2F);
  }
  
  .arrow {
    font-size: 1rem;
  }
  
  .period {
    font-weight: 400;
    font-size: 0.75rem;
    color: var(--color-text-secondary, #7D8491);
  }
}

.footer {
  margin-top: auto;
  padding-top: 0.75rem;
  font-size: 0.75rem;
  color: var(--color-text-secondary, #7D8491);
  border-top: 1px solid var(--color-border, #F0F0F0);
}
