import supabase from '../utils/supabase';
import { MessageStatus, ChatMessage } from '../types';

interface ReadStatus {
  messageId: string;
  userId?: string;
  temporaryUserId?: string;
  roomId: string;
  readAt: string;
}

/**
 * <PERSON><PERSON><PERSON> dấu tin nhắn đã đọc
 */
export const markAsRead = async (
  messageId: string,
  userId?: string,
  temporaryUserId?: string
): Promise<ReadStatus | null> => {
  try {
    if (!userId && !temporaryUserId) {
      throw new Error('Either userId or temporaryUserId is required');
    }
    
    // Lấy thông tin tin nhắn
    const { data: message, error: messageError } = await supabase
      .from('chat_messages')
      .select('chat_room_id')
      .eq('id', messageId)
      .single();
    
    if (messageError || !message) {
      console.error('Error finding message:', messageError);
      return null;
    }
    
    // Lấy participant_id của người dùng trong phòng chat
    const { data: participant, error: participantError } = await supabase
      .from('chat_participants')
      .select('id')
      .eq('chat_room_id', message.chat_room_id)
      .or(`user_id.eq.${userId || null},temporary_user_id.eq.${temporaryUserId || null}`)
      .single();
    
    if (participantError || !participant) {
      console.error('Error finding participant:', participantError);
      return null;
    }
    
    const participantId = participant.id;
    const currentTime = new Date().toISOString();
    
    // Kiểm tra xem đã có status chưa
    const { data: existingStatus, error: checkError } = await supabase
      .from('message_status')
      .select('*')
      .eq('message_id', messageId)
      .eq('participant_id', participantId)
      .single();
    
    if (existingStatus) {
      // Cập nhật status hiện có
      const { data: updatedStatus, error: updateError } = await supabase
        .from('message_status')
        .update({
          is_delivered: true,
          is_read: true,
          read_at: currentTime
        })
        .eq('id', existingStatus.id)
        .select()
        .single();
      
      if (updateError) {
        console.error('Error updating message status:', updateError);
        return null;
      }
      
      return {
        messageId,
        userId,
        temporaryUserId,
        roomId: message.chat_room_id,
        readAt: currentTime
      };
    } else {
      // Tạo status mới
      const { data: newStatus, error: insertError } = await supabase
        .from('message_status')
        .insert([{
          message_id: messageId,
          participant_id: participantId,
          is_delivered: true,
          is_read: true,
          delivered_at: currentTime,
          read_at: currentTime
        }])
        .select()
        .single();
      
      if (insertError) {
        console.error('Error creating message status:', insertError);
        return null;
      }
      
      return {
        messageId,
        userId,
        temporaryUserId,
        roomId: message.chat_room_id,
        readAt: currentTime
      };
    }
  } catch (error) {
    console.error('Error in markAsRead:', error);
    return null;
  }
};

/**
 * Đánh dấu tin nhắn đã gửi
 */
export const markAsDelivered = async (
  messageId: string,
  userId?: string,
  temporaryUserId?: string
): Promise<boolean> => {
  try {
    if (!userId && !temporaryUserId) {
      throw new Error('Either userId or temporaryUserId is required');
    }
    
    // Lấy thông tin tin nhắn
    const { data: message, error: messageError } = await supabase
      .from('chat_messages')
      .select('chat_room_id')
      .eq('id', messageId)
      .single();
    
    if (messageError || !message) {
      console.error('Error finding message:', messageError);
      return false;
    }
    
    // Lấy participant_id của người dùng trong phòng chat
    const { data: participant, error: participantError } = await supabase
      .from('chat_participants')
      .select('id')
      .eq('chat_room_id', message.chat_room_id)
      .or(`user_id.eq.${userId || null},temporary_user_id.eq.${temporaryUserId || null}`)
      .single();
    
    if (participantError || !participant) {
      console.error('Error finding participant:', participantError);
      return false;
    }
    
    const participantId = participant.id;
    const currentTime = new Date().toISOString();
    
    // Kiểm tra xem đã có status chưa
    const { data: existingStatus, error: checkError } = await supabase
      .from('message_status')
      .select('*')
      .eq('message_id', messageId)
      .eq('participant_id', participantId)
      .single();
    
    if (existingStatus) {
      // Cập nhật status hiện có nếu chưa đánh dấu là delivered
      if (!existingStatus.is_delivered) {
        const { error: updateError } = await supabase
          .from('message_status')
          .update({
            is_delivered: true,
            delivered_at: currentTime
          })
          .eq('id', existingStatus.id);
        
        if (updateError) {
          console.error('Error updating message status:', updateError);
          return false;
        }
      }
    } else {
      // Tạo status mới
      const { error: insertError } = await supabase
        .from('message_status')
        .insert([{
          message_id: messageId,
          participant_id: participantId,
          is_delivered: true,
          is_read: false,
          delivered_at: currentTime
        }]);
      
      if (insertError) {
        console.error('Error creating message status:', insertError);
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error in markAsDelivered:', error);
    return false;
  }
};

/**
 * Lấy trạng thái đọc của tin nhắn
 */
export const getMessageStatus = async (messageId: string): Promise<MessageStatus[]> => {
  try {
    const { data, error } = await supabase
      .from('message_status')
      .select('*')
      .eq('message_id', messageId);
    
    if (error) {
      console.error('Error getting message status:', error);
      return [];
    }
    
    return data as MessageStatus[];
  } catch (error) {
    console.error('Error in getMessageStatus:', error);
    return [];
  }
};
