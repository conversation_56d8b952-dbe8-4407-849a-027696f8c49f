@import '../../../styles/variables';

.container {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: var(--color-secondary, #EBEBEB);
  }
  
  &.active {
    background-color: var(--color-secondary, #EBEBEB);
  }
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 12px;
  overflow: hidden;
  flex-shrink: 0;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.avatarPlaceholder {
  width: 100%;
  height: 100%;
  background-color: var(--color-primary, #FF4D00);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: 500;
}

.content {
  flex: 1;
  min-width: 0; // For text truncation
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.title {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.time {
  font-size: 0.75rem;
  color: var(--color-gray, #7D8491);
  white-space: nowrap;
  margin-left: 8px;
}

.body {
  display: flex;
  align-items: center;
}

.message {
  font-size: 0.875rem;
  color: var(--color-gray, #7D8491);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.language {
  font-size: 0.75rem;
  background-color: var(--color-gray-100, #E1E1E1);
  color: var(--color-dark, #464646);
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.badge {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 22px;
  height: 22px;
  background-color: var(--color-primary, #FF4D00);
  color: white;
  border-radius: 11px;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0 6px;
  margin-left: 8px;
}
