// D:\loaloa\apps\admin-portal\app\api\qr-codes\[id]\guest\route.ts
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Tạo Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  return createClient(supabaseUrl, supabaseKey);
};

// GET: Lấy thông tin guest từ QR code
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const qrCodeId = params.id;

    // Tạo Supabase client
    const supabase = createSupabaseClient();

    // Lấy guest dựa vào QR code
    const { data, error } = await supabase
      .from('tenant_guests')
      .select(`
        *
      `)
      .eq('qr_code_id', qrCodeId)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error('Error fetching guest from QR code:', error);
      throw error;
    }

    // Nếu có guest và có room_number, lấy thông tin phòng
    let roomInfo = null;
    if (data && data.room_number) {
      const { data: roomData, error: roomError } = await supabase
        .from('tenant_rooms')
        .select('room_type, floor')
        .eq('tenant_id', data.tenant_id)
        .eq('room_number', data.room_number)
        .maybeSingle();
        
      if (!roomError && roomData) {
        data.tenant_rooms = roomData;
      }
    }

    // Trả về thông tin guest hoặc null nếu không tìm thấy
    if (!data) {
      return NextResponse.json({ data: null });
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error in GET guest from QR code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST: Tạo guest từ QR code
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const qrCodeId = params.id;
    const { tenant_id, full_name, email, phone, room_number } = await request.json();

    if (!tenant_id) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    // Tạo Supabase client
    const supabase = createSupabaseClient();

    // Gọi hàm tạo guest từ QR code
    const { data: guestId, error } = await supabase
      .rpc('create_guest_from_qr', {
        p_qr_code_id: qrCodeId,
        p_tenant_id: tenant_id,
        p_full_name: full_name || null,
        p_email: email || null,
        p_phone: phone || null,
        p_room_number: room_number || null
      });

    if (error) {
      console.error('Error creating guest from QR code:', error);
      // Thử cách thủ công nếu function gặp lỗi
      const { data: manualData, error: manualError } = await supabase
        .from('tenant_guests')
        .insert({
          tenant_id,
          qr_code_id: qrCodeId,
          full_name: full_name || 'Guest',
          email: email || null,
          phone: phone || null,
          room_number: room_number || null,
          check_in: new Date().toISOString(),
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (manualError) {
        console.error('Error manually creating guest:', manualError);
        throw manualError;
      }

      // Cập nhật trạng thái phòng nếu có thông tin phòng
      if (room_number) {
        await supabase
          .from('tenant_rooms')
          .update({
            status: 'occupied',
            last_checkin: new Date().toISOString()
          })
          .eq('tenant_id', tenant_id)
          .eq('room_number', room_number);
      }

      return NextResponse.json(
        { data: manualData, message: 'Guest created successfully' },
        { status: 201 }
      );
    }

    // Nếu dùng function thành công, lấy thông tin guest tạo
    const { data: guest, error: fetchError } = await supabase
      .from('tenant_guests')
      .select(`
        *
      `)
      .eq('id', guestId)
      .single();

    if (fetchError) {
      console.error('Error fetching created guest:', fetchError);
      throw fetchError;
    }

    // Nếu có guest và có room_number, lấy thông tin phòng
    if (guest && guest.room_number) {
      const { data: roomData, error: roomError } = await supabase
        .from('tenant_rooms')
        .select('room_type, floor')
        .eq('tenant_id', guest.tenant_id)
        .eq('room_number', guest.room_number)
        .maybeSingle();
        
      if (!roomError && roomData) {
        guest.tenant_rooms = roomData;
      }
    }

    // Trả về thông tin guest tạo
    return NextResponse.json(
      { data: guest, message: 'Guest created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST guest from QR code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
