import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Tạo Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }

  return createClient(supabaseUrl, supabaseKey);
};

// GET: Lấy danh sách tenants
export async function GET(request: Request) {
  try {
    // Tạo Supabase client
    const supabase = createSupabaseClient();

    // Lấy danh sách tenants
    const { data, error } = await supabase
      .from('tenants')
      .select('id, name, domain')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching tenants:', error);
      throw error;
    }

    // Trả về danh sách tenants
    return NextResponse.json({ data });
  } catch (error) {
    console.error('Error in GET tenants:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
