import type { Meta, StoryObj } from '@storybook/react';
import { Card } from './index';
import { Button } from '../Button';

const meta = {
  title: 'UI/Card',
  component: Card,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['basic', 'highlight'],
      description: 'Card variant',
    },
    title: {
      control: 'text',
      description: 'Card title/heading',
    },
    width: {
      control: 'text',
      description: 'Width of the card',
    },
    children: {
      control: 'text',
      description: 'Card content',
    },
    onClick: {
      action: 'clicked',
      description: 'Click handler',
    },
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

// Các story cho Card
export const Basic: Story = {
  args: {
    variant: 'basic',
    title: 'Card Title',
    width: '320px',
    children: (
      <p style={{ margin: 0 }}>This is a basic card with header, body, and footer sections.</p>
    ),
    footer: (
      <>
        <Button variant="outline" size="small" label="Cancel" />
        <Button variant="primary" size="small" label="Action" />
      </>
    ),
  },
};

export const Highlight: Story = {
  args: {
    variant: 'highlight',
    title: 'Highlighted Card',
    width: '320px',
    children: (
      <p style={{ margin: 0 }}>This card has a highlighted top border to make it stand out.</p>
    ),
    footer: (
      <>
        <Button variant="outline" size="small" label="Cancel" />
        <Button variant="primary" size="small" label="Action" />
      </>
    ),
  },
};

export const NoHeader: Story = {
  args: {
    width: '320px',
    children: (
      <p style={{ margin: 0 }}>This card has no header, only body content.</p>
    ),
  },
};

export const NoFooter: Story = {
  args: {
    title: 'Card without Footer',
    width: '320px',
    children: (
      <p style={{ margin: 0 }}>This card has no footer section.</p>
    ),
  },
};

export const ClickableCard: Story = {
  args: {
    title: 'Clickable Card',
    width: '320px',
    children: (
      <p style={{ margin: 0 }}>Click on this card to trigger an action.</p>
    ),
    onClick: () => alert('Card clicked!'),
  },
};

export const AdminPanel: Story = {
  args: {
    variant: 'highlight',
    title: 'Admin Panel',
    width: '320px',
    children: (
      <div>
        <p style={{ margin: '0 0 16px 0' }}>Management tools</p>
        <p style={{ margin: 0 }}>This is a sample card component demonstrating the studio theme styling for administrators.</p>
      </div>
    ),
    footer: (
      <>
        <Button variant="accent" size="small" label="Admin Action" />
      </>
    ),
  },
};
