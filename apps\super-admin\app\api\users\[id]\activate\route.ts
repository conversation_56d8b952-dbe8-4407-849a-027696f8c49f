import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '../../../../services/UserService';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id;
    
    // Kích ho<PERSON>t user
    const success = await UserService.activateUser(userId);
    
    if (!success) {
      return NextResponse.json({ error: 'Failed to activate user or user not found' }, { status: 404 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to activate user' },
      { status: 500 }
    );
  }
}
