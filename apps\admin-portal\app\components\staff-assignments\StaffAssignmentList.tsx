'use client';
import { useState } from 'react';
import Link from 'next/link';
import styles from './StaffAssignmentList.module.scss';
import { Pagination } from '@ui';

interface StaffAssignment {
  id: string;
  user: {
    id: string;
    email: string;
    display_name: string;
    avatar_url?: string;
  };
  department: string;
  assignment_type: string;
  priority: number;
  is_active: boolean;
  created_at: string;
}

interface StaffAssignmentListProps {
  assignments: StaffAssignment[];
  loading: boolean;
  error: string | null;
  onDelete: (assignment: StaffAssignment) => void;
  onPageChange: (page: number) => void;
  currentPage: number;
  totalPages: number;
}

export default function StaffAssignmentList({
  assignments,
  loading,
  error,
  onDelete,
  onPageChange,
  currentPage,
  totalPages,
}: StaffAssignmentListProps) {
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading assignments...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.error}>
        <p>Error: {error}</p>
        <button onClick={() => window.location.reload()} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  if (assignments.length === 0) {
    return (
      <div className={styles.emptyState}>
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
          <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#9CA3AF" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M9 9C9 9 10 8 12 8C14 8 15 9 15 9" stroke="#9CA3AF" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M15 14C15 14 14 16 12 16C10 16 9 14 9 14" stroke="#9CA3AF" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
        <h3>No assignments found</h3>
        <p>No staff assignments match your current filters</p>
      </div>
    );
  }

  // Get department display name
  const getDepartmentDisplay = (department: string) => {
    const departmentMap: Record<string, string> = {
      'reception': 'Reception',
      'housekeeping': 'Housekeeping',
      'restaurant': 'Restaurant',
      'spa': 'Spa',
      'concierge': 'Concierge',
      'maintenance': 'Maintenance',
    };
    return departmentMap[department] || department;
  };

  return (
    <div className={styles.container}>
      <div className={styles.tableWrapper}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>Staff Member</th>
              <th>Department</th>
              <th>Priority</th>
              <th>Status</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {assignments.map((assignment) => (
              <tr key={assignment.id}>
                <td className={styles.userCell}>
                  <div className={styles.userInfo}>
                    <div className={styles.avatar}>
                      {assignment.user?.avatar_url ? (
                        <img src={assignment.user.avatar_url} alt={assignment.user.display_name} />
                      ) : (
                        <div className={styles.avatarPlaceholder}>
                          {assignment.user?.display_name?.[0] || "?"}
                        </div>
                      )}
                    </div>
                    <div>
                      <div className={styles.userName}>{assignment.user?.display_name}</div>
                      <div className={styles.userEmail}>{assignment.user?.email}</div>
                    </div>
                  </div>
                </td>
                <td>{getDepartmentDisplay(assignment.department)}</td>
                <td>
                  <div className={styles.priorityBadge}>
                    {assignment.priority}
                  </div>
                </td>
                <td>
                  <span className={`${styles.statusBadge} ${assignment.is_active ? styles.active : styles.inactive}`}>
                    {assignment.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>{formatDate(assignment.created_at)}</td>
                <td className={styles.actions}>
                  <Link href={`/staff-assignments/${assignment.id}/edit`} className={styles.editButton}>
                    Edit
                  </Link>
                  <button 
                    onClick={() => onDelete(assignment)} 
                    className={styles.deleteButton}
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {totalPages > 1 && (
        <div className={styles.paginationContainer}>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
          />
        </div>
      )}
    </div>
  );
}