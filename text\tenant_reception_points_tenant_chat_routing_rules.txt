[
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "id",
    "data_type": "uuid",
    "character_maximum_length": null,
    "column_default": "gen_random_uuid()",
    "is_nullable": "NO",
    "column_comment": "ID duy nhất của quy tắc"
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "tenant_id",
    "data_type": "uuid",
    "character_maximum_length": null,
    "column_default": null,
    "is_nullable": "NO",
    "column_comment": "ID của tenant"
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "rule_name",
    "data_type": "character varying",
    "character_maximum_length": 100,
    "column_default": null,
    "is_nullable": "NO",
    "column_comment": "Tên quy tắc định tuyến"
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "rule_type",
    "data_type": "character varying",
    "character_maximum_length": 50,
    "column_default": null,
    "is_nullable": "NO",
    "column_comment": "Loại quy tắc: qr_code, room_type, area_type, language, time, guest_type"
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "rule_condition",
    "data_type": "jsonb",
    "character_maximum_length": null,
    "column_default": null,
    "is_nullable": "NO",
    "column_comment": "Điều kiện áp dụng quy tắc, dạng JSON tùy thuộc vào rule_type"
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "target_department",
    "data_type": "character varying",
    "character_maximum_length": 50,
    "column_default": null,
    "is_nullable": "YES",
    "column_comment": "Bộ phận đích để định tuyến"
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "target_user_id",
    "data_type": "uuid",
    "character_maximum_length": null,
    "column_default": null,
    "is_nullable": "YES",
    "column_comment": "ID của nhân viên cụ thể để định tuyến"
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "priority",
    "data_type": "integer",
    "character_maximum_length": null,
    "column_default": "1",
    "is_nullable": "YES",
    "column_comment": "Mức độ ưu tiên của quy tắc"
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "is_active",
    "data_type": "boolean",
    "character_maximum_length": null,
    "column_default": "true",
    "is_nullable": "YES",
    "column_comment": "Trạng thái kích hoạt của quy tắc"
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "character_maximum_length": null,
    "column_default": "CURRENT_TIMESTAMP",
    "is_nullable": "YES",
    "column_comment": "Thời điểm tạo bản ghi"
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "character_maximum_length": null,
    "column_default": "CURRENT_TIMESTAMP",
    "is_nullable": "YES",
    "column_comment": "Thời điểm cập nhật gần nhất"
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "column_name": "target_reception_point_id",
    "data_type": "uuid",
    "character_maximum_length": null,
    "column_default": null,
    "is_nullable": "YES",
    "column_comment": "Điểm nhận tin nhắn đích khi quy tắc này khớp"
  }
]
[
  {
    "table_name": "tenant_chat_routing_rules",
    "constraint_name": "tenant_chat_routing_rules_pkey",
    "column_name": "id",
    "constraint_comment": null
  }
]
[
  {
    "table_name": "tenant_chat_routing_rules",
    "constraint_name": "fk_tenant_chat_routing_rules_reception_point",
    "column_name": "target_reception_point_id",
    "foreign_table_name": "tenant_message_reception_points",
    "foreign_column_name": "id",
    "constraint_comment": null
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "constraint_name": "tenant_chat_routing_rules_target_reception_point_id_fkey",
    "column_name": "target_reception_point_id",
    "foreign_table_name": "tenant_message_reception_points",
    "foreign_column_name": "id",
    "constraint_comment": null
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "constraint_name": "tenant_chat_routing_rules_target_user_id_fkey",
    "column_name": "target_user_id",
    "foreign_table_name": "tenant_users",
    "foreign_column_name": "id",
    "constraint_comment": null
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "constraint_name": "tenant_chat_routing_rules_tenant_id_fkey",
    "column_name": "tenant_id",
    "foreign_table_name": "tenants",
    "foreign_column_name": "id",
    "constraint_comment": null
  }
]
[
  {
    "view_name": "tenant_active_staff_view",
    "view_definition": " SELECT tu.id AS user_id,\n    tu.tenant_id,\n    tud.display_name,\n    tud.email,\n    tu.role,\n    tsa.department,\n    tsa.assignment_type,\n    tsa.resource_id,\n    tsa.working_hours,\n        CASE\n            WHEN (tsa.working_hours IS NULL) THEN true\n            WHEN ((EXTRACT(hour FROM CURRENT_TIME) >= (COALESCE(((tsa.working_hours ->> 'start_hour'::text))::integer, 0))::numeric) AND (EXTRACT(hour FROM CURRENT_TIME) < (COALESCE(((tsa.working_hours ->> 'end_hour'::text))::integer, 24))::numeric)) THEN true\n            ELSE false\n        END AS is_currently_working\n   FROM ((tenant_users tu\n     JOIN tenant_users_details tud ON ((tu.id = tud.tenant_user_id)))\n     LEFT JOIN tenant_staff_assignments tsa ON ((tu.id = tsa.user_id)))\n  WHERE ((tu.is_active = true) AND ((tu.role = 'user'::tenant_user_role) OR (tu.role = 'manager'::tenant_user_role)));",
    "view_comment": "Hiển thị nhân viên đang hoạt động và thông tin phân công"
  },
  {
    "view_name": "tenant_chat_routing_summary_view",
    "view_definition": " SELECT tc.tenant_id,\n    tc.id AS chat_session_id,\n    tc.created_at,\n    tc.status,\n    tc.source_type,\n    tc.source_id,\n    tca.assigned_user_id,\n    tca.assignment_status,\n    tca.assigned_at,\n    tca.accepted_at,\n        CASE\n            WHEN (tca.accepted_at IS NOT NULL) THEN EXTRACT(epoch FROM (tca.accepted_at - tca.assigned_at))\n            ELSE NULL::numeric\n        END AS response_time_seconds\n   FROM (tenant_chat_sessions tc\n     LEFT JOIN tenant_chat_session_assignments tca ON ((tc.id = tca.chat_session_id)));",
    "view_comment": "Tổng hợp thông tin định tuyến phiên chat và thời gian phản hồi"
  }
]
[
  {
    "table_name": "tenant_chat_routing_rules",
    "trigger_name": "update_tenant_chat_routing_rules_updated_at",
    "event_manipulation": "UPDATE",
    "action_timing": "BEFORE",
    "action_statement": "EXECUTE FUNCTION update_tenant_chat_routing_rules_updated_at()",
    "trigger_comment": "Tự động cập nhật trường updated_at khi có thay đổi"
  }
]


[
  {
    "table_name": "tenant_chat_routing_rules",
    "constraint_name": "fk_tenant_chat_routing_rules_reception_point",
    "constraint_type": "FOREIGN KEY",
    "constraint_definition": "FOREIGN KEY (target_reception_point_id) REFERENCES tenant_message_reception_points(id) ON DELETE SET NULL",
    "constraint_comment": null
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "constraint_name": "tenant_chat_routing_rules_target_reception_point_id_fkey",
    "constraint_type": "FOREIGN KEY",
    "constraint_definition": "FOREIGN KEY (target_reception_point_id) REFERENCES tenant_message_reception_points(id)",
    "constraint_comment": null
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "constraint_name": "tenant_chat_routing_rules_target_user_id_fkey",
    "constraint_type": "FOREIGN KEY",
    "constraint_definition": "FOREIGN KEY (target_user_id) REFERENCES tenant_users(id)",
    "constraint_comment": null
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "constraint_name": "tenant_chat_routing_rules_tenant_id_fkey",
    "constraint_type": "FOREIGN KEY",
    "constraint_definition": "FOREIGN KEY (tenant_id) REFERENCES tenants(id)",
    "constraint_comment": null
  },
  {
    "table_name": "tenant_chat_routing_rules",
    "constraint_name": "tenant_chat_routing_rules_pkey",
    "constraint_type": "PRIMARY KEY",
    "constraint_definition": "PRIMARY KEY (id)",
    "constraint_comment": null
  }
]