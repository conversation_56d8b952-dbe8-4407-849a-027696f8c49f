import React from 'react';
import { Badge } from '../Badge';

interface StatusBadgeProps {
  status: 'active' | 'inactive' | 'pending' | 'blocked' | 'maintenance' | 'occupied' | 'available' | string;
  children: React.ReactNode;
  size?: 'small' | 'medium' | 'large';
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, children, size = 'medium' }) => {
  const getVariant = () => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'available':
        return 'success';
      case 'inactive':
      case 'blocked':
        return 'danger';
      case 'pending':
        return 'warning';
      case 'maintenance':
        return 'info';
      case 'occupied':
        return 'primary';
      default:
        return 'default';
    }
  };
  
  return (
    <Badge variant={getVariant()} size={size}>
      {children}
    </Badge>
  );
};
