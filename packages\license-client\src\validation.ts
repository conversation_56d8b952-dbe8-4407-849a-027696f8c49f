import axios from 'axios';
import { LicenseValidationRequest, LicenseValidationResponse } from './types';
import { generateHardwareFingerprint } from './fingerprint';

/**
 * License validation service
 */
export class LicenseValidationService {
  private apiUrl: string;
  
  /**
   * Constructor
   * @param apiUrl The URL of the license service API
   */
  constructor(apiUrl: string) {
    this.apiUrl = apiUrl;
  }
  
  /**
   * Validate a license
   * @param licenseKey The license key
   * @param useAutoFingerprint Whether to automatically generate hardware fingerprint
   * @param customFingerprint Optional custom hardware fingerprint
   * @returns Promise with validation response
   */
  async validateLicense(
    licenseKey: string,
    useAutoFingerprint: boolean = true,
    customFingerprint?: string
  ): Promise<LicenseValidationResponse> {
    try {
      // Generate hardware fingerprint if auto is enabled
      const hardwareFingerprint = useAutoFingerprint
        ? generateHardwareFingerprint()
        : customFingerprint || '';
      
      if (!hardwareFingerprint) {
        throw new Error('Hardware fingerprint is required for validation');
      }
      
      const request: LicenseValidationRequest = {
        license_key: licenseKey,
        hardware_fingerprint: hardwareFingerprint
      };
      
      const response = await axios.post<LicenseValidationResponse>(
        `${this.apiUrl}/license-validate`,
        request
      );
      
      return response.data;
    } catch (error: any) {
      console.error('License validation failed:', error);
      return {
        success: false,
        is_valid: false,
        message: 'License validation failed',
        error: error.message || 'Unknown error'
      };
    }
  }
}
