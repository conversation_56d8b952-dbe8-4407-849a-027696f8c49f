-- Tạo function để kiểm tra giới hạn QR code cải tiến
CREATE OR REPLACE FUNCTION public.check_qr_code_limit(tenant_id_param UUID, is_temporary_user BOOLEAN DEFAULT FALSE)
RETURNS BOOLEAN AS $$
DECLARE
    current_count INTEGER;
    max_allowed INTEGER;
    is_enforced BOOLEAN;
    temp_user_limit INTEGER := 3; -- <PERSON><PERSON>ớ<PERSON> hạn mặc định cho tài khoản tạm thời
BEGIN
    -- Nếu là tài khoản tạm thời, áp dụng quy tắc khác
    IF is_temporary_user THEN
        -- Đếm số lượng QR code thuộc về user tạm thời này
        SELECT COUNT(*) INTO current_count
        FROM public.qr_codes
        WHERE created_by_temporary_user = TRUE AND temporary_user_id = tenant_id_param;
        
        -- Kiểm tra giới hạn cho tài khoản tạm thời
        RETURN current_count < temp_user_limit;
    END IF;
    
    -- <PERSON><PERSON><PERSON> với tenant thông thường, giữ nguyên logic cũ
    SELECT 
        COUNT(*), 
        t.max_qr_codes, 
        t.is_limitation_enforced
    INTO 
        current_count, 
        max_allowed, 
        is_enforced
    FROM 
        public.qr_codes qr 
    JOIN 
        public.tenants t ON qr.tenant_id = t.id
    WHERE 
        qr.tenant_id = tenant_id_param
    GROUP BY 
        t.max_qr_codes, t.is_limitation_enforced;
    
    -- Xử lý trường hợp không tìm thấy tenant hoặc chưa có QR codes
    IF current_count IS NULL THEN
        SELECT 
            max_qr_codes, 
            is_limitation_enforced
        INTO 
            max_allowed, 
            is_enforced
        FROM 
            public.tenants
        WHERE 
            id = tenant_id_param;
        
        IF max_allowed IS NULL THEN
            RETURN FALSE;
        END IF;
        
        current_count := 0;
    END IF;
    
    -- Kiểm tra giới hạn cho tenant
    RETURN (NOT is_enforced) OR (max_allowed IS NULL) OR (current_count < max_allowed);
END;

$$ LANGUAGE plpgsql;

-- Thêm cột mới vào bảng qr_codes để đánh dấu QR code được tạo bởi tài khoản tạm thời
DO $$ 
BEGIN
    -- Thêm cột created_by_temporary_user nếu chưa tồn tại
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                   WHERE table_schema = 'public' AND table_name = 'qr_codes' 
                   AND column_name = 'created_by_temporary_user') THEN
        ALTER TABLE public.qr_codes 
        ADD COLUMN created_by_temporary_user BOOLEAN DEFAULT FALSE;
    END IF;

    -- Thêm cột temporary_user_id nếu chưa tồn tại
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                   WHERE table_schema = 'public' AND table_name = 'qr_codes' 
                   AND column_name = 'temporary_user_id') THEN
        ALTER TABLE public.qr_codes 
        ADD COLUMN temporary_user_id UUID NULL;
    END IF;
END $$;

-- Chắc chắn mỗi tenant có ít nhất một mã QR code mặc định
DO $$ 
DECLARE
    tenant_record RECORD;
    default_qr_exists BOOLEAN;
    code_value VARCHAR;
BEGIN
    -- Lặp qua từng tenant
    FOR tenant_record IN SELECT id, name FROM public.tenants WHERE is_active = true
    LOOP
        -- Kiểm tra xem tenant đã có mã QR mặc định chưa
        SELECT EXISTS (
            SELECT 1 FROM public.qr_codes
            WHERE tenant_id = tenant_record.id AND location = 'Mã QR mặc định'
        ) INTO default_qr_exists;
        
        -- Nếu chưa có, tạo mã QR mặc định
        IF NOT default_qr_exists THEN
            code_value := 'default-' || tenant_record.id::text;
            
            INSERT INTO public.qr_codes (
                tenant_id,
                code_value,
                location,
                description,
                status,
                scan_count,
                created_at,
                updated_at
            ) VALUES (
                tenant_record.id,
                code_value,
                'Mã QR mặc định',
                'Mã QR mặc định cho ' || tenant_record.name,
                'active',
                0,
                NOW(),
                NOW()
            );
        END IF;
    END LOOP;
END $$;

-- Thêm comment cho các cột mới
COMMENT ON COLUMN public.qr_codes.created_by_temporary_user IS 'Đánh dấu QR code được tạo bởi tài khoản tạm thời';
COMMENT ON COLUMN public.qr_codes.temporary_user_id IS 'ID của tài khoản tạm thời tạo ra QR code này';
