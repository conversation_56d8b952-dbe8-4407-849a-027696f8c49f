# LoaLoa - G<PERSON><PERSON>i pháp Chat Đa Ngôn Ngữ

Tổng quan về LoaLoa
LoaLoa là một giải pháp chat đa ngôn ngữ cho ngành dịch vụ, đặc biệt tập trung vào khả năng giao tiếp giữa khách hàng và nhà cung cấp dịch vụ nói các ngôn ngữ khác nhau. Hệ thống được thiết kế với kiến trúc microservices, hỗ trợ multiple tenancy (đa người thuê), dễ dàng mở rộng từ một khách sạn đến nhiều doanh nghiệp khác nhau.
Ngữ cảnh sử dụng
**Ngữ cảnh khách sạn/resort**
**Quy trình check-in và tương tác lễ tân:**
•	Quầy lễ tân có biển thông báo đa ngôn ngữ và mã QR dành cho LoaLoa
•	Khách quét mã QR bằng camera điện thoại hoặc app LoaLoa (nếu đã cài)
•	<PERSON>ệ thống tạo tài khoản khách tạm thời, kết nối với màn hình của nhân viên lễ tân
•	Tự động phát hiện ngôn ngữ mặc định của thiết bị khách
•	Giao diện hiển thị song song hai ngôn ngữ (của khách và nhân viên)
•	Nhân viên lễ tân nhận thông tin check-in qua chat, hỗ trợ khách một cách mượt mà
**Quy trình sử dụng dịch vụ trong kỳ nghỉ:**
•	Mỗi phòng có mã QR riêng kết nối khách với bộ phận dịch vụ phòng
•	Các khu vực chung (hồ bơi, nhà hàng, spa) có mã QR riêng
•	Khách có thể đặt đồ ăn, đặt lịch spa, yêu cầu dịch vụ phòng bằng tiếng mẹ đẻ
•	Thông báo được dịch ngay lập tức cho nhân viên phù hợp
**Quy trình check-out:**
•	Khách chat để yêu cầu check-out
•	Nhân viên gửi hóa đơn chi tiết thông qua hệ thống
•	Khách xác nhận và thanh toán
•	Khách được mời đánh giá trải nghiệm
**Mở rộng cho các ngữ cảnh khác
Ngoài khách sạn, hệ thống được thiết kế mở để hỗ trợ:**
•	Taxi/dịch vụ đi xe
•	Nhà hàng/cửa hàng bán lẻ
•	Dịch vụ y tế

## Cấu trúc dự án

Cấu trúc project:
•	Kiến trúc: Monorepo với Turborepo + PNPM
•	Thư mục gốc: D:\loaloa
•	Cấu trúc: /apps (ứng dụng), /packages (shared libraries), /services (backend)
•	Storybook: Đã cấu hình trong /apps/storybook
•	UI Components: /packages/ui
•	Design tokens: /packages/design-tokens
Công nghệ đang sử dụng:
•	Node.js 20.12.2 LTS
•	TypeScript 5.4.5
•	React 18.2.0
•	Next.js 14.1.3
•	Supabase (PostgreSQL)
•	JWT cho authentication
•	WebSocket cho real-time communication
