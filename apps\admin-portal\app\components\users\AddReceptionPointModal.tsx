'use client';
import { useState, useEffect } from 'react';
import styles from './AddReceptionPointModal.module.scss';
import { Modal, Button, Alert } from '@ui';

interface AddReceptionPointModalProps {
  userId: string;
  onClose: () => void;
  onSave: () => void;
}

export default function AddReceptionPointModal({
  userId,
  onClose,
  onSave
}: AddReceptionPointModalProps) {
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [receptionPoints, setReceptionPoints] = useState<any[]>([]);
  const [formData, setFormData] = useState({
    reception_point_id: '',
    priority: 1,
    is_primary: false
  });

  useEffect(() => {
    const fetchReceptionPoints = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/reception-points?is_active=true&limit=100');
        if (!response.ok) {
          throw new Error('Failed to fetch reception points');
        }
        const data = await response.json();
        setReceptionPoints(data.data || []);
      } catch (err) {
        console.error('Error fetching reception points:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchReceptionPoints();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: name === 'priority' ? parseInt(value) || 1 : value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.reception_point_id) {
      setError('Please select a reception point');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);
      
      const response = await fetch(`/api/users/${userId}/reception-points`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to assign reception point');
      }
      
      onSave();
    } catch (err) {
      console.error('Error assigning reception point:', err);
      setError(err instanceof Error ? err.message : 'Failed to assign reception point');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Modal isOpen={true} title="Assign Reception Point" onClose={onClose}>
      <form onSubmit={handleSubmit}>
        {error && (
          <Alert variant="error" title="Error" onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        <div className={styles.formGroup}>
          <label htmlFor="reception_point_id">
            Reception Point <span className={styles.required}>*</span>
          </label>
          <select
            id="reception_point_id"
            name="reception_point_id"
            value={formData.reception_point_id}
            onChange={handleInputChange}
            disabled={loading || submitting}
          >
            <option value="">Select a reception point</option>
            {receptionPoints.map(point => (
              <option key={point.id} value={point.id}>
                {point.name} ({point.code})
              </option>
            ))}
          </select>
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="priority">Priority</label>
          <input
            type="number"
            id="priority"
            name="priority"
            min="1"
            max="100"
            value={formData.priority}
            onChange={handleInputChange}
            disabled={submitting}
          />
          <div className={styles.fieldDescription}>
            Higher priority users will receive messages first (1-100)
          </div>
        </div>
        
        <div className={styles.formGroup}>
          <div className={styles.checkboxContainer}>
            <input
              type="checkbox"
              id="is_primary"
              name="is_primary"
              checked={formData.is_primary}
              onChange={handleInputChange}
              disabled={submitting}
            />
            <label htmlFor="is_primary">Set as primary reception point</label>
          </div>
          <div className={styles.fieldDescription}>
            Primary reception points take precedence over others
          </div>
        </div>
        
        <div className={styles.formActions}>
          <Button
            type="button"
            variant="secondary"
            label="Cancel"
            onClick={onClose}
            disabled={submitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            label="Submit"
            loading={submitting}
            disabled={loading || submitting}
          >
            Assign
          </Button>
        </div>
      </form>
    </Modal>
  );
}
