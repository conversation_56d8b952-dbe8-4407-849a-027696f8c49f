import type { Meta, StoryObj } from '@storybook/react';
import { FormGroup } from './index';
import { Input } from '../Input';
import { Checkbox } from '../Checkbox';
import { Radio } from '../Radio';
import { Select } from '../Select';

const meta = {
  title: 'UI/Form/FormGroup',
  component: FormGroup,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'Form group label',
    },
    helperText: {
      control: 'text',
      description: 'Helper text displayed below the form group',
    },
    errorText: {
      control: 'text',
      description: 'Error message displayed when error is true',
    },
    error: {
      control: 'boolean',
      description: 'Whether the form group is in error state',
    },
    required: {
      control: 'boolean',
      description: 'Whether the form group is required',
    },
  },
} satisfies Meta<typeof FormGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

export const WithInput: Story = {
  args: {
    label: 'Personal Information',
    required: true,
    children: (
      <Input 
        placeholder="Enter your full name"
      />
    ),
  },
};

export const WithMultipleInputs: Story = {
  args: {
    label: 'Contact Information',
    children: (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        <Input 
          label="Email"
          placeholder="Enter your email"
          type="email"
        />
        <Input 
          label="Phone"
          placeholder="Enter your phone number"
          type="tel"
        />
      </div>
    ),
  },
};

export const WithCheckboxes: Story = {
  args: {
    label: 'Notification Preferences',
    helperText: 'Choose how you would like to be notified',
    children: (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <Checkbox 
          label="Email notifications"
          checked={true}
        />
        <Checkbox 
          label="SMS notifications"
          checked={false}
        />
        <Checkbox 
          label="Push notifications"
          checked={false}
        />
      </div>
    ),
  },
};

export const WithRadioButtons: Story = {
  args: {
    label: 'Gender',
    required: true,
    children: (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <Radio 
          label="Male"
          name="gender"
          checked={false}
        />
        <Radio 
          label="Female"
          name="gender"
          checked={true}
        />
        <Radio 
          label="Other"
          name="gender"
          checked={false}
        />
      </div>
    ),
  },
};

export const WithError: Story = {
  args: {
    label: 'Password',
    error: true,
    errorText: 'Password does not meet the requirements',
    children: (
      <Input 
        type="password"
        placeholder="Enter password"
      />
    ),
  },
};

export const CompleteForm: Story = {
  name: 'Complete Form Example',
  parameters: { controls: { disable: true } },
  render: () => (
    <div style={{ maxWidth: '500px' }}>
      <FormGroup label="Personal Information" required>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <Input 
            label="Full Name"
            placeholder="Enter your full name"
          />
          <Input 
            label="Email Address"
            placeholder="Enter your email"
            type="email"
          />
        </div>
      </FormGroup>
      
      <FormGroup label="Address">
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <Input 
            label="Street Address"
            placeholder="Enter your street address"
          />
          <div style={{ display: 'flex', gap: '16px' }}>
            <Input 
              label="City"
              placeholder="City"
            />
            <Input 
              label="Postal Code"
              placeholder="Postal code"
            />
          </div>
          <Select 
            label="Country"
            options={[
              { value: '', label: 'Select a country' },
              { value: 'us', label: 'United States' },
              { value: 'ca', label: 'Canada' },
              { value: 'mx', label: 'Mexico' },
            ]}
          />
        </div>
      </FormGroup>
      
      <FormGroup label="Preferences">
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <Checkbox 
            label="Subscribe to newsletter"
            checked={true}
          />
          <Checkbox 
            label="Remember me"
            checked={false}
          />
        </div>
      </FormGroup>
    </div>
  ),
};
