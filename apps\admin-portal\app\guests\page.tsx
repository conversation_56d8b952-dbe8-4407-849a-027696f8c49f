'use client';
import { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import { Alert } from '@ui';
import styles from './guests.module.scss';
import RoomSelectionModal from '../components/RoomSelectionModal';


interface Guest {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  room_number: string;
  check_in: string;
  check_out: string | null;
  is_active: boolean;
}

export default function GuestsPage() {
  const [guests, setGuests] = useState<Guest[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

const [isCheckInModalOpen, setIsCheckInModalOpen] = useState(false);
const [isReactivateModalOpen, setIsReactivateModalOpen] = useState(false);
const [selectedGuestId, setSelectedGuestId] = useState<string | null>(null);
  
  // Filter states
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [roomFilter, setRoomFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('');
  
  // Pagination
  const [page, setPage] = useState<number>(1);
  const [limit] = useState<number>(10);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    fetchGuests();
  }, [page, limit, statusFilter, roomFilter, dateFilter]);

  const fetchGuests = async () => {
    try {
      setLoading(true);
      // Tạo query string cho các bộ lọc
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());
      
      if (roomFilter) params.append('room', roomFilter);
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (dateFilter) params.append('date', dateFilter);
      
      // Gọi API
      const response = await fetch(`/api/guests?${params.toString()}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch guests');
      }

      setGuests(result.data);
      setTotal(result.meta.total);
      setError(null);
    } catch (err) {
      console.error('Error fetching guests:', err);
      setError('Không thể tải dữ liệu khách hàng. Vui lòng thử lại sau.');
    } finally {
      setLoading(false);
    }
  };

  // Xử lý search theo tên hoặc email sau khi nhập
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Filter guests locally based on search query
  const filteredGuests = useMemo(() => {
    if (!searchQuery) return guests;
    
    return guests.filter(guest => 
      guest.full_name.toLowerCase().includes(searchQuery.toLowerCase()) || 
      (guest.email && guest.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (guest.phone && guest.phone.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [guests, searchQuery]);

  // Format date to display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Handle check-in
const handleCheckIn = (guestId: string) => {
  setSelectedGuestId(guestId);
  setIsCheckInModalOpen(true);
};

const handleCheckInConfirm = async (roomNumber: string) => {
  if (!selectedGuestId) return;
  
  try {
    const response = await fetch(`/api/guests/${selectedGuestId}/checkin`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ room_number: roomNumber })
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to check-in guest');
    }

    // Refresh guest list
    fetchGuests();
    alert('Check-in thành công!');
  } catch (err) {
    console.error('Error checking in guest:', err);
    alert('Không thể check-in khách. Vui lòng thử lại.');
  }
};



  // Handle check-out
  const handleCheckOut = async (guestId: string) => {
    if (!confirm('Bạn có chắc muốn check-out khách này?')) return;
    
    try {
      const response = await fetch(`/api/guests/${guestId}/checkout`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to check-out guest');
      }

      // Refresh guest list
      fetchGuests();
      alert('Check-out thành công!');
    } catch (err) {
      console.error('Error checking out guest:', err);
      alert('Không thể check-out khách. Vui lòng thử lại.');
    }
  };

  // Calculate total pages
  const totalPages = Math.ceil(total / limit);

  // Handle page change
  const changePage = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    setPage(newPage);
  };

  if (loading && page === 1) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Đang tải danh sách khách...</p>
      </div>
    );
  }
// Handle reactivate (undo check-out)
const handleReactivate = (guestId: string) => {
  setSelectedGuestId(guestId);
  setIsReactivateModalOpen(true);
};

const handleReactivateConfirm = async (roomNumber: string) => {
  if (!selectedGuestId) return;
  
  try {
    const response = await fetch(`/api/guests/${selectedGuestId}/reactivate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ room_number: roomNumber })
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to reactivate guest');
    }

    // Refresh guest list
    fetchGuests();
    alert('Kích hoạt lại khách thành công!');
  } catch (err) {
    console.error('Error reactivating guest:', err);
    alert('Không thể kích hoạt lại khách. Vui lòng thử lại.');
  }
};

// Handle delete
const handleDelete = async (guestId: string) => {
  if (!confirm('Bạn có chắc chắn muốn xóa khách hàng này? Hành động này không thể hoàn tác.')) return;
  
  try {
    const response = await fetch(`/api/guests/${guestId}`, {
      method: 'DELETE',
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to delete guest');
    }

    // Refresh guest list
    fetchGuests();
    alert('Xóa khách thành công!');
  } catch (err) {
    console.error('Error deleting guest:', err);
    alert('Không thể xóa khách. Vui lòng thử lại.');
  }
};
  return (
    <div className={styles.container}>
      <div className={styles.pageHeader}>
        <h1 className={styles.pageTitle}>Quản lý khách</h1>
        <Link href="/guests/create" className={styles.addButton}>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM15 *********************************" fill="currentColor"/>
          </svg>
          Thêm khách
        </Link>
      </div>

      {error && (
        <Alert
          variant="error"
          title="Lỗi tải dữ liệu"
          closable={true}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      <div className={styles.filterBar}>
        <div className={styles.searchBox}>
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M12.5 11H11.71L11.43 10.73C12.41 9.59 13 8.11 13 6.5C13 2.91 10.09 0 6.5 0C2.91 0 0 2.91 0 6.5C0 10.09 2.91 13 6.5 13C8.11 13 9.59 12.41 10.73 11.43L11 11.71V12.5L16 17.49L17.49 16L12.5 11ZM6.5 11C4.01 11 2 8.99 2 6.5C2 4.01 4.01 2 6.5 2C8.99 2 11 4.01 11 6.5C11 8.99 8.99 11 6.5 11Z" fill="#7D8491"/>
          </svg>
          <input
            type="text"
            placeholder="Tìm kiếm theo tên, email, số điện thoại..."
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>
        
        <div className={styles.filters}>
          <div className={styles.filterGroup}>
            <label htmlFor="room-filter">Phòng:</label>
            <input
              id="room-filter"
              type="text"
              value={roomFilter}
              onChange={(e) => setRoomFilter(e.target.value)}
              placeholder="Số phòng"
            />
          </div>
          
          <div className={styles.filterGroup}>
            <label htmlFor="status-filter">Trạng thái:</label>
            <select
              id="status-filter"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">Tất cả</option>
              <option value="active">Đang lưu trú</option>
              <option value="inactive">Đã check-out</option>
            </select>
          </div>
          
          <div className={styles.filterGroup}>
            <label htmlFor="date-filter">Ngày check-in:</label>
            <input
              id="date-filter"
              type="date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
            />
          </div>
          
          <button className={styles.filterButton} onClick={() => fetchGuests()}>
            Lọc
          </button>
        </div>
      </div>

      {filteredGuests.length === 0 ? (
        <div className={styles.noResults}>
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
            <path d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3C5.91 3 3 5.91 3 9.5C3 13.09 5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5C5 7.01 7.01 5 9.5 5C11.99 5 14 7.01 14 9.5C14 11.99 11.99 14 9.5 14Z" fill="#7D8491"/>
          </svg>
          <p>Không tìm thấy khách nào phù hợp với tiêu chí tìm kiếm</p>
        </div>
      ) : (
        <div className={styles.guestList}>
          <table className={styles.guestTable}>
            <thead>
              <tr>
                <th>Khách hàng</th>
                <th>Số phòng</th>
                <th>Thời gian check-in</th>
                <th>Thời gian check-out</th>
                <th>Trạng thái</th>
                <th>Hành động</th>
              </tr>
            </thead>
            <tbody>
              {filteredGuests.map(guest => (
                <tr key={guest.id} className={!guest.is_active ? styles.inactiveRow : ''}>
                  <td>
                    <Link href={`/guests/${guest.id}`} className={styles.guestName}>
                      <div className={styles.guestInfo}>
                        <div className={styles.guestAvatar}>
                          {guest.full_name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <p className={styles.guestFullName}>{guest.full_name}</p>
                          {guest.email && <p className={styles.guestEmail}>{guest.email}</p>}
                          {guest.phone && <p className={styles.guestPhone}>{guest.phone}</p>}
                        </div>
                      </div>
                    </Link>
                  </td>
                  <td>{guest.room_number || '—'}</td>
                  <td>{formatDate(guest.check_in)}</td>
                  <td>{guest.check_out ? formatDate(guest.check_out) : '—'}</td>
                  <td>
                    <span className={
                      `${styles.status} ${guest.is_active ? styles.active : styles.inactive}`
                    }>
                      {guest.is_active ? 'Đang lưu trú' : 'Đã check-out'}
                    </span>
                  </td>
                  <td>
  <div className={styles.actions}>
    <Link href={`/guests/${guest.id}/edit`} className={styles.actionButton} title="Chỉnh sửa">
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
        <path d="M10.5 6L12 7.5M2.25 15.75L5.4 15.12C5.64 15.07 5.85 14.95 6.02 14.78L15.75 5.05C16.08 4.72 16.08 4.18 15.75 3.85L14.15 2.25C13.82 1.92 13.28 1.92 12.95 2.25L3.22 11.98C3.05 12.15 2.93 12.36 2.88 12.6L2.25 15.75Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    </Link>
    
    {guest.is_active && !guest.room_number && (
      <button 
        className={styles.actionButton} 
        onClick={() => handleCheckIn(guest.id)}
        title="Check-in"
      >
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
          <path d="M9 1.5L11.3175 6.195L16.5 6.9525L12.75 10.605L13.635 15.765L9 13.3275L4.365 15.765L5.25 10.605L1.5 6.9525L6.6825 6.195L9 1.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </button>
    )}
    
    {guest.is_active && guest.room_number && (
      <button 
        className={styles.actionButton} 
        onClick={() => handleCheckOut(guest.id)}
        title="Check-out"
      >
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
          <path d="M6.75 15.75H3.75C3.35218 15.75 2.97064 15.592 2.68934 15.3107C2.40804 15.0294 2.25 14.6478 2.25 14.25V3.75C2.25 3.35218 2.40804 2.97064 2.68934 2.68934C2.97064 2.40804 3.35218 2.25 3.75 2.25H6.75M12.75 12.75L15.75 9.75M15.75 9.75L12.75 6.75M15.75 9.75H6.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </button>
    )}
    
    {!guest.is_active && (
      <>
        <button 
          className={styles.actionButton} 
          onClick={() => handleReactivate(guest.id)}
          title="Kích hoạt lại"
        >
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M1.5 9C1.5 13.1421 4.85786 16.5 9 16.5C13.1421 16.5 16.5 13.1421 16.5 9C16.5 4.85786 13.1421 1.5 9 1.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M9 4.5L6 7.5M6 7.5L9 10.5M6 7.5H15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        
        <button 
          className={`${styles.actionButton} ${styles.deleteButton}`} 
          onClick={() => handleDelete(guest.id)}
          title="Xóa"
        >
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M2.25 4.5H3.75H15.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M6 4.5V3C6 2.60218 6.15804 2.22064 6.43934 1.93934C6.72064 1.65804 7.10218 1.5 7.5 1.5H10.5C10.8978 1.5 11.2794 1.65804 11.5607 1.93934C11.842 2.22064 12 2.60218 12 3V4.5M14.25 4.5V15C14.25 15.3978 14.092 15.7794 13.8107 16.0607C13.5294 16.342 13.1478 16.5 12.75 16.5H5.25C4.85218 16.5 4.47064 16.342 4.18934 16.0607C3.90804 15.7794 3.75 15.3978 3.75 15V4.5H14.25Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M7.5 8.25V12.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M10.5 8.25V12.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </>
    )}
  </div>
</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      {/* Pagination */}
      {totalPages > 1 && (
        <div className={styles.pagination}>
          <button 
            className={styles.paginationButton} 
            onClick={() => changePage(page - 1)}
            disabled={page === 1}
          >
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
              <path d="M11.25 13.5L6.75 9L11.25 4.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Trước
          </button>
          
          <div className={styles.paginationPages}>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(p => (
              <button 
                key={p}
                className={`${styles.paginationPage} ${p === page ? styles.active : ''}`}
                onClick={() => changePage(p)}
              >
                {p}
              </button>
            ))}
          </div>
          
          <button 
            className={styles.paginationButton}
            onClick={() => changePage(page + 1)}
            disabled={page === totalPages}
          >
            Sau
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
              <path d="M6.75 4.5L11.25 9L6.75 13.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
      )}	
 {/* Modals */}
    <RoomSelectionModal
      isOpen={isCheckInModalOpen}
      onClose={() => setIsCheckInModalOpen(false)}
      onConfirm={handleCheckInConfirm}
      title="Chọn phòng để check-in"
    />
    
    <RoomSelectionModal
      isOpen={isReactivateModalOpen}
      onClose={() => setIsReactivateModalOpen(false)}
      onConfirm={handleReactivateConfirm}
      title="Chọn phòng để kích hoạt lại khách"
    />	  
    </div>
  );
}
