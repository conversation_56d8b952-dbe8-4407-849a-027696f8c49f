'use client';
import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '../../dashboard-layout';
import styles from './user-detail.module.scss';
import { But<PERSON>, <PERSON>ert } from '@ui';
import { Tab, <PERSON>b<PERSON>ist, TabPanel, TabPanels, Tabs } from '@ui';
import { Skeleton } from '@ui';
import UserReceptionPointsList from '../../components/users/UserReceptionPointsList';
import AddReceptionPointModal from '../../components/users/AddReceptionPointModal';

export default function UserDetailPage() {
  const { id: userId } = useParams<{ id: string }>();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [receptionPoints, setReceptionPoints] = useState<any[]>([]);
  const [loadingReceptionPoints, setLoadingReceptionPoints] = useState(false);
  const [showAddReceptionPointModal, setShowAddReceptionPointModal] = useState(false);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`/api/users/${userId}`);
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('User not found');
          }
          throw new Error('Failed to fetch user details');
        }
        const data = await response.json();
        setUser(data.data);
      } catch (err) {
        console.error('Error fetching user:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchUser();
    }
  }, [userId]);

  useEffect(() => {
    const fetchReceptionPoints = async () => {
      if (!userId) return;
      try {
        setLoadingReceptionPoints(true);
        const response = await fetch(`/api/users/${userId}/reception-points`);
        if (response.ok) {
          const data = await response.json();
          setReceptionPoints(data.data || []);
        } else {
          console.error('Error fetching user reception points');
        }
      } catch (err) {
        console.error('Error fetching user reception points:', err);
      } finally {
        setLoadingReceptionPoints(false);
      }
    };

    fetchReceptionPoints();
  }, [userId]);

  const handleAddReceptionPoint = async () => {
    // Refresh reception points after adding a new one
    const response = await fetch(`/api/users/${userId}/reception-points`);
    if (response.ok) {
      const data = await response.json();
      setReceptionPoints(data.data || []);
    }
    setShowAddReceptionPointModal(false);
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className={styles.container}>
          <div className={styles.header}>
            <Skeleton height="2rem" width="50%" />
          </div>
          <div className={styles.content}>
            <Skeleton height="20rem" />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !user) {
    return (
      <DashboardLayout>
        <div className={styles.container}>
          <Alert variant="error" title="Error" closable={false}>
            {error || 'Failed to load user details'}
          </Alert>
          <div className={styles.buttonContainer}>
            <Link href="/users">
              <Button variant="secondary">Back to Users</Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const userDetails = user.tenant_users_details || {};

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <div>
            <h1 className={styles.title}>{userDetails.display_name || 'User Details'}</h1>
            <p className={styles.subtitle}>{userDetails.email || 'No email provided'}</p>
          </div>
          <div className={styles.actions}>
            <Link href={`/users/${userId}/edit`}>
              <Button variant="secondary" label ="Edit User">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M11.3333 2.00001C11.5085 1.82494 11.7163 1.68605 11.9451 1.59129C12.1739 1.49653 12.4187 1.44775 12.6667 1.44775C12.9146 1.44775 13.1595 1.49653 13.3883 1.59129C13.6171 1.68605 13.8248 1.82494 14 2.00001C14.1751 2.17508 14.314 2.38283 14.4088 2.61162C14.5035 2.84041 14.5523 3.08536 14.5523 3.33334C14.5523 3.58132 14.5035 3.82627 14.4088 4.05506C14.314 4.28385 14.1751 4.4916 14 4.66667L5 13.6667L1.33334 14.6667L2.33334 11L11.3333 2.00001Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                Edit
              </Button>
            </Link>
          </div>
        </div>
        <div className={styles.content}>
          <Tabs>
            <TabList>
              <Tab index={0}>User Details</Tab>
              <Tab index={1}>Reception Points</Tab>
            </TabList>
            <TabPanels>
              <TabPanel index={0}>
                <div className={styles.detailsCard}>
                  <div className={styles.profileSection}>
                    <div className={styles.avatarContainer}>
                      {userDetails.avatar_url ? (
                        <img
                          src={userDetails.avatar_url}
                          alt={userDetails.display_name || 'User avatar'}
                          className={styles.avatar}
                        />
                      ) : (
                        <div className={styles.avatarPlaceholder}>
                          {(userDetails.display_name || '').charAt(0).toUpperCase() || 'U'}
                        </div>
                      )}
                    </div>
                    <div className={styles.profileInfo}>
                      <h2 className={styles.name}>{userDetails.display_name || 'No name'}</h2>
                      <div className={styles.infoItem}>
                        <span className={styles.label}>Email:</span>
                        <span>{userDetails.email || 'No email provided'}</span>
                      </div>
                      <div className={styles.infoItem}>
                        <span className={styles.label}>Role:</span>
                        <span className={styles.roleTag}>{user.role || 'User'}</span>
                      </div>
                      <div className={styles.infoItem}>
                        <span className={styles.label}>Status:</span>
                        <span
                          className={`${styles.statusTag} ${
                            user.is_active ? styles.active : styles.inactive
                          }`}
                        >
                          {user.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className={styles.detailsSection}>
                    <h3 className={styles.sectionTitle}>User Information</h3>
                    <div className={styles.detailsGrid}>
                      <div className={styles.detailItem}>
                        <span className={styles.label}>Title:</span>
                        <span>{userDetails.title || 'Not specified'}</span>
                      </div>
                      <div className={styles.detailItem}>
                        <span className={styles.label}>Department:</span>
                        <span>{userDetails.department || 'Not specified'}</span>
                      </div>
                      <div className={styles.detailItem}>
                        <span className={styles.label}>Phone:</span>
                        <span>{userDetails.phone || 'Not specified'}</span>
                      </div>
                      <div className={styles.detailItem}>
                        <span className={styles.label}>Preferred Language:</span>
                        <span>{userDetails.preferred_language || 'English'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </TabPanel>
              <TabPanel index={1}>
                <div className={styles.receptionPointsSection}>
                  <div className={styles.sectionHeader}>
                    <h3 className={styles.sectionTitle}>Assigned Reception Points</h3>
                    <Button
                      variant="primary" label="Assign"
                      onClick={() => setShowAddReceptionPointModal(true)}
                    >
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path
                          d="M8 3.33334V12.6667"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M3.33334 8H12.6667"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      Assign Reception Point
                    </Button>
                  </div>
                  <UserReceptionPointsList
                    userId={userId as string}
                    onReceptionPointsChange={() => handleAddReceptionPoint()}
                  />
                </div>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </div>
      </div>
      {showAddReceptionPointModal && (
        <AddReceptionPointModal
          userId={userId as string}
          onClose={() => setShowAddReceptionPointModal(false)}
          onSave={handleAddReceptionPoint}
        />
      )}
    </DashboardLayout>
  );
}
