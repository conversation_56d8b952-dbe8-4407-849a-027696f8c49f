.licenseDetail {
  padding: 24px;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .licenseInfo {
      .licenseKey {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        font-family: monospace;
      }
      
      .licenseMeta {
        display: flex;
        gap: 12px;
        align-items: center;
        
        .customerName {
          font-size: 16px;
          color: #666;
        }
      }
    }
    
    .headerActions {
      display: flex;
      gap: 12px;
    }
  }
  
  .tabsContainer {
    .tabContent {
      margin-top: 16px;
    }
    
    .alertBadge {
      margin-left: 8px;
      background-color: #ef4444;
      color: white;
      font-size: 12px;
      padding: 2px 6px;
      border-radius: 10px;
    }
  }
  // Tab Buttons
.tabButtons {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
  overflow-x: auto;
  padding-bottom: 1px; // <PERSON><PERSON> tránh border-bottom bị che khu<PERSON>t
}

.tabButton {
  padding: 12px 24px;
  background: transparent;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
  position: relative;
  white-space: nowrap;
  display: flex;
  align-items: center;
  
  &:hover {
    color: #4b5563;
  }
  
  &.activeTab {
    color: #2563eb;
    border-bottom-color: #2563eb;
  }

  // Style cho tab có alert badge
  .alertBadge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 18px;
    height: 18px;
    background-color: #ef4444;
    color: white;
    font-size: 12px;
    border-radius: 9px;
    margin-left: 8px;
    padding: 0 6px;
    font-weight: 600;
  }
}

// Phần nội dung của tab
.tabContent {
  margin-top: 24px;
  
  // Bổ sung hiệu ứng fade-in nhẹ
  animation: fadeIn 0.3s ease;
  
  @keyframes fadeIn {
    from { opacity: 0.7; }
    to { opacity: 1; }
  }
}
  .detailsGrid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    
    .detailItem {
      display: flex;
      flex-direction: column;
      
      &.fullWidth {
        grid-column: 1 / -1;
      }
      
      .detailLabel {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }
      
      .detailValue {
        font-size: 16px;
        
        code {
          font-family: monospace;
          background-color: #f3f4f6;
          padding: 2px 4px;
          border-radius: 4px;
          font-size: 14px;
        }
        
        .fingerprint {
          word-break: break-all;
        }
      }
      
      .revocationReason {
        color: #b91c1c;
      }
    }
  }
  
  .cloneAlerts {
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
    }
  }
  
  .noCloneAlerts {
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
    }
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  
  .spinner {
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
  
  h2 {
    font-size: 24px;
    margin-bottom: 16px;
  }
  
  p {
    margin-bottom: 24px;
    color: #666;
  }
}