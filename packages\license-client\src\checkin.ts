import axios from 'axios';
import os from 'os';
import { LicenseCheckInRequest, LicenseCheckInResponse } from './types';
import { generateHardwareFingerprint } from './fingerprint';

/**
 * License check-in service
 */
export class LicenseCheckInService {
  private apiUrl: string;
  
  /**
   * Constructor
   * @param apiUrl The URL of the license service API
   */
  constructor(apiUrl: string) {
    this.apiUrl = apiUrl;
  }
  
  /**
   * Get system information for check-in
   * @returns System information object
   */
  private getSystemInfo(): Record<string, any> {
    return {
      platform: os.platform(),
      release: os.release(),
      arch: os.arch(),
      hostname: os.hostname(),
      uptime: os.uptime(),
      memory_total: os.totalmem(),
      memory_free: os.freemem(),
      cpu_count: os.cpus().length,
      cpu_model: os.cpus()[0]?.model || 'unknown'
    };
  }
  
  /**
   * Perform a license check-in
   * @param licenseKey The license key
   * @param useAutoFingerprint Whether to automatically generate hardware fingerprint
   * @param customFingerprint Optional custom hardware fingerprint
   * @param includeSystemInfo Whether to include system information
   * @returns Promise with check-in response
   */
  async performCheckIn(
    licenseKey: string,
    useAutoFingerprint: boolean = true,
    customFingerprint?: string,
    includeSystemInfo: boolean = true
  ): Promise<LicenseCheckInResponse> {
    try {
      // Generate hardware fingerprint if auto is enabled
      const hardwareFingerprint = useAutoFingerprint
        ? generateHardwareFingerprint()
        : customFingerprint || '';
      
      if (!hardwareFingerprint) {
        throw new Error('Hardware fingerprint is required for check-in');
      }
      
      const request: LicenseCheckInRequest = {
        license_key: licenseKey,
        hardware_fingerprint: hardwareFingerprint
      };
      
      // Include system info if requested
      if (includeSystemInfo) {
        request.system_info = this.getSystemInfo();
      }
      
      const response = await axios.post<LicenseCheckInResponse>(
        `${this.apiUrl}/license-check`,
        request
      );
      
      return response.data;
    } catch (error: any) {
      console.error('License check-in failed:', error);
      return {
        success: false,
        is_valid: false,
        message: 'License check-in failed',
        error: error.message || 'Unknown error'
      };
    }
  }
  
  /**
   * Schedule periodic check-ins
   * @param licenseKey The license key
   * @param intervalHours How often to check in (in hours)
   * @param onSuccess Callback for successful check-ins
   * @param onError Callback for failed check-ins
   * @returns Timer ID for clearing the interval if needed
   */
  scheduleCheckIns(
    licenseKey: string,
    intervalHours: number = 24,
    onSuccess?: (response: LicenseCheckInResponse) => void,
    onError?: (error: any) => void
  ): NodeJS.Timeout {
    // Convert hours to milliseconds
    const intervalMs = intervalHours * 60 * 60 * 1000;
    
    // Perform first check-in immediately
    this.performCheckIn(licenseKey)
      .then(response => {
        if (response.success && onSuccess) {
          onSuccess(response);
        } else if (!response.success && onError) {
          onError(new Error(response.error || 'Check-in failed'));
        }
      })
      .catch(error => {
        if (onError) {
          onError(error);
        }
      });
    
    // Schedule periodic check-ins
    return setInterval(() => {
      this.performCheckIn(licenseKey)
        .then(response => {
          if (response.success && onSuccess) {
            onSuccess(response);
          } else if (!response.success && onError) {
            onError(new Error(response.error || 'Check-in failed'));
          }
        })
        .catch(error => {
          if (onError) {
            onError(error);
          }
        });
    }, intervalMs);
  }
}
