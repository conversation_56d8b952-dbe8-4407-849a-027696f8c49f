.form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.formSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.formGroup {
  margin-bottom: 0.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #333;
}

.input, .textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.875rem;
  
  &:focus {
    outline: none;
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
  }
  
  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
}

.textarea {
  resize: vertical;
  min-height: 100px;
}

.inputError, .textareaError {
  composes: input;
  border-color: #f44336;
  
  &:focus {
    box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
  }
}

.textareaError {
  composes: textarea;
}

.required {
  color: #f44336;
}

.errorText {
  color: #f44336;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.helpText {
  color: #666;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.checkboxContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkboxLabel {
  font-size: 0.875rem;
  user-select: none;
  cursor: pointer;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 0.5rem;
}

@media (max-width: 768px) {
  .formRow {
    grid-template-columns: 1fr;
  }
}
