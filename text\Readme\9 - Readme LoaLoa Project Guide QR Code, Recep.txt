LoaLoa Project Guide: QR Code, Reception Points & Chat Routing
Project Overview
LoaLoa is a multi-tenant, multi-language chat and translation application for hotels/resorts, built with NodeJS, Next.js, TypeScript, and Supabase. The system uses a database-per-tenant model and Docker for on-premise deployment.

Key Components
1. QR Code System
Purpose: Allow guests to scan QR codes placed around the property to initiate chats or access information
Central Table: tenant_qr_codes
Key Features:
Physical placement tracking
Association with rooms/areas
Message routing configuration
Various action types (chat, info, service, feedback)
2. Reception Points
Purpose: Define virtual units that receive and process messages
Central Table: tenant_message_reception_points
Key Features:
Named service centers (e.g., "Guest Services", "F&B Services")
Staff assignment to reception points
Priority-based message handling
3. Chat Routing Rules
Purpose: Define how messages are routed based on conditions
Central Table: tenant_chat_routing_rules
Key Features:
Condition-based routing (QR code, time, language, guest type)
Can route to departments, specific staff, or reception points
Priority-based rule application
Data Structure and Relationships
QR Codes (tenant_qr_codes)
id (uuid)                     - Primary key
tenant_id (uuid)              - Tenant reference
code_value (varchar)          - Unique QR code value
name (varchar)                - Display name
description (text)            - Optional description
location (varchar)            - Physical placement
status (varchar)              - 'active' or 'inactive'
reception_point_id (uuid)     - References tenant_message_reception_points
target_type (varchar)         - 'room', 'area', 'guest', 'general'
target_id (uuid)              - ID of the target based on target_type
room_number (varchar)         - Legacy field for room reference
qr_type_id (uuid)             - References qr code types (chat, info, etc)
custom_action (jsonb)         - Custom behavior when scanned
Reception Points (tenant_message_reception_points)
id (uuid)                     - Primary key
tenant_id (uuid)              - Tenant reference
name (varchar)                - Display name
code (varchar)                - Unique identifier
description (text)            - Optional description
is_active (boolean)           - Status flag
priority (int)                - Sorting priority
Chat Routing Rules (tenant_chat_routing_rules)
id (uuid)                     - Primary key
tenant_id (uuid)              - Tenant reference
rule_name (varchar)           - Display name
rule_type (varchar)           - 'qr_code', 'guest', 'time', 'language', 'custom'
rule_condition (jsonb)        - Condition that triggers this rule
target_department (varchar)   - Department to route to (optional)
target_user_id (uuid)         - Specific user to route to (optional)
target_reception_point_id     - Reception point to route to (optional)
priority (int)                - Rule application priority
Staff Assignments (tenant_staff_assignments)
id (uuid)                     - Primary key
tenant_id (uuid)              - Tenant reference
user_id (uuid)                - Staff user reference
reception_point_id (uuid)     - Reception point being staffed
assignment_type (varchar)     - Type of assignment
working_hours (jsonb)         - Staff availability hours
priority (int)                - Assignment priority
Message Routing Flow
Guest scans a QR code
System identifies the QR code's reception_point_id
Routing rules are applied (based on time, guest type, etc.)
If rule matches, message is routed according to rule target
If no rule matches, default routing via reception_point is used
System finds available staff assigned to the reception point
Staff with highest priority and currently working receives the message
UI Components
QR Code Management
QR code listing (/qr-codes)
QR code creation (/qr-codes/create)
QR code details (/qr-codes/[id])
QR code editing (/qr-codes/[id]/edit)
Reception Point Management
Reception point listing (/reception-points)
Reception point creation (/reception-points/create)
Reception point details (/reception-points/[id])
Reception point editing (/reception-points/[id]/edit)
Chat Routing Rules
Rules listing (/chat-routing-rules)
Rule creation (/chat-routing-rules/create)
Rule details (/chat-routing-rules/[id])
Rule editing (/chat-routing-rules/[id]/edit)
Recent Implementation Notes
QR Code Chuẩn hóa:

Added Reception Point selection to QR code form
Implemented target_type/target_id to standardize associations
Created tab-based form with better help text
Fixed relationship issues in SQL queries
UI/UX Improvements:

Added descriptive text to form fields
Improved validation and error handling
Ensured consistent styling across components
Known Issues:

Need to ensure consistent handling of target_type/target_id vs. room_number
JavaScript form validation needs alignment with backend expectations
Reception point information display needs improvement in list views
Next Development Tasks
Complete Staff Assignment UI
Enhance message routing visualization
Implement real-time chat notifications
Add analytics for QR code scans and message routing
Developer Tips
Always use explicit relationship names in Supabase queries (e.g., tenant_message_reception_points!tenant_qr_codes_reception_point_id_fkey)
Set null values explicitly (e.g., updateData.room_number = room_number || null)
Test QR code scanning with all routing scenarios
Ensure all components handle loading/error/empty states
This guide provides a quick overview of the LoaLoa messaging system architecture and current development status. Reference this document when starting new work on the QR code, reception points, or chat routing components.