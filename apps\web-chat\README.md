# 🏨 LoaLoa Web Chat

Multi-language hotel communication platform with real-time translation and smart message routing.

## ✨ Features

### 💬 Real-time Chat
- WebSocket-powered messaging
- Typing indicators
- Message delivery status
- Auto-scroll to latest messages

### 🌐 Multi-language Support
- 12+ supported languages (EN, VI, KO, JA, ZH, TH, ID, MS, ES, FR, DE, AR)
- Automatic language detection
- Real-time translation with confidence scores
- Original text preservation

### 📱 QR Code Integration
- Instant access via QR scan
- No app download required
- Location-based message routing
- Multi-device support

### 🎯 Smart Routing
- Automatic staff assignment
- Department-based routing
- Priority message handling
- Workload balancing

### 🔐 Security & Privacy
- Encrypted communications
- Session management
- GDPR compliant
- Audit trail logging

## 🚀 Quick Start

### Development
```bash
cd D:/loaloa/apps/web-chat
pnpm dev
Production
pnpm build
pnpm start
📱 Usage Flow
QR Code Scan → Guest scans QR code in hotel
Language Selection → Choose preferred language
Chat Session → Real-time chat with auto-translation
Staff Response → Intelligent routing to appropriate staff
Session Management → Secure session handling
🛠️ Technical Stack
Frontend: Next.js 14, <PERSON><PERSON>, SCSS Modules
Realtime: Supabase Realtime
Database: PostgreSQL (via Supabase)
Translation: Multi-provider support
Styling: Custom SCSS with animations
Components: @loaloa/ui package
📊 Architecture
web-chat/
├── app/
│   ├── components/
│   │   ├── chat/           # Chat interface components
│   │   └── language/       # Language selection components
│   ├── hooks/
│   │   └── useChat.ts      # Real-time chat hook
│   ├── api/
│   │   ├── chat-sessions/  # Session management
│   │   ├── messages/       # Message handling
│   │   └── qr-scan/        # QR code processing
│   └── pages/
│       ├── page.tsx        # Homepage
│       ├── qr/demo/        # QR demo page
│       ├── chat/[session]/ # Chat interface
│       └── error/          # Error handling
🎨 Design System
Colors
Primary: #f97316 (Orange)
Secondary: #6b7280 (Gray)
Success: #10b981 (Green)
Error: #ef4444 (Red)
Typography
Font Family: Inter
Monospace: JetBrains Mono
Animations
Smooth transitions (0.2s-0.3s)
Micro-interactions
Loading states
Typing indicators
🔧 Configuration
Environment Variables
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
License Integration
The web chat integrates with @loaloa/license-client for:

License validation
Tenant identification
Feature access control
📱 Responsive Design
Mobile-first approach
Optimized for touch interactions
Adaptive UI components
Cross-browser compatibility
🧪 Testing
Development URLs
Homepage: http://localhost:3002/
QR Demo: http://localhost:3002/qr/demo
Chat Test: http://localhost:3002/chat/test-session-001?temp_user=test-user&lang=en&qr=DEMO-QR-001
Error Test: http://localhost:3002/error?code=system&message=Test+error
Test Scenarios
Language switching functionality
Real-time message sending/receiving
Translation toggle behavior
QR code scanning simulation
Error state handling
Mobile responsiveness
🚀 Deployment
Docker
docker build -t loaloa-web-chat .
docker run -p 3002:3002 loaloa-web-chat
Vercel
vercel --prod
📈 Performance
Lighthouse Score: 95+ (target)
First Contentful Paint: <1.5s
Time to Interactive: <3s
Bundle Size: Optimized with Next.js
🔗 Integration
Admin Portal
Shared components from @loaloa/ui
Common design tokens
License management integration
Guest App
Shared API endpoints
Consistent user experience
Cross-platform compatibility
📞 Support
For technical support or questions:

Email: <EMAIL>
Documentation: https://docs.loaloa.app
GitHub Issues: https://github.com/loaloa/web-chat/issues
📄 License
Proprietary - LoaLoa Platform


## 🎉 **HOÀN THÀNH WEB CHAT!**

Chúng ta đã successfully hoàn thiện:

✅ **Real-time Chat System** với WebSocket  
✅ **Multi-language Support** (12+ ngôn ngữ)  
✅ **Beautiful SCSS Animations** cho tất cả components  
✅ **Smart QR Code Integration**  
✅ **Responsive Design** cho mobile  
✅ **Error Handling** với categorized states  
✅ **Complete Documentation**  

**🔍 Hãy kiểm tra toàn bộ hệ thống:**

1. **Homepage**: Animated landing page
2. **QR Demo**: Language selection + simulation
3. **Chat Interface**: Real-time messaging với animations
4. **Error Handling**: Proper error states
