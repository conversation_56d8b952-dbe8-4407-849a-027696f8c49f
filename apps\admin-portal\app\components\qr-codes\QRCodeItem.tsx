"use client";

import React from "react";
import styles from "./QRCodeItem.module.scss";
import { QRCode } from "@/app/types/qr-codes";
import Link from "next/link";
import { Badge } from "../../ui/badge";
import { PencilIcon, DocumentDuplicateIcon, TrashIcon, InformationCircleIcon } from "@heroicons/react/24/outline";
import { Button } from "../../ui/button";
import { useState } from "react";
import { useRouter } from "next/navigation";

interface QRCodeItemProps {
  qrCode: QRCode;
  onUpdate: () => void;
}

const QRCodeItem: React.FC<QRCodeItemProps> = ({ qrCode, onUpdate }) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  const handleDelete = async () => {
    if (confirm("Are you sure you want to delete this QR code?")) {
      setIsDeleting(true);
      try {
        const response = await fetch(`/api/qr-codes/${qrCode.id}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error("Failed to delete QR code");
        }

        onUpdate();
      } catch (error) {
        console.error("Error deleting QR code:", error);
        alert("Failed to delete QR code. Please try again.");
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const getTypeDisplayName = (type: string) => {
    switch (type) {
      case "room":
        return "Room";
      case "area":
        return "Area";
      case "generic":
        return "Generic";
      default:
        return "Unknown";
    }
  };

  return (
    <tr className={styles.qrCodeItem}>
      <td>
        <div className={styles.qrCodeImage}>
          <img
            src={`/api/qr-codes/${qrCode.id}/image`}
            alt={`QR Code for ${qrCode.name}`}
            width={40}
            height={40}
          />
        </div>
      </td>
      <td>{qrCode.name}</td>
      <td>{getTypeDisplayName(qrCode.qr_code_type)}</td>
      <td>{qrCode.linked_entity_name || "N/A"}</td>
      <td>{qrCode.reception_point_name || "N/A"}</td>
      <td>
        <Button 
          variant="ghost" 
          size="sm" 
          className={styles.infoButton}
          onClick={() => router.push(`/qr-codes/${qrCode.id}`)}
        >
          <InformationCircleIcon className="h-4 w-4 mr-1" />
          Info
        </Button>
      </td>
      <td>{qrCode.total_scans || 0}</td>
      <td>
        <Badge variant={qrCode.is_active ? "success" : "secondary"}>
          {qrCode.is_active ? "Active" : "Inactive"}
        </Badge>
      </td>
      <td>
        <div className={styles.actions}>
          <Link href={`/qr-codes/${qrCode.id}/edit`}>
            <Button variant="ghost" size="sm" className={styles.editButton}>
              <PencilIcon className="h-4 w-4" />
            </Button>
          </Link>
          <Button
            variant="ghost"
            size="sm"
            className={styles.downloadButton}
            onClick={() => window.open(`/api/qr-codes/${qrCode.id}/download`, "_blank")}
          >
            <DocumentDuplicateIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className={styles.deleteButton}
            onClick={handleDelete}
            disabled={isDeleting}
          >
            <TrashIcon className="h-4 w-4" />
          </Button>
        </div>
      </td>
    </tr>
  );
};

export default QRCodeItem;