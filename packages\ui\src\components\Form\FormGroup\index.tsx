import React, { ReactNode } from 'react';

export interface FormGroupProps {
  /**
   * Form group label
   */
  label?: string;
  /**
   * Form group children
   */
  children: ReactNode;
  /**
   * Helper text displayed below the form group
   */
  helperText?: string;
  /**
   * Error message displayed when error is true
   */
  errorText?: string;
  /**
   * Whether the form group is in error state
   */
  error?: boolean;
  /**
   * Whether the form group is required
   */
  required?: boolean;
  /**
   * Additional CSS properties
   */
  style?: React.CSSProperties;
  /**
   * Optional CSS class name
   */
  className?: string;
}

export const FormGroup: React.FC<FormGroupProps> = ({
  label,
  children,
  helperText,
  errorText,
  error = false,
  required = false,
  style,
  className,
}) => {
  // Container style
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    marginBottom: '24px',
    ...style,
  };
  
  // Label style
  const labelStyle: React.CSSProperties = {
    fontSize: '14px',
    fontWeight: 500,
    color: error ? '#B91C1C' : '#464646',
    marginBottom: '8px',
    display: 'flex',
    alignItems: 'center',
  };
  
  // Required mark style
  const requiredMarkStyle: React.CSSProperties = {
    color: '#B91C1C',
    marginLeft: '4px',
  };
  
  // Helper text style
  const helperTextStyle: React.CSSProperties = {
    fontSize: '12px',
    marginTop: '4px',
    color: error ? '#B91C1C' : '#6B7280',
  };

  return (
    <div style={containerStyle} className={className}>
      {label && (
        <div style={labelStyle}>
          {label}
          {required && <span style={requiredMarkStyle}>*</span>}
        </div>
      )}
      
      {children}
      
      {(error && errorText) ? (
        <p style={helperTextStyle}>{errorText}</p>
      ) : helperText ? (
        <p style={helperTextStyle}>{helperText}</p>
      ) : null}
    </div>
  );
};

export default FormGroup;
