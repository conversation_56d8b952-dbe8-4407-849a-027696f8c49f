'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import styles from './create-user.module.scss';
import DashboardLayout from '../../dashboard-layout';
import { Button } from '@ui';

const CreateUserPage = () => {
  const router = useRouter();
  
  // Form state
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    display_name: '',
    phone: '',
    role: 'user',
    is_active: true,
    department: '', // Thêm trường department
    title: '', // Thêm trường title
    preferred_language: 'en' // Thêm trường preferred_language với giá trị mặc định là 'en'
  });
  
  // Reception Points state
  const [receptionPoints, setReceptionPoints] = useState<any[]>([]);
  const [loadingReceptionPoints, setLoadingReceptionPoints] = useState(false);
  const [selectedReceptionPoints, setSelectedReceptionPoints] = useState<{
    id: string;
    is_primary: boolean;
    priority: number;
  }[]>([]);
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [createdUser, setCreatedUser] = useState<any>(null);

  // Fetch reception points when component mounts
  useEffect(() => {
    const fetchReceptionPoints = async () => {
      try {
        setLoadingReceptionPoints(true);
        const response = await fetch('/api/reception-points?is_active=true&limit=100');
        if (response.ok) {
          const data = await response.json();
          setReceptionPoints(data.data || []);
        } else {
          console.error('Error fetching reception points');
        }
      } catch (err) {
        console.error('Error fetching reception points:', err);
      } finally {
        setLoadingReceptionPoints(false);
      }
    };

    fetchReceptionPoints();
  }, []);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    });

    // Clear error for this field when user types
    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };

  // Handle reception point selection
  const handleReceptionPointChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedPointId = e.target.value;
    if (!selectedPointId) return;
    
    const alreadySelected = selectedReceptionPoints.some(p => p.id === selectedPointId);
    
    if (!alreadySelected) {
      const point = receptionPoints.find(p => p.id === selectedPointId);
      if (point) {
        // Add new reception point
        setSelectedReceptionPoints([
          ...selectedReceptionPoints,
          {
            id: selectedPointId,
            is_primary: selectedReceptionPoints.length === 0, // First one is primary by default
            priority: 1
          }
        ]);
      }
    }
  };

  // Update priority for a reception point
  const handlePriorityChange = (id: string, priority: number) => {
    setSelectedReceptionPoints(selectedReceptionPoints.map(point => {
      if (point.id === id) {
        return { ...point, priority };
      }
      return point;
    }));
  };

  // Set a reception point as primary
  const handleSetPrimary = (id: string) => {
    setSelectedReceptionPoints(selectedReceptionPoints.map(point => {
      if (point.id === id) {
        return { ...point, is_primary: true };
      }
      return { ...point, is_primary: false };
    }));
  };

  // Remove a reception point
  const handleRemoveReceptionPoint = (id: string) => {
    const updatedPoints = selectedReceptionPoints.filter(point => point.id !== id);
    
    // If we removed the primary and there are still points, set the first one as primary
    if (updatedPoints.length > 0 && !updatedPoints.some(p => p.is_primary)) {
      updatedPoints[0].is_primary = true;
    }
    
    setSelectedReceptionPoints(updatedPoints);
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    if (!formData.display_name) {
      newErrors.display_name = 'Name is required';
    }
    
    if (formData.phone && !/^\+?[0-9]{10,15}$/.test(formData.phone)) {
      newErrors.phone = 'Phone number is invalid';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setSubmitError(null);
    
    try {
      // Create the user
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create user');
      }
      
      // Set success state and store created user data
      setCreatedUser(data.data);
      
      // Assign selected reception points to the user
      if (selectedReceptionPoints.length > 0) {
        const userId = data.data.id;
        
        for (const point of selectedReceptionPoints) {
          await fetch(`/api/users/${userId}/reception-points`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              reception_point_id: point.id,
              is_primary: point.is_primary,
              priority: point.priority
            })
          });
        }
      }
      
      setIsSuccess(true);
    } catch (error: any) {
      setSubmitError(error.message);
      console.error('Error creating user:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCreateAnother = () => {
    setIsSuccess(false);
    setCreatedUser(null);
    setFormData({
      email: '',
      password: '',
      display_name: '',
      phone: '',
      role: 'user',
      is_active: true,
      department: '',
      title: '',
      preferred_language: 'en'
    });
    setSelectedReceptionPoints([]);
  };

  return (
    <DashboardLayout>
      <div className={styles.container}>
        {!isSuccess ? (
          <>
            <div className={styles.header}>
              <Link href="/users" className={styles.backButton}>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M10.6667 2.66667L5.33333 8.00001L10.6667 13.3333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Back to Users
              </Link>
              <h1 className={styles.title}>Create New User</h1>
            </div>
            <div className={styles.formContainer}>
              {submitError && (
                <div className={styles.errorMessage}>
                  <p>{submitError}</p>
                </div>
              )}
              <form onSubmit={handleSubmit}>
                <div className={styles.formGroup}>
                  <label htmlFor="email">Email Address <span className={styles.required}>*</span></label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter email address"
                    className={errors.email ? styles.inputError : ''}
                    disabled={isSubmitting}
                  />
                  {errors.email && <p className={styles.errorText}>{errors.email}</p>}
                </div>
                
                <div className={styles.formGroup}>
                  <label htmlFor="password">Password <span className={styles.required}>*</span></label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder="Enter password"
                    className={errors.password ? styles.inputError : ''}
                    disabled={isSubmitting}
                  />
                  {errors.password && <p className={styles.errorText}>{errors.password}</p>}
                </div>
                
                <div className={styles.formGroup}>
                  <label htmlFor="display_name">Name <span className={styles.required}>*</span></label>
                  <input
                    type="text"
                    id="display_name"
                    name="display_name"
                    value={formData.display_name}
                    onChange={handleInputChange}
                    placeholder="Enter user's name"
                    className={errors.display_name ? styles.inputError : ''}
                    disabled={isSubmitting}
                  />
                  {errors.display_name && <p className={styles.errorText}>{errors.display_name}</p>}
                </div>
                
                <div className={styles.formGroup}>
                  <label htmlFor="phone">Phone Number</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Enter phone number (optional)"
                    className={errors.phone ? styles.inputError : ''}
                    disabled={isSubmitting}
                  />
                  {errors.phone && <p className={styles.errorText}>{errors.phone}</p>}
                </div>

                {/* Thêm trường Department */}
                <div className={styles.formGroup}>
                  <label htmlFor="department">Department</label>
                  <input
                    type="text"
                    id="department"
                    name="department"
                    value={formData.department}
                    onChange={handleInputChange}
                    placeholder="Enter department (optional)"
                    disabled={isSubmitting}
                  />
                </div>

                {/* Thêm trường Title */}
                <div className={styles.formGroup}>
                  <label htmlFor="title">Title</label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="Enter job title (optional)"
                    disabled={isSubmitting}
                  />
                </div>

                {/* Thêm trường Preferred Language */}
                <div className={styles.formGroup}>
                  <label htmlFor="preferred_language">Preferred Language</label>
                  <select
                    id="preferred_language"
                    name="preferred_language"
                    value={formData.preferred_language}
                    onChange={handleInputChange}
                    disabled={isSubmitting}
                  >
                    <option value="en">English</option>
                    <option value="vi">Vietnamese</option>
                    <option value="fr">French</option>
                    <option value="ja">Japanese</option>
                    <option value="zh">Chinese</option>
                    <option value="ko">Korean</option>
                    <option value="ru">Russian</option>
                  </select>
                </div>
                
                <div className={styles.formGroup}>
                  <label htmlFor="role">Role <span className={styles.required}>*</span></label>
                  <select
                    id="role"
                    name="role"
                    value={formData.role}
                    onChange={handleInputChange}
                    disabled={isSubmitting}
                  >
                    <option value="admin">Admin</option>
                    <option value="manager">Manager</option>
                    <option value="user">User</option>
                  </select>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="reception_points">Reception Points</label>
                  <select
                    id="reception_points"
                    onChange={handleReceptionPointChange}
                    disabled={isSubmitting || loadingReceptionPoints}
                    value=""
                  >
                    <option value="">Select Reception Point...</option>
                    {receptionPoints.map(point => (
                      <option key={point.id} value={point.id}>
                        {point.name} ({point.code})
                      </option>
                    ))}
                  </select>
                  {loadingReceptionPoints && <p className={styles.helpText}>Loading reception points...</p>}
                  <p className={styles.helpText}>Assign user to one or more reception points</p>
                </div>

                {/* Selected Reception Points */}
                {selectedReceptionPoints.length > 0 && (
                  <div className={styles.selectedPointsContainer}>
                    <h3>Selected Reception Points</h3>
                    <ul className={styles.pointsList}>
                      {selectedReceptionPoints.map(point => {
                        const pointData = receptionPoints.find(p => p.id === point.id);
                        return (
                          <li key={point.id} className={styles.pointItem}>
                            <div className={styles.pointInfo}>
                              <span className={styles.pointName}>
                                {pointData?.name || 'Unknown'} ({pointData?.code || 'N/A'})
                                {point.is_primary && (
                                  <span className={styles.primaryBadge}>Primary</span>
                                )}
                              </span>
                              
                              <div className={styles.pointActions}>
                                <div className={styles.priorityControl}>
                                  <label>Priority:</label>
                                  <input
                                    type="number"
                                    min="1"
                                    max="100"
                                    value={point.priority}
                                    onChange={(e) => handlePriorityChange(point.id, parseInt(e.target.value) || 1)}
                                    disabled={isSubmitting}
                                  />
                                </div>
                                
                                {!point.is_primary && (
                                  <button
                                    type="button"
                                    className={styles.setPrimaryBtn}
                                    onClick={() => handleSetPrimary(point.id)}
                                    disabled={isSubmitting}
                                  >
                                    Set Primary
                                  </button>
                                )}
                                
                                <button
                                  type="button"
                                  className={styles.removePointBtn}
                                  onClick={() => handleRemoveReceptionPoint(point.id)}
                                  disabled={isSubmitting}
                                >
                                  Remove
                                </button>
                              </div>
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                )}

                <div className={styles.formGroup}>
                  <div className={styles.checkboxGroup}>
                    <input
                      type="checkbox"
                      id="is_active"
                      name="is_active"
                      checked={formData.is_active}
                      onChange={handleInputChange}
                      disabled={isSubmitting}
                    />
                    <label htmlFor="is_active">Active User</label>
                  </div>
                </div>
                
                <div className={styles.actions}>
                  <Link href="/users">
                    <Button variant="secondary" disabled={isSubmitting} label="Cancel">
                      Cancel
                    </Button>
                  </Link>
                  <Button
                    type="submit"
                    variant="primary"
                    loading={isSubmitting}
                    disabled={isSubmitting}
                    label="Create User"
                  >
                    Create User
                  </Button>
                </div>
              </form>
            </div>
          </>
        ) : (
          <div className={styles.successContainer}>
            <div className={styles.successCard}>
              <div className={styles.successIcon}>
                <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="24" cy="24" r="24" fill="#10B981" fillOpacity="0.1"/>
                  <path d="M32 20L22 30L18 26" stroke="#10B981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <h2 className={styles.successTitle}>User Created Successfully!</h2>
              <p className={styles.successDesc}>
                {createdUser?.display_name || createdUser?.email} has been added to your tenant.
              </p>
              <div className={styles.successActions}>
                <Button 
                  variant="primary" 
                  onClick={() => router.push('/users')} 
                  label="Back to Users List"
                >
                  Back to Users List
                </Button>
                <Button 
                  variant="secondary" 
                  onClick={handleCreateAnother} 
                  label="Create Another User"
                >
                  Create Another User
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default CreateUserPage;