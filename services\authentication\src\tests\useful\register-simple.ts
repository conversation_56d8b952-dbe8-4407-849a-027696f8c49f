import axios from 'axios';

// URL API của service
const API_URL = 'http://localhost:3001/api/auth';

async function testRegister() {
  try {
    console.log('Đang test API đăng ký người dùng...');
    
    // Tạo dữ liệu đăng ký với email ngẫu nhiên để tránh trùng lặp
    const timestamp = new Date().getTime();
    const testUser = {
      email: `test_${timestamp}@example.com`,
      password: 'password123',
      full_name: 'Test User'
    };
    
    console.log('Dữ liệu đăng ký:', testUser);
    
    // Gọi API đăng ký
    const response = await axios.post(`${API_URL}/register`, testUser);
    
    console.log('Trạng thái phản hồi:', response.status);
    console.log('Dữ liệu phản hồi:', response.data);
    
    if (response.status === 201 || response.status === 200) {
      console.log('✅ Đăng ký thành công!');
    } else {
      console.log('❌ Đăng ký thất bại!');
    }
  } catch (error: any) {
    console.error('❌ Lỗi khi test đăng ký:');
    
    if (error.response) {
      // Lỗi từ phản hồi của server
      console.error('Trạng thái lỗi:', error.response.status);
      console.error('Dữ liệu lỗi:', error.response.data);
    } else if (error.request) {
      // Không nhận được phản hồi
      console.error('Không nhận được phản hồi từ server');
    } else {
      // Lỗi khác
      console.error('Lỗi:', error.message);
    }
  }
}

// Chạy test
testRegister();
