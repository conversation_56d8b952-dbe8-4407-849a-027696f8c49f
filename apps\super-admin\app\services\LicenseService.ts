import { createClient } from '../../utils/supabase/server';
import crypto from 'crypto';

// <PERSON><PERSON>nh nghĩa các interface
export interface License {
  id: string;
  license_key: string;
  customer_id: string;
  customer_name: string;
  product_id: string;
  issue_date: string;
  expiry_date: string;
  is_active: boolean;
  revocation_reason?: string;
  hardware_fingerprint?: string;
  last_check_in?: string;
  check_in_count: number;
  activation_date?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  metadata: any;
}

export interface LicenseActivity {
  id: string;
  license_id: string;
  activity_type: 'ACTIVATION' | 'CHECK_IN' | 'WARNING' | 'VIOLATION' | 'REVOCATION';
  hardware_fingerprint?: string;
  ip_address?: string;
  timestamp: string;
  details: any;
}

export interface LicenseClone {
  id: string;
  license_id: string;
  detection_time: string;
  original_fingerprint: string;
  clone_fingerprint: string;
  original_ip?: string;
  clone_ip?: string;
  status: 'DETECTED' | 'UNDER_REVIEW' | 'CONFIRMED' | 'FALSE_ALARM' | 'REVOKED';
  review_notes?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  metadata: any;
}

export interface LicenseFilterOptions {
  status?: 'ACTIVE' | 'EXPIRED' | 'REVOKED' | 'NOT_ACTIVATED' | 'EXPIRING_SOON' | 'ALL';
  search?: string;
  customer_id?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
}

// Lớp dịch vụ quản lý License
export class LicenseService {
  /**
   * Tạo license mới
   */
  static async createLicense(
    customerName: string,
    expiryDays: number,
    customerId?: string,
    metadata?: any
  ): Promise<License> {
    try {
      const supabase = await createClient();
      
      // Lấy user ID hiện tại
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Unauthorized');
      }
      
      // Tính ngày hết hạn
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + expiryDays);
      
      // Tạo license key mới
      const { data: licenseKeyData, error: licenseKeyError } = await supabase
        .rpc('generate_license_key');
        
      if (licenseKeyError) {
        console.error('Error generating license key:', licenseKeyError);
        throw new Error(`Failed to generate license key: ${licenseKeyError.message}`);
      }

      // Tạo license mới trong database
      const { data: license, error } = await supabase
        .from('licenses')
        .insert({
          license_key: licenseKeyData,
          customer_name: customerName,
          customer_id: customerId,
          expiry_date: expiryDate.toISOString(),
          created_by: user.id,
          metadata: metadata || {}
        })
        .select('*')
        .single();

      if (error) {
        console.error('Error creating license:', error);
        throw new Error(`Failed to create license: ${error.message}`);
      }

      return license;
    } catch (error: any) {
      console.error('Error in createLicense:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách license với phân trang và lọc
   */
  static async getLicenses(
    pagination: PaginationOptions = { page: 1, limit: 10 },
    filter: LicenseFilterOptions = {}
  ): Promise<{ data: License[], count: number }> {
    try {
      const supabase = await createClient();
      const { page, limit } = pagination;
      const offset = (page - 1) * limit;

      // Xây dựng query
      let query = supabase
        .from('license_summary')
        .select('*', { count: 'exact' });

      // Áp dụng các bộ lọc
      if (filter.status && filter.status !== 'ALL') {
        query = query.eq('status', filter.status);
      }

      if (filter.customer_id) {
        query = query.eq('customer_id', filter.customer_id);
      }

      if (filter.search) {
        query = query.or(`license_key.ilike.%${filter.search}%,customer_name.ilike.%${filter.search}%`);
      }

      // Phân trang
      query = query
        .range(offset, offset + limit - 1)
        .order('updated_at', { ascending: false });

      // Thực thi query
      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching licenses:', error);
        throw new Error(`Failed to fetch licenses: ${error.message}`);
      }

      return { data, count: count || 0 };
    } catch (error: any) {
      console.error('Error in getLicenses:', error);
      throw error;
    }
  }

  /**
   * Lấy chi tiết license theo ID
   */
  static async getLicenseById(id: string): Promise<License> {
    try {
      const supabase = await createClient();
      const { data, error } = await supabase
        .from('licenses')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching license:', error);
        throw new Error(`Failed to fetch license: ${error.message}`);
      }

      return data;
    } catch (error: any) {
      console.error('Error in getLicenseById:', error);
      throw error;
    }
  }

  /**
   * Kích hoạt license
   */
  static async activateLicense(
    licenseKey: string, 
    hardwareFingerprint: string, 
    ipAddress: string
  ): Promise<{ success: boolean, license: License, message: string }> {
    try {
      const supabase = await createClient();
      
      // Tìm license theo key
      const { data: license, error: findError } = await supabase
        .from('licenses')
        .select('*')
        .eq('license_key', licenseKey)
        .single();

      if (findError || !license) {
        return { 
          success: false, 
          license: null as any, 
          message: 'Invalid license key' 
        };
      }

      // Kiểm tra nếu license đã bị vô hiệu hóa
      if (!license.is_active) {
        return { 
          success: false, 
          license, 
          message: 'License has been revoked' 
        };
      }

      // Kiểm tra nếu license đã hết hạn
      if (new Date(license.expiry_date) < new Date()) {
        return { 
          success: false, 
          license, 
          message: 'License has expired' 
        };
      }

      // Kiểm tra nếu license đã được kích hoạt trước đó
      if (license.hardware_fingerprint) {
        // Kiểm tra xem có phải là clone không
        if (license.hardware_fingerprint !== hardwareFingerprint) {
          // Gọi hàm phát hiện clone
          const { data: isClone, error: cloneError } = await supabase
            .rpc('detect_license_clone', {
              p_license_id: license.id,
              p_hardware_fingerprint: hardwareFingerprint,
              p_ip_address: ipAddress
            });

          if (cloneError) {
            console.error('Error detecting license clone:', cloneError);
          }

          // Vẫn cho phép sử dụng nhưng ghi log
          return {
            success: true,
            license,
            message: 'License already activated on a different device. This has been logged.'
          };
        }

        // Nếu cùng fingerprint, chỉ cần cập nhật check-in
        const { error: updateError } = await supabase
          .from('licenses')
          .update({ 
            last_check_in: new Date().toISOString(),
            check_in_count: license.check_in_count + 1
          })
          .eq('id', license.id);

        if (updateError) {
          console.error('Error updating license check-in:', updateError);
        }

        // Ghi log hoạt động
        await supabase.from('license_activities').insert({
          license_id: license.id,
          activity_type: 'CHECK_IN',
          hardware_fingerprint: hardwareFingerprint,
          ip_address: ipAddress,
          details: { message: 'Regular check-in' }
        });

        return {
          success: true,
          license: {
            ...license,
            last_check_in: new Date().toISOString(),
            check_in_count: license.check_in_count + 1
          },
          message: 'License checked-in successfully'
        };
      }

      // Kích hoạt license mới
      const { data: updatedLicense, error: updateError } = await supabase
        .from('licenses')
        .update({
          hardware_fingerprint: hardwareFingerprint,
          activation_date: new Date().toISOString(),
          last_check_in: new Date().toISOString(),
          check_in_count: 1
        })
        .eq('id', license.id)
        .select('*')
        .single();

      if (updateError) {
        console.error('Error activating license:', updateError);
        throw new Error(`Failed to activate license: ${updateError.message}`);
      }

      // Ghi log hoạt động
      await supabase.from('license_activities').insert({
        license_id: license.id,
        activity_type: 'ACTIVATION',
        hardware_fingerprint: hardwareFingerprint,
        ip_address: ipAddress,
        details: { message: 'Initial activation' }
      });

      return {
        success: true,
        license: updatedLicense,
        message: 'License activated successfully'
      };
    } catch (error: any) {
      console.error('Error in activateLicense:', error);
      throw error;
    }
  }

  /**
   * Check-in license (xác nhận license còn hoạt động)
   */
  static async checkInLicense(
    licenseKey: string,
    hardwareFingerprint: string,
    ipAddress: string
  ): Promise<{ success: boolean, license: License | null, message: string }> {
    try {
      const supabase = await createClient();
      
      // Tìm license theo key
      const { data: license, error: findError } = await supabase
        .from('licenses')
        .select('*')
        .eq('license_key', licenseKey)
        .single();

      if (findError || !license) {
        return { 
          success: false, 
          license: null, 
          message: 'Invalid license key' 
        };
      }

      // Kiểm tra nếu license đã bị vô hiệu hóa
      if (!license.is_active) {
        return { 
          success: false, 
          license, 
          message: 'License has been revoked' 
        };
      }

      // Kiểm tra nếu license đã hết hạn
      if (new Date(license.expiry_date) < new Date()) {
        return { 
          success: false, 
          license, 
          message: 'License has expired' 
        };
      }

      // Kiểm tra nếu license chưa được kích hoạt
      if (!license.hardware_fingerprint) {
        return {
          success: false,
          license,
          message: 'License has not been activated yet'
        };
      }

      // Kiểm tra xem có phải là clone không
      if (license.hardware_fingerprint !== hardwareFingerprint) {
        // Gọi hàm phát hiện clone
        const { data: isClone, error: cloneError } = await supabase
          .rpc('detect_license_clone', {
            p_license_id: license.id,
            p_hardware_fingerprint: hardwareFingerprint,
            p_ip_address: ipAddress
          });

        if (cloneError) {
          console.error('Error detecting license clone:', cloneError);
        }

        // Vẫn cho phép sử dụng nhưng ghi log
        return {
          success: true,
          license,
          message: 'Hardware fingerprint has changed. This has been logged.'
        };
      }

      // Cập nhật check-in
      const { data: updatedLicense, error: updateError } = await supabase
        .from('licenses')
        .update({ 
          last_check_in: new Date().toISOString(),
          check_in_count: license.check_in_count + 1
        })
        .eq('id', license.id)
        .select('*')
        .single();

      if (updateError) {
        console.error('Error updating license check-in:', updateError);
        throw new Error(`Failed to check-in license: ${updateError.message}`);
      }

      // Ghi log hoạt động
      await supabase.from('license_activities').insert({
        license_id: license.id,
        activity_type: 'CHECK_IN',
        hardware_fingerprint: hardwareFingerprint,
        ip_address: ipAddress,
        details: { message: 'Regular check-in' }
      });

      return {
        success: true,
        license: updatedLicense,
        message: 'License checked-in successfully'
      };
    } catch (error: any) {
      console.error('Error in checkInLicense:', error);
      throw error;
    }
  }

  /**
   * Vô hiệu hóa license
   */
  static async revokeLicense(
    id: string,
    reason: string
  ): Promise<{ success: boolean, message: string }> {
    try {
      const supabase = await createClient();
      
      // Cập nhật trạng thái license
      const { error } = await supabase
        .from('licenses')
        .update({ 
          is_active: false,
          revocation_reason: reason
        })
        .eq('id', id);

      if (error) {
        console.error('Error revoking license:', error);
        throw new Error(`Failed to revoke license: ${error.message}`);
      }

      // Ghi log hoạt động
      await supabase.from('license_activities').insert({
        license_id: id,
        activity_type: 'REVOCATION',
        details: { 
          message: 'License revoked by admin',
          reason
        }
      });

      return {
        success: true,
        message: 'License revoked successfully'
      };
    } catch (error: any) {
      console.error('Error in revokeLicense:', error);
      throw error;
    }
  }

  /**
   * Gia hạn license
   */
  static async extendLicense(
    id: string,
    additionalDays: number
  ): Promise<License> {
    try {
      const supabase = await createClient();
      
      // Lấy thông tin license hiện tại
      const { data: license, error: findError } = await supabase
        .from('licenses')
        .select('*')
        .eq('id', id)
        .single();

      if (findError) {
        console.error('Error fetching license:', findError);
        throw new Error(`Failed to fetch license: ${findError.message}`);
      }

      // Tính ngày hết hạn mới
      let newExpiryDate;
      if (new Date(license.expiry_date) < new Date()) {
        // Nếu đã hết hạn, tính từ ngày hiện tại
        newExpiryDate = new Date();
      } else {
        // Nếu còn hạn, tính từ ngày hết hạn hiện tại
        newExpiryDate = new Date(license.expiry_date);
      }
      newExpiryDate.setDate(newExpiryDate.getDate() + additionalDays);

      // Cập nhật license
      const { data: updatedLicense, error: updateError } = await supabase
        .from('licenses')
        .update({ 
          expiry_date: newExpiryDate.toISOString(),
          is_active: true // Đảm bảo license được kích hoạt nếu trước đó đã bị vô hiệu hóa
        })
        .eq('id', id)
        .select('*')
        .single();

      if (updateError) {
        console.error('Error extending license:', updateError);
        throw new Error(`Failed to extend license: ${updateError.message}`);
      }

      // Ghi log hoạt động
      await supabase.from('license_activities').insert({
        license_id: id,
        activity_type: 'CHECK_IN',
        details: { 
          message: 'License extended',
          additional_days: additionalDays,
          previous_expiry_date: license.expiry_date,
          new_expiry_date: newExpiryDate.toISOString()
        }
      });

      return updatedLicense;
    } catch (error: any) {
      console.error('Error in extendLicense:', error);
      throw error;
    }
  }
  
  /**
   * Lấy lịch sử hoạt động của license
   */
  static async getLicenseActivities(
    licenseId: string,
    pagination: PaginationOptions = { page: 1, limit: 20 }
  ): Promise<{ data: LicenseActivity[], count: number }> {
    try {
      const supabase = await createClient();
      const { page, limit } = pagination;
      const offset = (page - 1) * limit;

      // Thực thi query
      const { data, error, count } = await supabase
        .from('license_activities')
        .select('*', { count: 'exact' })
        .eq('license_id', licenseId)
        .order('timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Error fetching license activities:', error);
        throw new Error(`Failed to fetch license activities: ${error.message}`);
      }

      return { data, count: count || 0 };
    } catch (error: any) {
      console.error('Error in getLicenseActivities:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách license clone alerts
   */
  static async getLicenseCloneAlerts(
    pagination: PaginationOptions = { page: 1, limit: 10 },
    status?: 'DETECTED' | 'UNDER_REVIEW' | 'CONFIRMED' | 'FALSE_ALARM' | 'REVOKED'
  ): Promise<{ data: LicenseClone[], count: number }> {
    try {
      const supabase = await createClient();
      const { page, limit } = pagination;
      const offset = (page - 1) * limit;

      // Xây dựng query
      let query = supabase
        .from('license_clones')
        .select('*, licenses!inner(*)', { count: 'exact' });

      if (status) {
        query = query.eq('status', status);
      }

      // Phân trang
      query = query
        .range(offset, offset + limit - 1)
        .order('detection_time', { ascending: false });

      // Thực thi query
      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching license clone alerts:', error);
        throw new Error(`Failed to fetch license clone alerts: ${error.message}`);
      }

      // Xử lý dữ liệu kết quả
      const formattedData = data.map(item => {
        const { licenses, ...cloneData } = item;
        return {
          ...cloneData,
          license_key: licenses.license_key,
          customer_name: licenses.customer_name
        };
      });

      return { data: formattedData, count: count || 0 };
    } catch (error: any) {
      console.error('Error in getLicenseCloneAlerts:', error);
      throw error;
    }
  }
/**
   * Cập nhật trạng thái license clone alert
   */
  static async updateCloneAlertStatus(
    id: string,
    status: 'UNDER_REVIEW' | 'CONFIRMED' | 'FALSE_ALARM' | 'REVOKED',
    reviewNotes?: string
  ): Promise<{ success: boolean, message: string }> {
    try {
      const supabase = await createClient();
      
      // Lấy user ID hiện tại
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Unauthorized');
      }

      // Cập nhật trạng thái clone alert
      const { data: cloneAlert, error: updateError } = await supabase
        .from('license_clones')
        .update({ 
          status,
          review_notes: reviewNotes || null,
          reviewed_by: user.id,
          reviewed_at: new Date().toISOString()
        })
        .eq('id', id)
        .select('*, licenses(*)')
        .single();

      if (updateError) {
        console.error('Error updating clone alert:', updateError);
        throw new Error(`Failed to update clone alert status: ${updateError.message}`);
      }
      
      // Nếu status là REVOKED, vô hiệu hóa license
      if (status === 'REVOKED' && cloneAlert) {
        await this.revokeLicense(
          cloneAlert.license_id, 
          `Revoked due to confirmed clone: ${reviewNotes || 'No additional notes provided'}`
        );
      }

      return {
        success: true,
        message: `Clone alert status updated to ${status}`
      };
    } catch (error: any) {
      console.error('Error in updateCloneAlertStatus:', error);
      throw error;
    }
  }

  /**
   * Tạo hardware fingerprint
   * Phương thức này sẽ được chạy ở phía client
   */
  static generateHardwareFingerprint(systemInfo: any): string {
    try {
      // Hàm này thường chạy ở client và sử dụng thông tin hệ thống
      // Tạo một chuỗi từ các thông tin không thay đổi của máy chủ
      const fingerprintData = [
        systemInfo.hostname || '',
        systemInfo.cpuModel || '',
        systemInfo.cpuCores || '',
        systemInfo.totalMemory || '',
        systemInfo.macAddresses?.join(',') || '',
        systemInfo.diskId || '',
        systemInfo.osInfo || ''
      ].join('|');
      
      // Hash fingerprint data
      const hash = crypto.createHash('sha256');
      hash.update(fingerprintData);
      
      return hash.digest('hex');
    } catch (error) {
      console.error('Error generating hardware fingerprint:', error);
      // Trả về một giá trị ngẫu nhiên nếu không thể tạo fingerprint
      return crypto.randomBytes(32).toString('hex');
    }
  }

  /**
   * Xác thực license và trả về thông tin
   * Phương thức này sẽ được sử dụng bởi phần mềm cài đặt on-premise
   */
  static async validateLicense(
    licenseKey: string,
    hardwareFingerprint: string,
  ): Promise<{
    valid: boolean;
    message: string;
    licenseInfo?: {
      customerName: string;
      expiryDate: string;
      daysRemaining: number;
      status: string;
    };
  }> {
    try {
      const supabase = await createClient();
      
      // Tìm license theo key
      const { data: license, error: findError } = await supabase
        .from('licenses')
        .select('*')
        .eq('license_key', licenseKey)
        .single();

      if (findError || !license) {
        return { 
          valid: false, 
          message: 'Invalid license key' 
        };
      }

      // Kiểm tra nếu license đã bị vô hiệu hóa
      if (!license.is_active) {
        return { 
          valid: false, 
          message: 'License has been revoked', 
          licenseInfo: {
            customerName: license.customer_name,
            expiryDate: license.expiry_date,
            daysRemaining: 0,
            status: 'REVOKED'
          }
        };
      }

      // Kiểm tra nếu license đã hết hạn
      const currentDate = new Date();
      const expiryDate = new Date(license.expiry_date);
      
      if (expiryDate < currentDate) {
        return { 
          valid: false, 
          message: 'License has expired', 
          licenseInfo: {
            customerName: license.customer_name,
            expiryDate: license.expiry_date,
            daysRemaining: 0,
            status: 'EXPIRED'
          }
        };
      }
      
      // Tính số ngày còn lại
      const daysRemaining = Math.ceil((expiryDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
      
      // Nếu license chưa kích hoạt, trả về thông tin hợp lệ với trạng thái chưa kích hoạt
      if (!license.hardware_fingerprint) {
        return {
          valid: true,
          message: 'Valid license, not activated yet',
          licenseInfo: {
            customerName: license.customer_name,
            expiryDate: license.expiry_date,
            daysRemaining,
            status: 'NOT_ACTIVATED'
          }
        };
      }
      
      // Kiểm tra hardware fingerprint
      if (license.hardware_fingerprint !== hardwareFingerprint) {
        // Ghi log sự việc nhưng vẫn cho phép sử dụng
        await supabase.rpc('detect_license_clone', {
          p_license_id: license.id,
          p_hardware_fingerprint: hardwareFingerprint,
          p_ip_address: 'validation_request'
        });
        
        return {
          valid: true, // Vẫn trả về valid=true vì chúng ta muốn quản lý thủ công
          message: 'Hardware fingerprint mismatch detected and logged',
          licenseInfo: {
            customerName: license.customer_name,
            expiryDate: license.expiry_date,
            daysRemaining,
            status: 'WARNING'
          }
        };
      }
      
      // License hợp lệ
      return {
        valid: true,
        message: 'License is valid',
        licenseInfo: {
          customerName: license.customer_name,
          expiryDate: license.expiry_date,
          daysRemaining,
          status: 'ACTIVE'
        }
      };
    } catch (error: any) {
      console.error('Error validating license:', error);
      return {
        valid: false,
        message: `Error validating license: ${error.message}`
      };
    }
  }

  /**
   * Tạo một license key mẫu cho việc kiểm thử
   */
  static async createSampleLicense(): Promise<License> {
    return this.createLicense(
      'Demo Hotel',
      30, // 30 ngày
      undefined,
      { demo: true }
    );
  }
}