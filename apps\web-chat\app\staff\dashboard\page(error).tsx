// FIXED: Staff Dashboard with working Supabase Realtime
// Original backed up to dashboard.backup.tsx

'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import ChatMessage from './components/ChatMessage';
import ChatInput from './components/ChatInput';
import styles from './dashboard.module.scss';
import NotificationCenter from './components/notifications/NotificationCenter';
import StaffStatus from './components/StaffStatus';
import ChatSearch from './components/ChatSearch';
import ChatStats from './components/ChatStats';
import QuickActions from './components/QuickActions';
import { createClientSupabase } from '../../lib/supabase';

interface User {
  id: string;
  email: string;
  display_name: string;
  role: string;
  tenant_id: string;
  department?: string;
  title?: string;
  avatar_url?: string;
  reception_points?: any[];
}

interface ChatSession {
  id: string;
  guest_name: string;
  room_number?: string;
  language: string;
  status: 'active' | 'pending' | 'waiting';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  last_message: string;
  last_message_time: string;
  unread_count: number;
  source: string;
  session_ids?: string[];
}

interface Message {
  id: string;
  content: string;
  original_content?: string;
  translated_content?: string;
  sender_type: 'guest' | 'staff';
  sender_name: string;
  timestamp: string;
  is_translated: boolean;
  original_language?: string;
  translated_language?: string;
  translation_confidence?: number;
  show_translation: boolean;
}

export default function StaffDashboard() {
  // Core state
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeChatSessions, setActiveChatSessions] = useState<ChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [onlineStatus, setOnlineStatus] = useState<'online' | 'busy' | 'away'>('online');
  const [autoTranslate, setAutoTranslate] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [realtimeStatus, setRealtimeStatus] = useState<string>('Not connected');
  const [filteredChatSessions, setFilteredChatSessions] = useState<ChatSession[]>([]);
  const [usePollingFallback, setUsePollingFallback] = useState(false);
  const [lastPollingUpdate, setLastPollingUpdate] = useState<Date | null>(null);

  // Chat filters
  const [chatFilters, setChatFilters] = useState({
    status: 'all' as 'all' | 'active' | 'pending' | 'waiting',
    priority: 'all' as 'all' | 'low' | 'normal' | 'high' | 'urgent',
    language: 'all' as 'all' | 'en' | 'vi' | 'ko' | 'ja' | 'es' | 'fr' | 'de' | 'th' | 'id',
    timeRange: 'all' as 'all' | 'today' | 'week' | 'month',
    unreadOnly: false
  });

  // Refs
  const router = useRouter();
  const supabaseRef = useRef<any>(null);
  const channelRef = useRef<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const realtimeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Utility functions
  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Initialize Supabase (exactly like debugger)
  const initializeSupabase = useCallback(() => {
    try {
      supabaseRef.current = createClientSupabase();
      console.log('✅ Fixed Dashboard: Supabase client initialized');
      console.log(`📡 URL: ${supabaseRef.current.supabaseUrl}`);
      console.log(`🔑 Key: ${supabaseRef.current.supabaseKey.substring(0, 20)}...`);
    } catch (err) {
      console.error(`❌ Fixed Dashboard: Failed to initialize Supabase: ${err}`);
    }
  }, []);

  // Setup realtime subscription (exactly like debugger)
  const setupRealtimeSubscription = useCallback(() => {
    if (!supabaseRef.current || !user) {
      console.log('❌ Fixed Dashboard: Supabase not initialized or no user');
      return;
    }

    if (channelRef.current) {
      console.log('🔄 Fixed Dashboard: Unsubscribing existing channel...');
      channelRef.current.unsubscribe();
    }

    console.log('🔄 Fixed Dashboard: Setting up realtime subscription...');
    console.log(`🎯 Filter: tenant_id=eq.${user.tenant_id}`);

    // Use EXACT same logic as working debugger
    channelRef.current = supabaseRef.current
      .channel('fixed-staff-messages')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'tenant_chat_messages',
          filter: `tenant_id=eq.${user.tenant_id}`
        },
        (payload: any) => {
          console.log(`🔔 Fixed Dashboard: REALTIME MESSAGE RECEIVED!`);
          console.log(`📄 Message ID: ${payload.new.id}`);
          console.log(`👤 Sender: ${payload.new.sender_type} (${payload.new.sender_name})`);
          console.log(`💬 Content: ${payload.new.content?.substring(0, 50)}...`);
          console.log(`🏢 Tenant: ${payload.new.tenant_id}`);
          console.log(`⏰ Time: ${payload.new.created_at}`);

          // Update messages if for selected session
          if (selectedSession && payload.new.chat_session_id === selectedSession) {
            const newMessage: Message = {
              id: payload.new.id,
              content: payload.new.content,
              sender_type: payload.new.sender_type,
              sender_name: payload.new.sender_name || (payload.new.sender_type === 'guest' ? 'Guest' : 'Staff'),
              timestamp: payload.new.created_at,
              is_translated: payload.new.is_translated || false,
              show_translation: false
            };

            setMessages(prev => {
              const exists = prev.some(msg => msg.id === newMessage.id);
              if (exists) return prev;
              console.log('➕ Fixed Dashboard: Adding new message to chat:', newMessage.id);
              return [...prev, newMessage];
            });

            setTimeout(scrollToBottom, 100);
          }

          // Update session list
          refreshSessionList();
        }
      )
      .subscribe((status: string) => {
        setRealtimeStatus(status);
        console.log(`📡 Fixed Dashboard: Subscription status: ${status}`);
        
        if (status === 'SUBSCRIBED') {
          console.log('✅ Fixed Dashboard: REALTIME SUBSCRIPTION ACTIVE!');
        } else if (status === 'CHANNEL_ERROR') {
          console.log('❌ Fixed Dashboard: REALTIME SUBSCRIPTION FAILED!');
        } else if (status === 'CLOSED') {
          console.log('⚠️ Fixed Dashboard: REALTIME SUBSCRIPTION CLOSED!');
        }
      });
  }, [user, selectedSession]);

  // Session list refresh
  const refreshSessionList = useCallback(async () => {
    if (!user) return;

    try {
      const response = await fetch(`/api/chat-sessions?tenant_id=${user.tenant_id}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.sessions)) {
          const transformedSessions: ChatSession[] = data.sessions.map((session: any) => ({
            id: session.id,
            guest_name: session.qr_info?.room_number ?
              `Room ${session.qr_info.room_number} Guest` :
              'Guest User',
            room_number: session.qr_info?.room_number || undefined,
            language: session.guest_language?.toUpperCase() || 'EN',
            status: session.status as 'active' | 'pending' | 'waiting',
            priority: session.priority as 'low' | 'normal' | 'high' | 'urgent',
            last_message: 'Loading...',
            last_message_time: formatTimeAgo(session.updated_at),
            unread_count: 0,
            source: session.qr_info?.location || session.reception_point?.name || 'Direct'
          }));

          setActiveChatSessions(transformedSessions);
          console.log(`✅ Fixed Dashboard: Session list refreshed (${transformedSessions.length} sessions)`);
        }
      }
    } catch (error) {
      console.error('❌ Fixed Dashboard: Error refreshing session list:', error);
    }
  }, [user]);

  // Send message
  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!selectedSession || !content.trim()) return false;

    try {
      console.log(`📤 Fixed Dashboard: Sending message: "${content}"`);
      
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: selectedSession,
          sender_type: 'staff',
          sender_name: user?.display_name || 'Staff',
          content: content.trim()
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Fixed Dashboard: Message sent successfully: ${data.message?.id}`);
        return true;
      }
      
      throw new Error('Failed to send message');
    } catch (err) {
      console.error('❌ Fixed Dashboard: Failed to send message:', err);
      return false;
    }
  }, [selectedSession, user]);

  // Load messages for selected session
  const loadMessages = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/messages?session_id=${sessionId}&limit=50`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.messages)) {
          const transformedMessages: Message[] = data.messages.map((msg: any) => ({
            id: msg.id,
            content: msg.content,
            sender_type: msg.sender_type,
            sender_name: msg.sender_name || (msg.sender_type === 'guest' ? 'Guest' : 'Staff'),
            timestamp: msg.created_at,
            is_translated: msg.is_translated || false,
            show_translation: false
          }));
          setMessages(transformedMessages);
          setTimeout(scrollToBottom, 100);
        }
      }
    } catch (err) {
      console.error('❌ Fixed Dashboard: Failed to load messages:', err);
    }
  }, []);

  // Handle session selection
  const handleSessionSelect = useCallback((sessionId: string) => {
    setSelectedSession(sessionId);
    loadMessages(sessionId);

    // Mark session as read
    setActiveChatSessions(prev => prev.map(session =>
      session.id === sessionId ? { ...session, unread_count: 0 } : session
    ));
  }, [loadMessages]);

  // Apply chat filters
  useEffect(() => {
    let filtered = activeChatSessions;

    if (chatFilters.status !== 'all') {
      filtered = filtered.filter(session => session.status === chatFilters.status);
    }

    if (chatFilters.priority !== 'all') {
      filtered = filtered.filter(session => session.priority === chatFilters.priority);
    }

    if (chatFilters.unreadOnly) {
      filtered = filtered.filter(session => session.unread_count > 0);
    }

    if (searchQuery.trim()) {
      filtered = filtered.filter(session =>
        session.guest_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        session.room_number?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        session.source.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredChatSessions(filtered);
  }, [activeChatSessions, chatFilters, searchQuery]);

  // Initialize dashboard
  useEffect(() => {
    const token = localStorage.getItem('staff_token');
    const userData = localStorage.getItem('staff_user');

    if (!token || !userData) {
      router.push('/staff');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
      console.log('🚀 Fixed Dashboard: Initialized with user:', parsedUser.display_name);
    } catch (error) {
      console.error('Error parsing user data:', error);
      router.push('/staff');
    } finally {
      setLoading(false);
    }
  }, [router]);

  // Setup when user is loaded
  useEffect(() => {
    if (user) {
      initializeSupabase();
      refreshSessionList();

      // Setup realtime after a short delay
      setTimeout(() => {
        setupRealtimeSubscription();
      }, 1000);
    }

    return () => {
      if (channelRef.current) {
        console.log('🧹 Fixed Dashboard: Cleaning up realtime subscription');
        channelRef.current.unsubscribe();
      }
    };
  }, [user, initializeSupabase, refreshSessionList, setupRealtimeSubscription]);

  if (loading) {
    return <div className={styles.loading}>Loading Staff Dashboard...</div>;
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h1>Staff Dashboard</h1>
        <div className={styles.connectionStatus}>
          <span className={`${styles.statusIndicator} ${realtimeStatus === 'SUBSCRIBED' ? styles.connected : styles.disconnected}`}>
            {realtimeStatus === 'SUBSCRIBED' ? '🟢 Realtime Active' :
             realtimeStatus === 'CHANNEL_ERROR' ? '🔴 Realtime Failed' :
             realtimeStatus === 'CLOSED' ? '⚠️ Realtime Closed' :
             '🟡 Connecting...'}
          </span>
        </div>

        {/* User Info */}
        <div className={styles.userInfo}>
          <StaffStatus
            status={onlineStatus}
            onStatusChange={setOnlineStatus}
            user={user}
          />
        </div>
      </div>

      <div className={styles.content}>
        {/* Sidebar */}
        <div className={styles.sidebar}>
          {/* Search and Filters */}
          <div className={styles.searchSection}>
            <ChatSearch
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              filters={chatFilters}
              onFiltersChange={setChatFilters}
            />
          </div>

          {/* Stats */}
          <div className={styles.statsSection}>
            <ChatStats sessions={activeChatSessions} />
          </div>

          {/* Session List */}
          <div className={styles.sessionList}>
            <h3>Active Chats ({filteredChatSessions.length})</h3>
            {filteredChatSessions.map((session) => (
              <div
                key={session.id}
                className={`${styles.sessionItem} ${selectedSession === session.id ? styles.selected : ''}`}
                onClick={() => handleSessionSelect(session.id)}
              >
                <div className={styles.sessionHeader}>
                  <span className={styles.guestName}>{session.guest_name}</span>
                  <span className={`${styles.priority} ${styles[session.priority]}`}>
                    {session.priority.toUpperCase()}
                  </span>
                </div>
                <div className={styles.sessionMeta}>
                  <span className={styles.language}>{session.language}</span>
                  <span className={styles.source}>{session.source}</span>
                  {session.room_number && (
                    <span className={styles.room}>Room {session.room_number}</span>
                  )}
                </div>
                <div className={styles.lastMessage}>{session.last_message}</div>
                <div className={styles.sessionFooter}>
                  <span className={styles.time}>{session.last_message_time}</span>
                  {session.unread_count > 0 && (
                    <span className={styles.unreadBadge}>{session.unread_count}</span>
                  )}
                </div>
              </div>
            ))}

            {filteredChatSessions.length === 0 && (
              <div className={styles.noSessions}>
                <p>No active chat sessions</p>
                <p style={{ fontSize: '12px', color: '#666' }}>
                  Realtime Status: {realtimeStatus}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Main Chat Area */}
        <div className={styles.chatArea}>
          {selectedSession ? (
            <>
              {/* Chat Header */}
              <div className={styles.chatHeader}>
                <div className={styles.chatInfo}>
                  <h3>{activeChatSessions.find(s => s.id === selectedSession)?.guest_name}</h3>
                  <span className={styles.chatMeta}>
                    {activeChatSessions.find(s => s.id === selectedSession)?.language} •
                    {activeChatSessions.find(s => s.id === selectedSession)?.source}
                  </span>
                </div>

                <div className={styles.chatActions}>
                  <QuickActions
                    sessionId={selectedSession}
                    onAction={(action) => console.log('Quick action:', action)}
                  />
                </div>
              </div>

              {/* Messages */}
              <div className={styles.messagesContainer}>
                {messages.map((message) => (
                  <ChatMessage
                    key={message.id}
                    message={message}
                    showTranslation={autoTranslate}
                    onToggleTranslation={(messageId) => {
                      setMessages(prev => prev.map(msg =>
                        msg.id === messageId
                          ? { ...msg, show_translation: !msg.show_translation }
                          : msg
                      ));
                    }}
                  />
                ))}

                {isTyping && (
                  <div className={styles.typingIndicator}>
                    <span>Guest is typing...</span>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <div className={styles.messageInputContainer}>
                <ChatInput
                  onSendMessage={sendMessage}
                  disabled={realtimeStatus !== 'SUBSCRIBED'}
                  placeholder={realtimeStatus !== 'SUBSCRIBED' ? 'Connecting...' : 'Type your message...'}
                />
              </div>
            </>
          ) : (
            <div className={styles.noSelection}>
              <div className={styles.welcomeMessage}>
                <h2>Welcome to Staff Dashboard</h2>
                <p>Select a chat session from the sidebar to start messaging</p>

                <div className={styles.statusInfo}>
                  <p><strong>Connection Status:</strong> {realtimeStatus}</p>
                  <p><strong>Active Sessions:</strong> {activeChatSessions.length}</p>
                  {user && (
                    <p><strong>Logged in as:</strong> {user.display_name} ({user.role})</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Notification Center */}
      <NotificationCenter />
    </div>
  );
}
