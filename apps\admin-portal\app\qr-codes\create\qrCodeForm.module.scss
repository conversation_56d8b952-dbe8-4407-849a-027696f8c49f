@import '../../styles/_variables';

.container {
  width: 100%;
  max-width: 800px;
  padding: 24px;
  margin: 0 auto;
}

.pageHeader {
  margin-bottom: $spacing-lg;
}

.titleSection {
  display: flex;
  flex-direction: column;
}

.backLink {
  display: flex;
  align-items: center;
  color: $gray;
  text-decoration: none;
  margin-bottom: $spacing-xs;
  font-size: 14px;
  
  svg {
    margin-right: 8px;
  }
  
  &:hover {
    color: $primary-color;
  }
}

.pageTitle {
  font-size: 24px;
  font-weight: 600;
  color: $dark-gray;
  margin: 0;
}

.limitSection {
  background-color: white;
  border-radius: $border-radius-md;
  padding: $spacing-md;
  margin-bottom: $spacing-md;
  box-shadow: $shadow-sm;
}

.formCard {
  background-color: white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  padding: $spacing-lg;
  margin-bottom: $spacing-xl;
}

.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.label {
  font-size: 14px;
  font-weight: 500;
  color: $dark-gray;
  display: flex;
  align-items: center;
}

.required {
  color: #d63c00;
  margin-left: 4px;
}

.input, .textarea, .select {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: $border-radius-sm;
  font-size: 14px;
  font-family: inherit;
  outline: none;
  transition: border-color 0.2s;
  
  &:focus {
    border-color: $primary-color;
  }
  
  &::placeholder {
    color: #bbb;
  }
  
  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
}

.fieldHelp {
  font-size: 12px;
  color: $gray;
  margin-top: 4px;
  margin-bottom: 0;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: $spacing-md;
  margin-top: $spacing-md;
}

.cancelButton, .submitButton {
  padding: 10px 16px;
  border-radius: $border-radius-md;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.cancelButton {
  background-color: transparent;
  color: $gray;
  border: 1px solid #ddd;
  text-decoration: none;
  
  &:hover {
    background-color: #f5f5f5;
    color: $dark-gray;
  }
}

.submitButton {
  background-color: $primary-color;
  color: white;
  border: none;
  
  &:hover:not(:disabled) {
    background-color: darken($primary-color, 10%);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}
