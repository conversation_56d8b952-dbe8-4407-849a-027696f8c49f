import React, { ReactNode } from 'react';

export interface AlertProps {
  /**
   * Alert content
   */
  children: ReactNode;
  /**
   * Alert variant
   */
  variant?: 'info' | 'success' | 'warning' | 'error';
  /**
   * Optional title for the alert
   */
  title?: string;
  /**
   * Optional icon to display
   */
  icon?: ReactNode;
  /**
   * Show close button
   */
  closable?: boolean;
  /**
   * <PERSON><PERSON> for close button click
   */
  onClose?: () => void;
  /**
   * Additional CSS properties
   */
  style?: React.CSSProperties;
  /**
   * Optional CSS class name
   */
  className?: string;
}

export const Alert: React.FC<AlertProps> = ({
  children,
  variant = 'info',
  title,
  icon,
  closable = false,
  onClose,
  style,
  className,
  ...props
}) => {
  // Colors based on variant
  const variantStyles: Record<string, React.CSSProperties> = {
    info: {
      backgroundColor: '#EBF8FF', // Light blue
      color: '#1E40AF', // Dark blue
      borderColor: '#93C5FD', // Blue
    },
    success: {
      backgroundColor: '#ECFDF5', // Light green
      color: '#065F46', // Dark green
      borderColor: '#6EE7B7', // Green
    },
    warning: {
      backgroundColor: '#FFFBEB', // Light yellow
      color: '#92400E', // Dark orange
      borderColor: '#FCD34D', // Yellow
    },
    error: {
      backgroundColor: '#FEF2F2', // Light red
      color: '#B91C1C', // Dark red
      borderColor: '#FCA5A5', // Red
    },
  };

  // Default icons if not provided
  const getDefaultIcon = () => {
    if (icon) return icon;

    switch (variant) {
      case 'info':
        return (
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM7 8C7 7.44772 7.44772 7 8 7C8.55228 7 9 7.44772 9 8V11C9 11.5523 8.55228 12 8 12C7.44772 12 7 11.5523 7 11V8ZM8 4C7.44772 4 7 4.44772 7 5C7 5.55228 7.44772 6 8 6C8.55228 6 9 5.55228 9 5C9 4.44772 8.55228 4 8 4Z" fill="currentColor"/>
          </svg>
        );
      case 'success':
        return (
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM11.7071 6.70711C12.0976 6.31658 12.0976 5.68342 11.7071 5.29289C11.3166 4.90237 10.6834 4.90237 10.2929 5.29289L7 8.58579L5.70711 7.29289C5.31658 6.90237 4.68342 6.90237 4.29289 7.29289C3.90237 7.68342 3.90237 8.31658 4.29289 8.70711L6.29289 10.7071C6.68342 11.0976 7.31658 11.0976 7.70711 10.7071L11.7071 6.70711Z" fill="currentColor"/>
          </svg>
        );
      case 'warning':
        return (
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M8.00001 1.33325C8.36501 1.33325 8.70168 1.52659 8.88668 1.84159L15.22 13.5083C15.4067 13.8233 15.4067 14.2149 15.2217 14.5316C15.035 14.8483 14.6983 15.0416 14.335 15.0416H1.66668C1.30168 15.0416 0.965012 14.8483 0.778345 14.5316C0.593345 14.2149 0.593345 13.8233 0.778345 13.5083L7.11334 1.84159C7.29834 1.52659 7.63501 1.33325 8.00001 1.33325ZM8.00001 10.6666C7.63334 10.6666 7.33334 10.3666 7.33334 9.99992V7.33325C7.33334 6.96659 7.63334 6.66659 8.00001 6.66659C8.36668 6.66659 8.66668 6.96659 8.66668 7.33325V9.99992C8.66668 10.3666 8.36668 10.6666 8.00001 10.6666ZM8.00001 12.6666C7.63334 12.6666 7.33334 12.3666 7.33334 11.9999C7.33334 11.6333 7.63334 11.3333 8.00001 11.3333C8.36668 11.3333 8.66668 11.6333 8.66668 11.9999C8.66668 12.3666 8.36668 12.6666 8.00001 12.6666Z" fill="currentColor"/>
          </svg>
        );
      case 'error':
        return (
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM10.7071 6.70711C11.0976 6.31658 11.0976 5.68342 10.7071 5.29289C10.3166 4.90237 9.68342 4.90237 9.29289 5.29289L8 6.58579L6.70711 5.29289C6.31658 4.90237 5.68342 4.90237 5.29289 5.29289C4.90237 5.68342 4.90237 6.31658 5.29289 6.70711L6.58579 8L5.29289 9.29289C4.90237 9.68342 4.90237 10.3166 5.29289 10.7071C5.68342 11.0976 6.31658 11.0976 6.70711 10.7071L8 9.41421L9.29289 10.7071C9.68342 11.0976 10.3166 11.0976 10.7071 10.7071C11.0976 10.3166 11.0976 9.68342 10.7071 9.29289L9.41421 8L10.7071 6.70711Z" fill="currentColor"/>
          </svg>
        );
      default:
        return null;
    }
  };

  const alertStyle: React.CSSProperties = {
    display: 'flex',
    padding: '12px 16px',
    borderRadius: '4px',
    border: `1px solid ${variantStyles[variant].borderColor}`,
    ...variantStyles[variant],
    ...style,
  };

  const iconStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'flex-start',
    marginRight: '12px',
    color: variantStyles[variant].color,
  };

  const contentStyle: React.CSSProperties = {
    flex: 1,
  };

  const titleStyle: React.CSSProperties = {
    fontWeight: 600,
    fontSize: '14px',
    marginTop: 0,
    marginBottom: title ? '4px' : 0,
  };

  const messageStyle: React.CSSProperties = {
    fontSize: '14px',
    margin: 0,
  };

  const closeButtonStyle: React.CSSProperties = {
    background: 'none',
    border: 'none',
    cursor: 'pointer',
    padding: '4px',
    color: variantStyles[variant].color,
    fontSize: '16px',
    marginLeft: '12px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    outline: 'none',
  };

  return (
    <div
      style={alertStyle}
      className={className}
      role="alert"
      {...props}
    >
      <span style={iconStyle}>
        {getDefaultIcon()}
      </span>
      
      <div style={contentStyle}>
        {title && <p style={titleStyle}>{title}</p>}
        <div style={messageStyle}>{children}</div>
      </div>

      {closable && (
        <button
          style={closeButtonStyle}
          onClick={onClose}
          aria-label="Close"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.5 3.5L3.5 12.5M3.5 3.5L12.5 12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      )}
    </div>
  );
};

export default Alert;
