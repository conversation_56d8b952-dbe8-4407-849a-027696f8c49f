import { Router } from 'express';
import { body } from 'express-validator';
import * as authController from '../controllers/authController';
import { authenticate } from '../middlewares/authMiddleware';

const router = Router();

// Validation middleware
const registerValidation = [
  body('email').isEmail().withMessage('Invalid email format'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
  body('full_name').optional().isString().withMessage('Full name must be a string'),
  body('preferred_language').optional().isString().withMessage('Preferred language must be a string')
];

const loginValidation = [
  body('email').isEmail().withMessage('Invalid email format'),
  body('password').notEmpty().withMessage('Password is required')
];

const updateProfileValidation = [
  body('full_name').optional().isString().withMessage('Full name must be a string'),
  body('preferred_language').optional().isString().withMessage('Preferred language must be a string'),
  body('phone_number').optional().isString().withMessage('Phone number must be a string'),
  body('avatar_url').optional().isString().withMessage('Avatar URL must be a string')
];

const changePasswordValidation = [
  body('current_password').notEmpty().withMessage('Current password is required'),
  body('new_password').isLength({ min: 6 }).withMessage('New password must be at least 6 characters long')
];

// Public routes
router.post('/register', registerValidation, authController.register);
router.post('/login', loginValidation, authController.login);
router.post('/forgot-password', body('email').isEmail(), authController.forgotPassword);
router.post('/refresh-token', body('refresh_token').notEmpty(), authController.refreshToken);
router.get('/verify-email/:userId/:token', authController.verifyEmail);

// Protected routes
router.get('/profile', authenticate, authController.getProfile);
router.put('/profile', authenticate, updateProfileValidation, authController.updateProfile);
router.post('/change-password', authenticate, changePasswordValidation, authController.changePassword);

export default router;
