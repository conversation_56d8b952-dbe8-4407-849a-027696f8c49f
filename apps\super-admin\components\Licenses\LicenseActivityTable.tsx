import React, { useState } from 'react';
import { Table, Card } from '@loaloa/ui';
import useSWR from 'swr';
import styles from './LicenseActivityTable.module.scss';

interface LicenseActivity {
  id: string;
  license_id: string;
  activity_type: 'ACTIVATION' | 'CHECK_IN' | 'WARNING' | 'VIOLATION' | 'REVOCATION';
  hardware_fingerprint?: string;
  ip_address?: string;
  timestamp: string;
  details: any;
}

interface LicenseActivityResponse {
  data: LicenseActivity[];
  meta: {
    total: number;
    page: number;
    limit: number;
    pageCount: number;
  }
}

interface LicenseActivityTableProps {
  licenseId: string;
}

const LicenseActivityTable: React.FC<LicenseActivityTableProps> = ({ licenseId }) => {
  const [page, setPage] = useState(1);
  const limit = 10;
  
  // Fetch activities
  const { data, error, isLoading } = useSWR<LicenseActivityResponse>(
    `/api/licenses/${licenseId}/activities?page=${page}&limit=${limit}`
  );
  
  // Activity type badge
  const ActivityTypeBadge = ({ type }: { type: string }) => {
    const getConfig = () => {
      switch (type) {
        case 'ACTIVATION':
          return { label: 'Activation', className: styles.activation };
        case 'CHECK_IN':
          return { label: 'Check-in', className: styles.checkIn };
        case 'WARNING':
          return { label: 'Warning', className: styles.warning };
        case 'VIOLATION':
          return { label: 'Violation', className: styles.violation };
        case 'REVOCATION':
          return { label: 'Revocation', className: styles.revocation };
        default:
          return { label: type, className: '' };
      }
    };
    
    const { label, className } = getConfig();
    
    return <span className={`${styles.badge} ${className}`}>{label}</span>;
  };
  
  // Columns for table
  const columns = [
    {
      header: 'Type',
      accessor: (activity: LicenseActivity) => (
        <ActivityTypeBadge type={activity.activity_type} />
      ),
      width: '15%',
    },
    {
      header: 'Timestamp',
      accessor: (activity: LicenseActivity) => (
        new Date(activity.timestamp).toLocaleString()
      ),
      width: '20%',
    },
    {
      header: 'IP Address',
      accessor: (activity: LicenseActivity) => activity.ip_address || 'N/A',
      width: '15%',
    },
    {
      header: 'Details',
      accessor: (activity: LicenseActivity) => {
        if (activity.activity_type === 'WARNING' && activity.details?.message) {
          return <span className={styles.warning}>{activity.details.message}</span>;
        }
        
        if (activity.activity_type === 'REVOCATION' && activity.details?.reason) {
          return <span className={styles.revocation}>{activity.details.reason}</span>;
        }
        
        return activity.details?.message || 'No details provided';
      },
      width: '50%',
    },
  ];
  
  if (error) {
    return (
      <Card>
        <div className={styles.error}>
          Error loading activity log. Please try again.
        </div>
      </Card>
    );
  }
  
  return (
    <Card>
      <div className={styles.activityTable}>
        <h3 className={styles.sectionTitle}>Activity Log</h3>
        
        <Table
          columns={columns}
          data={data?.data || []}
          isLoading={isLoading}
          pagination={{
            currentPage: page,
            totalPages: data?.meta?.pageCount || 1,
            onPageChange: setPage
          }}
          emptyMessage="No activities recorded yet."
        />
      </div>
    </Card>
  );
};

export default LicenseActivityTable;