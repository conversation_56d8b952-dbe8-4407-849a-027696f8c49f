import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import logger from '../utils/logger';
import { EmailVerificationPayload } from '../types';

dotenv.config();

// Create mail transporter
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: parseInt(process.env.EMAIL_PORT || '587'),
  secure: parseInt(process.env.EMAIL_PORT || '587') === 465,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD
  }
});

export const emailService = {
  async sendActivationEmail(
    email: string,
    customerName: string,
    licenseKey: string,
    payload: EmailVerificationPayload
  ): Promise<boolean> {
    try {
      // Create verification URL
      const verificationUrl = `${process.env.FRONTEND_URL}/license/verify?activationId=${payload.activationId}&token=${payload.token}`;
      
      // Format the email content with HTML
      const mailOptions = {
        from: `"${process.env.EMAIL_FROM_NAME}" <${process.env.EMAIL_FROM}>`,
        to: email,
        subject: 'LoaLoa License Activation',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <img src="https://your-logo-url.com/logo.png" alt="LoaLoa Logo" width="150" />
            </div>
            <h2 style="color: #333; text-align: center;">License Activation Required</h2>
            <p style="color: #555; line-height: 1.5;">Hello ${customerName},</p>
            <p style="color: #555; line-height: 1.5;">Thank you for using LoaLoa! To activate your license, please click the button below:</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Activate License</a>
            </div>
            <p style="color: #555; line-height: 1.5;">License information:</p>
            <ul style="color: #555; line-height: 1.5;">
              <li><strong>License Key:</strong> ${licenseKey}</li>
              <li><strong>Customer:</strong> ${customerName}</li>
              <li><strong>Email:</strong> ${email}</li>
            </ul>
            <p style="color: #555; line-height: 1.5;">If you did not request this license activation, please ignore this email.</p>
            <p style="color: #555; line-height: 1.5;">This activation link will expire in 24 hours.</p>
            <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;" />
            <p style="color: #999; font-size: 12px; text-align: center;">© ${new Date().getFullYear()} LoaLoa. All rights reserved.</p>
          </div>
        `
      };
      
      // Send the email
      const info = await transporter.sendMail(mailOptions);
      logger.info('Activation email sent', { 
        messageId: info.messageId,
        email, 
        licenseKey
      });
      
      return true;
    } catch (error) {
      logger.error('Error sending activation email', { error, email, licenseKey });
      return false;
    }
  }
};