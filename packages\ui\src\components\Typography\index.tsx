import React, { ReactNode } from 'react';
import { typographyTokens, defaultTheme } from './typography-theme';

// Các props chung cho tất cả Typography components
interface BaseTypographyProps {
  /**
   * Text content
   */
  children: ReactNode;
  /**
   * Optional color override
   */
  color?: string;
  /**
   * Optional align (left, center, right)
   */
  align?: 'left' | 'center' | 'right';
  /**
   * Optional theme
   */
  theme?: 'light' | 'dark' | 'studio';
  /**
   * Additional CSS properties
   */
  style?: React.CSSProperties;
  /**
   * Optional CSS class name
   */
  className?: string;
}

// Heading components (H1-H5)
interface HeadingProps extends BaseTypographyProps {
  /**
   * Optional font weight
   */
  fontWeight?: 'regular' | 'medium' | 'semiBold' | 'bold';
}

const getThemeColors = (theme: 'light' | 'dark' | 'studio') => {
  switch (theme) {
    case 'dark':
      return typographyTokens.darkColors;
    case 'studio':
      return typographyTokens.studioColors;
    case 'light':
    default:
      return typographyTokens.lightColors;
  }
};

export const Heading1: React.FC<HeadingProps> = ({
  children,
  color,
  align = 'left',
  theme = 'light',
  fontWeight = 'bold',
  style,
  className,
  ...props
}) => {
  const themeColors = getThemeColors(theme);
  
  const headingStyle: React.CSSProperties = {
    fontFamily: typographyTokens.fontFamily.primary,
    fontSize: typographyTokens.fontSize.h1,
    fontWeight: typographyTokens.fontWeight[fontWeight],
    lineHeight: typographyTokens.lineHeight.tight,
    color: color || themeColors.heading,
    textAlign: align,
    margin: '0 0 24px 0',
    ...style,
  };
  
  return (
    <h1 style={headingStyle} className={className} {...props}>
      {children}
    </h1>
  );
};

export const Heading2: React.FC<HeadingProps> = ({
  children,
  color,
  align = 'left',
  theme = 'light',
  fontWeight = 'bold',
  style,
  className,
  ...props
}) => {
  const themeColors = getThemeColors(theme);
  
  const headingStyle: React.CSSProperties = {
    fontFamily: typographyTokens.fontFamily.primary,
    fontSize: typographyTokens.fontSize.h2,
    fontWeight: typographyTokens.fontWeight[fontWeight],
    lineHeight: typographyTokens.lineHeight.tight,
    color: color || themeColors.heading,
    textAlign: align,
    margin: '0 0 20px 0',
    ...style,
  };
  
  return (
    <h2 style={headingStyle} className={className} {...props}>
      {children}
    </h2>
  );
};

export const Heading3: React.FC<HeadingProps> = ({
  children,
  color,
  align = 'left',
  theme = 'light',
  fontWeight = 'semiBold',
  style,
  className,
  ...props
}) => {
  const themeColors = getThemeColors(theme);
  
  const headingStyle: React.CSSProperties = {
    fontFamily: typographyTokens.fontFamily.primary,
    fontSize: typographyTokens.fontSize.h3,
    fontWeight: typographyTokens.fontWeight[fontWeight],
    lineHeight: typographyTokens.lineHeight.tight,
    color: color || themeColors.heading,
    textAlign: align,
    margin: '0 0 16px 0',
    ...style,
  };
  
  return (
    <h3 style={headingStyle} className={className} {...props}>
      {children}
    </h3>
  );
};

export const Heading4: React.FC<HeadingProps> = ({
  children,
  color,
  align = 'left',
  theme = 'light',
  fontWeight = 'semiBold',
  style,
  className,
  ...props
}) => {
  const themeColors = getThemeColors(theme);
  
  const headingStyle: React.CSSProperties = {
    fontFamily: typographyTokens.fontFamily.primary,
    fontSize: typographyTokens.fontSize.h4,
    fontWeight: typographyTokens.fontWeight[fontWeight],
    lineHeight: typographyTokens.lineHeight.tight,
    color: color || themeColors.heading,
    textAlign: align,
    margin: '0 0 12px 0',
    ...style,
  };
  
  return (
    <h4 style={headingStyle} className={className} {...props}>
      {children}
    </h4>
  );
};

export const Heading5: React.FC<HeadingProps> = ({
  children,
  color,
  align = 'left',
  theme = 'light',
  fontWeight = 'semiBold',
  style,
  className,
  ...props
}) => {
  const themeColors = getThemeColors(theme);
  
  const headingStyle: React.CSSProperties = {
    fontFamily: typographyTokens.fontFamily.primary,
    fontSize: typographyTokens.fontSize.h5,
    fontWeight: typographyTokens.fontWeight[fontWeight],
    lineHeight: typographyTokens.lineHeight.tight,
    color: color || themeColors.heading,
    textAlign: align,
    margin: '0 0 8px 0',
    ...style,
  };
  
  return (
    <h5 style={headingStyle} className={className} {...props}>
      {children}
    </h5>
  );
};

// Body text components
interface BodyTextProps extends BaseTypographyProps {
  /**
   * Make text bold
   */
  bold?: boolean;
  /**
   * Make text italic
   */
  italic?: boolean;
}

export const BodyRegular: React.FC<BodyTextProps> = ({
  children,
  color,
  align = 'left',
  theme = 'light',
  bold = false,
  italic = false,
  style,
  className,
  ...props
}) => {
  const themeColors = getThemeColors(theme);
  
  const bodyStyle: React.CSSProperties = {
    fontFamily: typographyTokens.fontFamily.primary,
    fontSize: typographyTokens.fontSize.bodyRegular,
    fontWeight: bold ? typographyTokens.fontWeight.bold : typographyTokens.fontWeight.regular,
    fontStyle: italic ? 'italic' : 'normal',
    lineHeight: typographyTokens.lineHeight.normal,
    color: color || themeColors.body,
    textAlign: align,
    margin: '0 0 16px 0',
    ...style,
  };
  
  return (
    <p style={bodyStyle} className={className} {...props}>
      {children}
    </p>
  );
};

export const BodyMedium: React.FC<BodyTextProps> = ({
  children,
  color,
  align = 'left',
  theme = 'light',
  bold = false,
  italic = false,
  style,
  className,
  ...props
}) => {
  const themeColors = getThemeColors(theme);
  
  const bodyStyle: React.CSSProperties = {
    fontFamily: typographyTokens.fontFamily.primary,
    fontSize: typographyTokens.fontSize.bodyMedium,
    fontWeight: bold ? typographyTokens.fontWeight.bold : typographyTokens.fontWeight.regular,
    fontStyle: italic ? 'italic' : 'normal',
    lineHeight: typographyTokens.lineHeight.normal,
    color: color || themeColors.body,
    textAlign: align,
    margin: '0 0 12px 0',
    ...style,
  };
  
  return (
    <p style={bodyStyle} className={className} {...props}>
      {children}
    </p>
  );
};

export const BodySmall: React.FC<BodyTextProps> = ({
  children,
  color,
  align = 'left',
  theme = 'light',
  bold = false,
  italic = false,
  style,
  className,
  ...props
}) => {
  const themeColors = getThemeColors(theme);
  
  const bodyStyle: React.CSSProperties = {
    fontFamily: typographyTokens.fontFamily.primary,
    fontSize: typographyTokens.fontSize.bodySmall,
    fontWeight: bold ? typographyTokens.fontWeight.bold : typographyTokens.fontWeight.regular,
    fontStyle: italic ? 'italic' : 'normal',
    lineHeight: typographyTokens.lineHeight.normal,
    color: color || themeColors.body,
    textAlign: align,
    margin: '0 0 8px 0',
    ...style,
  };
  
  return (
    <p style={bodyStyle} className={className} {...props}>
      {children}
    </p>
  );
};

// UI Label components
interface LabelProps extends BaseTypographyProps {
  /**
   * Make text uppercase
   */
  uppercase?: boolean;
}

export const ButtonLabel: React.FC<LabelProps> = ({
  children,
  color,
  align = 'center',
  theme = 'light',
  uppercase = true,
  style,
  className,
  ...props
}) => {
  const themeColors = getThemeColors(theme);
  
  const labelStyle: React.CSSProperties = {
    fontFamily: typographyTokens.fontFamily.primary,
    fontSize: typographyTokens.fontSize.buttonLabel,
    fontWeight: typographyTokens.fontWeight.medium,
    lineHeight: typographyTokens.lineHeight.tight,
    color: color || themeColors.heading,
    textAlign: align,
    textTransform: uppercase ? 'uppercase' : 'none',
    letterSpacing: uppercase ? '0.5px' : 'normal',
    margin: 0,
    ...style,
  };
  
  return (
    <span style={labelStyle} className={className} {...props}>
      {children}
    </span>
  );
};

export const CaptionLabel: React.FC<LabelProps> = ({
  children,
  color,
  align = 'left',
  theme = 'light',
  uppercase = true,
  style,
  className,
  ...props
}) => {
  const themeColors = getThemeColors(theme);
  
  const labelStyle: React.CSSProperties = {
    fontFamily: typographyTokens.fontFamily.primary,
    fontSize: typographyTokens.fontSize.captionLabel,
    fontWeight: typographyTokens.fontWeight.medium,
    lineHeight: typographyTokens.lineHeight.tight,
    color: color || themeColors.muted,
    textAlign: align,
    textTransform: uppercase ? 'uppercase' : 'none',
    letterSpacing: uppercase ? '0.5px' : 'normal',
    margin: 0,
    ...style,
  };
  
  return (
    <span style={labelStyle} className={className} {...props}>
      {children}
    </span>
  );
};

export const SmallLabel: React.FC<LabelProps> = ({
  children,
  color,
  align = 'left',
  theme = 'light',
  uppercase = true,
  style,
  className,
  ...props
}) => {
  const themeColors = getThemeColors(theme);
  
  const labelStyle: React.CSSProperties = {
    fontFamily: typographyTokens.fontFamily.primary,
    fontSize: typographyTokens.fontSize.smallLabel,
    fontWeight: typographyTokens.fontWeight.medium,
    lineHeight: typographyTokens.lineHeight.tight,
    color: color || themeColors.muted,
    textAlign: align,
    textTransform: uppercase ? 'uppercase' : 'none',
    letterSpacing: uppercase ? '0.5px' : 'normal',
    margin: 0,
    ...style,
  };
  
  return (
    <span style={labelStyle} className={className} {...props}>
      {children}
    </span>
  );
};
