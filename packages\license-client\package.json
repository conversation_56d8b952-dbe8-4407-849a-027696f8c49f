{"name": "@loaloa/license-client", "version": "0.1.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "exports": {".": {"require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./web": {"require": "./dist/web-only.js", "types": "./dist/web-only.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"axios": "^1.6.7", "@supabase/supabase-js": "^2.38.4"}, "devDependencies": {"@types/node": "^20.11.5", "typescript": "^5.4.5", "rimraf": "^5.0.5"}}