-- Tạo các extension cầ<PERSON> thiết
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Tạo schema cho tenant mặc định
CREATE SCHEMA IF NOT EXISTS tenant_default;

-- T<PERSON><PERSON> các bảng chung
CREATE TABLE IF NOT EXISTS public.tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    address TEXT,
    logo_url TEXT,
    primary_color VARCHAR(7),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB
);

CREATE TABLE IF NOT EXISTS public.licenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    license_key VARCHAR(50) UNIQUE NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    issue_date TIMESTAMPTZ DEFAULT NOW(),
    expiry_date TIMESTAMPTZ NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    hardware_fingerprint VARCHAR(255),
    activation_date TIMESTAMPTZ,
    last_check_in TIMESTAMPTZ,
    check_in_count INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS public.license_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    license_id UUID REFERENCES public.licenses(id),
    activity_type VARCHAR(20) NOT NULL,
    hardware_fingerprint VARCHAR(255),
    ip_address VARCHAR(50),
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    details JSONB
);

CREATE TABLE IF NOT EXISTS public.license_clones (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    license_id UUID REFERENCES public.licenses(id),
    detection_time TIMESTAMPTZ DEFAULT NOW(),
    original_fingerprint VARCHAR(255),
    clone_fingerprint VARCHAR(255),
    status VARCHAR(20) DEFAULT 'DETECTED',
    details JSONB
);

-- Tạo bảng quản lý schema tenant
CREATE TABLE IF NOT EXISTS public.tenant_schemas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID REFERENCES public.tenants(id),
    schema_name VARCHAR(63) NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
