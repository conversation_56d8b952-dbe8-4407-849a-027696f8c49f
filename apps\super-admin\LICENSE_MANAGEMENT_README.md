# License Management System for LoaLoa

## Tổng quan

Hệ thống License Management cho LoaLoa cung cấp cách thức kiểm soát và quản lý license cho các triển khai on-premise của phần mềm. Hệ thống được thiết kế để đơn giản và hiệu quả, tập trung vào việc giới hạn thời gian sử dụng và phát hiện các trường hợp clone license.

## Tính năng chính

- **Quản lý License**: Tạo, gia hạn, vô hiệu hóa license
- **Theo dõi thời gian**: Giớ<PERSON> hạn thời gian sử dụng (ngày hết hạn)
- **Phát hiện Clone**: Phát hiện khi license được sử dụng trên nhiều máy khác nhau
- **<PERSON><PERSON><PERSON> thực từ xa**: Client on-premise kiểm tra tính hợp lệ với server định kỳ
- **<PERSON><PERSON><PERSON><PERSON> lý thủ công**: Admin xem xét và quyết định xử lý vi phạm

## Cấu trúc Database

### Bảng chính

1. **licenses** - Lưu thông tin license
   - id: UUID (primary key)
   - license_key: VARCHAR(50) (unique)
   - customer_name: VARCHAR(255)
   - issue_date: TIMESTAMPTZ
   - expiry_date: TIMESTAMPTZ
   - is_active: BOOLEAN
   - hardware_fingerprint: VARCHAR(255)
   - activation_date: TIMESTAMPTZ
   - last_check_in: TIMESTAMPTZ
   - check_in_count: INTEGER

2. **license_activities** - Ghi log hoạt động của license
   - id: UUID (primary key)
   - license_id: UUID (foreign key -> licenses.id)
   - activity_type: ENUM ('ACTIVATION', 'CHECK_IN', 'WARNING', 'VIOLATION', 'REVOCATION')
   - hardware_fingerprint: VARCHAR(255)
   - ip_address: VARCHAR(50)
   - timestamp: TIMESTAMPTZ
   - details: JSONB

3. **license_clones** - Theo dõi các trường hợp nghi ngờ clone
   - id: UUID (primary key)
   - license_id: UUID (foreign key -> licenses.id)
   - detection_time: TIMESTAMPTZ
   - original_fingerprint: VARCHAR(255)
   - clone_fingerprint: VARCHAR(255)
   - status: ENUM ('DETECTED', 'UNDER_REVIEW', 'CONFIRMED', 'FALSE_ALARM', 'REVOKED')

### Functions & Procedures

- **generate_license_key()**: Tạo license key mới với format `LLHM-XXXXX-XXXXX-XXXXX-XXXXX`
- **detect_license_clone()**: Phát hiện và ghi nhận trường hợp clone license
- **update_updated_at_column()**: Cập nhật trường `updated_at` khi có thay đổi

## Quy trình hoạt động

### 1. Tạo License

1. Admin tạo license mới cho khách hàng từ Super Admin Dashboard
2. Hệ thống tạo license key theo format `LLHM-XXXXX-XXXXX-XXXXX-XXXXX`
3. Admin cung cấp license key cho khách hàng

### 2. Kích hoạt License

1. Client on-premise gửi yêu cầu kích hoạt kèm license key và hardware fingerprint
2. Server xác thực license key và lưu hardware fingerprint
3. Server trả về kết quả xác thực và thông tin license

### 3. Check-in định kỳ

1. Client on-premise gửi yêu cầu check-in định kỳ (mỗi ngày)
2. Server kiểm tra tính hợp lệ của license và hardware fingerprint
3. Nếu phát hiện clone (hardware fingerprint khác), hệ thống ghi nhận nhưng vẫn cho phép sử dụng
4. Admin sẽ xem xét các trường hợp clone và quyết định xử lý

### 4. Gia hạn License

1. Admin gia hạn license từ Super Admin Dashboard
2. Hệ thống cập nhật ngày hết hạn của license

### 5. Vô hiệu hóa License

1. Admin vô hiệu hóa license từ Super Admin Dashboard
2. Client on-premise sẽ nhận được thông báo license đã bị vô hiệu hóa khi check-in lần tới

## Triển khai với Docker

Để triển khai hệ thống License Management với Docker, cần thêm một license agent container vào cấu hình docker-compose:

```yaml
# docker-compose.yml
version: '3.8'

services:
  # Các service khác...
  
  license-agent:
    image: loaloa/license-agent:latest
    restart: always
    volumes:
      - ./license:/app/license
    environment:
      LICENSE_KEY: ${LICENSE_KEY}
      CHECK_INTERVAL: 86400 # Kiểm tra mỗi ngày
      LICENSE_SERVER: https://license.loaloa.app
API Routes
1. License Management
GET /api/licenses: Lấy danh sách licenses
POST /api/licenses: Tạo license mới
GET /api/licenses/:id: Lấy chi tiết license
PUT /api/licenses/:id: Gia hạn license
POST /api/licenses/:id/revoke: Vô hiệu hóa license
GET /api/licenses/:id/activities: Lấy lịch sử hoạt động của license
2. License Check (Client On-premise)
POST /api/license-check?action=activate: Kích hoạt license
POST /api/license-check: Check-in license
POST /api/license-validate: Xác thực tính hợp lệ của license
Best Practices
Bảo mật: Sử dụng HTTPS cho tất cả các giao tiếp với license server
Graceful Degradation: Client nên có cơ chế dự phòng khi không thể kết nối với license server
Offline Mode: Cho phép sử dụng trong thời gian ngắn ngay cả khi không thể check-in
Logging: Ghi log đầy đủ mọi hoạt động liên quan đến license
Chú ý khi phát triển
Xử lý lỗi: Luôn xử lý lỗi một cách graceful và cung cấp thông báo rõ ràng cho người dùng
Sao lưu dữ liệu: Đảm bảo dữ liệu license được sao lưu định kỳ
Monitoring: Thiết lập hệ thống giám sát để phát hiện sớm các vấn đề
Next Steps
Dashboard Analytics: Thêm biểu đồ và thống kê về license usage
Email Notifications: Thông báo tự động cho admin khi có license sắp hết hạn hoặc phát hiện clone
Batch Operations: Cho phép thực hiện thao tác hàng loạt (gia hạn, vô hiệu hóa nhiều license)

------------------

Cập nhật 1: Thêm phần về giao diện người dùng
Copy## Giao diện người dùng

### Dashboard License
- **Tổng quan**: Hiển thị số liệu tổng hợp về các license (tổng số, đang hoạt động, sắp hết hạn, đã hết hạn)
- **Danh sách**: Bảng hiển thị tất cả license với thông tin cơ bản và khả năng tìm kiếm, lọc theo trạng thái
- **Thêm mới**: Form tạo license mới với các trường thông tin cần thiết (tên khách hàng, thời hạn...)

### Trang chi tiết License
- **Thông tin cơ bản**: Hiển thị đầy đủ thông tin license (license key, trạng thái, ngày hết hạn...)
- **Tabs**: Giao diện tab để xem các loại thông tin khác nhau:
  - **Details**: Thông tin chi tiết về license
  - **Activity**: Lịch sử các hoạt động của license
  - **Clone Alerts**: Cảnh báo về các trường hợp clone license
- **Quản lý trạng thái**: Các nút chức năng để:
  - Gia hạn license (Extend License)
  - Vô hiệu hóa license (Revoke License)
  - Kích hoạt license (Activate License)

### Modals
- **Extend License**: Form để gia hạn thêm ngày cho license
- **Revoke License**: Form để vô hiệu hóa license với lý do
- **Activate License**: Form để kích hoạt lại license đã bị vô hiệu hóa
Cập nhật 2: Thêm phần về API Endpoints đã triển khai
Copy## API Endpoints

### License Management
- `GET /api/licenses`: Lấy danh sách licenses với phân trang và lọc
- `POST /api/licenses`: Tạo license mới
- `GET /api/licenses/:id`: Lấy chi tiết license
- `PUT /api/licenses/:id`: Cập nhật license (gia hạn, kích hoạt, vô hiệu hóa)
- `GET /api/licenses/:id/activities`: Lấy lịch sử hoạt động của license

### License Operations
Các API routes này được triển khai để hỗ trợ các hoạt động với license:
- Gia hạn: Cập nhật ngày hết hạn của license
- Kích hoạt/Vô hiệu hóa: Đổi trạng thái is_active của license
- Ghi log: Lưu lại tất cả các hoạt động trong bảng license_activities
Cập nhật 3: Thêm phần về tiến độ và các tính năng đã hoàn thành
Copy## Tiến độ triển khai

### Đã hoàn thành
- ✅ Thiết kế và tạo schema database
- ✅ Triển khai API cơ bản cho quản lý license
- ✅ Dashboard hiển thị danh sách licenses
- ✅ Trang chi tiết license
- ✅ Chức năng tạo license mới
- ✅ Chức năng gia hạn license
- ✅ Chức năng vô hiệu hóa license
- ✅ Chức năng kích hoạt license

### Đang triển khai
- ⏳ Trang quản lý Clone Alerts
- ⏳ Báo cáo thống kê về việc sử dụng license

### Kế hoạch tiếp theo
- 📅 Tích hợp hệ thống thông báo tự động cho license sắp hết hạn
- 📅 Xây dựng API client SDK cho ứng dụng on-premise
- 📅 Phát triển trang quản lý khách hàng (Customer Management)
- 📅 Tích hợp với hệ thống thanh toán
Cập nhật 4: Thêm phần về kiểm thử và hướng dẫn sử dụng
Copy## Hướng dẫn sử dụng

### Tạo License mới
1. Truy cập trang Licenses
2. Click nút "New License"
3. Điền thông tin khách hàng, chọn thời hạn
4. Click "Submit" để tạo license

### Gia hạn License
1. Truy cập trang chi tiết license
2. Click nút "Extend License"
3. Chọn số ngày muốn gia hạn
4. Click "Extend License" để xác nhận

### Vô hiệu hóa License
1. Truy cập trang chi tiết license
2. Click nút "Revoke License"
3. Điền lý do vô hiệu hóa
4. Click "Revoke License" để xác nhận

### Kích hoạt lại License
1. Truy cập trang chi tiết license của license đã bị vô hiệu hóa
2. Click nút "Activate License"
3. Xác nhận kích hoạt

## Kiểm thử
Các kịch bản kiểm thử đã được triển khai:

1. **Tạo license mới**: Kiểm tra việc tạo license và hiển thị trên dashboard
2. **Gia hạn license**: Kiểm tra việc gia hạn và cập nhật ngày hết hạn
3. **Vô hiệu hóa license**: Kiểm tra việc vô hiệu hóa và ghi lại lý do
4. **Kích hoạt license**: Kiểm tra việc kích hoạt lại license đã bị vô hiệu hóa
Cập nhật 5: Thêm phần về bảo mật và những điều cần lưu ý
Copy## Lưu ý bảo mật và triển khai

### Bảo mật
- Đảm bảo API `/api/license-check` và `/api/license-validate` chỉ xử lý các yêu cầu hợp lệ
- Kiểm tra kỹ lưỡng tính xác thực của hardware fingerprint
- Sử dụng HTTPS cho mọi giao tiếp với license server
- Xem xét việc mã hóa các thông tin nhạy cảm trong database

### Sao lưu
- Đảm bảo dữ liệu license được sao lưu định kỳ
- Có quy trình khôi phục license trong trường hợp khẩn cấp

### Triển khai
- Cần kiểm tra tính tương thích của license agent với các môi trường khác nhau
- Xem xét khả năng offline operation cho license agent trong trường hợp không có kết nối internet
Cập nhật 6: Thêm phần giao diện người dùng đã cải thiện
Copy## Cải tiến giao diện người dùng

### Tabs trên trang chi tiết License
- Thiết kế tab với animation và hiệu ứng chuyển đổi mượt mà
- Hỗ trợ hiển thị badge thông báo cho Clone Alerts
- Giao diện responsive, tương thích với nhiều kích thước màn hình

### Modals
- Thiết kế modal hiện đại với khả năng responsive
- Cung cấp phản hồi trực quan cho người dùng (thông báo lỗi, thành công)
- Xác nhận các hành động quan trọng (vô hiệu hóa, gia hạn)

### Các nút chức năng
- Thiết kế nút với màu sắc phù hợp chức năng (Danger cho Revoke, Primary cho Extend và Activate)
- Cung cấp phản hồi trạng thái khi thực hiện các thao tác (loading, success)



## Kết luận

Hệ thống License Management đã được thiết kế để cung cấp giải pháp đơn giản nhưng hiệu quả cho việc quản lý license của các triển khai on-premise. Bằng cách tập trung vào việc giới hạn thời gian sử dụng và phát hiện clone, hệ thống đáp ứng được yêu cầu của bạn mà không quá phức tạp.