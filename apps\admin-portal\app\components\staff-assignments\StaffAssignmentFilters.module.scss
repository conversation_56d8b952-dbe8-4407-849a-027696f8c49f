.container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filterLabel {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.filterSelect {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: white;
  font-size: 0.875rem;
  color: #374151;
   &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5);
  }
}

@media (max-width: 640px) {
  .filterGroup {
    width: 100%;
  }
}