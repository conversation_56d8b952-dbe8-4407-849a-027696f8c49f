'use client';

import React, { useState, useEffect } from 'react';
import { DashboardLayout, Button, Table, Card, Badge } from '@loaloa/ui';
import { BuildingIcon, HomeIcon, UsersIcon, LicenseIcon, SettingsIcon } from '@loaloa/ui/src/components/Icons/icons';
import Link from 'next/link';
import styles from './tenants.module.scss';

// Mock data - sẽ thay thế bằng dữ liệu thật sau
const TENANTS_DATA = [
  {
    id: '1',
    name: 'Hotel California',
    domain: 'hotel-california.loaloa.app',
    contact_email: '<EMAIL>',
    contact_phone: '+****************',
    plan: 'premium',
    status: 'active',
    users_count: 25,
    created_at: '2025-04-01T10:30:00Z',
  },
  {
    id: '2',
    name: 'Grand Resort & Spa',
    domain: 'grand-resort.loaloa.app',
    contact_email: '<EMAIL>',
    contact_phone: '+****************',
    plan: 'enterprise',
    status: 'active',
    users_count: 42,
    created_at: '2025-03-15T14:45:00Z',
  },
  {
    id: '3',
    name: 'Seaside Inn',
    domain: 'seaside-inn.loaloa.app', 
    contact_email: '<EMAIL>',
    contact_phone: '+****************',
    plan: 'basic',
    status: 'pending',
    users_count: 8,
    created_at: '2025-03-25T09:15:00Z',
  },
  {
    id: '4',
    name: 'Mountain Lodge',
    domain: 'mountain-lodge.loaloa.app',
    contact_email: '<EMAIL>',
    contact_phone: '+****************',
    plan: 'basic',
    status: 'inactive',
    users_count: 5,
    created_at: '2025-02-20T16:30:00Z',
  },
  {
    id: '5',
    name: 'City Center Hotel',
    domain: 'city-center.loaloa.app',
    contact_email: '<EMAIL>',
    contact_phone: '+****************',
    plan: 'premium',
    status: 'active',
    users_count: 18,
    created_at: '2025-04-10T08:45:00Z',
  },
];

export default function Tenants() {
  const [tenants, setTenants] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);

  useEffect(() => {
    // Giả lập gọi API
    setTimeout(() => {
      setTenants(TENANTS_DATA);
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleAddTenant = () => {
    setShowAddModal(true);
  };

  const handleDeleteTenant = (id) => {
    // Hiển thị dialog xác nhận
    if (confirm('Are you sure you want to delete this tenant?')) {
      // Giả lập xóa tenant
      setTenants(tenants.filter(tenant => tenant.id !== id));
    }
  };

  const sidebarItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      href: '/',
      icon: <HomeIcon />,
    },
    {
      id: 'tenants',
      label: 'Tenants',
      href: '/tenants',
      icon: <BuildingIcon />,
      active: true,
    },
    {
      id: 'users',
      label: 'Users',
      href: '/users',
      icon: <UsersIcon />,
    },
    {
      id: 'licenses',
      label: 'Licenses',
      href: '/licenses',
      icon: <LicenseIcon />,
    },
    {
      id: 'settings',
      label: 'Settings',
      href: '/settings',
      icon: <SettingsIcon />,
    },
  ];
  
  const columns = [
    {
      header: 'Tenant',
      accessor: (tenant) => (
        <div className={styles.tenantCell}>
          <Link href={`/tenants/${tenant.id}`} className={styles.tenantName}>
            {tenant.name}
          </Link>
          <span className={styles.tenantDomain}>{tenant.domain}</span>
        </div>
      ),
    },
    {
      header: 'Contact',
      accessor: (tenant) => (
        <div className={styles.contactCell}>
          <div>{tenant.contact_email}</div>
          <div className={styles.phoneNumber}>{tenant.contact_phone}</div>
        </div>
      ),
    },
    {
      header: 'Plan',
      accessor: (tenant) => (
        <span className={`${styles.planBadge} ${styles[tenant.plan]}`}>
          {tenant.plan}
        </span>
      ),
      width: '10%',
    },
    {
      header: 'Status',
      accessor: (tenant) => {
        const statusClass = `status-badge status-${tenant.status}`;
        return <span className={statusClass}>{tenant.status}</span>;
      },
      width: '10%',
    },
    {
      header: 'Users',
      accessor: (tenant) => tenant.users_count,
      width: '8%',
    },
    {
      header: 'Created',
      accessor: (tenant) => new Date(tenant.created_at).toLocaleDateString(),
      width: '12%',
    },
    {
      header: 'Actions',
      accessor: (tenant) => (
        <div className={styles.actions}>
          <Button 
            size="small" 
            variant="outline"
			label="Edit"
            onClick={() => window.location.href = `/tenants/${tenant.id}`}
          >
            Edit
          </Button>
          <Button 
            size="small" 
            variant="danger"
			label="Delete"
            onClick={() => handleDeleteTenant(tenant.id)}
          >
            Delete
          </Button>
        </div>
      ),
      width: '15%',
    },
  ];

  return (
    <DashboardLayout
      sidebarItems={sidebarItems}
      title="Tenant Management"
      username="Admin User"
      breadcrumbs={[
        { label: 'Dashboard', href: '/' },
        { label: 'Tenants' }
      ]}
    >
      <div className={styles.tenantsContainer}>
        <div className={styles.pageHeader}>
          <div className={styles.titleSection}>
            <h1 className={styles.pageTitle}>Tenants</h1>
            <p className={styles.pageDescription}>
              Manage all tenant organizations in the LoaLoa platform
            </p>
          </div>
          <div className={styles.actions}>
            <Button onClick={handleAddTenant} variant="primary" label="Add New Tenant">
              Add New Tenant
            </Button>
          </div>
        </div>

        <Card className={styles.tenantTableCard}>
          <Table
            columns={columns}
            data={tenants}
            isLoading={isLoading}
            pagination={{
              currentPage: 1,
              totalPages: 1,
              onPageChange: () => {}
            }}
          />
        </Card>

        {/* Thêm modal thêm tenant ở đây */}
      </div>
    </DashboardLayout>
  );
}