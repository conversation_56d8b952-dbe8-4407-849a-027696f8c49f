.container {
  padding: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.backLink {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #666;
  font-size: 0.875rem;

  &:hover {
    color: #1976d2;
  }

  svg {
    stroke: currentColor;
  }
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.editButton, .deleteButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.editButton {
  background-color: #e3f2fd;
  color: #1976d2;
  text-decoration: none;
  
  &:hover {
    background-color: #bbdefb;
  }

  svg {
    stroke: currentColor;
  }
}

.deleteButton {
  background-color: #ffebee;
  color: #d32f2f;
  border: none;
  
  &:hover {
    background-color: #ffcdd2;
  }

  svg {
    stroke: currentColor;
  }
}

.ruleCard {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ruleHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fafafa;
}

.ruleName {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.statusBadge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  
  &.active {
    background-color: #e6f7e6;
    color: #2e7d32;
  }
  
  &.inactive {
    background-color: #f5f5f5;
    color: #757575;
  }
}

.detailsContainer {
  padding: 1.5rem;
}

.section {
  margin-bottom: 2rem;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.sectionTitle {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detailLabel {
  font-size: 0.75rem;
  font-weight: 500;
  color: #666;
}

.detailValue {
  font-size: 0.875rem;
}

.priorityBadge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background-color: #f5f5f5;
  color: #333;
  font-weight: 600;
  font-size: 0.75rem;
}

.departmentBadge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background-color: #e3f2fd;
  color: #0277bd;
  font-size: 0.75rem;
  font-weight: 500;
}

.userInfo {
  display: flex;
  flex-direction: column;
}

.userName {
  font-weight: 500;
}

.userEmail {
  font-size: 0.75rem;
  color: #666;
}

.receptionPointInfo {
  display: flex;
  flex-direction: column;
}

.receptionPointName {
  font-weight: 500;
}

.receptionPointCode {
  font-size: 0.75rem;
  color: #666;
}

.notFound {
  color: #f44336;
  font-size: 0.75rem;
  font-style: italic;
}

.jsonDisplay {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  overflow: auto;
  font-family: monospace;
  font-size: 0.875rem;
  margin: 0;
  max-height: 300px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 1rem;
}

.error {
  padding: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .detailsGrid {
    grid-template-columns: 1fr;
  }
}
