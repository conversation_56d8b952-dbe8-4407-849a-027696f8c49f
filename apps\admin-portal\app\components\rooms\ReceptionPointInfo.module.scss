.receptionPointInfo {
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.name {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.statusBadge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;

  &.active {
    background-color: #e6f7e6;
    color: #2e7d32;
  }

  &.inactive {
    background-color: #f5f5f5;
    color: #757575;
  }
}

.details {
  margin-bottom: 1rem;
}

.detailItem {
  display: flex;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.label {
  font-weight: 500;
  color: #666;
  width: 60px;
}

.description {
  font-size: 0.875rem;
  color: #555;
  margin-top: 0.5rem;
  line-height: 1.4;
}

.actions {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.viewLink, .editLink {
  font-size: 0.875rem;
  text-decoration: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: all 0.2s;
}

.viewLink {
  color: #1976d2;
  background-color: #e3f2fd;

  &:hover {
    background-color: #bbdefb;
  }
}

.editLink {
  color: #ed6c02;
  background-color: #fff4e5;

  &:hover {
    background-color: #ffe0b2;
  }
}

.notAssigned {
  background-color: #f5f5f5;
  border: 1px dashed #ccc;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  color: #666;

  p {
    margin: 0 0 0.5rem 0;
  }
}

.actionLinks {
  a {
    color: #1976d2;
    font-size: 0.875rem;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.loading {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.875rem;
  padding: 0.5rem;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #d32f2f;
  font-size: 0.875rem;
  padding: 0.5rem;
  
  svg {
    stroke: #d32f2f;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}