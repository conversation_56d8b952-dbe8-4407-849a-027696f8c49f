'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '../../../dashboard-layout';
import QrCodeForm from '../../../components/qr-codes/QrCodeForm';
import { Alert } from '@ui';
import styles from './edit-qrcode.module.scss';

export default function EditQrCodePage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [initialData, setInitialData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch QR code details
  useEffect(() => {
    const fetchQrCode = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/qr-codes/${params.id}`);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch QR code details');
        }
        const result = await response.json();
        
        // Convert data from API to form format
        const qrData = result.data;
        
        // Handle target_type and target_id mapping
        let target_type = 'none';
        let target_id = '';
        
        // Map room to target if available
        if (qrData.room_id) {
          target_type = 'room';
          target_id = qrData.room_id;
        } else if (qrData.target_type && qrData.target_id) {
          target_type = qrData.target_type;
          target_id = qrData.target_id;
        }
        
        setInitialData({
          ...qrData,
          target_type,
          target_id
        });
      } catch (err) {
        console.error('Error fetching QR code:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while loading QR code data.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchQrCode();
  }, [params.id]);

  // Handle form submission
  const handleSubmit = async (formData: any) => {
    try {
      setIsSubmitting(true);
      setError(null);
      
      // Send update request
      const response = await fetch(`/api/qr-codes/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update QR code');
      }
      
      // Navigate back to QR code details
      router.push(`/qr-codes/${params.id}`);
    } catch (err) {
      console.error('Error updating QR code:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while updating the QR code.');
      setIsSubmitting(false);
    }
  };

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Link href={`/qr-codes/${params.id}`} className={styles.backButton}>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path 
                d="M10.6667 2.66667L5.33333 8.00001L10.6667 13.3333" 
                stroke="currentColor" 
                strokeWidth="1.5" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
            Back to QR Code Details
          </Link>
          <h1 className={styles.title}>Edit QR Code</h1>
        </div>
        
        {error && (
          <Alert 
            variant="error" 
            title="Error" 
            closable 
            onClose={() => setError(null)}
          >
            {error}
          </Alert>
        )}
        
        {loading ? (
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Loading QR code data...</p>
          </div>
        ) : initialData ? (
          <QrCodeForm 
            initialData={initialData} 
            onSubmit={handleSubmit} 
            isSubmitting={isSubmitting} 
            isEditing={true}
          />
        ) : (
          <div className={styles.notFound}>
            <h2>QR Code Not Found</h2>
            <p>The requested QR code could not be found or has been deleted.</p>
            <Link href="/qr-codes" className={styles.linkButton}>
              Return to QR Codes
            </Link>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
