.container {
  padding: 24px;
}

.header {
  margin-bottom: 24px;
  
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 4px 0;
  }
  
  .description {
    font-size: 14px;
    color: #6B7280;
    margin: 0;
  }
}

.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.searchAndFilters {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  flex: 1;
  max-width: 800px;
}

.search {
  width: 300px;
}

.filters {
  display: flex;
  gap: 16px;
}

.filterItem {
  display: flex;
  align-items: center;
  gap: 8px;
  
  label {
    font-size: 14px;
    color: #555;
  }
  
  select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: #fff;
  }
}

.tableWrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid #eee;
  }
  
  th {
    font-weight: 600;
    color: #333;
    background-color: #f9f9f9;
  }
  
  tr:last-child td {
    border-bottom: none;
  }
  
  tr:hover td {
    background-color: #f5f8ff;
  }
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-weight: 600;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.role {
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  
  &.admin {
    color: #9333ea;
    background-color: #f3e8ff;
  }
  
  &.manager {
    color: #0369a1;
    background-color: #e0f2fe;
  }
  
  &.user {
    color: #15803d;
    background-color: #dcfce7;
  }
}

.actions {
  display: flex;
  gap: 8px;
  
  a, button {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 13px;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    border: none;
    
    svg {
      width: 14px;
      height: 14px;
    }
  }
  
  .viewButton {
    color: #555;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    
    &:hover {
      background-color: #f0f0f0;
    }
  }
  
  .editButton {
    color: #0369a1;
    background-color: #e0f2fe;
    border: 1px solid #bae6fd;
    
    &:hover {
      background-color: #bae6fd;
    }
  }
  
  .deleteButton {
    color: #dc2626;
    background-color: #fee2e2;
    border: 1px solid #fecaca;
    
    &:hover {
      background-color: #fecaca;
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.loading, .noData {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  
  svg {
    color: #d1d5db;
    margin-bottom: 16px;
  }
  
  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 8px;
  }
  
  p {
    font-size: 14px;
    color: #6b7280;
    margin: 0 0 24px;
    max-width: 400px;
    text-align: center;
  }
}

.error {
  background-color: #fee2e2;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  button {
    background-color: #dc2626;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      background-color: #b91c1c;
    }
  }
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #4b5563;
  text-decoration: none;
  padding: 6px 12px;
  border-radius: 4px;
  transition: all 0.2s;
  margin-bottom: 12px;
  
  &:hover {
    background-color: #f3f4f6;
  }
}

@media (max-width: 768px) {
  .controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .searchAndFilters {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .search {
    width: 100%;
  }
  
  .filters {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .table {
    font-size: 14px;
    
    th:nth-child(4), 
    th:nth-child(5), 
    td:nth-child(4), 
    td:nth-child(5) {
      display: none;
    }
  }
  
  .actions {
    flex-direction: column;
    gap: 4px;
  }
}