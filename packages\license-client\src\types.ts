/**
 * License status enum
 */
export enum LicenseStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  REVOKED = 'REVOKED',
  NOT_ACTIVATED = 'NOT_ACTIVATED',
  EXPIRING_SOON = 'EXPIRING_SOON'
}

/**
 * Activity type enum for license logging
 */
export enum LicenseActivityType {
  ACTIVATION = 'ACTIVATION',
  CHECK_IN = 'CHECK_IN',
  WARNING = 'WARNING',
  VIOLATION = 'VIOLATION',
  REVOCATION = 'REVOCATION'
}

/**
 * Clone status enum
 */
export enum CloneStatus {
  DETECTED = 'DETECTED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  CONFIRMED = 'CONFIRMED',
  FALSE_ALARM = 'FALSE_ALARM',
  REVOKED = 'REVOKED'
}

/**
 * License interface
 */
export interface License {
  id: string;
  license_key: string;
  customer_name: string;
  issue_date: string;
  expiry_date: string;
  is_active: boolean;
  hardware_fingerprint?: string;
  activation_date?: string;
  last_check_in?: string;
  check_in_count: number;
  days_remaining?: number;
  status: LicenseStatus;
  metadata?: Record<string, any>;
  revocation_reason?: string;
  unreviewed_clone_alerts?: number;
  clone_alerts?: number;
}

/**
 * License activation request
 */
export interface LicenseActivationRequest {
  license_key: string;
  customer_name: string;
  customer_email: string;
  hardware_fingerprint: string;
}

/**
 * License activation response
 */
export interface LicenseActivationResponse {
  success: boolean;
  message: string;
  license?: License;
  activation_id?: string;
  error?: string;
  requires_email_verification?: boolean;
}

/**
 * License check-in request
 */
export interface LicenseCheckInRequest {
  license_key: string;
  hardware_fingerprint: string;
  ip_address?: string;
  system_info?: Record<string, any>;
}

/**
 * License check-in response
 */
export interface LicenseCheckInResponse {
  success: boolean;
  message: string;
  is_valid: boolean;
  days_remaining?: number;
  error?: string;
}

/**
 * License validation request
 */
export interface LicenseValidationRequest {
  license_key: string;
  hardware_fingerprint: string;
}

/**
 * License validation response
 */
export interface LicenseValidationResponse {
  success: boolean;
  is_valid: boolean;
  message: string;
  license?: License;
  error?: string;
}
