import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Tạo Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// POST: Check-out guest
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const guestId = params.id;
    
    // Tạo Supabase client
    const supabase = createSupabaseClient();
    
    // Thực hiện hàm check_out_guest
    const { data: fnResult, error: fnError } = await supabase
      .rpc('check_out_guest', {
        p_guest_id: guestId
      });
    
    if (fnError) {
      console.error('Error calling check_out_guest function:', fnError);
      // Nếu function gặp lỗi, thử cách thủ công
      
      // Lấy thông tin guest hiện tại
      const { data: guest, error: fetchError } = await supabase
        .from('tenant_guests')
        .select('tenant_id, room_number')
        .eq('id', guestId)
        .single();
      
      if (fetchError) {
        return NextResponse.json(
          { error: 'Guest not found' },
          { status: 404 }
        );
      }
      
      // Cập nhật guest để check-out
      const { data, error } = await supabase
        .from('tenant_guests')
        .update({
          is_active: false,
          check_out: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', guestId)
        .select()
        .single();
      
      if (error) {
        console.error('Error checking out guest:', error);
        throw error;
      }
      
      // Kiểm tra nếu còn guest nào trong phòng
      if (guest.room_number) {
        const { count } = await supabase
          .from('tenant_guests')
          .select('id', { count: 'exact' })
          .eq('tenant_id', guest.tenant_id)
          .eq('room_number', guest.room_number)
          .eq('is_active', true);
        
        // Nếu không còn guest nào trong phòng, cập nhật trạng thái phòng
        if (count === 0) {
          await supabase
            .from('tenant_rooms')
            .update({
              status: 'available',
              last_checkout: new Date().toISOString()
            })
            .eq('tenant_id', guest.tenant_id)
            .eq('room_number', guest.room_number);
        }
      }
      
      return NextResponse.json({
        data,
        message: 'Guest checked out successfully'
      });
    }
    
    // Nếu function thành công
    if (fnResult) {
      // Lấy thông tin guest đã check-out
      const { data: guest } = await supabase
        .from('tenant_guests')
        .select('*')
        .eq('id', guestId)
        .single();
      
      return NextResponse.json({
        data: guest,
        message: 'Guest checked out successfully'
      });
    }
    
    // Nếu không có kết quả nhưng cũng không có lỗi
    return NextResponse.json(
      { error: 'Failed to check out guest' },
      { status: 500 }
    );
    
  } catch (error) {
    console.error('Error in check-out guest:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}