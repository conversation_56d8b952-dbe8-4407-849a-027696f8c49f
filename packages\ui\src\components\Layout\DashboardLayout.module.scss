@import '../../styles/variables';

.dashboardContainer {
  display: flex;
  min-height: 100vh;
  background-color: var(--color-background, #FFFFFF);
}

.sidebar {
  width: 250px;
  background-color: var(--color-sidebar-bg, #16262E); // Gunmetal from dark.800
  color: white;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 30;
  
  &.collapsed {
    width: 70px;
  }
}

.logoContainer {
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo, .logoSmall {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.logoText {
  font-size: 1.5rem;
  margin: 0;
  font-weight: 600;
  color: white;
  white-space: nowrap;
  overflow: hidden;
}

.collapseBtn {
  background: transparent;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.navigation {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
}

.navItem {
  margin-bottom: 0.25rem;
}

.navLink {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: background-color 0.2s, color 0.2s;
  border-radius: 4px;
  margin: 0 0.5rem;
  
  &:hover, &.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  &.active {
    border-left: 3px solid var(--color-primary, #FF4D00);
  }
  
  .icon {
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
  }
}

.subNav {
  padding-left: 2.5rem !important;
  margin-top: 0.25rem;
  margin-bottom: 0.5rem;
  
  li {
    margin: 0.25rem 0;
  }
  
  a {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s;
    
    &:hover, &.active {
      color: white;
    }
    
    .subIcon {
      margin-right: 8px;
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.userSection {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem;
  margin-top: auto;
}

.user {
  display: flex;
  align-items: center;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
}

.username {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.9rem;
}

.logoutBtn {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 0;
  font-size: 0.8rem;
  margin-left: 0.5rem;
  transition: color 0.2s;
  
  &:hover {
    color: white;
    text-decoration: underline;
  }
}

.content {
  flex: 1;
  margin-left: 250px;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
  
  .sidebar.collapsed + & {
    margin-left: 70px;
  }
}

.header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
  background-color: white;
  border-bottom: 1px solid var(--color-border, #D4D4D4);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.headerLeft {
  display: flex;
  align-items: center;
}

.breadcrumbs {
  font-size: 0.85rem;
  color: var(--color-text-secondary, #7D8491);
  margin-right: 1rem;
  display: flex;
  align-items: center;
}

.breadcrumbSeparator {
  margin: 0 0.5rem;
}

.breadcrumbLink {
  color: var(--color-text-secondary, #7D8491);
  text-decoration: none;
  
  &:hover {
    color: var(--color-primary, #FF4D00);
    text-decoration: underline;
  }
}

.breadcrumbCurrent {
  color: var(--color-text, #010103);
  font-weight: 500;
}

.pageTitle {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.pageContent {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background-color: var(--color-background-tertiary, #F8F9FA);
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 250px;
    position: fixed;
    z-index: 40;
    
    &.collapsed {
      transform: translateX(0);
      width: 70px;
    }
  }
  
  .content {
    margin-left: 0;
  }
  
  .sidebar.collapsed + .content {
    margin-left: 70px;
  }
}
