import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { createAdminClient } from '../../../lib/supabase/admin';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// Tạo Supabase client với service role key
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// GET: Lấy danh sách người dùng với filter và phân trang
export async function GET(request: Request) {
  try {
    // Trích xuất query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const role = searchParams.get('role') || '';
    const is_active = searchParams.get('status') === 'active' ? true : 
                      searchParams.get('status') === 'inactive' ? false : undefined;
    
    // Lấy tenant_id từ file config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }
    
    // Tính toán offset cho phân trang
    const offset = (page - 1) * limit;
    
    // Tạo Supabase admin client
    const supabase = createAdminClient(cookies());
    
    // Query để lấy người dùng thuộc tenant này
    const query = supabase
      .from('tenant_users')
      .select(`
        id,
        role,
        is_active,
        joined_at,
        last_login_at,
        expiry_date,
        tenant_users_details (
          email,
          display_name,
          avatar_url,
          phone
        )
      `, { count: 'exact' })
      .eq('tenant_id', tenant_id);
    
    // Áp dụng các filter
    if (search) {
      query.or(`tenant_users_details.email.ilike.%${search}%,tenant_users_details.display_name.ilike.%${search}%,tenant_users_details.phone.ilike.%${search}%`);
    }
    
    // Filter theo role
    if (role) {
      query.eq('role', role);
    }
    
    // Filter theo trạng thái active
    if (is_active !== undefined) {
      query.eq('is_active', is_active);
    }
    
    // Phân trang và sắp xếp
    query.order('joined_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    // Thực hiện truy vấn
    const { data, error, count } = await query;
    
    if (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
    
    // Định dạng lại dữ liệu để dễ sử dụng
    const formattedData = data?.map(item => ({
      id: item.id,
      email: item.tenant_users_details?.email,
      display_name: item.tenant_users_details?.display_name,
      avatar_url: item.tenant_users_details?.avatar_url,
      phone: item.tenant_users_details?.phone,
      role: item.role,
      is_active: item.is_active,
      joined_at: item.joined_at,
      last_login_at: item.last_login_at,
      expiry_date: item.expiry_date
    }));
    
    // Trả về kết quả
    return NextResponse.json({
      data: formattedData,
      meta: {
        total: count || 0,
        page,
        limit,
        pageCount: Math.ceil((count || 0) / limit)
      }
    });
    
  } catch (error) {
    console.error('Error in GET users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST: Tạo người dùng mới
export async function POST(request: Request) {
  try {
    // Lấy dữ liệu từ request body
    const userData = await request.json();
    
    // Lấy tenant_id từ file config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ 
        error: 'Tenant ID not found. Please activate your license.' 
      }, { status: 400 });
    }
    
    // Tạo Supabase client để gọi các API với quyền admin
    const supaClient = createSupabaseClient();
    
    // Tìm hoặc tạo user trong hệ thống auth
    let user_id;
    
    if (userData.email) {
      // Kiểm tra xem email đã tồn tại trong bảng users chưa
      const { data: existingUser, error: findError } = await supaClient
        .from('auth.users')  // Thay đổi tên này nếu bảng users của bạn có tên khác
        .select('id')
        .eq('email', userData.email)
        .maybeSingle();
      
      if (!findError && existingUser) {
        // Nếu user đã tồn tại, sử dụng ID của họ
        user_id = existingUser.id;
      } else {
        // Nếu user chưa tồn tại, tạo mới trong Auth
        const { data: newUser, error: createError } = await supaClient.auth.admin.createUser({
          email: userData.email,
          password: userData.password || Math.random().toString(36).slice(-12), // Password ngẫu nhiên nếu không có
          email_confirm: true
        });
        
        if (createError) {
          console.error('Error creating auth user:', createError);
          return NextResponse.json({ error: createError.message }, { status: 500 });
        }
        
        user_id = newUser.user.id;
      }
    } else {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }
    
    // Kiểm tra xem user đã thuộc tenant này chưa
    const { data: existingTenantUser, error: checkError } = await supaClient
      .from('tenant_users')
      .select('id')
      .eq('tenant_id', tenant_id)
      .eq('user_id', user_id)
      .maybeSingle();
    
    if (checkError && checkError.code !== 'PGRST116') {
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }
    
    if (existingTenantUser) {
      return NextResponse.json({ 
        error: 'User already exists in this tenant' 
      }, { status: 400 });
    }
    
    // Thêm người dùng vào tenant với user_id
    const { data: tenantUser, error: tenantUserError } = await supaClient
      .from('tenant_users')
      .insert({
        tenant_id: tenant_id,
        user_id: user_id, // Đảm bảo user_id được cung cấp
        role: userData.role || 'user',
        is_active: userData.is_active !== undefined ? userData.is_active : true,
        joined_at: new Date().toISOString(),
        expiry_date: userData.expiry_date || null,
        permissions: userData.permissions || {}
      })
      .select()
      .single();
    
    if (tenantUserError) {
      console.error('Error adding user to tenant:', tenantUserError);
      return NextResponse.json({ error: tenantUserError.message }, { status: 500 });
    }
    
    // Thêm thông tin chi tiết người dùng
    const { data: userDetails, error: userDetailsError } = await supaClient
  .from('tenant_users_details')
  .insert({
    tenant_user_id: tenantUser.id,
    email: userData.email,
    display_name: userData.display_name || userData.email.split('@')[0],
    avatar_url: userData.avatar_url,
    phone: userData.phone,
    department: userData.department, // Thêm trường department
    title: userData.title, // Thêm trường title
    preferred_language: userData.preferred_language || 'en' // Thêm trường preferred_language
  })
  .select()
  .single();
    
    if (userDetailsError) {
      // Nếu lỗi, xóa tenant_user đã tạo
      await supaClient.from('tenant_users').delete().eq('id', tenantUser.id);
      console.error('Error adding user details:', userDetailsError);
      return NextResponse.json({ error: userDetailsError.message }, { status: 500 });
    }
    
    // Trả về kết quả
    return NextResponse.json({
      data: {
        ...tenantUser,
        ...userDetails
      },
      message: 'User added successfully'
    }, { status: 201 });
    
  } catch (error) {
    console.error('Error in POST users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}