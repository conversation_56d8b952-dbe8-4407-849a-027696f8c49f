import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Modal } from './index';
import { Button } from '../Button';
import { Input } from '../Form/Input';

const meta = {
  title: 'UI/Modal',
  component: Modal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    isOpen: {
      control: 'boolean',
      description: 'Control whether the modal is open',
    },
    title: {
      control: 'text',
      description: 'Modal title',
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large', 'fullscreen'],
      description: 'Modal size',
    },
    variant: {
      control: 'select',
      options: ['light', 'dark', 'studio'],
      description: 'Theme variant',
    },
    closeOnOverlayClick: {
      control: 'boolean',
      description: 'Whether to close on overlay click',
    },
    closeOnEsc: {
      control: 'boolean',
      description: 'Whether to close on escape key',
    },
    centered: {
      control: 'boolean',
      description: 'Center the modal vertically',
    },
    onClose: {
      action: 'closed',
      description: 'Handler for closing the modal',
    },
  },
} satisfies Meta<typeof Modal>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Modal
export const Basic: Story = {
  render: function BasicModal() {
    const [isOpen, setIsOpen] = useState(false);
    return (
      <>
        <Button 
          variant="primary" 
          label="Open Modal" 
          onClick={() => setIsOpen(true)} 
        />
        <Modal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Basic Modal"
          footer={
            <Button 
              variant="primary" 
              label="Close" 
              onClick={() => setIsOpen(false)} 
            />
          }
        >
          <p>This is a basic modal with a title and footer.</p>
        </Modal>
      </>
    );
  }
};

// Different sizes
export const Sizes: Story = {
  render: function SizesModal() {
    const [open, setOpen] = useState<Record<string, boolean>>({
      small: false,
      medium: false,
      large: false,
      fullscreen: false,
    });

    const toggleModal = (size: string) => {
      setOpen({ ...open, [size]: !open[size] });
    };

    return (
      <div style={{ display: 'flex', gap: '12px' }}>
        <Button 
          variant="primary" 
          label="Small" 
          onClick={() => toggleModal('small')}
        />
        <Button 
          variant="primary" 
          label="Medium" 
          onClick={() => toggleModal('medium')}
        />
        <Button 
          variant="primary" 
          label="Large" 
          onClick={() => toggleModal('large')}
        />
        <Button 
          variant="primary" 
          label="Fullscreen" 
          onClick={() => toggleModal('fullscreen')}
        />

        <Modal
          isOpen={open.small}
          onClose={() => toggleModal('small')}
          title="Small Modal"
          size="small"
          footer={
            <Button 
              variant="primary" 
              label="Close" 
              onClick={() => toggleModal('small')}
            />
          }
        >
          <p>This is a small modal.</p>
        </Modal>

        <Modal
          isOpen={open.medium}
          onClose={() => toggleModal('medium')}
          title="Medium Modal"
          size="medium"
          footer={
            <Button 
              variant="primary" 
              label="Close" 
              onClick={() => toggleModal('medium')} 
            />
          }
        >
          <p>This is a medium modal (default size).</p>
        </Modal>

        <Modal
          isOpen={open.large}
          onClose={() => toggleModal('large')}
          title="Large Modal"
          size="large"
          footer={
            <Button 
              variant="primary" 
              label="Close" 
              onClick={() => toggleModal('large')} 
            />
          }
        >
          <p>This is a large modal with more content space.</p>
        </Modal>

        <Modal
          isOpen={open.fullscreen}
          onClose={() => toggleModal('fullscreen')}
          title="Fullscreen Modal"
          size="fullscreen"
          footer={
            <Button 
              variant="primary" 
              label="Close" 
              onClick={() => toggleModal('fullscreen')} 
            />
          }
        >
                    <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <p>This is a fullscreen modal that covers the entire viewport.</p>
          </div>
        </Modal>
      </div>
    );
  }
};

// Different variants
export const Variants: Story = {
  render: function VariantsModal() {
    const [open, setOpen] = useState<Record<string, boolean>>({
      light: false,
      dark: false,
      studio: false,
    });

    const toggleModal = (variant: string) => {
      setOpen({ ...open, [variant]: !open[variant] });
    };

    return (
      <div style={{ display: 'flex', gap: '12px' }}>
        <Button 
          variant="primary" 
          label="Light" 
          onClick={() => toggleModal('light')}
        />
        <Button 
          variant="primary" 
          label="Dark" 
          onClick={() => toggleModal('dark')}
        />
        <Button 
          variant="primary" 
          label="Studio" 
          onClick={() => toggleModal('studio')}
        />

        <Modal
          isOpen={open.light}
          onClose={() => toggleModal('light')}
          title="Light Modal"
          variant="light"
          footer={
            <Button 
              variant="primary" 
              label="Close" 
              onClick={() => toggleModal('light')}
            />
          }
        >
          <p>This is a modal with light theme.</p>
        </Modal>

        <Modal
          isOpen={open.dark}
          onClose={() => toggleModal('dark')}
          title="Dark Modal"
          variant="dark"
          footer={
            <Button 
              variant="primary" 
              label="Close" 
              onClick={() => toggleModal('dark')} 
            />
          }
        >
          <p>This is a modal with dark theme.</p>
        </Modal>

        <Modal
          isOpen={open.studio}
          onClose={() => toggleModal('studio')}
          title="Studio Modal"
          variant="studio"
          footer={
            <Button 
              variant="primary" 
              label="Close" 
              onClick={() => toggleModal('studio')} 
            />
          }
        >
          <p>This is a modal with studio theme.</p>
        </Modal>
      </div>
    );
  }
};

// Form Modal
export const FormModal: Story = {
  render: function FormModalExample() {
    const [isOpen, setIsOpen] = useState(false);
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');

    const handleSubmit = () => {
      alert(`Submitted: ${name}, ${email}`);
      setIsOpen(false);
    };

    return (
      <>
        <Button 
          variant="primary" 
          label="Open Form Modal" 
          onClick={() => setIsOpen(true)}
        />

        <Modal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="User Information"
          footer={
            <>
              <Button 
                variant="outline" 
                label="Cancel" 
                onClick={() => setIsOpen(false)} 
              />
              <Button 
                variant="primary" 
                label="Submit" 
                onClick={handleSubmit} 
              />
            </>
          }
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <Input
              label="Name"
              placeholder="Enter your name"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
            <Input
              label="Email"
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
        </Modal>
      </>
    );
  }
};

// Confirmation Modal
export const ConfirmationModal: Story = {
  render: function ConfirmationModalExample() {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <>
        <Button 
          variant="accent" 
          label="Delete Item" 
          onClick={() => setIsOpen(true)}
        />

        <Modal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Confirm Deletion"
          size="small"
          footer={
            <>
              <Button 
                variant="outline" 
                label="Cancel" 
                onClick={() => setIsOpen(false)} 
              />
              <Button 
                variant="primary" 
                label="Delete" 
                onClick={() => {
                  alert('Item deleted!');
                  setIsOpen(false);
                }} 
                style={{ backgroundColor: '#EF4444' }}
              />
            </>
          }
        >
          <p>Are you sure you want to delete this item? This action cannot be undone.</p>
        </Modal>
      </>
    );
  }
};

// Modal without title
export const WithoutTitle: Story = {
  render: function WithoutTitleModal() {
    const [isOpen, setIsOpen] = useState(false);
    
    return (
      <>
        <Button 
          variant="primary" 
          label="Open Modal" 
          onClick={() => setIsOpen(true)}
        />
        
        <Modal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          footer={
            <Button 
              variant="primary" 
              label="Close" 
              onClick={() => setIsOpen(false)} 
            />
          }
        >
          <div style={{ textAlign: 'center', padding: '24px' }}>
            <div style={{ fontSize: '64px', marginBottom: '16px' }}>🎉</div>
            <h3 style={{ margin: '0 0 16px 0' }}>Congratulations!</h3>
            <p>You've completed the task successfully.</p>
          </div>
        </Modal>
      </>
    );
  }
};

// Modal without footer
export const WithoutFooter: Story = {
  render: function WithoutFooterModal() {
    const [isOpen, setIsOpen] = useState(false);
    
    return (
      <>
        <Button 
          variant="primary" 
          label="Open Modal" 
          onClick={() => setIsOpen(true)}
        />
        
        <Modal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Information"
        >
          <div>
            <p>This is a modal without a footer. Use the close button in the header or click outside to dismiss.</p>
            <p>Modals without footers are useful for displaying information that doesn't require additional actions.</p>
          </div>
        </Modal>
      </>
    );
  }
};

// Modal with long content
export const WithLongContent: Story = {
  render: function LongContentModal() {
    const [isOpen, setIsOpen] = useState(false);
    
    return (
      <>
        <Button 
          variant="primary" 
          label="Open Modal with Long Content" 
          onClick={() => setIsOpen(true)}
        />
        
        <Modal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Terms and Conditions"
          footer={
            <Button 
              variant="primary" 
              label="I Accept" 
              onClick={() => setIsOpen(false)} 
            />
          }
        >
          <div>
            <p>This modal demonstrates scrollable content. The following text will overflow the modal body.</p>
            
            {Array(20).fill(null).map((_, i) => (
              <p key={i}>
                This is paragraph {i + 1} with some sample text to demonstrate scrolling behavior 
                within the modal. When there's a lot of content, the modal body becomes scrollable 
                while keeping the header and footer fixed.
              </p>
            ))}
          </div>
        </Modal>
      </>
    );
  }
};

// Nested Modals
export const NestedModals: Story = {
  render: function NestedModalsExample() {
    const [isFirstOpen, setIsFirstOpen] = useState(false);
    const [isSecondOpen, setIsSecondOpen] = useState(false);
    
    return (
      <>
        <Button 
          variant="primary" 
          label="Open First Modal" 
          onClick={() => setIsFirstOpen(true)}
        />
        
        <Modal
          isOpen={isFirstOpen}
          onClose={() => setIsFirstOpen(false)}
          title="First Modal"
          footer={
            <>
              <Button 
                variant="outline" 
                label="Close" 
                onClick={() => setIsFirstOpen(false)} 
              />
              <Button 
                variant="primary" 
                label="Open Second Modal" 
                onClick={() => setIsSecondOpen(true)} 
              />
            </>
          }
        >
          <p>This is the first modal. You can open another modal from here.</p>
        </Modal>

        <Modal
          isOpen={isSecondOpen}
          onClose={() => setIsSecondOpen(false)}
          title="Second Modal"
          size="small"
          variant="dark"
          footer={
            <Button 
              variant="primary" 
              label="Close Both" 
              onClick={() => {
                setIsSecondOpen(false);
                setIsFirstOpen(false);
              }} 
            />
          }
        >
          <p>This is the second modal, nested inside the first one.</p>
        </Modal>
      </>
    );
  }
};
