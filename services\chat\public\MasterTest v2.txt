<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LoaLoa Chat - Test Final</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <style>
    :root {
      --primary: #4CAF50;
      --primary-dark: #45a049;
      --secondary: #2196F3;
      --secondary-dark: #0b7dda;
      --warning: #ff9800;
      --danger: #f44336;
      --success: #4CAF50;
      --dark: #333;
      --light: #f5f5f5;
      --border: #ddd;
    }
    * { box-sizing: border-box; }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f9f9f9;
      color: #333;
      line-height: 1.6;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 15px;
    }
    h1, h2, h3, h4 {
      color: var(--dark);
      margin-top: 0;
    }
    .card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      overflow: hidden;
    }
    .card-header {
      background: var(--light);
      padding: 15px 20px;
      border-bottom: 1px solid var(--border);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .card-body {
      padding: 20px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"],
    input[type="password"],
    textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--border);
      border-radius: 4px;
      font-size: 14px;
    }
    textarea {
      min-height: 100px;
      font-family: monospace;
    }
    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      margin-right: 5px;
      margin-bottom: 5px;
    }
    .btn-primary {
      background-color: var(--primary);
      color: white;
    }
    .btn-primary:hover {
      background-color: var(--primary-dark);
    }
    .btn-secondary {
      background-color: var(--secondary);
      color: white;
    }
    .btn-secondary:hover {
      background-color: var(--secondary-dark);
    }
    .btn-warning {
      background-color: var(--warning);
      color: white;
    }
    .btn-danger {
      background-color: var(--danger);
      color: white;
    }
    .status {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 20px;
      font-weight: 600;
      font-size: 12px;
    }
    .status-pending {
      background-color: #ffeaa7;
      color: #d35400;
    }
    .status-success {
      background-color: #c4e6ca;
      color: #2d6a4f;
    }
    .status-error {
      background-color: #ffcccc;
      color: #cc0000;
    }
    .grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 20px;
    }
    @media (min-width: 768px) {
      .grid-2 {
        grid-template-columns: 1fr 1fr;
      }
    }
    .tab-header {
      display: flex;
      border-bottom: 1px solid var(--border);
    }
    .tab-btn {
      padding: 10px 20px;
      cursor: pointer;
      border: none;
      background: none;
      font-weight: 500;
      border-bottom: 2px solid transparent;
    }
    .tab-btn.active {
      border-bottom: 2px solid var(--primary);
      color: var(--primary);
    }
    .tab-content {
      padding: 20px 0;
    }
    .tab-panel {
      display: none;
    }
    .tab-panel.active {
      display: block;
    }
    .data-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    .data-table th,
    .data-table td {
      padding: 10px;
      border: 1px solid var(--border);
      text-align: left;
    }
    .data-table th {
      background: var(--light);
      font-weight: 600;
    }
    .data-table tr:nth-child(even) {
      background-color: rgba(0, 0, 0, 0.02);
    }
    .console {
      background: #272822;
      color: #f8f8f2;
      font-family: monospace;
      padding: 15px;
      border-radius: 4px;
      height: 200px;
      overflow-y: auto;
    }
    .console-line {
      margin-bottom: 5px;
      word-wrap: break-word;
    }
    .timestamp {
      color: #a6e22e;
    }
    .chat-container {
      display: flex;
      gap: 20px;
    }
    .chat-box {
      flex: 1;
      border: 1px solid var(--border);
      border-radius: 8px;
      overflow: hidden;
    }
    .chat-header {
      background: var(--light);
      padding: 10px 15px;
      border-bottom: 1px solid var(--border);
    }
    .chat-messages {
      height: 300px;
      padding: 15px;
      overflow-y: auto;
      background: white;
    }
    .chat-input {
      display: flex;
      padding: 10px;
      border-top: 1px solid var(--border);
    }
    .chat-input input {
      flex: 1;
      margin-right: 10px;
    }
    .message {
      margin-bottom: 10px;
      padding: 8px 12px;
      border-radius: 8px;
      max-width: 70%;
      word-break: break-word;
    }
    .message.sent {
      background: #e3f2fd;
      margin-left: auto;
      text-align: right;
    }
    .message.received {
      background: #f1f1f1;
    }
    .message-meta {
      font-size: 12px;
      color: #666;
    }
    .user-info {
      margin-top: 10px;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 4px;
    }
    .step-number {
      display: inline-block;
      width: 24px;
      height: 24px;
      background: var(--primary);
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 24px;
      font-weight: bold;
      margin-right: 8px;
    }
	/* Cải thiện tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--border);
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-radius: 4px 4px 0 0;
}

.tab-btn {
  padding: 10px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-weight: 500;
  color: #555;
  border-bottom: 3px solid transparent;
  transition: all 0.3s;
}

.tab-btn:hover {
  background-color: #e9e9e9;
  color: #333;
}

.tab-btn.active {
  border-bottom: 3px solid var(--primary);
  color: var(--primary);
  font-weight: bold;
  background-color: #fff;
}

/* Style cho panel */
.tab-panel {
  display: none;
  animation: fadeIn 0.5s;
}

.tab-panel.active {
  display: block;
}

/* Card styling */
.card {
  border: 1px solid var(--border);
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  background-color: white;
  padding: 20px;
  margin-bottom: 20px;
}

/* Form layout */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #444;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border);
  border-radius: 4px;
  font-size: 14px;
}

.form-control:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Button styling */
.button-row {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.button {
  padding: 10px 15px;
  border: 1px solid var(--border);
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.button:hover {
  background-color: #f5f5f5;
}

.button-primary {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary-dark);
}

.button-primary:hover {
  background-color: var(--primary-dark);
}

/* Info panel styling */
.info-panel {
  border: 1px solid var(--border);
  border-radius: 4px;
  background-color: #f9f9f9;
  margin-top: 15px;
  animation: fadeIn 0.3s;
}

.info-content {
  padding: 15px;
}

.status-message {
  margin-top: 0;
  font-weight: 600;
  margin-bottom: 10px;
}

.details {
  font-size: 14px;
}

/* Status colors */
.success-message {
  color: var(--success);
}

.warning-message {
  color: var(--warning);
}

.error-message {
  color: var(--danger);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* Cải thiện giao diện chat */
.chat-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chat-box {
  border: 1px solid var(--border);
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  background-color: white;
  display: flex;
  flex-direction: column;
  height: 500px;
}

.chat-header {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border);
  background-color: #f8f9fa;
}

.chat-header h3 {
  margin: 0;
  margin-bottom: 10px;
}

.user-info {
  font-size: 14px;
  color: #555;
}

.user-info div {
  margin-bottom: 5px;
}

.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #f9f9f9;
}

.chat-controls {
  padding: 10px 15px;
  border-top: 1px solid var(--border);
}

.input-group {
  display: flex;
  margin-bottom: 10px;
}

.input-group .form-control {
  flex-grow: 1;
  margin-right: 10px;
}

/* Message styling */
.message {
  margin-bottom: 10px;
  max-width: 80%;
  padding: 8px 12px;
  border-radius: 8px;
  position: relative;
  clear: both;
}

.message.sent {
  background-color: #dcf8c6;
  float: right;
}

.message.received {
  background-color: #fff;
  border: 1px solid #e5e5e5;
  float: left;
}

.message-meta {
  font-size: 11px;
  color: #888;
  margin-top: 5px;
}

/* Styling for translated content */
.translated-content {
  margin-top: 5px;
  padding-top: 5px;
  border-top: 1px dashed #ccc;
  font-style: italic;
  color: #6c757d;
}

/* Language selection */
.language-group {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.language-group label {
  margin-right: 10px;
  margin-bottom: 0;
  white-space: nowrap;
}

.language-selector {
  width: auto;
  min-width: 150px;
}
  </style>
</head>
<body>
  <div class="container">
    <h1>LoaLoa Chat - Test Final</h1>
    
    <!-- Step 1: Connect to Supabase -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">1</span> Connect to Supabase</h3>
        <span class="status status-pending" id="supabase-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label for="supabase-url">Supabase URL</label>
          <input type="text" id="supabase-url" value="https://iwzwbrbmojvvvfstbqow.supabase.co">
        </div>
        <div class="form-group">
          <label for="supabase-key">Supabase Key (Anon)</label>
          <input type="text" id="supabase-key" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3endicmJtb2p2dnZmc3RicW93Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMjkyNjEsImV4cCI6MjA2MTkwNTI2MX0.tyVtaSclUKC5fGh7I7Ohpm7c4FniXphYe34-cxBvo6E">
        </div>
        <div class="form-group">
          <label for="service-role-key">Service Role Key (Optional, for admin operations)</label>
          <input type="password" id="service-role-key" placeholder="Service Role Key (optional)">
        </div>
        <button class="btn btn-primary" id="connect-supabase">Connect to Supabase</button>
        <div id="connection-details" class="user-info" style="display: none;"></div>
      </div>
    </div>
    
    <!-- Step 2: Validate JWT Token -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">2</span> Validate JWT Token</h3>
        <span class="status status-pending" id="jwt-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="note" style="background-color: #ffeaa7; padding: 15px; border-radius: 4px; margin-bottom: 15px;">
          <strong>Need a JWT Token?</strong> Run this command in your terminal:<br>
          <code>cd D:\loaloa\services\chat</code><br>
          <code>npx ts-node src\tests\generate-token.ts</code>
        </div>
        <div class="form-group">
          <label for="jwt-token">JWT Token</label>
          <textarea id="jwt-token" placeholder="Paste your JWT token here"></textarea>
        </div>
        <button class="btn btn-primary" id="validate-token">Validate Token</button>
        <div id="token-info" class="user-info" style="display: none;"></div>
      </div>
    </div>
    
    <!-- Step 3: Database Explorer -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">3</span> Database Explorer</h3>
        <span class="status status-pending" id="db-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="tab-header">
          <button class="tab-btn active" data-tab="users">Users</button>
          <button class="tab-btn" data-tab="rooms">Chat Rooms</button>
          <button class="tab-btn" data-tab="participants">Participants</button>
          <button class="tab-btn" data-tab="messages">Messages</button>
        </div>
        
        <div class="tab-content">
          <!-- Users Tab -->
          <div class="tab-panel active" id="users-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-users">Load Users</button>
              <span id="users-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="users-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Email</th>
                    <th>Created At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="3">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Rooms Tab -->
          <div class="tab-panel" id="rooms-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-rooms">Load Chat Rooms</button>
              <span id="rooms-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="rooms-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Created At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="4">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Participants Tab -->
          <div class="tab-panel" id="participants-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-participants">Load Participants</button>
              <span id="participants-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="participants-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Room ID</th>
                    <th>User ID</th>
                    <th>Role</th>
                    <th>Joined At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="5">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Messages Tab -->
          <div class="tab-panel" id="messages-panel">
            <div class="form-group">
              <button class="btn btn-primary" id="load-messages">Load Messages</button>
              <span id="messages-count" style="margin-left: 10px;"></span>
            </div>
            <div class="table-container">
              <table class="data-table" id="messages-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Room ID</th>
                    <th>Sender ID</th>
                    <th>Content</th>
                    <th>Language</th>
                    <th>Sent At</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colspan="6">No data loaded yet</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    
   <!-- STEP 4: USER-ROOM MANAGEMENT -->
<div class="section">
  <div class="section-header">
    <h2>4 User-Room Management</h2>
    <div class="status" id="manage-status">Pending</div>
  </div>
  <div class="section-content">
    <div class="tabs">
      <button class="tab-btn active" data-tab="user1-manage">User 1</button>
      <button class="tab-btn" data-tab="user2-manage">User 2</button>
    </div>
    
    <!-- User 1 Management Panel -->
    <div class="tab-panel active" id="user1-manage-panel">
      <div class="card">
        <div class="form-grid">
          <div class="form-group">
            <label for="select-user1">Select User 1</label>
            <select id="select-user1" class="form-control">
              <option value="">-- Select User --</option>
            </select>
          </div>
          <div class="form-group">
            <label for="select-room1">Select Room</label>
            <select id="select-room1" class="form-control">
              <option value="">-- Select Room --</option>
            </select>
          </div>
        </div>
        <div class="button-row">
          <button id="check-participation1" class="button">Check Participation</button>
          <button id="join-room1" class="button button-primary">Join Room</button>
        </div>
        <div id="participation-status1" class="info-panel" style="display: none;">
          <div class="info-content">
            <p class="status-message"></p>
            <div class="details"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- User 2 Management Panel -->
    <div class="tab-panel" id="user2-manage-panel">
      <div class="card">
        <div class="form-grid">
          <div class="form-group">
            <label for="select-user2">Select User 2</label>
            <select id="select-user2" class="form-control">
              <option value="">-- Select User --</option>
            </select>
          </div>
          <div class="form-group">
            <label for="select-room2">Select Room</label>
            <select id="select-room2" class="form-control">
              <option value="">-- Select Room --</option>
            </select>
          </div>
        </div>
        <div class="button-row">
          <button id="check-participation2" class="button">Check Participation</button>
          <button id="join-room2" class="button button-primary">Join Room</button>
        </div>
        <div id="participation-status2" class="info-panel" style="display: none;">
          <div class="info-content">
            <p class="status-message"></p>
            <div class="details"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

    
    <!-- Step 5: WebSocket Connection -->
    <div class="card">
      <div class="card-header">
        <h3><span class="step-number">5</span> WebSocket Connection</h3>
        <span class="status status-pending" id="ws-status">Pending</span>
      </div>
      <div class="card-body">
        <div class="form-group">
          <label for="ws-url">WebSocket Server URL</label>
          <input type="text" id="ws-url" value="http://localhost:3002">
        </div>
        <div class="form-group">
          <label for="ws-room-id">Room ID to Join</label>
          <input type="text" id="ws-room-id" placeholder="Room ID">
        </div>
        <div class="form-group">
          <button class="btn btn-primary" id="connect-ws">Connect WebSocket</button>
          <button class="btn btn-secondary" id="ws-join-room">Join Room</button>
          <button class="btn btn-secondary" id="show-debug" data-state="show">Hide Debug Console</button>
        </div>
        <div class="console" id="debug-console">
          <!-- Debug messages will appear here -->
        </div>
      </div>
    </div>
    
    <!-- STEP 6: TEST CHAT INTERFACE -->
<div class="section">
  <div class="section-header">
    <h2>6 Test Chat Interface</h2>
    <div class="status" id="chat-status">Pending</div>
  </div>
  <div class="section-content">
    <div class="chat-container">
      <!-- User 1 (Vietnamese) -->
      <div class="chat-box">
        <div class="chat-header">
          <h3>User 1 (Vietnamese)</h3>
          <div class="user-info">
            <div><strong>ID:</strong> <span id="user1-id"></span></div>
            <div><strong>Email:</strong> <span id="user1-email">Not loaded</span></div>
            <div><strong>Status:</strong> <span id="user1-status">Disconnected</span></div>
          </div>
        </div>
        <div class="chat-messages" id="user1-messages"></div>
        <div class="chat-controls">
          <div class="form-group language-group">
            <label for="user1-language">Translate to:</label>
            <select id="user1-language" class="form-control language-selector" disabled>
              <option value="vi">Vietnamese (Default)</option>
              <option value="en">English</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="zh">Chinese</option>
              <option value="ja">Japanese</option>
            </select>
          </div>
          <div class="input-group">
            <input type="text" id="user1-input" class="form-control" placeholder="Nhập tin nhắn...">
            <button id="user1-send" class="button button-primary">Send</button>
          </div>
          <button id="user1-join" class="button">Join as Participant</button>
        </div>
      </div>
      <!-- User 2 (English) -->
      <div class="chat-box">
        <div class="chat-header">
          <h3>User 2 (English)</h3>
          <div class="user-info">
            <div><strong>ID:</strong> <span id="user2-id"></span></div>
            <div><strong>Email:</strong> <span id="user2-email">Not loaded</span></div>
            <div><strong>Status:</strong> <span id="user2-status">Disconnected</span></div>
          </div>
        </div>
        <div class="chat-messages" id="user2-messages"></div>
        <div class="chat-controls">
          <div class="form-group language-group">
            <label for="user2-language">Translate to:</label>
            <select id="user2-language" class="form-control language-selector" disabled>
              <option value="en">English (Default)</option>
              <option value="vi">Vietnamese</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="zh">Chinese</option>
              <option value="ja">Japanese</option>
            </select>
          </div>
          <div class="input-group">
            <input type="text" id="user2-input" class="form-control" placeholder="Type a message...">
            <button id="user2-send" class="button button-primary">Send</button>
          </div>
          <button id="user2-join" class="button">Join as Participant</button>
        </div>
      </div>
    </div>
  </div>
</div>
  <!-- STEP 7: TRANSLATION API -->
<div class="section">
  <div class="section-header">
    <h2>7 Translation API</h2>
    <div class="status" id="translation-api-status">Pending</div>
  </div>
  <div class="section-content">
    <div class="card">
      <div class="form-group">
        <label for="api-key">MyMemory API Key (Optional)</label>
        <input type="text" id="api-key" class="form-control" placeholder="Để trống nếu muốn sử dụng API miễn phí">
      </div>
      <div class="button-row">
        <button id="check-api-status" class="button button-primary">Check API Status</button>
      </div>
      <div id="api-status-result" class="info-panel" style="display: none;">
        <div class="info-content">
          <p class="status-message"></p>
          <div class="details"></div>
        </div>
      </div>
    </div>
  </div>
</div>
  <script>
   // Global variables
    let supabaseClient = null;
    let serviceClient = null;
    let jwtToken = '';
    let userId = '';
    let socket1 = null;
    let socket2 = null;
    let currentRoomId = '';
    
    // DOM references
    const statuses = {
      supabase: document.getElementById('supabase-status'),
      jwt: document.getElementById('jwt-status'),
      db: document.getElementById('db-status'),
      manage: document.getElementById('manage-status'),
      ws: document.getElementById('ws-status'),
      chat: document.getElementById('chat-status')
    };
    
    // Helper function to update status
    function updateStatus(key, status, message = '') {
      if (statuses[key]) {
        statuses[key].className = `status status-${status}`;
        statuses[key].textContent = status.charAt(0).toUpperCase() + status.slice(1);
        
        if (message && status === 'error') {
          console.error(message);
          addDebugMessage(message, true);
        }
      }
    }
    
    // Helper function to add debug messages
    function addDebugMessage(message, isError = false) {
      const debugConsole = document.getElementById('debug-console');
      const lineElement = document.createElement('div');
      lineElement.className = 'console-line';
      
      const timestamp = document.createElement('span');
      timestamp.className = 'timestamp';
      timestamp.textContent = `[${new Date().toLocaleTimeString()}] `;
      
      const messageText = document.createTextNode(message);
      
      lineElement.appendChild(timestamp);
      lineElement.appendChild(messageText);
      
      if (isError) {
        lineElement.style.color = '#f92672'; // Red color for errors
      }
      
      debugConsole.appendChild(lineElement);
      debugConsole.scrollTop = debugConsole.scrollHeight;
    }
    
    // Format date for display
    function formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString();
    }
    
    // Truncate text for table display
    function truncateText(text, length = 30) {
      if (!text) return '';
      return text.length > length ? text.substring(0, length) + '...' : text;
    }
    
    // Load table data
    function loadTableData(tableId, data, columns) {
      const table = document.getElementById(tableId);
      const tbody = table.getElementsByTagName('tbody')[0];
      tbody.innerHTML = '';
      
      if (data && data.length > 0) {
        data.forEach(item => {
          const row = document.createElement('tr');
          columns.forEach(col => {
            const cell = document.createElement('td');
            
            // Handle different data types
            if (col.format === 'date') {
              cell.textContent = formatDate(item[col.field]);
            } else if (col.format === 'truncate') {
              cell.textContent = truncateText(item[col.field], col.length || 30);
              if (item[col.field] && item[col.field].length > (col.length || 30)) {
                cell.title = item[col.field]; // Show full text on hover
              }
            } else {
              cell.textContent = item[col.field] || '';
            }
            
            row.appendChild(cell);
          });
          tbody.appendChild(row);
        });
      } else {
        const row = document.createElement('tr');
        const cell = document.createElement('td');
        cell.colSpan = columns.length;
        cell.textContent = 'No data available';
        cell.style.textAlign = 'center';
        row.appendChild(cell);
        tbody.appendChild(row);
      }
    }
    
       // Populate dropdown - hỗ trợ function định dạng
function populateDropdown(selectId, data, valueField, textField) {
  const select = document.getElementById(selectId);
  const defaultOption = select.options[0];
  select.innerHTML = '';
  select.appendChild(defaultOption);
  
  if (data && data.length > 0) {
    data.forEach(item => {
      const option = document.createElement('option');
      option.value = item[valueField];
      
      // Kiểm tra xem textField là function hay field name
      if (typeof textField === 'function') {
        option.textContent = textField(item);
      } else {
        option.textContent = item[textField] || item[valueField];
      }
      
      select.appendChild(option);
    });
  }
}
    
    // ===== STEP 1: CONNECT TO SUPABASE =====
    document.getElementById('connect-supabase').addEventListener('click', async () => {
      try {
        const supabaseUrl = document.getElementById('supabase-url').value;
        const supabaseKey = document.getElementById('supabase-key').value;
        const serviceRoleKey = document.getElementById('service-role-key').value;
        
        if (!supabaseUrl || !supabaseKey) {
          updateStatus('supabase', 'error', 'URL and key are required');
          return;
        }
        
        addDebugMessage(`Connecting to Supabase at ${supabaseUrl}...`);
        
        // Check if Supabase library is available
        if (typeof supabase === 'undefined') {
          updateStatus('supabase', 'error', 'Supabase library not loaded');
          addDebugMessage('Supabase library not loaded', true);
          return;
        }
        
        // Create client with anon key
        supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
        
        // If service role key is provided, create admin client
        if (serviceRoleKey) {
          serviceClient = supabase.createClient(supabaseUrl, serviceRoleKey);
        }
        
      // Test connection
let testResult;
try {
  // Thử với user_details view đã tạo 
  testResult = await supabaseClient
    .from('user_details')
    .select('*', { count: 'exact', head: true });
    
  if (testResult.error) throw new Error('user_details failed');
  
} catch (userDetailsError) {
  try {
    // Thử với auth.users
    testResult = await supabaseClient
      .from('auth.users')
      .select('*', { count: 'exact', head: true });
      
    if (testResult.error) throw new Error('auth.users failed');
    
  } catch (authUsersError) {
    // Thử với public.user_profiles
    testResult = await supabaseClient
      .from('user_profiles')
      .select('*', { count: 'exact', head: true });
      
    if (testResult.error) {
      throw testResult.error;
    }
  }
}

addDebugMessage(`Kết nối thành công. Có thể truy cập database.`);       
        updateStatus('supabase', 'success');
        addDebugMessage('Successfully connected to Supabase');
        
        // Show connection details
        document.getElementById('connection-details').style.display = 'block';
        document.getElementById('connection-details').innerHTML = `
          <p><strong>Connection Status:</strong> Success</p>
          <p><strong>URL:</strong> ${supabaseUrl}</p>
          <p><strong>Service Role Key:</strong> ${serviceRoleKey ? 'Provided ✓' : 'Not provided'}</p>
        `;
        
        // Enable database tabs
        updateStatus('db', 'pending');
      } catch (err) {
        updateStatus('supabase', 'error');
        addDebugMessage(`Failed to connect to Supabase: ${err.message}`, true);
        
        document.getElementById('connection-details').style.display = 'block';
        document.getElementById('connection-details').innerHTML = `
          <p><strong>Connection Status:</strong> <span style="color: red;">Failed</span></p>
          <p><strong>Error:</strong> ${err.message}</p>
          <p>Please check your URL and API key and try again.</p>
        `;
      }
    });
    
    // ===== STEP 2: VALIDATE JWT TOKEN =====
    document.getElementById('validate-token').addEventListener('click', () => {
      const token = document.getElementById('jwt-token').value.trim();
      
      if (!token) {
        updateStatus('jwt', 'error', 'Please enter a JWT token');
        return;
      }
      
      try {
        // Decode JWT token to get payload
        const base64Url = token.split('.')[1];
        if (!base64Url) {
          updateStatus('jwt', 'error', 'Invalid JWT token format');
          addDebugMessage('Invalid JWT token format', true);
          return;
        }
        
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        
        const payload = JSON.parse(jsonPayload);
        
        // Check if token is expired
        const currentTime = Math.floor(Date.now() / 1000);
        const expiryDate = new Date(payload.exp * 1000);
        
        if (payload.exp < currentTime) {
          updateStatus('jwt', 'error', 'Token has expired');
          addDebugMessage('JWT token has expired', true);
          
          document.getElementById('token-info').style.display = 'block';
          document.getElementById('token-info').innerHTML = `
            <p style="color: red;"><strong>Token Status:</strong> Expired</p>
            <p><strong>Expiration Date:</strong> ${expiryDate.toLocaleString()}</p>
            <p><strong>Current Time:</strong> ${new Date().toLocaleString()}</p>
            <p>Please generate a new token using the command provided.</p>
          `;
          return;
        }
        
        // Save token and userId
        jwtToken = token;
        userId = payload.userId;
        
         // Update user1 ID field
        document.getElementById('user1-id').value = userId;
        
        updateStatus('jwt', 'success');
        addDebugMessage(`Valid JWT token for user: ${userId}`);
        
        // Display token info
        document.getElementById('token-info').style.display = 'block';
        document.getElementById('token-info').innerHTML = `
          <p><strong>Token Status:</strong> <span style="color: green;">Valid</span></p>
          <p><strong>User ID:</strong> ${payload.userId || 'Not found'}</p>
          <p><strong>Email:</strong> ${payload.email || 'Not found'}</p>
          <p><strong>Roles:</strong> ${payload.roles ? payload.roles.join(', ') : 'None'}</p>
          <p><strong>Language:</strong> ${payload.preferredLanguage || 'Not specified'}</p>
          <p><strong>Expiry:</strong> ${expiryDate.toLocaleString()}</p>
        `;
        
        // Fetch user email
        //fetchUserEmail(userId, 'user1-email');
		// Tự động thực hiện chức năng các nút Load trong Database Explorer
    setTimeout(() => {
  addDebugMessage('Tự động tải dữ liệu sau khi xác thực token thành công...');
  
  // Kiểm tra kết nối Supabase trước khi tải dữ liệu
  if (!supabaseClient) {
    addDebugMessage('Cần kết nối Supabase trước khi tải dữ liệu', true);
    return;
  }
  
  // Tự động tải Users
  addDebugMessage('Đang tải danh sách Users...');
  document.getElementById('load-users').click();
  
  // Tự động tải Rooms sau 800ms (tăng thời gian để đảm bảo thực hiện tuần tự)
  setTimeout(() => {
    addDebugMessage('Đang tải danh sách Rooms...');
    document.getElementById('load-rooms').click();
    
    // Tự động tải Participants sau 800ms
    setTimeout(() => {
      addDebugMessage('Đang tải danh sách Participants...');
      document.getElementById('load-participants').click();
      
      // Tự động tải Messages sau 800ms
      setTimeout(() => {
        addDebugMessage('Đang tải danh sách Messages...');
        document.getElementById('load-messages').click();
        
        addDebugMessage('Hoàn thành tải tất cả dữ liệu tự động!');
      }, 800);
    }, 800);
  }, 800);
}, 1500);
      } catch (err) {
        updateStatus('jwt', 'error');
        addDebugMessage(`Invalid JWT token: ${err.message}`, true);
        
        document.getElementById('token-info').style.display = 'block';
        document.getElementById('token-info').innerHTML = `
          <p style="color: red;"><strong>Token Status:</strong> Invalid</p>
          <p><strong>Error:</strong> ${err.message}</p>
          <p>Please check the token format and try again.</p>
        `;
      }
    });
    
    // Fetch user email by ID
async function fetchUserEmail(userId, elementId) {
  if (!supabaseClient || !userId) {
    document.getElementById(elementId).textContent = 'Connect to Supabase first';
    return;
  }
  
  try {
    // Thử với user_details view trước
    let data, error;
    
    try {
      const result = await supabaseClient
        .from('user_details')
        .select('email, full_name')
        .eq('id', userId)
        .single();
        
      data = result.data;
      error = result.error;
      
    } catch (viewError) {
      // Nếu view không khả dụng, thử trực tiếp với auth.users
      const result = await supabaseClient
        .from('auth.users')
        .select('email')
        .eq('id', userId)
        .single();
        
      data = result.data;
      error = result.error;
    }
      
    if (error) {
      document.getElementById(elementId).textContent = 'User not found';
      return;
    }
    
    if (data && data.email) {
      const displayText = data.full_name ? 
        `${data.email} (${data.full_name})` : 
        data.email;
        
      document.getElementById(elementId).textContent = displayText;
      return data.email;
    } else {
      document.getElementById(elementId).textContent = 'No email available';
      return null;
    }
  } catch (err) {
    document.getElementById(elementId).textContent = 'Error fetching user data';
    console.error('Error fetching user email:', err);
    return null;
  }
}
    
    // ===== STEP 3: DATABASE EXPLORER =====
    // Tab switching
    document.querySelectorAll('.tab-btn').forEach(button => {
      button.addEventListener('click', () => {
        // Remove active class from all tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
          btn.classList.remove('active');
        });
        
        // Hide all panels
        document.querySelectorAll('.tab-panel').forEach(panel => {
          panel.classList.remove('active');
        });
        
        // Add active class to clicked tab
        button.classList.add('active');
        
        // Show corresponding panel
        const tabId = button.getAttribute('data-tab');
        document.getElementById(`${tabId}-panel`).classList.add('active');
      });
    });
    
    // Load Users
    document.getElementById('load-users').addEventListener('click', async () => {
  if (!supabaseClient) {
    addDebugMessage('Please connect to Supabase first', true);
    return;
  }
  
  try {
    addDebugMessage('Loading users...');
    
    // Use service client if available for better access
    const client = serviceClient || supabaseClient;
    
    // Thử cách 1: Sử dụng user_details view
    let data, error;
    try {
      const result = await client
        .from('user_details')
        .select('*')
        .limit(20);
      
      data = result.data;
      error = result.error;
      
      if (error) throw new Error('Could not load from user_details');
      
    } catch (viewError) {
      // Cách 2: Kết hợp auth.users và user_profiles
      try {
        const result = await client
          .from('auth.users')
          .select(`
            id,
            email,
            confirmed_at,
            last_sign_in_at,
            user_profiles:user_profiles!id(
              full_name, 
              preferred_language,
              avatar_url,
              phone_number,
              created_at
            )
          `)
          .limit(20);
          
        if (result.error) throw result.error;
          
        // Lấy dữ liệu và chuẩn hóa format
        data = result.data.map(user => ({
          id: user.id,
          email: user.email,
          full_name: user.user_profiles?.full_name || '',
          preferred_language: user.user_profiles?.preferred_language || 'en',
          created_at: user.user_profiles?.created_at || user.confirmed_at
        }));
        
      } catch (joinError) {
        // Cách 3: Truy vấn riêng lẻ
        const authResult = await client
          .from('auth.users')
          .select('id, email, confirmed_at')
          .limit(20);
          
        data = authResult.data || [];
        error = authResult.error;
      }
    }
    
    if (error) {
      throw error;
    }
    
    updateStatus('db', 'success');
    addDebugMessage(`Loaded ${data.length} users`);
    document.getElementById('users-count').textContent = `${data.length} users loaded`;
    
    // Cập nhật columns để hiển thị thêm full_name và preferred_language
    const columns = [
      { field: 'id', label: 'ID' },
      { field: 'email', label: 'Email' },
      { field: 'full_name', label: 'Full Name' },
      { field: 'preferred_language', label: 'Language' }, 
      { field: 'created_at', label: 'Created At', format: 'date' }
    ];
    
    // Cập nhật table header khi cần thêm cột mới
    const tableHeader = document.querySelector('#users-table thead tr');
    
    // Xóa tất cả cột cũ
    while (tableHeader.firstChild) {
      tableHeader.removeChild(tableHeader.firstChild);
    }
    
    // Thêm header cho cột mới
    columns.forEach(column => {
      const th = document.createElement('th');
      th.textContent = column.label;
      tableHeader.appendChild(th);
    });
    
    loadTableData('users-table', data, columns);
    
    // Cập nhật dropdown với email hoặc full_name nếu có
    populateDropdown('select-user1', data, 'id', item => 
      item.full_name ? `${item.full_name} (${item.email})` : item.email
    );
    populateDropdown('select-user2', data, 'id', item => 
      item.full_name ? `${item.full_name} (${item.email})` : item.email
    );
    
    // Cache user data for later use
    window.userData = data;
  } catch (err) {
    updateStatus('db', 'error');
    addDebugMessage(`Failed to load users: ${err.message}`, true);
  }
});
    
    // Load Chat Rooms
    document.getElementById('load-rooms').addEventListener('click', async () => {
  if (!supabaseClient) {
    addDebugMessage('Please connect to Supabase first', true);
    return;
  }
  
  try {
    addDebugMessage('Loading chat rooms...');
    
    const client = serviceClient || supabaseClient;
    let data, error;
    
    // Try 'chat_rooms' first - with organization details
    try {
      const response = await client
        .from('chat_rooms')
        .select(`
          id,
          name,
          room_type,
          created_at,
          organization_id,
          organizations:organization_id (name)
        `)
        .limit(20);
        
      data = response.data;
      error = response.error;
      
      if (!error && data) {
        // Format data to include organization name
        data = data.map(room => ({
          ...room,
          organization_name: room.organizations ? room.organizations.name : 'None'
        }));
        addDebugMessage('Successfully loaded rooms from chat_rooms table with organization info');
      }
    } catch (e) {
      addDebugMessage('Error with chat_rooms table, trying without join...');
      
      const response = await client
        .from('chat_rooms')
        .select('*')
        .limit(20);
        
      data = response.data;
      error = response.error;
      
      if (!error && data) {
        addDebugMessage('Successfully loaded rooms from chat_rooms table');
      }
    }
    
    // If no data from 'chat_rooms', try 'rooms' (legacy)
    if (!data || error) {
      const response = await client
        .from('rooms')
        .select('*')
        .limit(20);
        
      data = response.data;
      error = response.error;
      
      if (!error && data) {
        addDebugMessage('Successfully loaded rooms from legacy rooms table');
      }
    }
    
    if (error) {
      throw error;
    }
    
    updateStatus('db', 'success');
    addDebugMessage(`Loaded ${data.length} rooms`);
    document.getElementById('rooms-count').textContent = `${data.length} rooms loaded`;
    
    // Update columns for rooms table to include organization info
    const columns = [
      { field: 'id', label: 'ID' },
      { field: 'name', label: 'Name' },
      { field: 'room_type', label: 'Type' },
      { field: 'organization_name', label: 'Organization' },
      { field: 'created_at', label: 'Created At', format: 'date' }
    ];
    
    // Cập nhật table header khi cần thêm cột mới
    const tableHeader = document.querySelector('#rooms-table thead tr');
    
    // Xóa tất cả cột cũ
    while (tableHeader.firstChild) {
      tableHeader.removeChild(tableHeader.firstChild);
    }
    
    // Thêm header cho cột mới
    columns.forEach(column => {
      const th = document.createElement('th');
      th.textContent = column.label;
      tableHeader.appendChild(th);
    });
    
    loadTableData('rooms-table', data, columns);
    
    populateDropdown('select-room1', data, 'id', item => 
      `${item.name || 'Unnamed'} (${item.room_type || 'Unknown'})`
    );
    populateDropdown('select-room2', data, 'id', item => 
      `${item.name || 'Unnamed'} (${item.room_type || 'Unknown'})`
    );
    
    // Update room ID field for WebSocket
    if (data && data.length > 0) {
      document.getElementById('ws-room-id').value = data[0].id;
    }
    
    // Cache room data for later use
    window.roomData = data;
  } catch (err) {
    updateStatus('db', 'error');
    addDebugMessage(`Failed to load rooms: ${err.message}`, true);
  }
});
    
    // Load Participants
    document.getElementById('load-participants').addEventListener('click', async () => {
  if (!supabaseClient) {
    addDebugMessage('Please connect to Supabase first', true);
    return;
  }
  
  try {
    addDebugMessage('Loading participants...');
    
    const client = serviceClient || supabaseClient;
    
    // Sử dụng truy vấn đơn giản không join
    const { data, error } = await client
      .from('chat_participants')
      .select('*')
      .limit(20);
      
    if (error) {
      throw error;
    }
    
    // Sau khi có danh sách participants, nếu cần thì lấy thêm thông tin email
    // bằng cách lấy dữ liệu riêng
    if (data && data.length > 0) {
      // Lấy danh sách user_id để truy vấn thông tin
      const userIds = data.filter(p => p.user_id).map(p => p.user_id);
      
      if (userIds.length > 0) {
        try {
          // Thử lấy thông tin từ user_details hoặc auth.users
          let userData;
          try {
            const { data: userDetails } = await client
              .from('user_details')
              .select('id, email')
              .in('id', userIds);
              
            userData = userDetails;
          } catch (e) {
            // Nếu không được, thử với auth.users
            const { data: authUsers } = await client
              .from('auth.users')
              .select('id, email')
              .in('id', userIds);
              
            userData = authUsers;
          }
          
          // Nếu lấy được thông tin user, bổ sung vào dữ liệu participants
          if (userData && userData.length > 0) {
            // Map email vào dữ liệu participants
            data.forEach(participant => {
              if (participant.user_id) {
                const user = userData.find(u => u.id === participant.user_id);
                if (user) {
                  participant.email = user.email;
                }
              }
            });
          }
        } catch (userError) {
          // Nếu không lấy được thông tin user, vẫn tiếp tục với dữ liệu participants
          addDebugMessage(`Could not fetch user details: ${userError.message}`, true);
        }
      }
    }
    
    // Thêm thông tin trạng thái và thông tin mặc định
    data.forEach(participant => {
      participant.email = participant.email || 'N/A';
      participant.status = participant.is_active ? 'Active' : 'Inactive';
    });
    
    updateStatus('db', 'success');
    addDebugMessage(`Loaded ${data.length} participants`);
    document.getElementById('participants-count').textContent = `${data.length} participants loaded`;
    
    // Define columns for participants table with additional info
    const columns = [
      { field: 'id', label: 'ID' },
      { field: 'chat_room_id', label: 'Room ID' },
      { field: 'user_id', label: 'User ID' },
      { field: 'email', label: 'Email' },
      { field: 'participant_role', label: 'Role' },
      { field: 'preferred_language', label: 'Language' },
      { field: 'status', label: 'Status' },
      { field: 'joined_at', label: 'Joined At', format: 'date' }
    ];
    
    // Cập nhật table header khi cần thêm cột mới
    const tableHeader = document.querySelector('#participants-table thead tr');
    
    // Xóa tất cả cột cũ
    while (tableHeader.firstChild) {
      tableHeader.removeChild(tableHeader.firstChild);
    }
    
    // Thêm header cho cột mới
    columns.forEach(column => {
      const th = document.createElement('th');
      th.textContent = column.label;
      tableHeader.appendChild(th);
    });
    
    loadTableData('participants-table', data, columns);
    
    // Cache participant data for later use
    window.participantData = data;
  } catch (err) {
    updateStatus('db', 'error');
    addDebugMessage(`Failed to load participants: ${err.message}`, true);
  }
});
    
    // Load Messages
    document.getElementById('load-messages').addEventListener('click', async () => {
  if (!supabaseClient) {
    addDebugMessage('Please connect to Supabase first', true);
    return;
  }
  
  try {
    addDebugMessage('Loading messages...');
    
    const client = serviceClient || supabaseClient;
    
    // Thử load messages với join để lấy thông tin về bản dịch và sender
    let data, error;
    try {
      // Dùng cú pháp join để lấy thông tin bản dịch
      const response = await client
        .from('chat_messages')
        .select(`
          id,
          chat_room_id,
          sender_id,
          participant_id,
          temporary_sender_id,
          content,
          content_type,
          original_language,
          sent_at,
          is_translated,
          message_translations:message_translations(
            id,
            language,
            translated_content,
            is_automatic
          )
        `)
        .order('sent_at', { ascending: false })
        .limit(20);
      
      data = response.data;
      error = response.error;
      
      if (!error && data) {
        addDebugMessage('Successfully loaded messages with translations');
        
        // Định dạng lại dữ liệu để hiển thị tốt hơn
        data = data.map(message => {
          // Tính số lượng bản dịch và các ngôn ngữ đã dịch
          const translations = message.message_translations || [];
          const languages = translations.map(t => t.language).join(', ');
          
          return {
            ...message,
            translations_count: translations.length,
            translated_languages: languages,
            // Truncate content nếu quá dài
            display_content: message.content.length > 50 
              ? message.content.substring(0, 50) + '...' 
              : message.content
          };
        });
      }
    } catch (joinError) {
      addDebugMessage(`Error with join query: ${joinError.message}. Trying simple query...`, true);
      
      // Nếu join không thành công, dùng truy vấn đơn giản
      const response = await client
        .from('chat_messages')
        .select('*')
        .order('sent_at', { ascending: false })
        .limit(20);
        
      data = response.data;
      error = response.error;
      
      if (!error && data) {
        // Thêm các trường thông tin để tương thích
        data = data.map(message => ({
          ...message,
          translations_count: 0,
          translated_languages: 'None',
          display_content: message.content.length > 50 
            ? message.content.substring(0, 50) + '...' 
            : message.content
        }));
      }
    }
      
    if (error) {
      throw error;
    }
    
    updateStatus('db', 'success');
    addDebugMessage(`Loaded ${data.length} messages`);
    document.getElementById('messages-count').textContent = `${data.length} messages loaded`;
    
    // Define columns for messages table with additional info
    const columns = [
      { field: 'id', label: 'ID' },
      { field: 'chat_room_id', label: 'Room ID' },
      { field: 'sender_id', label: 'Sender ID' },
      { field: 'display_content', label: 'Content' },
      { field: 'original_language', label: 'Language' },
      { field: 'translations_count', label: 'Translations' },
      { field: 'translated_languages', label: 'Languages' },
      { field: 'sent_at', label: 'Sent At', format: 'date' }
    ];
    
    // Cập nhật table header để hiển thị các cột mới
    const tableHeader = document.querySelector('#messages-table thead tr');
    
    // Xóa tất cả cột cũ
    while (tableHeader.firstChild) {
      tableHeader.removeChild(tableHeader.firstChild);
    }
    
    // Thêm header cho cột mới
    columns.forEach(column => {
      const th = document.createElement('th');
      th.textContent = column.label;
      tableHeader.appendChild(th);
    });
    
    loadTableData('messages-table', data, columns);
    
    // Cache message data for later use
    window.messageData = data;
  } catch (err) {
    updateStatus('db', 'error');
    addDebugMessage(`Failed to load messages: ${err.message}`, true);
  }
});
       
   // ===== STEP 4: USER-ROOM MANAGEMENT =====
// Tab switching for User Management
document.querySelectorAll('.tab-btn[data-tab^="user"]').forEach(button => {
  button.addEventListener('click', () => {
    // Remove active class from all tabs
    document.querySelectorAll('.tab-btn[data-tab^="user"]').forEach(btn => {
      btn.classList.remove('active');
    });
    
    // Hide all panels
    document.querySelectorAll('.tab-panel[id$="manage-panel"]').forEach(panel => {
      panel.classList.remove('active');
    });
    
    // Add active class to clicked tab
    button.classList.add('active');
    
    // Show corresponding panel
    const tabId = button.getAttribute('data-tab');
    document.getElementById(`${tabId}-panel`).classList.add('active');
  });
});

// Check Participation - User 1
document.getElementById('check-participation1').addEventListener('click', async () => {
  const userId = document.getElementById('select-user1').value;
  const roomId = document.getElementById('select-room1').value;
  
  if (!supabaseClient) {
    addDebugMessage('Please connect to Supabase first', true);
    return;
  }
  
  if (!userId || !roomId) {
    addDebugMessage('Please select both user and room', true);
    document.getElementById('participation-status1').style.display = 'block';
    document.getElementById('participation-status1').innerHTML = `
      <p style="color: orange;"><strong>Error:</strong> Please select both user and room</p>
    `;
    return;
  }
  
  checkParticipation(userId, roomId, 'participation-status1', '1');
});

// Check Participation - User 2
document.getElementById('check-participation2').addEventListener('click', async () => {
  const userId = document.getElementById('select-user2').value;
  const roomId = document.getElementById('select-room2').value;
  
  if (!supabaseClient) {
    addDebugMessage('Please connect to Supabase first', true);
    return;
  }
  
  if (!userId || !roomId) {
    addDebugMessage('Please select both user and room', true);
    document.getElementById('participation-status2').style.display = 'block';
    document.getElementById('participation-status2').innerHTML = `
      <p style="color: orange;"><strong>Error:</strong> Please select both user and room</p>
    `;
    return;
  }
  
  checkParticipation(userId, roomId, 'participation-status2', '2');
});

// Join Room - User 1
document.getElementById('join-room1').addEventListener('click', async () => {
  const userId = document.getElementById('select-user1').value;
  const roomId = document.getElementById('select-room1').value;
  
  if (!supabaseClient) {
    addDebugMessage('Please connect to Supabase first', true);
    return;
  }
  
  if (!userId || !roomId) {
    addDebugMessage('Please select both user and room', true);
    document.getElementById('participation-status1').style.display = 'block';
    document.getElementById('participation-status1').innerHTML = `
      <p style="color: orange;"><strong>Error:</strong> Please select both user and room</p>
    `;
    return;
  }
  
  joinRoom(userId, roomId, 'participation-status1', '1');
  
  // Auto update User 1 ID in chat interface
  document.getElementById('user1-id').value = userId;
});

// Join Room - User 2
document.getElementById('join-room2').addEventListener('click', async () => {
  const userId = document.getElementById('select-user2').value;
  const roomId = document.getElementById('select-room2').value;
  
  if (!supabaseClient) {
    addDebugMessage('Please connect to Supabase first', true);
    return;
  }
  
  if (!userId || !roomId) {
    addDebugMessage('Please select both user and room', true);
    document.getElementById('participation-status2').style.display = 'block';
    document.getElementById('participation-status2').innerHTML = `
      <p style="color: orange;"><strong>Error:</strong> Please select both user and room</p>
    `;
    return;
  }
  
  joinRoom(userId, roomId, 'participation-status2', '2');
  
  // Auto update User 2 ID in chat interface
  document.getElementById('user2-id').value = userId;
});

// Common function to check participation
async function checkParticipation(userId, roomId, statusElementId, userNumber) {
  try {
    addDebugMessage(`Checking if user ${userId} is in room ${roomId}...`);
    
    // Kiểm tra tham số đầu vào
    if (!userId || !roomId) {
      addDebugMessage('Missing userId or roomId for checkParticipation', true);
      throw new Error('User ID and Room ID are required');
    }
    
    // Kiểm tra elements tồn tại
    const statusElement = document.getElementById(statusElementId);
    if (!statusElement) {
      addDebugMessage(`Status element with ID ${statusElementId} not found`, true);
      throw new Error('Status element not found');
    }
    
    const statusMessage = statusElement.querySelector('.status-message');
    const detailsDiv = statusElement.querySelector('.details');
    
    if (!statusMessage || !detailsDiv) {
      addDebugMessage('Status message or details div not found', true);
      throw new Error('Required elements not found in status container');
    }
    
    const client = serviceClient || supabaseClient;
    
    // Truy vấn kiểm tra participant
    addDebugMessage(`Executing query to check participant...`);
    const { data, error } = await client
      .from('chat_participants')
      .select('*')
      .eq('user_id', userId)
      .eq('chat_room_id', roomId)
      .eq('is_active', true);
    
    if (error) {
      addDebugMessage(`Error in chat_participants query: ${error.message}`, true);
      throw error;
    }
    
    addDebugMessage(`Got response with ${data ? data.length : 0} participants`);
    
    // Lấy thông tin user riêng
    let userEmail = "Unknown user";
    try {
      // Thử lấy từ auth.users
      addDebugMessage(`Fetching user email for ${userId}...`);
      const { data: userData, error: userError } = await client
        .from('auth.users')
        .select('email')
        .eq('id', userId)
        .single();
      
      if (!userError && userData && userData.email) {
        userEmail = userData.email;
        addDebugMessage(`Found user email: ${userEmail}`);
      } else if (userError) {
        addDebugMessage(`Error fetching user email: ${userError.message}`, true);
      }
    } catch (e) {
      addDebugMessage(`Exception fetching user email: ${e.message}`, true);
    }
    
    updateStatus('manage', 'success');
    
    statusElement.style.display = 'block';
    
    if (data && data.length > 0) {
      addDebugMessage(`User ${userNumber} is active in this room - updating UI`);
      
      statusMessage.className = 'status-message success-message';
      statusMessage.textContent = 'Active Participation';
      
      detailsDiv.innerHTML = 
        "<p><strong>Participant ID:</strong> " + data[0].id + "</p>" +
        "<p><strong>Role:</strong> " + (data[0].participant_role || 'Not specified') + "</p>" +
        "<p><strong>Joined:</strong> " + formatDate(data[0].joined_at) + "</p>" +
        "<p><strong>Email:</strong> " + userEmail + "</p>";
    } else {
      addDebugMessage(`User ${userNumber} is NOT active in this room - updating UI`);
      
      statusMessage.className = 'status-message warning-message';
      statusMessage.textContent = 'Not Active in This Room';
      
      detailsDiv.innerHTML = 
        "<p>This user is not currently a participant in this room.</p>" +
        "<p>Use the 'Join Room' button to add this user to the room.</p>";
    }
    
    addDebugMessage(`Participation check complete for User ${userNumber}`);
  } catch (err) {
    addDebugMessage(`Failed to check participation: ${err.message}`, true);
    updateStatus('manage', 'error');
    
    try {
      const statusElement = document.getElementById(statusElementId);
      if (statusElement) {
        statusElement.style.display = 'block';
        
        const statusMessage = statusElement.querySelector('.status-message');
        if (statusMessage) {
          statusMessage.className = 'status-message error-message';
          statusMessage.textContent = 'Error';
        }
        
        const detailsDiv = statusElement.querySelector('.details');
        if (detailsDiv) {
          detailsDiv.innerHTML = "<p>" + err.message + "</p>";
        }
      }
    } catch (uiError) {
      addDebugMessage(`Error updating UI: ${uiError.message}`, true);
    }
  }
}

// Common function to join room
async function joinRoom(userId, roomId, statusElementId, userNumber) {
  try {
    addDebugMessage(`Adding user ${userId} to room ${roomId}...`);
    
    const client = serviceClient || supabaseClient;
    
    // Fetch room info to get organization_id
    let orgId = null;
    try {
      const { data: roomData, error: roomError } = await client
        .from('chat_rooms')
        .select('organization_id')
        .eq('id', roomId)
        .single();
        
      if (!roomError && roomData && roomData.organization_id) {
        orgId = roomData.organization_id;
        addDebugMessage(`Room belongs to organization ${orgId}`);
      }
    } catch (e) {
      addDebugMessage('Could not determine organization ID, continuing without it', true);
    }
    
    // Check if user is already part of the organization
    if (orgId) {
      try {
        const { data: orgMember, error: orgError } = await client
          .from('organization_members')
          .select('*')
          .eq('user_id', userId)
          .eq('organization_id', orgId)
          .maybeSingle();
          
        // If user is not in organization, add them
        if (!orgMember && (!orgError || orgError.code === 'PGRST116')) {
          const { data: newOrgMember, error: insertError } = await client
            .from('organization_members')
            .insert([{
              organization_id: orgId,
              user_id: userId,
              role: 'member'
            }])
            .select();
            
          if (!insertError) {
            addDebugMessage(`Added User ${userNumber} to organization ${orgId}`);
          } else {
            addDebugMessage(`Could not add user to organization: ${insertError.message}`, true);
            // Continue anyway to add to chat room
          }
        }
      } catch (e) {
        addDebugMessage(`Error checking organization membership: ${e.message}`, true);
        // Continue anyway to add to chat room
      }
    }
    
    // First check if user is already in the room
    const { data: existingData, error: checkError } = await client
      .from('chat_participants')
      .select('*')
      .eq('user_id', userId)
      .eq('chat_room_id', roomId);
      
    if (checkError) {
      throw checkError;
    }
    
    let result;
    
    if (existingData && existingData.length > 0) {
      // Update existing participant record to active
      const { data, error } = await client
        .from('chat_participants')
        .update({
          is_active: true,
          left_at: null
        })
        .eq('id', existingData[0].id)
        .select();
        
      if (error) throw error;
      result = data;
      addDebugMessage(`Reactivated existing participant record for User ${userNumber}`);
    } else {
      // Insert new participant record
      const { data, error } = await client
        .from('chat_participants')
        .insert([
          {
            chat_room_id: roomId,
            user_id: userId,
            participant_role: 'member',
            is_active: true,
            preferred_language: 'en' // Default language
          }
        ])
        .select();
        
      if (error) throw error;
      result = data;
      addDebugMessage(`Created new participant record for User ${userNumber}`);
    }
    
    updateStatus('manage', 'success');
    
    const statusElement = document.getElementById(statusElementId);
    const statusMessage = statusElement.querySelector('.status-message');
    const detailsDiv = statusElement.querySelector('.details');
    
    statusElement.style.display = 'block';
    statusMessage.className = 'status-message success-message';
    statusMessage.textContent = 'User added to room successfully';
    
   detailsDiv.innerHTML = 
  "<p><strong>Participant ID:</strong> " + result[0].id + "</p>" +
  "<p><strong>Role:</strong> " + (result[0].participant_role || 'member') + "</p>" +
  "<p><strong>Joined:</strong> " + formatDate(result[0].joined_at || new Date()) + "</p>" +
  (orgId ? "<p><strong>Organization:</strong> " + orgId + "</p>" : "");
    
    // Reload participants data
    document.getElementById('load-participants').click();
    
    // Auto update room ID for WebSocket
    document.getElementById('ws-room-id').value = roomId;
    currentRoomId = roomId;
    
    // Fetch and update user email in chat interface
    if (userNumber === '1') {
      fetchUserEmail(userId, 'user1-email');
      document.getElementById('user1-id').value = userId;
    } else if (userNumber === '2') {
      fetchUserEmail(userId, 'user2-email');
      document.getElementById('user2-id').value = userId;
    }
  } catch (err) {
    updateStatus('manage', 'error');
    addDebugMessage(`Failed to add user to room: ${err.message}`, true);
    
    const statusElement = document.getElementById(statusElementId);
    const statusMessage = statusElement.querySelector('.status-message');
    const detailsDiv = statusElement.querySelector('.details');
    
    statusElement.style.display = 'block';
    statusMessage.className = 'status-message error-message';
    statusMessage.textContent = 'Error';
    detailsDiv.innerHTML = `<p>${err.message}</p>`;
  }
}


// Load Users
document.getElementById('load-users').addEventListener('click', async () => {
  if (!supabaseClient) {
    addDebugMessage('Please connect to Supabase first', true);
    return;
  }
  
  try {
    addDebugMessage('Loading users...');
    
    // Use service client if available for better access
    const client = serviceClient || supabaseClient;
    
    const { data, error } = await client
  .from('user_details') // View đã tạo ra để biểu diễn thông tin người dùng
  .select('*')
  .limit(20);
    if (error) {
      throw error;
    }
    
    updateStatus('db', 'success');
    addDebugMessage(`Loaded ${data.length} users`);
    document.getElementById('users-count').textContent = `${data.length} users loaded`;
    
    // Define columns for users table
    const columns = [
      { field: 'id', label: 'ID' },
      { field: 'email', label: 'Email' },
      { field: 'created_at', label: 'Created At', format: 'date' }
    ];
    
    loadTableData('users-table', data, columns);
    
    // Populate user dropdowns for both User 1 and User 2
    populateDropdown('select-user1', data, 'id', 'email');
    populateDropdown('select-user2', data, 'id', 'email');
    
    // Cache user data for later use
    window.userData = data;
  } catch (err) {
    updateStatus('db', 'error');
    addDebugMessage(`Failed to load users: ${err.message}`, true);
  }
});

// Load Chat Rooms
document.getElementById('load-rooms').addEventListener('click', async () => {
  if (!supabaseClient) {
    addDebugMessage('Please connect to Supabase first', true);
    return;
  }
  
  try {
    addDebugMessage('Loading chat rooms...');
    
    const client = serviceClient || supabaseClient;
    let data, error;
    
    // Try 'chat_rooms' first
    try {
      const response = await client
        .from('chat_rooms')
        .select('*')
        .limit(20);
        
      data = response.data;
      error = response.error;
      
      if (!error && data) {
        addDebugMessage('Successfully loaded rooms from chat_rooms table');
      }
    } catch (e) {
      addDebugMessage('Error with chat_rooms table, trying rooms table...');
    }
    
    // If no data from 'chat_rooms', try 'rooms'
    if (!data || error) {
      const response = await client
        .from('rooms')
        .select('*')
        .limit(20);
        
      data = response.data;
      error = response.error;
      
      if (!error && data) {
        addDebugMessage('Successfully loaded rooms from rooms table');
      }
    }
    
    if (error) {
      throw error;
    }
    
    updateStatus('db', 'success');
    addDebugMessage(`Loaded ${data.length} rooms`);
    document.getElementById('rooms-count').textContent = `${data.length} rooms loaded`;
    
    // Define columns for rooms table
    const columns = [
      { field: 'id', label: 'ID' },
      { field: 'name', label: 'Name' },
      { field: 'room_type', label: 'Type' },
      { field: 'created_at', label: 'Created At', format: 'date' }
    ];
    
    loadTableData('rooms-table', data, columns);
    
    // Update room dropdowns for both User 1 and User 2
    populateDropdown('select-room1', data, 'id', 'name');
    populateDropdown('select-room2', data, 'id', 'name');
    
    // Update room ID field for WebSocket
    if (data && data.length > 0) {
      document.getElementById('ws-room-id').value = data[0].id;
    }
    
    // Cache room data for later use
    window.roomData = data;
  } catch (err) {
    updateStatus('db', 'error');
    addDebugMessage(`Failed to load rooms: ${err.message}`, true);
  }
});
// Kiểm tra đảm bảo các event handlers cho các nút tồn tại
function checkAndAddEventListeners() {
  addDebugMessage('Checking and adding event listeners...');
  
  // Check Participation - User 1
  const checkParticipation1Button = document.getElementById('check-participation1');
  if (checkParticipation1Button) {
    // Xóa event listeners cũ nếu có
    const oldCheck1 = checkParticipation1Button.onclick;
    if (oldCheck1) {
      checkParticipation1Button.removeEventListener('click', oldCheck1);
    }
    
    // Thêm event listener mới
    checkParticipation1Button.addEventListener('click', function() {
      const userId = document.getElementById('select-user1').value;
      const roomId = document.getElementById('select-room1').value;
      
      addDebugMessage(`Check Participation button clicked for User 1 - userId: ${userId}, roomId: ${roomId}`);
      
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      if (!userId || !roomId) {
        addDebugMessage('Please select both user and room', true);
        document.getElementById('participation-status1').style.display = 'block';
        const statusMessage = document.getElementById('participation-status1').querySelector('.status-message');
        if (statusMessage) statusMessage.textContent = 'Please select both user and room';
        const detailsDiv = document.getElementById('participation-status1').querySelector('.details');
        if (detailsDiv) detailsDiv.innerHTML = '<p style="color: orange;"><strong>Error:</strong> Please select both user and room</p>';
        return;
      }
      
      // Gọi hàm kiểm tra tham gia
      checkParticipation(userId, roomId, 'participation-status1', '1');
    });
    
    addDebugMessage('Added event listener to Check Participation button for User 1');
  } else {
    addDebugMessage('Check Participation button for User 1 not found', true);
  }
  
  // Check Participation - User 2
  const checkParticipation2Button = document.getElementById('check-participation2');
  if (checkParticipation2Button) {
    // Xóa event listeners cũ nếu có
    const oldCheck2 = checkParticipation2Button.onclick;
    if (oldCheck2) {
      checkParticipation2Button.removeEventListener('click', oldCheck2);
    }
    
    // Thêm event listener mới
    checkParticipation2Button.addEventListener('click', function() {
      const userId = document.getElementById('select-user2').value;
      const roomId = document.getElementById('select-room2').value;
      
      addDebugMessage(`Check Participation button clicked for User 2 - userId: ${userId}, roomId: ${roomId}`);
      
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      if (!userId || !roomId) {
        addDebugMessage('Please select both user and room', true);
        document.getElementById('participation-status2').style.display = 'block';
        const statusMessage = document.getElementById('participation-status2').querySelector('.status-message');
        if (statusMessage) statusMessage.textContent = 'Please select both user and room';
        const detailsDiv = document.getElementById('participation-status2').querySelector('.details');
        if (detailsDiv) detailsDiv.innerHTML = '<p style="color: orange;"><strong>Error:</strong> Please select both user and room</p>';
        return;
      }
      
      // Gọi hàm kiểm tra tham gia
      checkParticipation(userId, roomId, 'participation-status2', '2');
    });
    
    addDebugMessage('Added event listener to Check Participation button for User 2');
  } else {
    addDebugMessage('Check Participation button for User 2 not found', true);
  }
}

// Gọi hàm đăng ký event listeners khi document đã load
document.addEventListener('DOMContentLoaded', function() {
  checkAndAddEventListeners();
});

// Ngoài ra, chúng ta cũng nên thêm nút này vào sau khi kết nối Supabase thành công
document.getElementById('connect-supabase').addEventListener('click', async () => {
  // [Mã hiện tại để kết nối Supabase]
  
  // Sau khi kết nối thành công:
  checkAndAddEventListeners();
});
    
    // ===== STEP 5: WEBSOCKET CONNECTION =====
    document.getElementById('connect-ws').addEventListener('click', () => {
      const wsUrl = document.getElementById('ws-url').value;
      const roomId = document.getElementById('ws-room-id').value;
      
      if (!wsUrl) {
        addDebugMessage('WebSocket URL is required', true);
        return;
      }
      
      if (!jwtToken) {
        addDebugMessage('JWT Token is required. Please validate a token first.', true);
        return;
      }
      
      connectWebSocket(wsUrl);
    });
    
    function connectWebSocket(serverUrl) {
      try {
        addDebugMessage(`Connecting to WebSocket server at ${serverUrl}...`);
        
        // Disconnect existing connections if any
        if (socket1 && socket1.connected) {
          socket1.disconnect();
        }
        
        if (socket2 && socket2.connected) {
          socket2.disconnect();
        }
        
        // Connect User 1
        socket1 = io(serverUrl, {
          auth: { token: jwtToken }
        });
        
        socket1.on('connect', () => {
          addDebugMessage('User 1 connected to WebSocket server');
          document.getElementById('user1-status').textContent = 'Connected';
          
          // Setup User 1
          const user1Id = document.getElementById('user1-id').value;
          
          socket1.emit('setup', {
            userId: user1Id,
            preferredLanguage: 'vi',
            deviceId: `test-device-user1-${Date.now()}`
          }, (response) => {
            if (response.success) {
              addDebugMessage(`User 1 setup success: ${JSON.stringify(response.data)}`);
            } else {
              addDebugMessage(`User 1 setup failed: ${response.error}`, true);
            }
          });
        });
        
        // Connect User 2
        socket2 = io(serverUrl, {
          auth: { token: jwtToken }
        });
        
        socket2.on('connect', () => {
          addDebugMessage('User 2 connected to WebSocket server');
          document.getElementById('user2-status').textContent = 'Connected';
          
          // Setup User 2
          const user2Id = document.getElementById('user2-id').value;
          
          socket2.emit('setup', {
            userId: user2Id,
            preferredLanguage: 'vi',
            deviceId: `test-device-user2-${Date.now()}`
          }, (response) => {
            if (response.success) {
              addDebugMessage(`User 2 setup success: ${JSON.stringify(response.data)}`);
            } else {
              addDebugMessage(`User 2 setup failed: ${response.error}`, true);
            }
          });
        });
        
        // Message received handlers
  socket1.on('message_received', (data) => {
  addDebugMessage(`User 1 received message DATA: ${JSON.stringify(data)}`);
  
  // Kiểm tra cấu trúc dữ liệu
  if (data && data.message) {
    addDebugMessage(`User 1 processing message: ${JSON.stringify(data.message)}`);
    
    // Kiểm tra nếu có bản dịch trong message_translations
    if (data.message.translations && Array.isArray(data.message.translations)) {
      const userLang = document.getElementById('user1-language').value || 'vi';
      
      // Tìm bản dịch phù hợp với ngôn ngữ người dùng
      const translation = data.message.translations.find(t => 
        t.language === userLang && t.translated_content
      );
      
      // Hiển thị bản dịch nếu có
      if (translation) {
        data.message.translation = {
          language: translation.language,
          content: translation.translated_content
        };
      }
    }
    
    displayMessage('user1-messages', data.message, false);
  } else if (data && data.content) {
    // Trường hợp định dạng khác
    addDebugMessage(`User 1 processing message (alternative format): ${JSON.stringify(data)}`);
    
    // Kiểm tra nếu có bản dịch trong message_translations
    if (data.translations && Array.isArray(data.translations)) {
      const userLang = document.getElementById('user1-language').value || 'vi';
      
      // Tìm bản dịch phù hợp với ngôn ngữ người dùng
      const translation = data.translations.find(t => 
        t.language === userLang && t.translated_content
      );
      
      // Hiển thị bản dịch nếu có
      if (translation) {
        data.translation = {
          language: translation.language,
          content: translation.translated_content
        };
      }
    }
    
    displayMessage('user1-messages', data, false);
  } else {
    addDebugMessage(`User 1 received message but format is unknown: ${JSON.stringify(data)}`, true);
  }
});
        
       socket2.on('message_received', (data) => {
  addDebugMessage(`User 2 received message DATA: ${JSON.stringify(data)}`);
  
  // Kiểm tra cấu trúc dữ liệu
  if (data && data.message) {
    addDebugMessage(`User 2 processing message: ${JSON.stringify(data.message)}`);
    
    // Kiểm tra nếu có bản dịch trong message_translations
    if (data.message.translations && Array.isArray(data.message.translations)) {
      const userLang = document.getElementById('user2-language').value || 'en';
      
      // Tìm bản dịch phù hợp với ngôn ngữ người dùng
      const translation = data.message.translations.find(t => 
        t.language === userLang && t.translated_content
      );
      
      // Hiển thị bản dịch nếu có
      if (translation) {
        data.message.translation = {
          language: translation.language,
          content: translation.translated_content
        };
      }
    }
    
    displayMessage('user2-messages', data.message, false);
  } else if (data && data.content) {
    // Trường hợp định dạng khác
    addDebugMessage(`User 2 processing message (alternative format): ${JSON.stringify(data)}`);
    
    // Kiểm tra nếu có bản dịch trong message_translations
    if (data.translations && Array.isArray(data.translations)) {
      const userLang = document.getElementById('user2-language').value || 'en';
      
      // Tìm bản dịch phù hợp với ngôn ngữ người dùng
      const translation = data.translations.find(t => 
        t.language === userLang && t.translated_content
      );
      
      // Hiển thị bản dịch nếu có
      if (translation) {
        data.translation = {
          language: translation.language,
          content: translation.translated_content
        };
      }
    }
    
    displayMessage('user2-messages', data, false);
  } else {
    addDebugMessage(`User 2 received message but format is unknown: ${JSON.stringify(data)}`, true);
  }
});
        
        // Translation received handlers
        socket1.on('translation_received', (data) => {
          addDebugMessage(`User 1 received translation: ${JSON.stringify(data)}`);
        });
        
        socket2.on('translation_received', (data) => {
          addDebugMessage(`User 2 received translation: ${JSON.stringify(data)}`);
        });
        
        // Error handlers
        socket1.on('error', (error) => {
          addDebugMessage(`User 1 error: ${JSON.stringify(error)}`, true);
        });
        
        socket2.on('error', (error) => {
          addDebugMessage(`User 2 error: ${JSON.stringify(error)}`, true);
        });
        
        // Disconnect handlers
        socket1.on('disconnect', () => {
          addDebugMessage('User 1 disconnected from server');
          document.getElementById('user1-status').textContent = 'Disconnected';
        });
        
        socket2.on('disconnect', () => {
          addDebugMessage('User 2 disconnected from server');
          document.getElementById('user2-status').textContent = 'Disconnected';
        });
        
        updateStatus('ws', 'success');
      } catch (err) {
        updateStatus('ws', 'error');
        addDebugMessage(`Failed to connect to WebSocket: ${err.message}`, true);
      }
    }
    
    // Join room with WebSocket
    document.getElementById('ws-join-room').addEventListener('click', () => {
      const roomId = document.getElementById('ws-room-id').value;
      
      if (!roomId) {
        addDebugMessage('Please enter a Room ID', true);
        return;
      }
      
      if (!socket1 || !socket1.connected || !socket2 || !socket2.connected) {
        addDebugMessage('WebSocket is not connected. Please connect first.', true);
        return;
      }
      
      currentRoomId = roomId;
      
      // User 1 join room
      socket1.emit('join_room', roomId, (response) => {
        if (response.success) {
          addDebugMessage(`User 1 joined room ${roomId}: ${JSON.stringify(response.data.room)}`);
          updateStatus('chat', 'success');
        } else {
          addDebugMessage(`User 1 failed to join room: ${response.error}`, true);
        }
      });
      
      // User 2 join room
      socket2.emit('join_room', roomId, (response) => {
        if (response.success) {
          addDebugMessage(`User 2 joined room ${roomId}: ${JSON.stringify(response.data.room)}`);
          updateStatus('chat', 'success');
        } else {
          addDebugMessage(`User 2 failed to join room: ${response.error}`, true);
        }
      });
    });
    
    // Show/Hide debug console
    document.getElementById('show-debug').addEventListener('click', (event) => {
      const button = event.target;
      const state = button.getAttribute('data-state');
      const debugConsole = document.getElementById('debug-console');
      
      if (state === 'show') {
        debugConsole.style.display = 'none';
        button.setAttribute('data-state', 'hide');
        button.textContent = 'Show Debug Console';
      } else {
        debugConsole.style.display = 'block';
        button.setAttribute('data-state', 'show');
        button.textContent = 'Hide Debug Console';
      }
    });
    
    // ===== STEP 6: TEST CHAT INTERFACE =====
     // Join as participant for User 1
 document.getElementById('user1-join').addEventListener('click', async () => {
      const user1Id = document.getElementById('user1-id').value;
      const roomId = currentRoomId || document.getElementById('ws-room-id').value;
      
      if (!user1Id) {
        addDebugMessage('User 1 ID is required', true);
        return;
      }
      
      if (!roomId) {
        addDebugMessage('Please select a Room ID first', true);
        return;
      }
      
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      try {
        addDebugMessage(`Adding User 1 (${user1Id}) to room ${roomId}...`);
        
        const client = serviceClient || supabaseClient;
        
        // First check if user is already in the room
        const { data: existingData, error: checkError } = await client
          .from('chat_participants')
          .select('*')
          .eq('user_id', user1Id)
          .eq('chat_room_id', roomId);
          
        if (checkError) {
          throw checkError;
        }
        
        let result;
        
        if (existingData && existingData.length > 0) {
          // Update existing participant record to active
          const { data, error } = await client
            .from('chat_participants')
            .update({
              is_active: true,
              left_at: null
            })
            .eq('id', existingData[0].id)
            .select();
            
          if (error) throw error;
          result = data;
          addDebugMessage('User 1: Reactivated existing participant record');
        } else {
          // Insert new participant record
          const { data, error } = await client
            .from('chat_participants')
            .insert([
              {
                chat_room_id: roomId,
                user_id: user1Id,
                participant_role: 'member',
                is_active: true
              }
            ])
            .select();
            
          if (error) throw error;
          result = data;
          addDebugMessage('User 1: Created new participant record');
        }
        
        addDebugMessage('User 1 added to room successfully');
        fetchUserEmail(user1Id, 'user1-email');
      } catch (err) {
        addDebugMessage(`Failed to add User 1 to room: ${err.message}`, true);
      }
    });
    
    // Join as participant for User 2
    document.getElementById('user2-join').addEventListener('click', async () => {
      const user2Id = document.getElementById('user2-id').value;
      const roomId = currentRoomId || document.getElementById('ws-room-id').value;
      
      if (!user2Id) {
        addDebugMessage('User 2 ID is required', true);
        return;
      }
      
      if (!roomId) {
        addDebugMessage('Please select a Room ID first', true);
        return;
      }
      
      if (!supabaseClient) {
        addDebugMessage('Please connect to Supabase first', true);
        return;
      }
      
      try {
        addDebugMessage(`Adding User 2 (${user2Id}) to room ${roomId}...`);
        
        const client = serviceClient || supabaseClient;
        
        // First check if user is already in the room
        const { data: existingData, error: checkError } = await client
          .from('chat_participants')
          .select('*')
          .eq('user_id', user2Id)
          .eq('chat_room_id', roomId);
          
        if (checkError) {
          throw checkError;
        }
        
        let result;
        
        if (existingData && existingData.length > 0) {
          // Update existing participant record to active
          const { data, error } = await client
            .from('chat_participants')
            .update({
              is_active: true,
              left_at: null
            })
            .eq('id', existingData[0].id)
            .select();
            
          if (error) throw error;
          result = data;
          addDebugMessage('User 2: Reactivated existing participant record');
        } else {
          // Insert new participant record
          const { data, error } = await client
            .from('chat_participants')
            .insert([
              {
                chat_room_id: roomId,
                user_id: user2Id,
                participant_role: 'member',
                is_active: true
              }
            ])
            .select();
            
          if (error) throw error;
          result = data;
          addDebugMessage('User 2: Created new participant record');
        }
        
        addDebugMessage('User 2 added to room successfully');
        fetchUserEmail(user2Id, 'user2-email');
      } catch (err) {
        addDebugMessage(`Failed to add User 2 to room: ${err.message}`, true);
      }
    });
    
    // Send message from User 1
    document.getElementById('user1-send').addEventListener('click', () => {
      sendMessageUser1();
    });
    
    document.getElementById('user1-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessageUser1();
      }
    });
    
    // Send message from User 2
    document.getElementById('user2-send').addEventListener('click', () => {
      sendMessageUser2();
    });
    
    document.getElementById('user2-input').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessageUser2();
      }
    });
    
  // Send message function for User 1
function sendMessageUser1() {
  const input = document.getElementById('user1-input');
  const content = input.value.trim();
  const userId = document.getElementById('user1-id').value;
  const language = 'vi'; // Ngôn ngữ mặc định của User 1 là tiếng Việt
  
  if (!content) {
    return;
  }
  
  if (!currentRoomId) {
    addDebugMessage('Please join a room first', true);
    return;
  }
  
  if (!socket1 || !socket1.connected) {
    addDebugMessage('WebSocket connection for User 1 is not established', true);
    return;
  }
  
  addDebugMessage(`User 1 sending message: ${content}`);
  
  socket1.emit('send_message', {
    roomId: currentRoomId,
    content,
    originalLanguage: language
  }, async (response) => {
    if (response && response.success) {
      addDebugMessage('User 1 sent message successfully');
      displayMessage('user1-messages', response.data.message, true);
      input.value = '';
    } else {
      addDebugMessage(`User 1 failed to send message: ${response ? response.error : 'Unknown error'}`, true);
      
      // If socket fails, try to send directly to database
      if (supabaseClient) {
        try {
          const client = serviceClient || supabaseClient;
          const { data, error } = await client
            .from('chat_messages')
            .insert([
              {
                chat_room_id: currentRoomId,
                sender_id: userId,
                content,
                original_language: language,
                sent_at: new Date().toISOString(),
                is_translated: false
              }
            ])
            .select();
            
          if (error) {
            throw error;
          }
          
          addDebugMessage('User 1 message saved directly to database');
          displayMessage('user1-messages', data[0], true);
          input.value = '';
        } catch (err) {
          addDebugMessage(`Failed to save User 1 message to database: ${err.message}`, true);
        }
      }
    }
  });
}

// Send message function for User 2
function sendMessageUser2() {
  const input = document.getElementById('user2-input');
  const content = input.value.trim();
  const userId = document.getElementById('user2-id').value;
  const language = 'en'; // Ngôn ngữ mặc định của User 2 là tiếng Anh
  
  if (!content) {
    return;
  }
  
  if (!currentRoomId) {
    addDebugMessage('Please join a room first', true);
    return;
  }
  
  if (!socket2 || !socket2.connected) {
    addDebugMessage('WebSocket connection for User 2 is not established', true);
    return;
  }
  
  addDebugMessage(`User 2 sending message: ${content}`);
  
  socket2.emit('send_message', {
    roomId: currentRoomId,
    content,
    originalLanguage: language
  }, async (response) => {
    if (response && response.success) {
      addDebugMessage('User 2 sent message successfully');
      displayMessage('user2-messages', response.data.message, true);
      input.value = '';
    } else {
      addDebugMessage(`User 2 failed to send message: ${response ? response.error : 'Unknown error'}`, true);
      
      // If socket fails, try to send directly to database
      if (supabaseClient) {
        try {
          const client = serviceClient || supabaseClient;
          const { data, error } = await client
            .from('chat_messages')
            .insert([
              {
                chat_room_id: currentRoomId,
                sender_id: userId,
                content,
                original_language: language,
                sent_at: new Date().toISOString(),
                is_translated: false
              }
            ])
            .select();
            
          if (error) {
            throw error;
          }
          
          addDebugMessage('User 2 message saved directly to database');
          displayMessage('user2-messages', data[0], true);
          input.value = '';
        } catch (err) {
          addDebugMessage(`Failed to save User 2 message to database: ${err.message}`, true);
        }
      }
    }
  });
}
    
    // Display message in chat window
function displayMessage(containerId, message, isSent) {
  const container = document.getElementById(containerId);
  const messageDiv = document.createElement('div');
  messageDiv.className = `message ${isSent ? 'sent' : 'received'}`;
  
  // Xử lý ID tin nhắn nếu có
  if (message.id) {
    messageDiv.dataset.messageId = message.id;
  }
  
  // Message content - kiểm tra định dạng
  const contentDiv = document.createElement('div');
  // Kiểm tra nếu content nằm trực tiếp hay là property
  const messageContent = message.content || (typeof message === 'string' ? message : JSON.stringify(message));
  contentDiv.textContent = messageContent;
  messageDiv.appendChild(contentDiv);
  
  // Message metadata
  const metaDiv = document.createElement('div');
  metaDiv.className = 'message-meta';
  
  // Xác định ngôn ngữ và thời gian
  const language = message.original_language || message.originalLanguage || 'unknown';
  const time = message.sent_at || message.timestamp || new Date().toISOString();
  
  metaDiv.textContent = `[${language}] ${new Date(time).toLocaleTimeString()}`;
  messageDiv.appendChild(metaDiv);
  
  container.appendChild(messageDiv);
  container.scrollTop = container.scrollHeight;
  
  // Log để debug
  console.log(`Message displayed in ${containerId}:`, messageContent);
}
    // Initialize the page
document.addEventListener('DOMContentLoaded', async () => {
  // Khởi tạo các biến theo dõi trạng thái API
  window.translationEnabled = false;
  
  // Check if user2 ID is filled
  const user2Id = document.getElementById('user2-id').value;
  if (user2Id) {
    fetchUserEmail(user2Id, 'user2-email');
  }
  
  // Set default room ID
  if (window.roomData && window.roomData.length > 0) {
    document.getElementById('ws-room-id').value = window.roomData[0].id;
    currentRoomId = window.roomData[0].id;
  }
  
  // Initial tab setup for User Management
  document.querySelectorAll('.tab-btn[data-tab^="user"]').forEach(button => {
    button.addEventListener('click', (e) => {
      const tabId = e.target.getAttribute('data-tab');
      
      // Deactivate all tabs
      document.querySelectorAll('.tab-btn[data-tab^="user"]').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelectorAll('.tab-panel[id$="manage-panel"]').forEach(panel => {
        panel.classList.remove('active');
      });
      
      // Activate selected tab
      e.target.classList.add('active');
      document.getElementById(`${tabId}-panel`).classList.add('active');
    });
  });
});
// ===== STEP 7: TRANSLATION API =====
document.getElementById('check-api-status').addEventListener('click', async () => {
  const apiKey = document.getElementById('api-key').value.trim();
  const statusElement = document.getElementById('api-status-result');
  const statusMessage = statusElement.querySelector('.status-message');
  const detailsDiv = statusElement.querySelector('.details');
  
  try {
    addDebugMessage('Kiểm tra kết nối API MyMemory...');
    statusElement.style.display = 'block';
    statusMessage.textContent = 'Đang kiểm tra...';
    statusMessage.className = 'status-message';
    detailsDiv.textContent = '';
    
    // Gọi API với một cụm từ đơn giản để kiểm tra
    const testText = 'Hello';
    const langPair = 'en|vi'; // Từ tiếng Anh sang tiếng Việt
    
    let apiUrl = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(testText)}&langpair=${langPair}`;
    
    if (apiKey) {
      apiUrl += `&key=${apiKey}`;
    }
    
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.responseStatus === 200) {
      updateStatus('translation-api', 'success');
      statusMessage.textContent = 'API MyMemory hoạt động tốt!';
      statusMessage.className = 'status-message success-message';
      
      // Hiển thị kết quả
      let quotaInfo = '';
      if (data.responseData && data.responseData.translatedText) {
        if (apiKey) {
          quotaInfo = `<p><strong>Loại tài khoản:</strong> API Key</p>`;
        } else {
          quotaInfo = `<p><strong>Loại tài khoản:</strong> Miễn phí</p>
                      <p><small>Lưu ý: Giới hạn API miễn phí là 5000 từ/ngày.</small></p>`;
        }
        
        detailsDiv.innerHTML = `
          <p><strong>Bản dịch thử nghiệm:</strong></p>
          <p>"${testText}" → "${data.responseData.translatedText}"</p>
          ${quotaInfo}
          <p><strong>Thời gian phản hồi:</strong> ${(Date.now() - startTime) / 1000}s</p>
        `;
      }
      
      addDebugMessage('Kết nối API MyMemory thành công');
      
      // Kích hoạt các tùy chọn ngôn ngữ
      enableLanguageSelectors();
    } else {
      throw new Error(data.responseDetails || 'API responded with an error');
    }
  } catch (err) {
    updateStatus('translation-api', 'error');
    addDebugMessage(`Lỗi kết nối API MyMemory: ${err.message}`, true);
    
    statusMessage.textContent = 'Lỗi kết nối API';
    statusMessage.className = 'status-message error-message';
    detailsDiv.innerHTML = `<p>${err.message}</p>
                          <p>Vui lòng kiểm tra kết nối internet hoặc thử lại sau.</p>`;
  }
});

// Biến để theo dõi thời gian bắt đầu request API
let startTime = 0;

// Trước khi gửi request, lưu thời gian bắt đầu
function checkApiConnection() {
  startTime = Date.now();
  // ... phần còn lại
}

// Kích hoạt các tùy chọn ngôn ngữ
function enableLanguageSelectors() {
  const languageSelectors = document.querySelectorAll('.language-selector');
  languageSelectors.forEach(selector => {
    selector.disabled = false;
  });
  addDebugMessage('Đã kích hoạt các tùy chọn ngôn ngữ');
}
// ===== TRANSLATION FUNCTIONS =====
// Cấu hình API MyMemory
const translationConfig = {
  apiBaseUrl: 'https://api.mymemory.translated.net/get',
  apiKey: '', // Sẽ được cập nhật khi người dùng nhập
  requestDelay: 1000, // Độ trễ giữa các request để tránh giới hạn API
};

// Hàm dịch văn bản
async function translateText(text, fromLang, toLang) {
  try {
    if (!text || fromLang === toLang) {
      return text; // Không cần dịch nếu ngôn ngữ giống nhau
    }

    // Sử dụng API key nếu có
    const apiKey = document.getElementById('api-key').value.trim() || translationConfig.apiKey;
    const langPair = `${fromLang}|${toLang}`;
    
    let apiUrl = `${translationConfig.apiBaseUrl}?q=${encodeURIComponent(text)}&langpair=${langPair}`;
    
    if (apiKey) {
      apiUrl += `&key=${apiKey}`;
    }
    
    addDebugMessage(`Translating from ${fromLang} to ${toLang}: "${text.substring(0, 30)}${text.length > 30 ? '...' : ''}"`);
    
    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.responseStatus === 200 && data.responseData) {
      addDebugMessage(`Translation successful`);
      return data.responseData.translatedText;
    } else {
      throw new Error(data.responseDetails || 'Unknown translation error');
    }
  } catch (err) {
    addDebugMessage(`Translation error: ${err.message}`, true);
    return `[Error: ${err.message}]`;
  }
}

// Cập nhật hàm hiển thị tin nhắn để hỗ trợ dịch thuật
function displayMessage(containerId, message, isSent) {
  const container = document.getElementById(containerId);
  const messageDiv = document.createElement('div');
  messageDiv.className = `message ${isSent ? 'sent' : 'received'}`;
  
  // Xử lý ID tin nhắn nếu có
  if (message.id) {
    messageDiv.dataset.messageId = message.id;
  }
  
  // Message content - kiểm tra định dạng
  const contentDiv = document.createElement('div');
  // Kiểm tra nếu content nằm trực tiếp hay là property
  const messageContent = message.content || (typeof message === 'string' ? message : JSON.stringify(message));
  contentDiv.textContent = messageContent;
  contentDiv.className = 'original-content';
  messageDiv.appendChild(contentDiv);
  
  // Tạo phần hiển thị bản dịch (ban đầu trống)
  const translatedDiv = document.createElement('div');
  translatedDiv.className = 'translated-content';
  translatedDiv.style.display = 'none'; // Ẩn cho đến khi có bản dịch
  messageDiv.appendChild(translatedDiv);
  
  // Message metadata
  const metaDiv = document.createElement('div');
  metaDiv.className = 'message-meta';
  
  // Xác định ngôn ngữ và thời gian
  const language = message.original_language || message.originalLanguage || 'unknown';
  const time = message.sent_at || message.timestamp || new Date().toISOString();
  
  metaDiv.textContent = `[${language}] ${new Date(time).toLocaleTimeString()}`;
  messageDiv.appendChild(metaDiv);
  
  container.appendChild(messageDiv);
  container.scrollTop = container.scrollHeight;
  
  // Auto translate if needed
  const userNumber = containerId === 'user1-messages' ? '1' : '2';
  const targetLang = document.getElementById(`user${userNumber}-language`).value;
  const sourceLang = language === 'unknown' ? (userNumber === '1' ? 'vi' : 'en') : language;
  
  // Chỉ dịch khi ngôn ngữ nguồn và đích khác nhau
  if (sourceLang !== targetLang) {
    setTimeout(() => {
      translateMessageInUI(messageDiv, messageContent, sourceLang, targetLang);
    }, translationConfig.requestDelay);
  }
  
  return messageDiv;
}

// Hàm dịch tin nhắn trong UI
async function translateMessageInUI(messageElement, text, fromLang, toLang) {
  try {
    const translatedContent = messageElement.querySelector('.translated-content');
    
    // Hiển thị "Đang dịch..."
    translatedContent.textContent = 'Đang dịch...';
    translatedContent.style.display = 'block';
    
    // Thực hiện dịch
    const translation = await translateText(text, fromLang, toLang);
    
    // Hiển thị kết quả
    translatedContent.textContent = translation;
  } catch (error) {
    console.error('Translation error:', error);
    const translatedContent = messageElement.querySelector('.translated-content');
    translatedContent.textContent = `[Lỗi dịch: ${error.message}]`;
    translatedContent.style.display = 'block';
  }
}

// Xử lý thay đổi ngôn ngữ và dịch lại các tin nhắn
document.getElementById('user1-language').addEventListener('change', function() {
  translateAllMessages('user1-messages', this.value);
});

document.getElementById('user2-language').addEventListener('change', function() {
  translateAllMessages('user2-messages', this.value);
});

// Hàm dịch tất cả tin nhắn trong container
async function translateAllMessages(containerId, targetLang) {
  const container = document.getElementById(containerId);
  const messages = container.querySelectorAll('.message');
  const defaultLang = containerId === 'user1-messages' ? 'vi' : 'en';
  
  // Loop qua từng tin nhắn
  messages.forEach((msgElement, index) => {
    const originalContent = msgElement.querySelector('.original-content').textContent;
    const translatedContent = msgElement.querySelector('.translated-content');
    const metaDiv = msgElement.querySelector('.message-meta');
    
    // Lấy ngôn ngữ nguồn từ metadata
    let sourceLang = defaultLang;
    const metaMatch = metaDiv.textContent.match(/\[(.*?)\]/);
    if (metaMatch && metaMatch[1] && metaMatch[1] !== 'unknown') {
      sourceLang = metaMatch[1];
    }
    
    // Nếu ngôn ngữ nguồn và đích giống nhau, ẩn bản dịch
    if (sourceLang === targetLang) {
      translatedContent.style.display = 'none';
    } else {
      // Dịch với độ trễ để tránh giới hạn API
      setTimeout(() => {
        translateMessageInUI(msgElement, originalContent, sourceLang, targetLang);
      }, index * translationConfig.requestDelay);
    }
  });
  
  addDebugMessage(`Đang dịch ${messages.length} tin nhắn từ ${containerId} sang ${targetLang}`);
}

// Cập nhật API key khi người dùng nhập
document.getElementById('api-key').addEventListener('change', function() {
  translationConfig.apiKey = this.value.trim();
  addDebugMessage('Đã cập nhật API key');
});
  </script>
</body>
</html>