@echo off
REM Script kích hoạt license cho hệ thống LoaLoa On-Premise

REM Kiểm tra nếu đã nhập license key
if "%~1"=="" (
    echo Sử dụng: %0 ^<license_key^> ^<customer_name^> ^<email^>
    echo Ví dụ: %0 LLHM-ABCDE-12345-FGHIJ-67890 "Resort ABC" <EMAIL>
    exit /b 1
)

set LICENSE_KEY=%~1
set CUSTOMER_NAME=%~2
set EMAIL=%~3

REM Kiểm tra container admin-portal đang chạy
docker ps | findstr "loaloa-admin-portal" >nul
if %ERRORLEVEL% NEQ 0 (
    echo Container admin-portal không hoạt động. Vui lòng chạy install.bat trước.
    exit /b 1
)

REM Tạo file cấu hình license
echo Đang kích hoạt license...
docker exec loaloa-admin-portal sh -c "echo '{\"licenseKey\":\"%LICENSE_KEY%\",\"customerName\":\"%CUSTOMER_NAME%\",\"email\":\"%EMAIL%\"}' > /app/license_config.json"

echo License đã được kích hoạt!
echo Vui lòng truy cập Admin Portal tại http://localhost:3000 để xác nhận kích hoạt thành công.