import { createClient } from '../../../../lib/supabase/server';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Hàm đọc tenant_id từ file license_config.json
function getTenantIdFromConfig() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Lấy tenant_id từ config file
    const tenant_id = getTenantIdFromConfig();
    
    if (!tenant_id) {
      return NextResponse.json({
        error: 'Không thể tìm thấy Tenant ID. Vui lòng kích hoạt license.'
      }, { status: 400 });
    }
    
    // Truy vấn khu vực theo ID và tenant_id
    const { data, error } = await supabase
      .from('tenant_areas')
      .select('*')
      .eq('id', params.id)
      .eq('tenant_id', tenant_id)  // Thêm điều kiện tenant_id
      .single();
    
    if (error) {
      console.error('Error fetching area:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    if (!data) {
      return NextResponse.json({ error: 'Area not found' }, { status: 404 });
    }
    
    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    const body = await request.json();
    
    // Lấy tenant_id từ config file
    const tenant_id = getTenantIdFromConfig();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Không thể tìm thấy Tenant ID. Vui lòng kích hoạt license.' }, { status: 400 });
    }

    const { 
      name, 
      area_type, 
      floor, 
      location, 
      description, 
      staff_count, 
      opening_hours, 
      closing_hours, 
      image_url, 
      is_active,
      reception_point_id // Thêm reception_point_id
    } = body;

    // Kiểm tra khu vực tồn tại và thuộc tenant này
    const { data: existingArea, error: checkError } = await supabase
      .from('tenant_areas')
      .select('id')
      .eq('id', params.id)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError || !existingArea) {
      return NextResponse.json({ error: 'Khu vực không tồn tại hoặc không thuộc tenant này' }, { status: 404 });
    }

    // Kiểm tra reception_point_id nếu được cung cấp
    if (reception_point_id) {
      const { data: receptionPoint, error: rpError } = await supabase
        .from('tenant_message_reception_points')
        .select('id')
        .eq('id', reception_point_id)
        .eq('tenant_id', tenant_id)
        .single();

      if (rpError || !receptionPoint) {
        return NextResponse.json({ error: 'Reception point không tồn tại hoặc không thuộc tenant này' }, { status: 400 });
      }
    }

    // Cập nhật khu vực
    const { data, error } = await supabase
      .from('tenant_areas')
      .update({ 
        name, 
        area_type, 
        floor, 
        location, 
        description, 
        staff_count, 
        opening_hours, 
        closing_hours, 
        image_url, 
        is_active,
        reception_point_id: reception_point_id || null // Thêm trường reception_point_id
      })
      .eq('id', params.id)
      .eq('tenant_id', tenant_id)
      .select();

    if (error) {
      console.error('Error updating area:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data: data[0] });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Lấy tenant_id từ config file
    const tenant_id = getTenantIdFromConfig();
    
    if (!tenant_id) {
      return NextResponse.json({
        error: 'Không thể tìm thấy Tenant ID. Vui lòng kích hoạt license.'
      }, { status: 400 });
    }
    
    // Kiểm tra khu vực tồn tại và thuộc tenant này
    const { data: existingArea, error: checkError } = await supabase
      .from('tenant_areas')
      .select('id')
      .eq('id', params.id)
      .eq('tenant_id', tenant_id)
      .single();
    
    if (checkError || !existingArea) {
      return NextResponse.json({
        error: 'Khu vực không tồn tại hoặc không thuộc tenant này'
      }, { status: 404 });
    }
    
    // Xóa khu vực
    const { error } = await supabase
      .from('tenant_areas')
      .delete()
      .eq('id', params.id)
      .eq('tenant_id', tenant_id);  // Thêm điều kiện tenant_id
    
    if (error) {
      console.error('Error deleting area:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
