import React from 'react';

interface ArrowIconSvgProps {
  width?: number;
  height?: number;
  color?: string;
  [key: string]: any;
}

// SVG icon component
const ArrowIconSvg: React.FC<ArrowIconSvgProps> = ({ width = 24, height = 24, color = '#464646', ...props }) => {
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path 
        d="M5 13h11.17l-4.88 4.88c-.39.39-.39 1.03 0 1.42.39.39 1.02.39 1.41 0l6.59-6.59c.39-.39.39-1.02 0-1.41l-6.58-6.6c-.39-.39-1.02-.39-1.41 0-.39.39-.39 1.02 0 1.41L16.17 11H5c-.55 0-1 .45-1 1s.45 1 1 1z" 
        fill={color} 
      />
    </svg>
  );
};

// Outlined version
const ArrowOutlinedIconSvg: React.FC<ArrowIconSvgProps> = ({ width = 24, height = 24, color = '#464646', ...props }) => {
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path 
        d="M16.01 11H5c-.55 0-1 .45-1 1s.45 1 1 1h11.01v1.79c0 .45.54.67.85.35l2.78-2.79c.19-.2.19-.51 0-.71l-2.78-2.79c-.31-.32-.85-.09-.85.35V11z" 
        fill={color} 
      />
    </svg>
  );
};

// Export the filled version as default
export const ArrowIcon = React.memo(ArrowIconSvg);

// Export the outlined version
export const ArrowOutlinedIcon = React.memo(ArrowOutlinedIconSvg);