import type { Meta, StoryObj } from '@storybook/react';
import { LoginScreen } from './LoginScreen';
import { ChatScreen } from './ChatScreen';
import { HotelListingScreen } from './HotelListingScreen';
import { HotelDetailScreen } from './HotelDetailScreen';

// Create separate Meta for each screen type
const loginMeta = {
  title: 'Screens/Login',
  component: LoginScreen,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['light', 'dark', 'studio'],
      description: 'Theme variant',
    },
  },
} satisfies Meta<typeof LoginScreen>;

export default loginMeta;
type LoginStory = StoryObj<typeof loginMeta>;

export const LoginLight: LoginStory = {
  args: {
    variant: 'light',
  },
};

export const LoginDark: LoginStory = {
  args: {
    variant: 'dark',
  },
};

export const LoginStudio: LoginStory = {
  args: {
    variant: 'studio',
  },
};

// Chat Screen stories
export const ChatScreenLight: StoryObj<typeof ChatScreen> = {
  render: () => <ChatScreen variant="light" />,
  parameters: {
    layout: 'fullscreen',
    storySource: {
      source: '<ChatScreen variant="light" />',
    },
  },
};

export const ChatScreenDark: StoryObj<typeof ChatScreen> = {
  render: () => <ChatScreen variant="dark" />,
  parameters: {
    layout: 'fullscreen',
    storySource: {
      source: '<ChatScreen variant="dark" />',
    },
  },
};

export const ChatScreenStudio: StoryObj<typeof ChatScreen> = {
  render: () => <ChatScreen variant="studio" />,
  parameters: {
    layout: 'fullscreen',
    storySource: {
      source: '<ChatScreen variant="studio" />',
    },
  },
};

// Hotel Listing Screen stories
export const HotelListingLight: StoryObj<typeof HotelListingScreen> = {
  render: () => <HotelListingScreen variant="light" />,
  parameters: {
    layout: 'fullscreen',
    storySource: {
      source: '<HotelListingScreen variant="light" />',
    },
  },
};

export const HotelListingDark: StoryObj<typeof HotelListingScreen> = {
  render: () => <HotelListingScreen variant="dark" />,
  parameters: {
    layout: 'fullscreen',
    storySource: {
      source: '<HotelListingScreen variant="dark" />',
    },
  },
};

export const HotelListingStudio: StoryObj<typeof HotelListingScreen> = {
  render: () => <HotelListingScreen variant="studio" />,
  parameters: {
    layout: 'fullscreen',
    storySource: {
      source: '<HotelListingScreen variant="studio" />',
    },
  },
};

// Hotel Detail Screen stories
export const HotelDetailLight: StoryObj<typeof HotelDetailScreen> = {
  render: () => <HotelDetailScreen variant="light" />,
  parameters: {
    layout: 'fullscreen',
    storySource: {
      source: '<HotelDetailScreen variant="light" />',
    },
  },
};

export const HotelDetailDark: StoryObj<typeof HotelDetailScreen> = {
  render: () => <HotelDetailScreen variant="dark" />,
  parameters: {
    layout: 'fullscreen',
    storySource: {
      source: '<HotelDetailScreen variant="dark" />',
    },
  },
};

export const HotelDetailStudio: StoryObj<typeof HotelDetailScreen> = {
  render: () => <HotelDetailScreen variant="studio" />,
  parameters: {
    layout: 'fullscreen',
    storySource: {
      source: '<HotelDetailScreen variant="studio" />',
    },
  },
};
