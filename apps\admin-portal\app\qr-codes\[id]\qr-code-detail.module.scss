.container {
  padding: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
  
  @media (min-width: 768px) {
    flex-wrap: nowrap;
  }
}

.backButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  text-decoration: none;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  
  &:hover {
    color: #0284c7;
  }
}

.title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #334155;
  margin: 0;
}

.actions {
  display: flex;
  gap: 0.5rem;
}

// Main content layout
.mainContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  
  @media (min-width: 1024px) {
    flex-direction: row;
  }
}

.qrDetails {
  flex: 1;
  order: 2;
  
  @media (min-width: 1024px) {
    order: 1;
  }
}

.qrPreview {
  width: 100%;
  order: 1;
  
  @media (min-width: 1024px) {
    width: 280px;
    order: 2;
  }
}

// Sections
.section {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #334155;
  margin-top: 0;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

// Details grid
.detailsGrid {
  display: grid;
  gap: 1.5rem;
  
  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.detailItem {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 0.375rem;
}

.value {
  font-weight: 500;
  color: #334155;
}

// Status badges
.statusBadge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  
  &.active {
    background-color: #dcfce7;
    color: #16a34a;
  }
  
  &.inactive {
    background-color: #fef3c7;
    color: #d97706;
  }
}

// Department & Reception Point badges
.departmentBadge, .receptionPointBadge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.departmentBadge {
  background-color: #f1f5f9;
  color: #334155;
}

.receptionPointBadge {
  background-color: #f0f9ff;
  color: #0369a1;
  
  &::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-right: 0.5rem;
    background-color: #0ea5e9;
    border-radius: 50%;
  }
}

// Description
.descriptionText {
  white-space: pre-wrap;
  line-height: 1.6;
  color: #334155;
}

// QR Code Preview
.qrImageContainer {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.5rem;
}

.qrImage {
  width: 200px;
  height: 200px;
  margin-bottom: 1.5rem;
}

.downloadButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: #0284c7;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #0369a1;
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
}

// Scan History
.scanHistoryContainer {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.scanHistoryTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #334155;
  margin-top: 0;
  margin-bottom: 1rem;
}

.scanHistoryList {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.scanHistoryItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f5f9;
  color: #64748b;
  font-size: 0.875rem;
  
  &:last-child {
    border-bottom: none;
  }
  
  svg {
    color: #94a3b8;
  }
}

// Loading & Error states
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-left-color: #0284c7;
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
  }
  
  p {
    color: #64748b;
  }
}

.errorActions {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

.notFound {
  text-align: center;
  padding: 4rem 2rem;
  
  h2 {
    font-size: 1.5rem;
    color: #334155;
    margin-bottom: 1rem;
  }
  
  p {
    color: #64748b;
    margin-bottom: 2rem;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}