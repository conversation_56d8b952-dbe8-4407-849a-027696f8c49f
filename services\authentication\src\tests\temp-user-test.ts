import axios from 'axios';

const API_URL = 'http://localhost:3001/api';
let accessToken = '';

async function testTemporaryUserFlow() {
  try {
    console.log('=== Test Temporary User Flow ===\n');
    
    // 1. Đăng nhập với người dùng đã có role admin
    console.log('1. Đang đăng nhập với tài khoản admin...');
    const email = '<EMAIL>'; // Thay bằng email đã đăng ký
    const password = '123';
    
    try {
      const loginResponse = await axios.post(`${API_URL}/auth/login`, {
        email,
        password
      });
      
      if (loginResponse.status !== 200) {
        console.log('⚠️ Đăng nhập không thành công:', loginResponse.data);
        return;
      }
      
      accessToken = loginResponse.data.data.access_token;
      console.log('✅ Đăng nhập thành công! Token nhận được');
      
      // 2. Thử tạo temporary user
      console.log('\n2. Thử tạo temporary user...');
      try {
        const tempUserResponse = await axios.post(
          `${API_URL}/temporary-users`,
          {
            preferred_language: 'vi',
            room_number: '101',
            metadata: { source: 'test' }
          },
          { headers: { Authorization: `Bearer ${accessToken}` } }
        );
        
        console.log('✅ Tạo temporary user thành công:');
        console.log('Temporary User ID:', tempUserResponse.data.data.temporary_user.id);
        console.log('QR Code ID:', tempUserResponse.data.data.temporary_user.qr_code_id);
        
        // Hiển thị QR code URL nếu có
        if (tempUserResponse.data.data?.qr_code_url) {
          console.log('\nQR Code URL được tạo thành công (URL dài nên không hiển thị toàn bộ)');
          console.log('QR Code URL (phần đầu):', tempUserResponse.data.data.qr_code_url.substring(0, 50) + '...');
        }
        
        const tempUserId = tempUserResponse.data.data.temporary_user.id;
        const qrCodeId = tempUserResponse.data.data.temporary_user.qr_code_id;
        
        // 3. Thử kích hoạt temporary user (giả lập quét QR)
        console.log('\n3. Thử kích hoạt temporary user (mô phỏng quét QR)...');
        console.log('Lưu ý: Trong thực tế, QR code chứa token JWT, không phải QR code ID trực tiếp');
        console.log('Bước này cần token JWT từ QR nên có thể không thành công trong test đơn giản này');
        
        try {
          // Đây chỉ là mô phỏng, trong thực tế token sẽ lấy từ QR code
          // Tạo một "fake token" để test - không hoạt động trong thực tế
          const fakeQrToken = 'test_token_' + qrCodeId;
          const deviceId = 'test_device_' + Date.now();
          
          const activateResponse = await axios.post(
            `${API_URL}/temporary-users/activate/${fakeQrToken}`,
            { device_id: deviceId }
          );
          
          console.log('✅ Kích hoạt temporary user thành công:', activateResponse.data);
        } catch (activateError: any) {
          console.log('⚠️ Lỗi khi kích hoạt temporary user (dự kiến do không có token JWT thực):');
          if (activateError.response) {
            console.log('Status:', activateError.response.status);
            console.log('Data:', activateError.response.data);
          } else {
            console.log('Error:', activateError.message);
          }
        }
        
        // 4. Thử lấy thông tin temporary user bằng device ID
        console.log('\n4. Trong ứng dụng thực, sau khi kích hoạt, temporary user có thể được truy cập qua device ID');
        
      } catch (error: any) {
        console.log('⚠️ Lỗi khi tạo temporary user:');
        
        if (error.response) {
          console.log('Status:', error.response.status);
          console.log('Data:', error.response.data);
          
          // Nếu lỗi là do không có quyền
          if (error.response.status === 403) {
            console.log('\nBạn không có quyền tạo temporary user.');
            console.log('Hãy đảm bảo đã cập nhật role thành admin hoặc staff!');
            console.log('Sử dụng SQL Editor trong Supabase để chạy:');
            console.log(`
-- Lấy role_id của admin
SELECT id FROM roles WHERE name = 'admin';

-- Thêm role admin cho user (thay YOUR_USER_ID và ADMIN_ROLE_ID)
INSERT INTO user_roles (user_id, role_id) 
VALUES ('YOUR_USER_ID', 'ADMIN_ROLE_ID');
            `);
          }
        } else {
          console.log('Error:', error.message);
        }
      }
    } catch (loginError: any) {
      console.log('⚠️ Lỗi khi đăng nhập:');
      
      if (loginError.response) {
        console.log('Status:', loginError.response.status);
        console.log('Data:', loginError.response.data);
        
        // Nếu không tìm thấy người dùng
        if (loginError.response.status === 401) {
          console.log('\nEmail không tồn tại hoặc mật khẩu không đúng.');
          console.log('Hãy đảm bảo đã đăng ký người dùng và cập nhật email trong file test.');
          
          console.log('\nThử đăng ký người dùng mới...');
          
          const timestamp = new Date().getTime();
          const newEmail = `admin_${timestamp}@example.com`;
          
          try {
            const registerResponse = await axios.post(`${API_URL}/auth/register`, {
              email: newEmail,
              password,
              full_name: 'Admin Test User'
            });
            
            if (registerResponse.status === 201) {
              const userId = registerResponse.data.data.id;
              console.log(`✅ Đăng ký thành công! User ID: ${userId}`);
              console.log(`\nHãy cập nhật file test với email mới: ${newEmail}`);
              console.log('Và thêm role admin cho user mới này bằng SQL:');
              console.log(`
-- Lấy role_id của admin
SELECT id FROM roles WHERE name = 'admin';

-- Thêm role admin cho user
INSERT INTO user_roles (user_id, role_id) 
VALUES ('${userId}', 'ADMIN_ROLE_ID');
              `);
            }
          } catch (regError) {
            console.log('⚠️ Lỗi khi đăng ký người dùng mới:', regError);
          }
        }
      } else {
        console.log('Error:', loginError.message);
      }
    }
    
  } catch (error: any) {
    console.error('Lỗi không xác định:', error.message);
  }
}

// Chạy test
testTemporaryUserFlow();
