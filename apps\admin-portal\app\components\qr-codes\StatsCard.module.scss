.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.iconContainer {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: #eff6ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  flex-shrink: 0;
}

.content {
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 4px;
}

.valueContainer {
  display: flex;
  align-items: baseline;
  gap: 8px;
  flex-wrap: wrap;
}

.value {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
}

.trend {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  font-weight: 500;

  &.up {
    color: #16a34a;
  }

  &.down {
    color: #dc2626;
  }

  &.neutral {
    color: #6b7280;
  }
}

.loading {
  height: 24px;
  display: flex;
  align-items: center;
}

.skeleton {
  width: 80px;
  height: 24px;
  background: linear-gradient(90deg, #f3f4f6 0%, #e5e7eb 50%, #f3f4f6 100%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@media (max-width: 480px) {
  .card {
    padding: 12px;
  }

  .iconContainer {
    width: 40px;
    height: 40px;
    
    svg {
      width: 20px;
      height: 20px;
    }
  }

  .value {
    font-size: 20px;
  }
}
