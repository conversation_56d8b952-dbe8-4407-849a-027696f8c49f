.container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 160px;
  
  label {
    font-size: 12px;
    color: #6b7280;
  }
}

.select {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  color: #1f2937;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.5 4.5L6 8L9.5 4.5' stroke='%236B7280' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 32px;
  
  &:focus {
    outline: none;
    border-color: #93c5fd;
    ring: 2px solid #eff6ff;
  }
  
  &:hover {
    border-color: #d1d5db;
  }
}

@media (max-width: 640px) {
  .container {
    flex-direction: column;
    width: 100%;
  }
  
  .filterGroup {
    width: 100%;
  }
  
  .select {
    width: 100%;
  }
}
.filters {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  
  @media (min-width: 768px) {
    flex-direction: row;
    align-items: flex-end;
  }
}

.searchContainer {
  position: relative;
  flex: 1;
  
  .searchIcon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.25rem;
    height: 1.25rem;
    color: #6b7280;
  }
  
  .searchInput {
    padding-left: 2.5rem;
  }
}

.selectFilters {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  
  @media (min-width: 768px) {
    flex-wrap: nowrap;
  }
}

.filterGroup {
  display: flex;
  flex-direction: column;
  min-width: 150px;
  
  label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
  }
}
