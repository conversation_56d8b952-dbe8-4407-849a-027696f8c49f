import { NextRequest, NextResponse } from 'next/server';
import { validateLicense, getLicenseConfig } from '@loaloa/license-client';

export async function GET(request: NextRequest) {
  try {
    // Get license config using shared license client
    const licenseConfig = getLicenseConfig();
    
    if (!licenseConfig || !licenseConfig.licenseKey || !licenseConfig.tenant_id) {
      return NextResponse.json({ 
        error: 'License not activated',
        licenseKey: licenseConfig?.licenseKey || '',
        customerName: licenseConfig?.customerName || ''
      }, { status: 400 });
    }

    // Validate license using shared license client
    const validationResult = await validateLicense();

    if (!validationResult.success) {
      return NextResponse.json({
        error: validationResult.error || 'License validation failed',
        licenseKey: licenseConfig.licenseKey,
        customerName: licenseConfig.customerName
      }, { status: 400 });
    }

    // Return successful validation
    return NextResponse.json({
      success: true,
      licenseKey: validationResult.licenseKey,
      customerName: validationResult.customerName,
      product_id: validationResult.product_id,
      tenant_id: validationResult.tenant_id,
      issueDate: validationResult.issueDate,
      expiryDate: validationResult.expiryDate,
      isActive: validationResult.isActive,
      daysRemaining: validationResult.daysRemaining
    });

  } catch (error) {
    console.error('Error checking license:', error);
    return NextResponse.json({ 
      error: 'An error occurred while checking license' 
    }, { status: 500 });
  }
}
