[{"table_name": "tenant_areas", "constraint_name": "tenant_areas_qr_code_id_fkey", "column_name": "qr_code_id", "foreign_table_name": "tenant_qr_codes", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_areas", "constraint_name": "tenant_areas_reception_point_id_fkey", "column_name": "reception_point_id", "foreign_table_name": "tenant_message_reception_points", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_areas", "constraint_name": "tenant_areas_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_chat_messages", "constraint_name": "tenant_chat_messages_chat_session_id_fkey", "column_name": "chat_session_id", "foreign_table_name": "tenant_chat_sessions", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_chat_routing_rules", "constraint_name": "fk_tenant_chat_routing_rules_reception_point", "column_name": "target_reception_point_id", "foreign_table_name": "tenant_message_reception_points", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_chat_routing_rules", "constraint_name": "tenant_chat_routing_rules_target_reception_point_id_fkey", "column_name": "target_reception_point_id", "foreign_table_name": "tenant_message_reception_points", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_chat_routing_rules", "constraint_name": "tenant_chat_routing_rules_target_user_id_fkey", "column_name": "target_user_id", "foreign_table_name": "tenant_users", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_chat_routing_rules", "constraint_name": "tenant_chat_routing_rules_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_chat_session_assignments", "constraint_name": "tenant_chat_session_assignments_assigned_user_id_fkey", "column_name": "assigned_user_id", "foreign_table_name": "tenant_users", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_chat_session_assignments", "constraint_name": "tenant_chat_session_assignments_chat_session_id_fkey", "column_name": "chat_session_id", "foreign_table_name": "tenant_chat_sessions", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_chat_session_assignments", "constraint_name": "tenant_chat_session_assignments_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_chat_sessions", "constraint_name": "fk_tenant_chat_sessions_reception_point", "column_name": "reception_point_id", "foreign_table_name": "tenant_message_reception_points", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_chat_sessions", "constraint_name": "tenant_chat_sessions_reception_point_id_fkey", "column_name": "reception_point_id", "foreign_table_name": "tenant_message_reception_points", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_chat_sessions", "constraint_name": "tenant_chat_sessions_source_qr_code_id_fkey", "column_name": "source_qr_code_id", "foreign_table_name": "tenant_qr_codes", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_guests", "constraint_name": "tenant_guests_qr_code_id_fkey", "column_name": "qr_code_id", "foreign_table_name": "tenant_qr_codes", "foreign_column_name": "id", "constraint_comment": "<PERSON>ên kết khách với mã QR cá nhân của họ nếu có"}, {"table_name": "tenant_guests", "constraint_name": "tenant_guests_room_number_tenant_id_fkey", "column_name": "room_number", "foreign_table_name": "tenant_rooms", "foreign_column_name": "tenant_id", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> kh<PERSON> chỉ có thể check-in vào một phòng có thật trong cùng tenant"}, {"table_name": "tenant_guests", "constraint_name": "tenant_guests_room_number_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenant_rooms", "foreign_column_name": "room_number", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> kh<PERSON> chỉ có thể check-in vào một phòng có thật trong cùng tenant"}, {"table_name": "tenant_guests", "constraint_name": "tenant_guests_room_number_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenant_rooms", "foreign_column_name": "tenant_id", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> kh<PERSON> chỉ có thể check-in vào một phòng có thật trong cùng tenant"}, {"table_name": "tenant_guests", "constraint_name": "tenant_guests_room_number_tenant_id_fkey", "column_name": "room_number", "foreign_table_name": "tenant_rooms", "foreign_column_name": "room_number", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> kh<PERSON> chỉ có thể check-in vào một phòng có thật trong cùng tenant"}, {"table_name": "tenant_guests", "constraint_name": "tenant_guests_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": "<PERSON><PERSON><PERSON> bảo mỗi khách phải thuộc về một tenant hợp lệ trong hệ thống"}, {"table_name": "tenant_message_reception_points", "constraint_name": "tenant_message_reception_points_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_message_reception_points", "constraint_name": "tenant_message_reception_points_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_qr_codes", "constraint_name": "fk_tenant_qr_codes_reception_point", "column_name": "reception_point_id", "foreign_table_name": "tenant_message_reception_points", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_qr_type_id_fkey", "column_name": "qr_type_id", "foreign_table_name": "tenant_qr_code_types", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_reception_point_id_fkey", "column_name": "reception_point_id", "foreign_table_name": "tenant_message_reception_points", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_room_number_tenant_id_fkey", "column_name": "room_number", "foreign_table_name": "tenant_rooms", "foreign_column_name": "room_number", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON>o mã QR chỉ liên kết với một phòng có thật trong cùng tenant"}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_room_number_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenant_rooms", "foreign_column_name": "room_number", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON>o mã QR chỉ liên kết với một phòng có thật trong cùng tenant"}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_room_number_tenant_id_fkey", "column_name": "room_number", "foreign_table_name": "tenant_rooms", "foreign_column_name": "tenant_id", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON>o mã QR chỉ liên kết với một phòng có thật trong cùng tenant"}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_room_number_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenant_rooms", "foreign_column_name": "tenant_id", "constraint_comment": "<PERSON><PERSON><PERSON> b<PERSON>o mã QR chỉ liên kết với một phòng có thật trong cùng tenant"}, {"table_name": "tenant_qr_codes", "constraint_name": "tenant_qr_codes_user_id_fkey", "column_name": "user_id", "foreign_table_name": "tenant_users", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_qr_code_scans", "constraint_name": "tenant_qr_code_scans_guest_id_fkey", "column_name": "guest_id", "foreign_table_name": "tenant_guests", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_qr_code_scans", "constraint_name": "tenant_qr_code_scans_qr_code_id_fkey", "column_name": "qr_code_id", "foreign_table_name": "tenant_qr_codes", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_qr_code_scans", "constraint_name": "tenant_qr_code_scans_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_qr_code_types", "constraint_name": "tenant_qr_code_types_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_rooms", "constraint_name": "tenant_rooms_qr_code_id_fkey", "column_name": "qr_code_id", "foreign_table_name": "tenant_qr_codes", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_rooms", "constraint_name": "tenant_rooms_reception_point_id_fkey", "column_name": "reception_point_id", "foreign_table_name": "tenant_message_reception_points", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_staff_assignments", "constraint_name": "fk_tenant_staff_assignments_reception_point", "column_name": "reception_point_id", "foreign_table_name": "tenant_message_reception_points", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_staff_assignments", "constraint_name": "tenant_staff_assignments_reception_point_id_fkey", "column_name": "reception_point_id", "foreign_table_name": "tenant_message_reception_points", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_staff_assignments", "constraint_name": "tenant_staff_assignments_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_staff_assignments", "constraint_name": "tenant_staff_assignments_user_id_fkey", "column_name": "user_id", "foreign_table_name": "tenant_users", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_translation_cache", "constraint_name": "tenant_translation_cache_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_translation_settings", "constraint_name": "fk_translation_tenant", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_translation_settings", "constraint_name": "tenant_translation_settings_guest_id_fkey", "column_name": "guest_id", "foreign_table_name": "tenant_guests", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_translation_settings", "constraint_name": "tenant_translation_settings_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_translation_settings", "constraint_name": "tenant_translation_settings_user_id_fkey", "column_name": "user_id", "foreign_table_name": "tenant_users", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_typing_status", "constraint_name": "fk_typing_status_session", "column_name": "session_id", "foreign_table_name": "tenant_chat_sessions", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_typing_status", "constraint_name": "fk_typing_status_tenant", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_typing_status", "constraint_name": "tenant_typing_status_session_id_fkey", "column_name": "session_id", "foreign_table_name": "tenant_chat_sessions", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_users", "constraint_name": "tenant_users_tenant_id_fkey", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_users", "constraint_name": "tenant_users_user_id_fkey", "column_name": "user_id", "foreign_table_name": "users", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_users_details", "constraint_name": "tenant_users_details_tenant_user_id_fkey", "column_name": "tenant_user_id", "foreign_table_name": "tenant_users", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_voice_messages", "constraint_name": "fk_voice_messages_message", "column_name": "message_id", "foreign_table_name": "tenant_chat_messages", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_voice_messages", "constraint_name": "fk_voice_messages_tenant", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_web_sessions", "constraint_name": "fk_web_sessions_guest", "column_name": "guest_id", "foreign_table_name": "tenant_guests", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_web_sessions", "constraint_name": "fk_web_sessions_temp_user", "column_name": "temporary_user_id", "foreign_table_name": "temporary_users", "foreign_column_name": "id", "constraint_comment": null}, {"table_name": "tenant_web_sessions", "constraint_name": "fk_web_sessions_tenant", "column_name": "tenant_id", "foreign_table_name": "tenants", "foreign_column_name": "id", "constraint_comment": null}]