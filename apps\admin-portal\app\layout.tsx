import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { TenantProvider } from '../context/TenantContext';

const inter = Inter({ subsets: ['latin', 'vietnamese'] });

export const metadata: Metadata = {
  title: 'LoaLoa Admin Portal',
  description: 'Admin Portal cho hệ thống chat và dịch thuật đa ngôn ngữ LoaLoa',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="vi">
      <body className={inter.className}>
        <TenantProvider>
          <main>{children}</main>
        </TenantProvider>
      </body>
    </html>
  );
}
