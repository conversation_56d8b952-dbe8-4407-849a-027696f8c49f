import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import os from 'os';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

// Tạo hardware fingerprint từ thông tin hệ thống
function generateHardwareFingerprint() {
  const cpus = os.cpus();
  const network = os.networkInterfaces();
  const platform = os.platform();
  const release = os.release();
  const hostname = os.hostname();
  
  const systemInfo = JSON.stringify({
    cpuModel: cpus[0]?.model || '',
    cpuCount: cpus.length,
    network: Object.keys(network),
    platform,
    release,
    hostname
  });
  
  return crypto.createHash('sha256').update(systemInfo).digest('hex');
}

// Hàm đọc thông tin license từ file cấu hình cục bộ
// Trong thực tế, bạn sẽ cần một cách an toàn hơn để lưu trữ license key
function getLicenseFromConfig() {
  try {
    const configPath = path.join(process.cwd(), 'license_config.json');
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8');
      return JSON.parse(configData);
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET(request: NextRequest) {
  try {
    const configPath = path.resolve('./license_config.json');
    let licenseConfig;
    
    try {
      if (fs.existsSync(configPath)) {
        const fileContent = fs.readFileSync(configPath, 'utf8');
        licenseConfig = JSON.parse(fileContent);
        console.log('License config loaded:', licenseConfig);
      } else {
        return NextResponse.json({ 
          valid: false, 
          message: 'License config file not found' 
        });
      }
    } catch (error) {
      console.error('Error reading license config:', error);
      return NextResponse.json({ 
        valid: false, 
        message: 'Error reading license configuration' 
      });
    }
    
    if (!licenseConfig?.licenseKey) {
      return NextResponse.json({ 
        valid: false, 
        message: 'License key not found in configuration' 
      });
    }
    
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Kiểm tra license key trong database
    console.log('Querying license with key:', licenseConfig.licenseKey);
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*, tenants(*)')
      .eq('license_key', licenseConfig.licenseKey)
      .single();
    
    if (error || !license) {
      console.error('License validation error:', error);
      return NextResponse.json({ 
        valid: false, 
        message: 'License not found or invalid' 
      });
    }
    
    console.log('License found:', license);
    
    // Nếu license hợp lệ, cập nhật tenant_id vào file config
    let tenant_id = license.tenant_id;
    
    // Nếu license không có tenant_id, tìm hoặc tạo một tenant cho khách hàng này
    if (!tenant_id) {
      // Tìm tenant với tên khách hàng
      const { data: existingTenant } = await supabase
        .from('tenants')
        .select('id')
        .eq('name', licenseConfig.customerName)
        .single();
      
      if (existingTenant) {
        tenant_id = existingTenant.id;
      } else {
        // Tạo tenant mới nếu không tìm thấy
        const { data: newTenant, error: tenantError } = await supabase
          .from('tenants')
          .insert([
            { 
              name: licenseConfig.customerName,
              email: licenseConfig.email,
              max_rooms: license.metadata?.features?.maxRooms || 50,
			      max_users: license.metadata?.features?.maxUsers || 10,
              is_active: true
            }
          ])
          .select()
          .single();
        
        if (tenantError || !newTenant) {
          console.error('Error creating tenant:', tenantError);
        } else {
          tenant_id = newTenant.id;
          
          // Cập nhật tenant_id vào license
          await supabase
            .from('licenses')
            .update({ tenant_id: newTenant.id })
            .eq('id', license.id);
        }
      }
    }
    
    // Kiểm tra trạng thái active của license
    if (!license.is_active) {
      return NextResponse.json({
        valid: false,
        message: 'License is inactive or revoked',
        revocation_reason: license.revocation_reason
      });
    }
    
    // Kiểm tra thời hạn license
    const currentDate = new Date();
    const expiryDate = new Date(license.expiry_date);
    
    if (currentDate > expiryDate) {
      return NextResponse.json({
        valid: false,
        message: 'License has expired',
        expiry_date: license.expiry_date
      });
    }
    
    // Cập nhật file config với tenant_id
    if (tenant_id) {
      licenseConfig.tenant_id = tenant_id;
      fs.writeFileSync(configPath, JSON.stringify(licenseConfig, null, 2));
      
      // Lưu tenant_id vào cookie để sử dụng trong toàn bộ ứng dụng
      cookieStore.set('currentTenantId', tenant_id, {
        path: '/',
        maxAge: 60 * 60 * 24 * 7, // 7 ngày
        httpOnly: true,
        sameSite: 'strict'
      });
      
      // Lưu tenant name vào cookie nếu có
      if (license.tenants?.name) {
        cookieStore.set('currentTenantName', license.tenants.name, {
          path: '/',
          maxAge: 60 * 60 * 24 * 7,
          httpOnly: true,
          sameSite: 'strict'
        });
      }
    }
    
    // Cập nhật last_check_in và check_in_count
    await supabase
      .from('licenses')
      .update({
        last_check_in: new Date().toISOString(),
        check_in_count: license.check_in_count + 1
      })
      .eq('id', license.id);
    
    // Trả về thông tin license hợp lệ và tenant_id
    return NextResponse.json({
      valid: true,
      license_info: {
        id: license.id,
        license_key: license.license_key,
        customer_name: licenseConfig.customerName,
        issue_date: license.issue_date,
        expiry_date: license.expiry_date,
        tenant_id: tenant_id
      }
    });
  } catch (error) {
    console.error('Unexpected error during license validation:', error);
    return NextResponse.json({
      valid: false,
      message: 'An unexpected error occurred during license validation'
    });
  }
}