import React from "react";
import styles from "./QRCodeStats.module.scss";
import { 
  DocumentDuplicateIcon, 
  QrCodeIcon, 
  ChartBarIcon 
} from "@heroicons/react/24/outline";

interface QRCodeStatsProps {
  totalCount: number;
  totalScans: number;
  todayScans: number;
  percentChange: number;
}

const QRCodeStats: React.FC<QRCodeStatsProps> = ({
  totalCount,
  totalScans,
  todayScans,
  percentChange,
}) => {
  return (
    <div className={styles.statsContainer}>
      <div className={styles.statCard}>
        <div className={styles.statIcon}>
          <QrCodeIcon className={styles.icon} />
        </div>
        <div className={styles.statContent}>
          <span className={styles.statValue}>{totalCount}</span>
          <span className={styles.statTitle}>Total QR Codes</span>
        </div>
      </div>

      <div className={styles.statCard}>
        <div className={styles.statIcon}>
          <DocumentDuplicateIcon className={styles.icon} />
        </div>
        <div className={styles.statContent}>
          <span className={styles.statValue}>{totalScans}</span>
          <span className={styles.statTitle}>Total Scans</span>
        </div>
      </div>

      <div className={styles.statCard}>
        <div className={styles.statIcon}>
          <ChartBarIcon className={styles.icon} />
        </div>
        <div className={styles.statContent}>
          <div className={styles.statWithChange}>
            <span className={styles.statValue}>{todayScans}</span>
            {percentChange !== 0 && (
              <span className={percentChange > 0 ? styles.positive : styles.negative}>
                {percentChange > 0 ? '+' : ''}{percentChange}%
              </span>
            )}
          </div>
          <span className={styles.statTitle}>Today's Scans</span>
        </div>
      </div>
    </div>
  );
};

export default QRCodeStats;