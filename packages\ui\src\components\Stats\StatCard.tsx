import React, { ReactNode } from 'react';
import styles from './StatCard.module.scss';

export interface StatCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  change?: {
    value: number;
    isPositive: boolean;
  };
  footer?: ReactNode;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger';
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  change,
  footer,
  variant = 'default'
}) => {
  return (
    <div className={`${styles.statCard} ${styles[variant]}`}>
      <div className={styles.cardHeader}>
        <h3 className={styles.title}>{title}</h3>
        {icon && <div className={styles.icon}>{icon}</div>}
      </div>
      
      <div className={styles.value}>{value}</div>
      
      {change && (
        <div className={`${styles.change} ${change.isPositive ? styles.positive : styles.negative}`}>
          <span className={styles.arrow}>
            {change.isPositive ? '↑' : '↓'}
          </span>
          {Math.abs(change.value)}%
          <span className={styles.period}> vs last period</span>
        </div>
      )}
      
      {footer && <div className={styles.footer}>{footer}</div>}
    </div>
  );
};
