[
  {
    "oid": 77245,
    "pubname": "supabase_realtime_messages_publication",
    "pubowner": 10,
    "puballtables": false,
    "pubinsert": true,
    "pubupdate": true,
    "pubdelete": true,
    "pubtruncate": true,
    "pubviaroot": false
  },
  {
    "oid": 81672,
    "pubname": "supabase_realtime",
    "pubowner": 16384,
    "puballtables": false,
    "pubinsert": true,
    "pubupdate": true,
    "pubdelete": true,
    "pubtruncate": true,
    "pubviaroot": false
  }
]

[
  {
    "schemaname": "public",
    "tablename": "tenant_chat_sessions"
  },
  {
    "schemaname": "public",
    "tablename": "tenant_chat_messages"
  },
  {
    "schemaname": "public",
    "tablename": "tenant_chat_session_assignments"
  }
]


[
  {
    "schemaname": "public",
    "tablename": "tenant_chat_messages",
    "policyname": "Users can read messages from their tenant",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(tenant_id = ((auth.jwt() ->> 'tenant_id'::text))::uuid)"
  },
  {
    "schemaname": "public",
    "tablename": "tenant_chat_sessions",
    "policyname": "Users can read sessions from their tenant",
    "permissive": "PERMISSIVE",
    "roles": "{public}",
    "cmd": "SELECT",
    "qual": "(tenant_id = ((auth.jwt() ->> 'tenant_id'::text))::uuid)"
  }
]
