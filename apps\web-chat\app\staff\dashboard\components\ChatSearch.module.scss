.chatSearch {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.searchContainer {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  
  .searchInputGroup {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s;
    
    &:focus-within {
      background: white;
      border-color: #f97316;
      box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
    }
    
    .searchIcon {
      padding: 0 12px;
      color: #9ca3af;
      font-size: 14px;
    }
    
    .searchInput {
      flex: 1;
      border: none;
      background: transparent;
      padding: 10px 8px 10px 0;
      font-size: 14px;
      color: #111827;
      outline: none;
      
      &::placeholder {
        color: #9ca3af;
      }
    }
    
    .clearSearch {
      padding: 8px 12px;
      background: none;
      border: none;
      color: #9ca3af;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s;
      
      &:hover {
        color: #6b7280;
        background: #f3f4f6;
        border-radius: 4px;
      }
    }
  }
  
  .searchActions {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .filterButton {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 12px;
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      cursor: pointer;
      font-size: 13px;
      font-weight: 500;
      color: #374151;
      transition: all 0.2s;
      position: relative;
      
      &:hover {
        background: #f3f4f6;
        border-color: #d1d5db;
      }
      
      &.active {
        background: #fef3e2;
        border-color: #fed7aa;
        color: #ea580c;
      }
      
      .filterIcon {
        font-size: 12px;
      }
      
      .filterBadge {
        position: absolute;
        top: -6px;
        right: -6px;
        background: #ef4444;
        color: white;
        font-size: 10px;
        font-weight: 600;
        padding: 2px 5px;
        border-radius: 8px;
        min-width: 16px;
        text-align: center;
      }
    }
    
    .chatCount {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .count {
        font-size: 16px;
        font-weight: 600;
        color: #f97316;
      }
      
      .label {
        font-size: 11px;
        color: #9ca3af;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
    }
  }
}

.filtersPanel {
  background: #fafafa;
  border-top: 1px solid #f3f4f6;
  animation: slideDown 0.2s ease-out;
  
  .filtersHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px 12px 20px;
    
    h4 {
      font-size: 14px;
      font-weight: 600;
      margin: 0;
      color: #374151;
    }
    
    .filtersActions {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .clearFilters {
        font-size: 12px;
        color: #f97316;
        background: none;
        border: none;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s;
        
        &:hover {
          background: #fef3e2;
        }
      }
      
      .closeFilters {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 14px;
        color: #6b7280;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s;
        
        &:hover {
          background: #f3f4f6;
          color: #374151;
        }
      }
    }
  }
  
  .filtersGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    padding: 0 20px 16px 20px;
    
    .filterGroup {
      .filterLabel {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: #6b7280;
        margin-bottom: 6px;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
      
      .filterSelect {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: white;
        font-size: 13px;
        color: #374151;
        cursor: pointer;
        
        &:focus {
          outline: none;
          border-color: #f97316;
          box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }
      }
    }
  }
  
  .quickFilters {
    padding: 16px 20px;
    border-top: 1px solid #e5e7eb;
    
    .filterLabel {
      display: block;
      font-size: 12px;
      font-weight: 500;
      color: #6b7280;
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
    
    .quickFilterButtons {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .quickFilter {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px 10px;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        color: #374151;
        transition: all 0.2s;
        position: relative;
        
        &:hover {
          background: #f9fafb;
          border-color: #d1d5db;
        }
        
        &.active {
          background: #dcfce7;
          border-color: #bbf7d0;
          color: #166534;
        }
        
        .quickFilterIcon {
          font-size: 12px;
        }
        
        .checkIcon {
          font-size: 10px;
          font-weight: 600;
          color: #059669;
        }
      }
    }
  }
  
  .filterSummary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: #f3f4f6;
    border-top: 1px solid #e5e7eb;
    font-size: 12px;
    
    .summaryText {
      color: #6b7280;
      font-weight: 500;
    }
    
    .summaryCount {
      color: #f97316;
      font-weight: 600;
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .searchContainer {
    flex-direction: column;
    gap: 12px;
    
    .searchInputGroup {
      width: 100%;
    }
    
    .searchActions {
      width: 100%;
      justify-content: space-between;
    }
  }
  
  .filtersGrid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .quickFilterButtons {
    justify-content: flex-start;
  }
}