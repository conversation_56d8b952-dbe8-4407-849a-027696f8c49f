'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import styles from './activate.module.css';

export default function ActivateLicense() {
  const [licenseKey, setLicenseKey] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/license/activate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          licenseKey,
          customerName,
          email,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to activate license');
      }

      // Lưu thông tin license vào cookie
      document.cookie = `licenseInfo=${JSON.stringify({
        licenseKey,
        customerName,
        email,
        tenant_id: data.tenant_id
      })}; path=/; max-age=${60 * 60 * 24 * 7}; SameSite=Strict`;

      // Chuyển hướng về dashboard
      router.push('/dashboard');
    } catch (err: any) {
      console.error('License activation error:', err);
      setError(err.message || 'Có lỗi xảy ra khi kích hoạt license');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.activateContainer}>
      <div className={styles.activateCard}>
        <h1 className={styles.activateTitle}>Kích hoạt License</h1>
        
        {error && (
          <div className={styles.error}>{error}</div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className={styles.formGroup}>
            <label htmlFor="licenseKey">License Key</label>
            <input
              type="text"
              id="licenseKey"
              value={licenseKey}
              onChange={(e) => setLicenseKey(e.target.value)}
              placeholder="LLHM-XXXXX-XXXXX-XXXXX-XXXXX"
              className={styles.input}
              required
            />
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="customerName">Tên khách hàng</label>
            <input
              type="text"
              id="customerName"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              placeholder="Tên khách hàng hoặc tên resort/khách sạn"
              className={styles.input}
              required
            />
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Địa chỉ email liên hệ"
              className={styles.input}
              required
            />
          </div>
          
          <button
            type="submit"
            className={styles.activateButton}
            disabled={loading}
          >
            {loading ? 'Đang kích hoạt...' : 'Kích hoạt'}
          </button>
        </form>
      </div>
    </div>
  );
}
