'use client';
import { useState, useEffect } from 'react';
import styles from './UserReceptionPointsList.module.scss';
import { Al<PERSON>, Button } from '@ui';

interface UserReceptionPointsListProps {
  userId: string;
  onReceptionPointsChange?: () => void;
}

export default function UserReceptionPointsList({ 
  userId,
  onReceptionPointsChange 
}: UserReceptionPointsListProps) {
  const [receptionPoints, setReceptionPoints] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchReceptionPoints();
  }, []);

  const fetchReceptionPoints = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/users/${userId}/reception-points`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch reception points');
      }

      const data = await response.json();
      setReceptionPoints(data.data || []);
    } catch (err) {
      console.error('Error fetching reception points:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleSetPrimary = async (linkId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}/reception-points/${linkId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ is_primary: true })
      });

      if (!response.ok) {
        throw new Error('Failed to update reception point');
      }

      // Refresh reception points
      fetchReceptionPoints();
      if (onReceptionPointsChange) {
        onReceptionPointsChange();
      }
    } catch (err) {
      console.error('Error updating reception point:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleUpdatePriority = async (linkId: string, priority: number) => {
    try {
      const response = await fetch(`/api/users/${userId}/reception-points/${linkId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ priority })
      });

      if (!response.ok) {
        throw new Error('Failed to update priority');
      }

      // Refresh reception points
      fetchReceptionPoints();
      if (onReceptionPointsChange) {
        onReceptionPointsChange();
      }
    } catch (err) {
      console.error('Error updating priority:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleRemoveReceptionPoint = async (linkId: string) => {
    if (!confirm('Are you sure you want to remove this reception point?')) {
      return;
    }

    try {
      const response = await fetch(`/api/users/${userId}/reception-points/${linkId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to remove reception point');
      }

      // Refresh reception points
      fetchReceptionPoints();
      if (onReceptionPointsChange) {
        onReceptionPointsChange();
      }
    } catch (err) {
      console.error('Error removing reception point:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <p>Loading reception points...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="error" title="Error" closable onClose={() => setError(null)}>
        {error}
      </Alert>
    );
  }

  if (!receptionPoints.length) {
    return (
      <div className={styles.emptyState}>
        <p>No reception points assigned to this user.</p>
        <p className={styles.emptyStateSubtext}>
          Assign reception points to this user to enable them to receive guest messages from specific areas.
        </p>
      </div>
    );
  }

  return (
    <div className={styles.receptionPointsList}>
      <div className={styles.header}>
        <div className={styles.nameHeader}>Reception Point</div>
        <div className={styles.priorityHeader}>Priority</div>
        <div className={styles.actionHeader}>Actions</div>
      </div>
      
      <ul className={styles.list}>
        {receptionPoints.map((point) => (
          <li key={point.id} className={styles.item}>
            <div className={styles.name}>
              {point.reception_point?.name || 'Unknown'}
              {point.is_primary && (
                <span className={styles.primaryBadge}>Primary</span>
              )}
            </div>
            <div className={styles.priority}>
              <input 
                type="number" 
                min="1"
                max="100"
                value={point.priority || 1}
                onChange={(e) => handleUpdatePriority(point.id, parseInt(e.target.value) || 1)}
                className={styles.priorityInput}
              />
            </div>
            <div className={styles.actions}>
              {!point.is_primary && (
                <button 
                  className={styles.primaryButton}
                  onClick={() => handleSetPrimary(point.id)}
                >
                  Set as Primary
                </button>
              )}
              <button 
                className={styles.removeButton}
                onClick={() => handleRemoveReceptionPoint(point.id)}
              >
                Remove
              </button>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}
