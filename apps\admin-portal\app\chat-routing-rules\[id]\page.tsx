'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '../../dashboard-layout';
import styles from './rule-detail.module.scss';
import { <PERSON><PERSON>, Button } from '@ui';
import DeleteConfirmModal from '../../components/modals/DeleteConfirmModal';

interface RuleDetailParams {
  params: {
    id: string;
  };
}

export default function ChatRoutingRuleDetailPage({ params }: RuleDetailParams) {
  const router = useRouter();
  const ruleId = params.id;
  
  const [rule, setRule] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  
  const [receptionPoint, setReceptionPoint] = useState<any>(null);
  const [loadingReceptionPoint, setLoadingReceptionPoint] = useState(false);

  useEffect(() => {
    const fetchRule = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/chat-routing-rules/${ruleId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Rule not found');
          }
          throw new Error('Failed to fetch rule details');
        }
        
        const data = await response.json();
        setRule(data.data);

        // If there's a reception point, fetch its details
        if (data.data.target_reception_point_id) {
          fetchReceptionPoint(data.data.target_reception_point_id);
        }
      } catch (err) {
        console.error('Error fetching rule:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (ruleId) {
      fetchRule();
    }
  }, [ruleId]);

  const fetchReceptionPoint = async (pointId: string) => {
    try {
      setLoadingReceptionPoint(true);
      
      const response = await fetch(`/api/reception-points/${pointId}`);
      
      if (response.ok) {
        const data = await response.json();
        setReceptionPoint(data.data);
      }
    } catch (err) {
      console.error('Error fetching reception point:', err);
    } finally {
      setLoadingReceptionPoint(false);
    }
  };

  const handleDelete = async () => {
    try {
      setDeleteError(null);
      
      const response = await fetch(`/api/chat-routing-rules/${ruleId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete rule');
      }

      router.push('/chat-routing-rules');
    } catch (err) {
      console.error('Error deleting rule:', err);
      setDeleteError(err instanceof Error ? err.message : 'Failed to delete rule');
    }
  };

  // Format rule type for display
  const formatRuleType = (type: string) => {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format JSON for display
  const formatJSON = (jsonData: any) => {
    return JSON.stringify(jsonData, null, 2);
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading rule details...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className={styles.error}>
          <Alert variant="error" title="Error" closable={false}>
            {error}
          </Alert>
          <Button
            variant="secondary"
            onClick={() => router.push('/chat-routing-rules')}
          >
            Back to Routing Rules
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  if (!rule) return null;

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Link href="/chat-routing-rules" className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M10 15.8333L4.16667 10L10 4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to Routing Rules
          </Link>
          <div className={styles.actions}>
            <Link href={`/chat-routing-rules/${ruleId}/edit`} className={styles.editButton}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M11.3333 2.00001C11.5085 1.82494 11.7163 1.68605 11.9451 1.59129C12.1739 1.49653 12.4187 1.44775 12.6667 1.44775C12.9146 1.44775 13.1595 1.49653 13.3883 1.59129C13.6171 1.68605 13.8248 1.82494 14 2.00001C14.1751 2.17508 14.314 2.38283 14.4088 2.61162C14.5035 2.84041 14.5523 3.08536 14.5523 3.33334C14.5523 3.58132 14.5035 3.82627 14.4088 4.05506C14.314 4.28385 14.1751 4.4916 14 4.66667L5 13.6667L1.33334 14.6667L2.33334 11L11.3333 2.00001Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Edit Rule
            </Link>
            <button onClick={() => setShowDeleteModal(true)} className={styles.deleteButton}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M2 4H3.33333H14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M5.33334 4.00001V2.66667C5.33334 2.31305 5.47381 1.97391 5.7239 1.72386C5.97399 1.47381 6.31313 1.33334 6.66667 1.33334H9.33334C9.68688 1.33334 10.026 1.47381 10.2761 1.72386C10.5262 1.97391 10.6667 2.31305 10.6667 2.66667V4.00001" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12.6667 4V13.3333C12.6667 13.687 12.5262 14.0261 12.2761 14.2761C12.026 14.5262 11.6869 14.6667 11.3333 14.6667H4.66667C4.31305 14.6667 3.9739 14.5262 3.72386 14.2761C3.47381 14.0261 3.33334 13.687 3.33334 13.3333V4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Delete Rule
            </button>
          </div>
        </div>

        <div className={styles.ruleCard}>
          <div className={styles.ruleHeader}>
            <h1 className={styles.ruleName}>{rule.rule_name}</h1>
            <span className={`${styles.statusBadge} ${rule.is_active ? styles.active : styles.inactive}`}>
              {rule.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
          
          <div className={styles.detailsContainer}>
            <div className={styles.section}>
              <h3 className={styles.sectionTitle}>Rule Information</h3>
              <div className={styles.detailsGrid}>
                <div className={styles.detailItem}>
                  <span className={styles.detailLabel}>Rule Type</span>
                  <span className={styles.detailValue}>{formatRuleType(rule.rule_type)}</span>
                </div>
                
                <div className={styles.detailItem}>
                  <span className={styles.detailLabel}>Priority</span>
                  <span className={styles.detailValue}>
                    <span className={styles.priorityBadge}>{rule.priority}</span>
                  </span>
                </div>
                
                {rule.target_department && (
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Department</span>
                    <span className={styles.detailValue}>
                      <span className={styles.departmentBadge}>{rule.target_department}</span>
                    </span>
                  </div>
                )}
                
                {rule.target_user && (
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Staff</span>
                    <div className={styles.userInfo}>
                      <span className={styles.userName}>
                        {rule.target_user.tenant_users_details?.display_name}
                      </span>
                      <span className={styles.userEmail}>
                        {rule.target_user.tenant_users_details?.email}
                      </span>
                    </div>
                  </div>
                )}
                
                {rule.target_reception_point_id && (
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Reception Point</span>
                    <div className={styles.receptionPointInfo}>
                      {loadingReceptionPoint ? (
                        <span className={styles.loading}>Loading...</span>
                      ) : receptionPoint ? (
                        <div>
                          <span className={styles.receptionPointName}>{receptionPoint.name}</span>
                          <span className={styles.receptionPointCode}>{receptionPoint.code}</span>
                        </div>
                      ) : (
                        <span className={styles.notFound}>Reception point not found</span>
                      )}
                    </div>
                  </div>
                )}
                
                <div className={styles.detailItem}>
                  <span className={styles.detailLabel}>Created</span>
                  <span className={styles.detailValue}>{formatDate(rule.created_at)}</span>
                </div>
                
                <div className={styles.detailItem}>
                  <span className={styles.detailLabel}>Updated</span>
                  <span className={styles.detailValue}>{formatDate(rule.updated_at)}</span>
                </div>
              </div>
            </div>
            
            <div className={styles.section}>
              <h3 className={styles.sectionTitle}>Rule Condition</h3>
              <pre className={styles.jsonDisplay}>
                {formatJSON(rule.rule_condition)}
              </pre>
            </div>
          </div>
        </div>
      </div>

      <DeleteConfirmModal
        isOpen={showDeleteModal}
        title="Delete Routing Rule"
        message={`Are you sure you want to delete "${rule.rule_name}"? This action cannot be undone.`}
        onConfirm={handleDelete}
        onCancel={() => {
          setShowDeleteModal(false);
          setDeleteError(null);
        }}
        error={deleteError}
      />
    </DashboardLayout>
  );
}
