.container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.pageButton {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background-color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pageButton:hover:not(:disabled) {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.pageButton.active {
  background-color: #2563eb;
  border-color: #2563eb;
  color: #ffffff;
}

.navButton {
  padding: 0;
  width: 36px;
}

.navButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dots {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  color: #6b7280;
  font-weight: 500;
}
