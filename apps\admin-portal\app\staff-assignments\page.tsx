'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import DashboardLayout from '../dashboard-layout';
import styles from './staff-assignments.module.scss';
import { <PERSON>ton, Alert, Skeleton } from '@ui';
import AssignStaffModal from '../components/staff-assignments/AssignStaffModal';

interface ReceptionPoint {
  id: string;
  name: string;
  code: string;
  description: string;
  assigned_staff_count: number;
  message_count: number;
}

interface Staff {
  id: string;
  user_id: string;
  display_name: string;
  email: string;
  avatar_url: string | null;
  is_assigned: boolean;
  priority?: number;
  is_primary?: boolean;
  assignment_id?: string;
}

export default function StaffAssignmentsPage() {
  const [receptionPoints, setReceptionPoints] = useState<ReceptionPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPointId, setSelectedPointId] = useState<string | null>(null);
  const [staffList, setStaffList] = useState<Staff[]>([]);
  const [loadingStaff, setLoadingStaff] = useState(false);
  const [showAssignStaffModal, setShowAssignStaffModal] = useState(false);
  
  // Fetch reception points
  useEffect(() => {
    const fetchReceptionPoints = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/reception-points/stats');
        
        if (!response.ok) {
          throw new Error('Failed to fetch reception points');
        }
        
        const data = await response.json();
        setReceptionPoints(data.data || []);
        
        // Select first reception point by default
        if (data.data && data.data.length > 0) {
          setSelectedPointId(data.data[0].id);
        }
      } catch (err) {
        console.error('Error fetching reception points:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };
    
    fetchReceptionPoints();
  }, []);
  
  // Fetch staff for selected reception point
  useEffect(() => {
    if (!selectedPointId) return;
    
    const fetchStaffList = async () => {
      try {
        setLoadingStaff(true);
        const response = await fetch(`/api/reception-points/${selectedPointId}/staff`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch staff list');
        }
        
        const data = await response.json();
        setStaffList(data.data || []);
      } catch (err) {
        console.error('Error fetching staff list:', err);
      } finally {
        setLoadingStaff(false);
      }
    };
    
    fetchStaffList();
  }, [selectedPointId]);
  
  const handleSelectReceptionPoint = (id: string) => {
    setSelectedPointId(id);
  };
  
  const handleAssignStaff = () => {
    setShowAssignStaffModal(true);
  };
  
  const handleAssignmentSaved = async () => {
    // Refresh staff list after assignment
    if (selectedPointId) {
      setLoadingStaff(true);
      const response = await fetch(`/api/reception-points/${selectedPointId}/staff`);
      if (response.ok) {
        const data = await response.json();
        setStaffList(data.data || []);
      }
      setLoadingStaff(false);
    }
    
    // Refresh reception points stats
    const response = await fetch('/api/reception-points/stats');
    if (response.ok) {
      const data = await response.json();
      setReceptionPoints(data.data || []);
    }
    
    setShowAssignStaffModal(false);
  };
  
  const handleRemoveAssignment = async (assignmentId: string) => {
    try {
      if (!confirm('Are you sure you want to remove this staff member from this reception point?')) {
        return;
      }
      
      const response = await fetch(`/api/users/reception-points/${assignmentId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to remove staff assignment');
      }
      
      // Refresh staff list
      handleAssignmentSaved();
    } catch (err) {
      console.error('Error removing staff assignment:', err);
      alert('Failed to remove staff assignment');
    }
  };
  
  const handleUpdatePriority = async (assignmentId: string, priority: number) => {
    try {
      const response = await fetch(`/api/users/reception-points/${assignmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ priority }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update priority');
      }
      
      // No need to refresh the entire list, just update the state
      setStaffList(prevStaff => 
        prevStaff.map(staff => 
          staff.assignment_id === assignmentId 
            ? { ...staff, priority } 
            : staff
        )
      );
    } catch (err) {
      console.error('Error updating priority:', err);
      alert('Failed to update priority');
    }
  };
  
  const handleSetPrimary = async (assignmentId: string) => {
    try {
      const response = await fetch(`/api/users/reception-points/${assignmentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_primary: true }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to set as primary');
      }
      
      // Update staff list to reflect the change
      setStaffList(prevStaff => 
        prevStaff.map(staff => ({
          ...staff,
          is_primary: staff.assignment_id === assignmentId
        }))
      );
    } catch (err) {
      console.error('Error setting primary:', err);
      alert('Failed to set as primary');
    }
  };
  
  if (loading) {
    return (
      <DashboardLayout>
        <div className={styles.container}>
          <div className={styles.header}>
            <Skeleton height="2rem" width="50%" />
          </div>
          <div className={styles.content}>
            <Skeleton height="20rem" />
          </div>
        </div>
      </DashboardLayout>
    );
  }
  
  if (error) {
    return (
      <DashboardLayout>
        <div className={styles.container}>
          <Alert variant="error" title="Error" closable={false}>
            {error}
          </Alert>
        </div>
      </DashboardLayout>
    );
  }
  
  // Find the selected reception point
  const selectedPoint = receptionPoints.find(point => point.id === selectedPointId);
  
  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Reception Points Dashboard</h1>
          <Link href="/reception-points/create">
            <Button variant="primary" label="Create Reception Point">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M8 3.33334V12.6667"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M3.33334 8H12.6667"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              Create Reception Point
            </Button>
          </Link>
        </div>
        
        <div className={styles.content}>
          {/* Reception points list */}
          <div className={styles.sidebar}>
            <div className={styles.sidebarHeader}>
              <h2 className={styles.sidebarTitle}>Reception Points</h2>
              <span className={styles.pointCount}>{receptionPoints.length} points</span>
            </div>
            
            <ul className={styles.pointsList}>
              {receptionPoints.map(point => (
                <li 
                  key={point.id} 
                  className={`${styles.pointItem} ${selectedPointId === point.id ? styles.active : ''}`}
                  onClick={() => handleSelectReceptionPoint(point.id)}
                >
                  <div className={styles.pointInfo}>
                    <h3 className={styles.pointName}>{point.name}</h3>
                    <span className={styles.pointCode}>{point.code}</span>
                  </div>
                  <div className={styles.pointStats}>
                    <div className={styles.statItem}>
                      <span className={styles.statLabel}>Staff:</span>
                      <span className={styles.statValue}>{point.assigned_staff_count}</span>
                    </div>
                    <div className={styles.statItem}>
                      <span className={styles.statLabel}>Messages:</span>
                      <span className={styles.statValue}>{point.message_count}</span>
                    </div>
                  </div>
                </li>
              ))}
              
              {receptionPoints.length === 0 && (
                <li className={styles.emptyState}>
                  No reception points found
                </li>
              )}
            </ul>
          </div>
          
          {/* Selected reception point details */}
          <div className={styles.mainContent}>
            {selectedPoint ? (
              <>
                <div className={styles.pointHeader}>
                  <div className={styles.pointHeaderInfo}>
                    <h2 className={styles.pointHeaderTitle}>{selectedPoint.name}</h2>
                    <p className={styles.pointHeaderDesc}>{selectedPoint.description || 'No description'}</p>
                  </div>
                  <div className={styles.pointHeaderActions}>
                    <Button 
                      variant="primary" 
                      onClick={handleAssignStaff} 
                      label="Assign Staff"
                    >
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path
                          d="M8 3.33334V12.6667"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M3.33334 8H12.6667"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      Assign Staff
                    </Button>
                    <Link href={`/reception-points/${selectedPoint.id}`}>
                      <Button variant="secondary" label="View Details">
                        View Details
                      </Button>
                    </Link>
                  </div>
                </div>
                
                <div className={styles.staffListSection}>
                  <h3 className={styles.sectionTitle}>
                    Assigned Staff 
                    <span className={styles.staffCount}>
                      {staffList.filter(staff => staff.is_assigned).length} staff members
                    </span>
                  </h3>
                  
                  {loadingStaff ? (
                    <div className={styles.loadingState}>
                      <Skeleton height="2rem" count={3} />
                    </div>
                  ) : (
                    <>
                      {staffList.filter(staff => staff.is_assigned).length === 0 ? (
                        <div className={styles.emptyStaffState}>
                          <p>No staff assigned to this reception point</p>
                          <Button 
                            variant="primary" 
                            onClick={handleAssignStaff}
                            label="Assign Staff"
                          >
                            Assign Staff
                          </Button>
                        </div>
                      ) : (
                        <div className={styles.staffList}>
                          <table className={styles.staffTable}>
                            <thead>
                              <tr>
                                <th className={styles.nameColumn}>Name</th>
                                <th className={styles.emailColumn}>Email</th>
                                <th className={styles.priorityColumn}>Priority</th>
                                <th className={styles.primaryColumn}>Primary</th>
                                <th className={styles.actionsColumn}>Actions</th>
                              </tr>
                            </thead>
                            <tbody>
                              {staffList
                                .filter(staff => staff.is_assigned)
                                .sort((a, b) => {
                                  // Sort by is_primary first, then by priority
                                  if (a.is_primary && !b.is_primary) return -1;
                                  if (!a.is_primary && b.is_primary) return 1;
                                  return (a.priority || 1) - (b.priority || 1);
                                })
                                .map(staff => (
                                  <tr key={staff.id} className={styles.staffRow}>
                                    <td className={styles.nameCell}>
                                      <div className={styles.staffInfo}>
                                        <div className={styles.avatar}>
                                          {staff.avatar_url ? (
                                            <img src={staff.avatar_url} alt={staff.display_name} />
                                          ) : (
                                            <div className={styles.avatarPlaceholder}>
                                              {(staff.display_name || '').charAt(0).toUpperCase()}
                                            </div>
                                          )}
                                        </div>
                                        <span className={styles.staffName}>
                                          {staff.display_name}
                                        </span>
                                      </div>
                                    </td>
                                    <td className={styles.emailCell}>{staff.email}</td>
                                    <td className={styles.priorityCell}>
                                      <input
                                        type="number"
                                        min="1"
                                        max="100"
                                        value={staff.priority || 1}
                                        onChange={(e) => {
                                          if (staff.assignment_id) {
                                            handleUpdatePriority(
                                              staff.assignment_id, 
                                              parseInt(e.target.value) || 1
                                            );
                                          }
                                        }}
                                        className={styles.priorityInput}
                                      />
                                    </td>
                                    <td className={styles.primaryCell}>
                                      {staff.is_primary ? (
                                        <span className={styles.primaryBadge}>Primary</span>
                                      ) : (
                                        <button 
                                          className={styles.setPrimaryButton}
                                          onClick={() => {
                                            if (staff.assignment_id) {
                                              handleSetPrimary(staff.assignment_id);
                                            }
                                          }}
                                        >
                                          Set Primary
                                        </button>
                                      )}
                                    </td>
                                    <td className={styles.actionsCell}>
                                      <button 
                                        className={styles.removeButton}
                                        onClick={() => {
                                          if (staff.assignment_id) {
                                            handleRemoveAssignment(staff.assignment_id);
                                          }
                                        }}
                                      >
                                        Remove
                                      </button>
                                    </td>
                                  </tr>
                                ))}
                            </tbody>
                          </table>
                        </div>
                      )}
                      
                      {/* Unassigned staff section */}
                      <div className={styles.unassignedSection}>
                        <h3 className={styles.sectionTitle}>
                          Available Staff
                          <span className={styles.staffCount}>
                            {staffList.filter(staff => !staff.is_assigned).length} staff members
                          </span>
                        </h3>
                        
                        {staffList.filter(staff => !staff.is_assigned).length === 0 ? (
                          <div className={styles.emptyStaffState}>
                            <p>All staff members are already assigned to this reception point</p>
                          </div>
                        ) : (
                          <div className={styles.unassignedList}>
                            {staffList
                              .filter(staff => !staff.is_assigned)
                              .map(staff => (
                                <div key={staff.id} className={styles.unassignedItem}>
                                  <div className={styles.staffInfo}>
                                    <div className={styles.avatar}>
                                      {staff.avatar_url ? (
                                        <img src={staff.avatar_url} alt={staff.display_name} />
                                      ) : (
                                        <div className={styles.avatarPlaceholder}>
                                          {(staff.display_name || '').charAt(0).toUpperCase()}
                                        </div>
                                      )}
                                    </div>
                                    <div>
                                      <span className={styles.staffName}>{staff.display_name}</span>
                                      <span className={styles.staffEmail}>{staff.email}</span>
                                    </div>
                                  </div>
                                  <button 
                                    className={styles.assignButton}
                                    onClick={async () => {
                                      try {
                                        const response = await fetch(`/api/users/${staff.user_id}/reception-points`, {
                                          method: 'POST',
                                          headers: {
                                            'Content-Type': 'application/json',
                                          },
                                          body: JSON.stringify({
                                            reception_point_id: selectedPoint.id,
                                            priority: 1,
                                            is_primary: false
                                          }),
                                        });
                                        
                                        if (!response.ok) {
                                          throw new Error('Failed to assign staff');
                                        }
                                        
                                        // Refresh list
                                        handleAssignmentSaved();
                                      } catch (err) {
                                        console.error('Error assigning staff:', err);
                                        alert('Failed to assign staff');
                                      }
                                    }}
                                  >
                                    Quick Assign
                                  </button>
                                </div>
                              ))}
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
              </>
            ) : (
              <div className={styles.noSelectionState}>
                <div className={styles.noSelectionMessage}>
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#94A3B8" strokeWidth="1.5"/>
                    <path d="M12 8V16" stroke="#94A3B8" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M8 12H16" stroke="#94A3B8" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <p>Select a reception point to view details or <Link href="/reception-points/create">create a new one</Link></p>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Assign Staff Modal */}
        {showAssignStaffModal && selectedPointId && (
          <AssignStaffModal
            receptionPointId={selectedPointId}
            onClose={() => setShowAssignStaffModal(false)}
            onSave={handleAssignmentSaved}
          />
        )}
      </div>
    </DashboardLayout>
  );
}