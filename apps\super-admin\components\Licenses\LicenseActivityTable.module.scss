.activityTable {
  width: 100%;
  
  .table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;
    
    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e5e7eb;
    }
    
    th {
      font-weight: 600;
      color: #6b7280;
      font-size: 14px;
      background-color: #f9fafb;
    }
  }
  
  .badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    
    &.activation {
      background-color: #dbeafe;
      color: #1e40af;
    }
    
    &.checkIn {
      background-color: #d1fae5;
      color: #065f46;
    }
    
    &.warning {
      background-color: #fef3c7;
      color: #92400e;
    }
    
    &.violation {
      background-color: #fecaca;
      color: #b91c1c;
    }
    
    &.revocation {
      background-color: #fee2e2;
      color: #991b1b;
    }
  }
  
  .details {
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
    max-width: 300px;
  }
  
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 16px;
    
    .pageButton {
      padding: 6px 12px;
      border: 1px solid #e5e7eb;
      background-color: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      
      &:hover:not(:disabled) {
        background-color: #f3f4f6;
      }
      
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
    
    .pageInfo {
      margin: 0 16px;
      font-size: 14px;
      color: #6b7280;
    }
  }
  
  .loading {
    padding: 24px;
    text-align: center;
    color: #6b7280;
  }
  
  .error {
    padding: 16px;
    background-color: #fef2f2;
    color: #b91c1c;
    border-radius: 4px;
    margin-bottom: 16px;
  }
  
  .empty {
    padding: 24px;
    text-align: center;
    color: #6b7280;
  }
}