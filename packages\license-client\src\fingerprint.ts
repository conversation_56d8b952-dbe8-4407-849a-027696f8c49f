import { execSync } from 'child_process';
import crypto from 'crypto';
import os from 'os';

/**
 * Generates a hardware fingerprint based on system information
 * This is a simplified version, a real implementation would need to be more robust
 */
export const generateHardwareFingerprint = (): string => {
  try {
    // Collect hardware information
    const cpuInfo = os.cpus();
    const networkInterfaces = os.networkInterfaces();
    const totalMemory = os.totalmem();
    const hostname = os.hostname();
    
    // On Windows, get the volume serial number
    let diskSerial = '';
    if (os.platform() === 'win32') {
      try {
        const output = execSync('wmic diskdrive get SerialNumber').toString();
        diskSerial = output.split('\n')[1]?.trim() || '';
      } catch (e) {
        // If wmic command fails, use an alternative approach
        diskSerial = 'unknown-disk-serial';
      }
    }
    
    // Collect network MAC addresses (excluding virtual adapters)
    const macAddresses: string[] = [];
    Object.values(networkInterfaces).forEach((interfaces) => {
      interfaces?.forEach((iface) => {
        // Skip over internal and non-physical interfaces
        if (!iface.internal && iface.mac !== '00:00:00:00:00:00') {
          macAddresses.push(iface.mac);
        }
      });
    });
    
    // Create a string representation of the CPU info
    const cpuString = JSON.stringify({
      model: cpuInfo[0]?.model || 'unknown',
      speed: cpuInfo[0]?.speed || 0,
      cores: cpuInfo.length
    });
    
    // Combine all collected information
    const hardwareInfo = [
      cpuString,
      macAddresses.sort().join(','),
      totalMemory.toString(),
      hostname,
      diskSerial
    ].join('|');
    
    // Create a hash from the hardware information
    const hash = crypto
      .createHash('sha256')
      .update(hardwareInfo)
      .digest('hex');
    
    return hash;
  } catch (error) {
    console.error('Failed to generate hardware fingerprint:', error);
    // Return a fallback fingerprint based on hostname and architecture
    return crypto
      .createHash('sha256')
      .update(`${os.hostname()}-${os.arch()}-${os.totalmem()}`)
      .digest('hex');
  }
};
