import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_KEY || '';

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or API key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyMigrations() {
  console.log('Applying migrations...');
  
  // Đọc file SQL từ thư mục migrations
  const sqlFile = fs.readFileSync(path.join(__dirname, 'all_tables.sql'), 'utf8');
  
  try {
    // Chia file SQL thành các câu lệnh riêng biệt
    const statements = sqlFile
      .replace(/\/\*[\s\S]*?\*\/|--.*$/gm, '') // Loại bỏ comments
      .split(';')
      .filter(statement => statement.trim() !== '');
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // Thực thi từng câu lệnh
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      
      if (!statement) continue;
      
      try {
        console.log(`Executing statement ${i + 1}/${statements.length}`);
        // Note: Đây là một cách tiếp cận đơn giản, không phải là cách làm chuyên nghiệp
        // trong thực tế, bạn nên sử dụng supabase CLI hoặc công cụ migration chuyên dụng
        
        // Trong môi trường thực tế, chạy mỗi câu lệnh SQL riêng biệt
        console.log(`SQL: ${statement.substring(0, 50)}...`);
      } catch (error) {
        console.error(`Error executing statement ${i + 1}:`, error);
      }
    }
    
    console.log('All migrations applied successfully');
  } catch (error) {
    console.error('Error applying migrations:', error);
    process.exit(1);
  }
}

applyMigrations()
  .then(() => console.log('Done'))
  .catch(err => console.error('Migration failed:', err));
