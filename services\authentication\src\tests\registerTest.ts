import axios from 'axios';

const API_URL = 'http://localhost:3001/api';

const testRegister = async () => {
  try {
    console.log('Testing registration with detailed logging...');
    
    // Create unique test user
    const email = `test_${Date.now()}@example.com`;
    const password = 'password123';
    const full_name = 'Test User';
    
    console.log('Registration payload:', { email, password, full_name });
    
    try {
      const response = await axios.post(
        `${API_URL}/auth/register`,
        { email, password, full_name },
        { 
          validateStatus: status => true, // Accept all status codes to see the response
          timeout: 10000 // 10 seconds timeout
        }
      );
      
      console.log('Registration response status:', response.status);
      console.log('Registration response data:', response.data);
      
      if (response.status === 201 || response.status === 200) {
        console.log('✅ Registration successful!');
      } else {
        console.log('❌ Registration failed with status:', response.status);
      }
    } catch (error: any) {
      if (error.response) {
        console.log('Response status:', error.response.status);
        console.log('Response data:', error.response.data);
      } else if (error.request) {
        console.log('No response received:', error.request);
      } else {
        console.log('Error setting up request:', error.message);
      }
      
      console.log('Error details:', error);
    }
  } catch (generalError) {
    console.error('Unexpected error:', generalError);
  }
};

// Đảm bảo server API đang chạy trước khi chạy test này
testRegister()
  .then(() => console.log('Test completed'))
  .catch(error => console.error('Test failed with exception:', error));
