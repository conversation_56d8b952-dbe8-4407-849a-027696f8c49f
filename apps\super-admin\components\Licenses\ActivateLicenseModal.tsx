import React, { useState } from 'react';
import { Button } from '@loaloa/ui';
import styles from './ActivateLicenseModal.module.scss';

interface ActivateLicenseModalProps {
  licenseId: string;
  licenseKey: string;
  onClose: () => void;
  onSuccess: (license: any) => void;
}

const ActivateLicenseModal: React.FC<ActivateLicenseModalProps> = ({
  licenseId,
  licenseKey,
  onClose,
  onSuccess
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Handle submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/licenses/${licenseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_active: true }),
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to activate license');
      }
      
      onSuccess(result.data);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modal}>
        <div className={styles.modalHeader}>
          <h2>Activate License</h2>
          <button className={styles.closeButton} onClick={onClose} type="button">×</button>
        </div>
        
        {error && (
          <div className={styles.errorMessage}>{error}</div>
        )}
        
        <div className={styles.modalBody}>
          <div className={styles.info}>
            <p>
              You are about to activate license <code>{licenseKey}</code>.
              This will allow the license to be used for on-premise deployments.
            </p>
          </div>
        </div>
        
        <div className={styles.modalFooter}>
          <Button 
            variant="outline" 
            onClick={onClose} 
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            variant="primary" 
            onClick={handleSubmit} 
            disabled={isLoading}
          >
            {isLoading ? 'Activating...' : 'Activate License'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ActivateLicenseModal;