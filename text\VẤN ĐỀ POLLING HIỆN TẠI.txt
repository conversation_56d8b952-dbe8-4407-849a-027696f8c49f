🔍 PHÂN TÍCH VẤN ĐỀ POLLING HIỆN TẠI
Vấn đề đang gặp:
Database Load: Mỗi 3-5 giây query toàn bộ sessions và messages
Network Overhead: Continuous HTTP requests
Server Resources: CPU và memory usage cao
Scaling Issues: 100 users = 2000+ queries/phút
Impact khi có nhiều users:
10 users online: ~200 queries/phút
100 users online: ~2000 queries/phút
1000 users online: ~20,000 queries/phút
🎯 GIẢI PHÁP TỐI ÚU NGAY LẬP TỨC
Option 1: Tối ưu Polling hiện tại (Nhanh - 30 phút)
Lệnh CLI:

Copynotepad D:\loaloa\apps\web-chat\app\hooks\useChat.ts
Thay đổi từ polling thành event-based checking:

Copy// Thay thế polling interval thành adaptive polling:
const startSmartPolling = useCallback(() => {
  if (pollingIntervalRef.current) {
    clearInterval(pollingIntervalRef.current);
  }

  // Adaptive polling - slow down when no activity
  let currentInterval = 5000; // Start with 5 seconds
  let noActivityCount = 0;

  const poll = async () => {
    if (mountedRef.current && connected && sessionId && !document.hidden) {
      const hasNewMessages = await loadMessages(true);
      
      if (hasNewMessages) {
        // Reset to fast polling when active
        currentInterval = 3000;
        noActivityCount = 0;
      } else {
        // Slow down gradually when no activity
        noActivityCount++;
        if (noActivityCount > 3) {
          currentInterval = Math.min(currentInterval * 1.5, 30000); // Max 30 seconds
        }
      }
    }
    
    // Schedule next poll with adaptive interval
    if (mountedRef.current) {
      pollingIntervalRef.current = setTimeout(poll, currentInterval);
    }
  };

  // Start first poll
  poll();
}, [connected, sessionId, loadMessages]);
Staff Dashboard tương tự:

Copynotepad D:\loaloa\apps\web-chat\app\staff\dashboard\page.tsx
Copy// Thay thế fixed interval thành adaptive:
useEffect(() => {
  if (selectedSession && isAutoRefreshEnabled) {
    let currentInterval = 3000;
    let noActivityCount = 0;
    
    const adaptiveRefresh = async () => {
      if (!document.hidden) {
        const hasNewMessages = await smartRefreshMessages();
        
        if (hasNewMessages) {
          currentInterval = 3000; // Fast when active
          noActivityCount = 0;
        } else {
          noActivityCount++;
          if (noActivityCount > 5) {
            currentInterval = Math.min(currentInterval * 1.2, 15000); // Max 15s
          }
        }
      }
      
      if (smartRefreshIntervalRef.current) {
        smartRefreshIntervalRef.current = setTimeout(adaptiveRefresh, currentInterval);
      }
    };
    
    adaptiveRefresh();
    
    return () => {
      if (smartRefreshIntervalRef.current) {
        clearTimeout(smartRefreshIntervalRef.current);
      }
    };
  }
}, [selectedSession, isAutoRefreshEnabled, smartRefreshMessages]);
Option 2: Database-level Optimization (Tốt hơn - 1 giờ)
Thực hiện SQL optimizations trên Supabase:

Copy-- Tạo indexes để tăng tốc queries
CREATE INDEX IF NOT EXISTS idx_tenant_chat_sessions_tenant_status 
ON tenant_chat_sessions(tenant_id, status, updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_tenant_chat_messages_session_created 
ON tenant_chat_messages(chat_session_id, created_at DESC);

-- Tạo view để optimize session list queries
CREATE OR REPLACE VIEW active_sessions_with_last_message AS
SELECT 
    s.*,
    m.content as last_message_content,
    m.created_at as last_message_time,
    m.sender_type as last_sender_type
FROM tenant_chat_sessions s
LEFT JOIN LATERAL (
    SELECT content, created_at, sender_type
    FROM tenant_chat_messages 
    WHERE chat_session_id = s.id 
    ORDER BY created_at DESC 
    LIMIT 1
) m ON true
WHERE s.status = 'active';

-- Add comments
COMMENT ON INDEX idx_tenant_chat_sessions_tenant_status IS 'Optimize session list queries by tenant and status';
COMMENT ON INDEX idx_tenant_chat_messages_session_created IS 'Optimize message loading by session and time';
COMMENT ON VIEW active_sessions_with_last_message IS 'Optimized view for session list with latest messages';
Cập nhật API để sử dụng optimized view:

Copynotepad D:\loaloa\apps\web-chat\app\api\chat-sessions\route.ts
Copy// Thay thế query để sử dụng optimized view:
const { data: sessions, error } = await supabase
  .from('active_sessions_with_last_message')
  .select(`
    *,
    tenant_qr_codes(code_value, location, room_number, description),
    tenant_message_reception_points!tenant_chat_sessions_reception_point_id_fkey(name, code, description)
  `)
  .eq('tenant_id', tenant_id)
  .order('last_message_time', { ascending: false, nullsFirst: false })
  .order('updated_at', { ascending: false })
  .range(offset, offset + limit - 1);
Option 3: Supabase Realtime (Tốt nhất - 2 giờ)
Cài đặt realtime subscription:

Copycd D:\loaloa\apps\web-chat
npm install @supabase/realtime-js
Tạo realtime service:

Copymkdir D:\loaloa\apps\web-chat\app\lib\realtime
notepad D:\loaloa\apps\web-chat\app\lib\realtime\useRealtimeMessages.ts
Copyimport { useEffect, useState, useCallback } from 'react';
import { createClientSupabase } from '@/lib/supabase';

export function useRealtimeMessages(sessionId: string) {
  const [messages, setMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  
  const supabase = createClientSupabase();

  // Load initial messages
  const loadInitialMessages = useCallback(async () => {
    if (!sessionId) return;
    
    const { data, error } = await supabase
      .from('tenant_chat_messages')
      .select('*')
      .eq('chat_session_id', sessionId)
      .order('created_at', { ascending: true });
    
    if (!error && data) {
      setMessages(data);
    }
    setLoading(false);
  }, [sessionId, supabase]);

  // Set up realtime subscription
  useEffect(() => {
    if (!sessionId) return;

    // Load initial data
    loadInitialMessages();

    // Subscribe to realtime changes
    const subscription = supabase
      .channel(`messages:${sessionId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'tenant_chat_messages',
          filter: `chat_session_id=eq.${sessionId}`
        },
        (payload) => {
          console.log('🔔 New message received:', payload.new);
          setMessages(prev => [...prev, payload.new]);
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [sessionId, supabase, loadInitialMessages]);

  return { messages, loading, setMessages };
}
🤔 KHUYẾN NGHỊ
Nếu muốn tiếp tục Mobile QR Scanner ngay:
Tạm thời giữ polling hiện tại - nó hoạt động tốt với số lượng user ít (<50 concurrent users)

Nếu muốn tối ưu trước khi scale:
Option 1 (30p): Adaptive polling - giảm 60% database load
Option 2 (1h): Database optimization - giảm 80% query time
Option 3 (2h): Supabase Realtime - giảm 95% polling load
💭 QUYẾT ĐỊNH