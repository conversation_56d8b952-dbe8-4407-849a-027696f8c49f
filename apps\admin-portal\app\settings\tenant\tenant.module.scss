@import '../../styles/_variables.scss';

.container {
  padding: $spacing-md;
  max-width: 1200px;
  margin: 0 auto;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
}

.titleSection {
  display: flex;
  flex-direction: column;
}

.backLink {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $gray;
  text-decoration: none;
  margin-bottom: $spacing-xs;
  font-size: 14px;
  
  &:hover {
    color: $primary-color;
  }
}

.pageTitle {
  font-size: 24px;
  font-weight: 600;
  color: $black;
  margin: 0;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: $primary-color;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: $spacing-md;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.settingsLayout {
  display: flex;
  gap: $spacing-lg;
  margin-top: $spacing-lg;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.settingsSidebar {
  width: 240px;
  flex-shrink: 0;
  
  @media (max-width: 768px) {
    width: 100%;
  }
}

.settingsNav {
  position: sticky;
  top: 16px;
  background-color: $white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  overflow: hidden;
}

.settingsNavItem {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-md;
  color: $dark-gray;
  text-decoration: none;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
  
  svg {
    width: 20px;
    height: 20px;
  }
  
  &:hover {
    background-color: #f9f9f9;
  }
  
  &.active {
    color: $primary-color;
    border-left-color: $primary-color;
    background-color: rgba($primary-color, 0.05);
    font-weight: 500;
  }
}

.settingsContent {
  flex-grow: 1;
  background-color: $white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  padding: $spacing-lg;
}

.settingsSection {
  margin-bottom: $spacing-xl;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.sectionTitle {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 $spacing-lg;
  padding-bottom: $spacing-sm;
  border-bottom: 1px solid $secondary-color;
}

.formGroup {
  margin-bottom: $spacing-lg;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  display: block;
  font-weight: 500;
  margin-bottom: $spacing-sm;
}

.input, .textarea {
  display: block;
  width: 100%;
  padding: 10px 12px;
  border: 1px solid $secondary-color;
  border-radius: $border-radius-md;
  font-size: 16px;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
}

.textarea {
  resize: vertical;
}

.fieldHelp {
  margin: $spacing-xs 0 0;
  font-size: 14px;
  color: $gray;
}

.inputGroup {
  display: flex;
  align-items: stretch;
}

.inputPrefix {
  flex-grow: 1;
  padding: 10px 12px;
  border: 1px solid $secondary-color;
  border-right: none;
  border-radius: $border-radius-md 0 0 $border-radius-md;
  font-size: 16px;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
}

.inputSuffix {
  display: flex;
  align-items: center;
  padding: 0 12px;
  background-color: #f9f9f9;
  border: 1px solid $secondary-color;
  border-left: none;
  border-radius: 0 $border-radius-md $border-radius-md 0;
  color: $gray;
  font-size: 16px;
}

.logoField {
  display: flex;
  gap: $spacing-md;
  align-items: flex-start;
  
  .input {
    flex-grow: 1;
  }
}

.logoPreview {
  width: 60px;
  height: 60px;
  border-radius: $border-radius-sm;
  overflow: hidden;
  border: 1px solid $secondary-color;
}

.logoImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.colorField {
  display: flex;
  gap: $spacing-sm;
}

.colorInput {
  width: 60px;
  height: 38px;
  border: 1px solid $secondary-color;
  border-radius: $border-radius-md;
  background-color: $white;
  padding: 2px;
}

.colorText {
  flex-grow: 1;
  padding: 10px 12px;
  border: 1px solid $secondary-color;
  border-radius: $border-radius-md;
  font-size: 16px;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
}

.checkboxGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: $spacing-md;
}

.checkbox {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  cursor: pointer;
  user-select: none;
  
  input {
    margin: 0;
  }
  
  span {
    font-size: 16px;
  }
}

.switchField {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-sm 0;
}

.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
  
  input {
    opacity: 0;
    width: 0;
    height: 0;
    
    &:checked + .slider {
      background-color: $primary-color;
      
      &:before {
        transform: translateX(26px);
      }
    }
    
    &:focus + .slider {
      box-shadow: 0 0 1px $primary-color;
    }
  }
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: $gray;
  transition: .4s;
  border-radius: 34px;
  
  &:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: $white;
    transition: .4s;
    border-radius: 50%;
  }
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: $spacing-md;
  margin-top: $spacing-xl;
  padding-top: $spacing-lg;
  border-top: 1px solid $secondary-color;
}

.primaryButton {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: 10px 20px;
  background-color: $primary-color;
  color: $white;
  border-radius: $border-radius-md;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    opacity: 0.9;
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.secondaryButton {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: 10px 20px;
  background-color: $secondary-color;
  color: $black;
  border-radius: $border-radius-md;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    opacity: 0.9;
  }
}