'use client';
import Link from 'next/link';
import styles from './UserSidebar.module.scss';

interface UserSidebarProps {
  userId: string;
  activeTab: string;
}

export default function UserSidebar({ userId, activeTab }: UserSidebarProps) {
  const tabs = [
    {
      id: 'details',
      name: 'User Details',
      path: `/users/${userId}`,
      icon: (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path d="M8 8C9.10457 8 10 7.10457 10 6C10 4.89543 9.10457 4 8 4C6.89543 4 6 4.89543 6 6C6 7.10457 6.89543 8 8 8Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M8 10C5.79086 10 4 11.7909 4 14H12C12 11.7909 10.2091 10 8 10Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
    },
    {
      id: 'assignments',
      name: 'Assignments',
      path: `/users/${userId}/assignments`,
      icon: (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path d="M6 4H13.3333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M6 8H13.3333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M6 12H13.3333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M3.33333 4H3.34" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M3.33333 8H3.34" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M3.33333 12H3.34" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
    },
    {
      id: 'permissions',
      name: 'Permissions',
      path: `/users/${userId}/permissions`,
      icon: (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path d="M12.6666 7.33331H3.33331C2.59693 7.33331 1.99998 7.93026 1.99998 8.66665V12.6666C1.99998 13.403 2.59693 14 3.33331 14H12.6666C13.403 14 14 13.403 14 12.6666V8.66665C14 7.93026 13.403 7.33331 12.6666 7.33331Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M4.66669 7.33331V4.66665C4.66669 3.78259 5.01788 2.93474 5.64301 2.30962C6.26813 1.68449 7.11598 1.33331 8.00002 1.33331C8.88407 1.33331 9.73192 1.68449 10.357 2.30962C10.9822 2.93474 11.3334 3.78259 11.3334 4.66665V7.33331" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
    },
    {
      id: 'activity',
      name: 'Activity',
      path: `/users/${userId}/activity`,
      icon: (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path d="M8 4V8L10.6667 9.33333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M8.00002 14C11.3137 14 14.0001 11.3137 14.0001 8.00001C14.0001 4.68629 11.3137 2 8.00002 2C4.68631 2 2.00002 4.68629 2.00002 8.00001C2.00002 11.3137 4.68631 14 8.00002 14Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
    },
  ];

  return (
    <div className={styles.sidebar}>
      <ul className={styles.menu}>
        {tabs.map((tab) => (
          <li key={tab.id} className={tab.id === activeTab ? styles.active : ''}>
            <Link href={tab.path}>
              {tab.icon}
              <span>{tab.name}</span>
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
}
