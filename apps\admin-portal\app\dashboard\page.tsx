'use client';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Alert } from '@ui';
import styles from './dashboard.module.scss';

interface LicenseInfo {
  licenseKey: string;
  customerName: string;
  email: string;
  issueDate: string;
  expiryDate: string;
  isActive: boolean;
  lastCheckIn: string;
  checkInCount: number;
  tenant_id: string;
}

// Add interface for QR code stats
interface QRCodeStats {
  total: number;
  active: number;
  totalScans: number;
  remainingLimit: number | null;
}

interface SystemStatus {
  database: 'operational' | 'degraded' | 'down';
  api: 'operational' | 'degraded' | 'down';
  licenseAgent: 'operational' | 'degraded' | 'down';
}

export default function DashboardPage() {
  const [licenseInfo, setLicenseInfo] = useState<LicenseInfo | null>(null);
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    database: 'operational',
    api: 'operational',
    licenseAgent: 'operational'
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Add state for QR code stats
  const [qrCodeStats, setQrCodeStats] = useState<QRCodeStats | null>(null);
  const [qrLoading, setQrLoading] = useState(true);
  const [qrError, setQrError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch license info
        const response = await fetch('/api/license/info');
        if (!response.ok) {
          throw new Error('Failed to fetch license information');
        }
        const data = await response.json();
        setLicenseInfo({
          ...data,
          tenant_id: data.tenant_id
        });

        // Get system status
        const statusResponse = await fetch('/api/system/status').catch(() => null);
        if (statusResponse && statusResponse.ok) {
          const statusData = await statusResponse.json();
          setSystemStatus(statusData);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError('Could not load dashboard information. Please try again later.');
      } finally {
        setLoading(false);
      }
      
      // Fetch QR code statistics separately
      fetchQrCodeStats();
    };
    
    // Separate function to fetch QR code stats
    const fetchQrCodeStats = async () => {
      try {
        setQrLoading(true);
        setQrError(null);
        
        const qrResponse = await fetch('/api/qr-codes?limit=1');
        if (!qrResponse.ok) {
          throw new Error('Failed to fetch QR code statistics');
        }
        
        const qrData = await qrResponse.json();
        console.log('QR Code stats data:', qrData);
        
        // Calculate QR code statistics from returned data
        const total = qrData.meta?.total || 0;
        const active = qrData.data?.filter((code: any) => code.status === 'active').length || 0;
        const totalScans = qrData.data?.reduce((sum: number, code: any) => sum + (code.scan_count || 0), 0) || 0;
        
        setQrCodeStats({
          total,
          active,
          totalScans,
          remainingLimit: qrData.limits?.remaining || null
        });
      } catch (error) {
        console.error('Error fetching QR code stats:', error);
        setQrError('Could not load QR code statistics');
      } finally {
        setQrLoading(false);
      }
    };

    fetchDashboardData();
    
    // Set up refresh interval - every 5 minutes
    const intervalId = setInterval(fetchDashboardData, 5 * 60 * 1000);
    return () => clearInterval(intervalId);
  }, []);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading system information...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <Alert variant="error" title="Data Loading Error" closable={false}>
          {error}
        </Alert>
        <div className={styles.actionButtons}>
          <Link href="/activate" className={styles.primaryButton}>
            Activate New License
          </Link>
          <button className={styles.secondaryButton} onClick={() => window.location.reload()}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!licenseInfo) {
    return (
      <div className={styles.errorContainer}>
        <Alert variant="warning" title="License Not Activated" closable={false}>
          The system has not been activated. Please activate a license to continue.
        </Alert>
        <div className={styles.actionButtons}>
          <Link href="/activate" className={styles.primaryButton}>
            Activate License
          </Link>
        </div>
      </div>
    );
  }

  // Calculate days remaining
  const expiryDate = new Date(licenseInfo.expiryDate);
  const today = new Date();
  const daysRemaining = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

  // Check if license is expiring soon
  const isExpiringSoon = daysRemaining <= 30;

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  // Format date time
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <div className={styles.dashboardContainer}>
      <div className={styles.dashboardHeader}>
        <h1 className={styles.dashboardTitle}>Admin Portal Dashboard</h1>
        <div className={styles.refreshButton} onClick={() => window.location.reload()}>
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M13.65 2.35C12.2 0.9 10.21 0 8 0C3.58 0 0 3.58 0 8C0 12.42 3.58 16 8 16C11.73 16 14.84 13.45 15.73 10H13.65C12.83 12.33 10.61 14 8 14C4.69 14 2 11.31 2 8C2 4.69 4.69 2 8 2C9.66 2 11.14 2.69 12.22 3.78L9 7H16V0L13.65 2.35Z" fill="currentColor"/>
          </svg>
          <span>Refresh</span>
        </div>
      </div>

      {/* License expiry notification */}
      {isExpiringSoon && (
        <Alert variant="warning" title="License Expiring Soon" closable={true}>
          Your license will expire in {daysRemaining} days. Please renew your license to continue using the service.
          <div className={styles.alertAction}>
            <Link href="/settings/license" className={styles.alertButton}>
              Renew License
            </Link>
          </div>
        </Alert>
      )}

      {/* Info cards */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <div className={styles.statCardHeader}>
            <h3>License Information</h3>
          </div>
          <div className={styles.statCardBody}>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>License Key:</span>
              <span className={styles.statValue}>{licenseInfo.licenseKey}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Tenant ID:</span>
              <span className={styles.statValue} style={{ fontSize: '0.8rem', wordBreak: 'break-all' }}>
                {licenseInfo.tenant_id || 'Not specified'}
              </span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Customer:</span>
              <span className={styles.statValue}>{licenseInfo.customerName}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Email:</span>
              <span className={styles.statValue}>{licenseInfo.email}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Status:</span>
              <span className={`${styles.statValue} ${licenseInfo.isActive ? styles.active : styles.inactive}`}>
                {licenseInfo.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statCardHeader}>
            <h3>License Period</h3>
          </div>
          <div className={styles.statCardBody}>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Activation Date:</span>
              <span className={styles.statValue}>{formatDate(licenseInfo.issueDate)}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Expiry Date:</span>
              <span className={styles.statValue}>{formatDate(licenseInfo.expiryDate)}</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Time Remaining:</span>
              <span className={`${styles.statValue} ${
                daysRemaining > 30 ? styles.good : daysRemaining > 0 ? styles.warning : styles.danger
              }`}>
                {daysRemaining > 0 ? `${daysRemaining} days left` : 'Expired'}
              </span>
            </div>
          </div>
        </div>

        {/* QR Code Stats Card */}
        <div className={styles.statCard}>
          <div className={styles.statCardHeader}>
            <h3>QR Codes</h3>
            <Link href="/qr-codes" className={styles.viewAllLink}>
              View All
            </Link>
          </div>
          <div className={styles.statCardBody}>
            {qrLoading ? (
              <div className={styles.loadingState}>Loading...</div>
            ) : qrError ? (
              <div className={styles.errorState}>
                <p>{qrError}</p>
                <button onClick={() => window.location.reload()} className={styles.retryButton}>
                  Retry
                </button>
              </div>
            ) : qrCodeStats ? (
              <>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Total QR Codes:</span>
                  <span className={styles.statValue}>{qrCodeStats.total}</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Active QR Codes:</span>
                  <span className={styles.statValue}>{qrCodeStats.active}</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>Total Scans:</span>
                  <span className={styles.statValue}>{qrCodeStats.totalScans}</span>
                </div>
                {qrCodeStats.remainingLimit !== null && (
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>Remaining:</span>
                    <span className={`${styles.statValue} ${qrCodeStats.remainingLimit <= 5 ? styles.warning : ''}`}>
                      {qrCodeStats.remainingLimit} codes
                    </span>
                  </div>
                )}
              </>
            ) : (
              <div className={styles.emptyState}>No QR code data available</div>
            )}
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statCardHeader}>
            <h3>Check-in Information</h3>
          </div>
          <div className={styles.statCardBody}>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Last Check-in:</span>
              <span className={styles.statValue}>
                {licenseInfo.lastCheckIn ? formatDateTime(licenseInfo.lastCheckIn) : 'None'}
              </span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>Total Check-ins:</span>
              <span className={styles.statValue}>{licenseInfo.checkInCount}</span>
            </div>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className={styles.statsGridHalf}>
        <div className={styles.statCard}>
          <div className={styles.statCardHeader}>
            <h3>System Status</h3>
          </div>
          <div className={styles.statCardBody}>
            <div className={styles.statusItem}>
              <div className={`${styles.statusIndicator} ${styles[systemStatus.database]}`}></div>
              <span className={styles.statusLabel}>Database:</span>
              <span className={styles.statusValue}>
                {systemStatus.database === 'operational'
                  ? 'Operational'
                  : systemStatus.database === 'degraded'
                  ? 'Degraded Performance'
                  : 'Down'}
              </span>
            </div>
            <div className={styles.statusItem}>
              <div className={`${styles.statusIndicator} ${styles[systemStatus.api]}`}></div>
              <span className={styles.statusLabel}>API Server:</span>
              <span className={styles.statusValue}>
                {systemStatus.api === 'operational'
                  ? 'Operational'
                  : systemStatus.api === 'degraded'
                  ? 'Degraded Performance'
                  : 'Down'}
              </span>
            </div>
            <div className={styles.statusItem}>
              <div className={`${styles.statusIndicator} ${styles[systemStatus.licenseAgent]}`}></div>
              <span className={styles.statusLabel}>License Agent:</span>
              <span className={styles.statusValue}>
                {systemStatus.licenseAgent === 'operational'
                  ? 'Operational'
                  : systemStatus.licenseAgent === 'degraded'
                  ? 'Degraded Performance'
                  : 'Down'}
              </span>
            </div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statCardHeader}>
            <h3>System Management</h3>
          </div>
          <div className={styles.actionGrid}>
            <Link href="/settings" className={styles.actionButton}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M19.4 15C19.2 15.4 19.2 15.7 19 16L17.2 18.4C17 18.7 16.7 18.8 16.4 18.8C16.1 18.8 15.8 18.7 15.6 18.4L13.8 16C13.6 15.7 13.6 15.4 13.4 15C13.1 14.4 13.1 13.6 13.4 13C13.6 12.6 13.6 12.3 13.8 12L15.6 9.6C15.8 9.3 16.1 9.2 16.4 9.2C16.7 9.2 17 9.3 17.2 9.6L19 12C19.2 12.3 19.2 12.6 19.4 13C19.9 13.6 19.9 14.4 19.4 15ZM10.6 15C10.4 15.4 10.4 15.7 10.2 16L8.4 18.4C8.2 18.7 7.9 18.8 7.6 18.8C7.3 18.8 7 18.7 6.8 18.4L5 16C4.8 15.7 4.8 15.4 4.6 15C4.1 14.4 4.1 13.6 4.6 13C4.8 12.6 4.8 12.3 5 12L6.8 9.6C7 9.3 7.3 9.2 7.6 9.2C7.9 9.2 8.2 9.3 8.4 9.6L10.2 12C10.4 12.3 10.4 12.6 10.6 13C10.9 13.6 10.9 14.4 10.6 15Z" fill="currentColor"/>
              </svg>
              System Settings
            </Link>
            <Link href="/users" className={styles.actionButton}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="currentColor"/>
              </svg>
              User Management
            </Link>
            <Link href="/settings/tenant" className={styles.actionButton}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M4 10V17H7V10H4ZM10 10V17H13V10H10ZM2 22H21V19H2V22ZM16 10V17H19V10H16ZM2 8H21V6L2 6V8ZM2 4H21V2H2V4Z" fill="currentColor"/>
              </svg>
              Tenant Settings
            </Link>
            <Link href="/settings/license" className={styles.actionButton}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M20 12V6H4V12H20ZM20 4C21.1 4 22 4.9 22 6V19C22 20.1 21.1 21 20 21H4C2.9 21 2 20.1 2 19V6C2 4.9 2.9 4 4 4H20ZM13 15V13H11V15H13ZM15 15H19V13H15V15ZM11 17V19H13V17H11ZM5 17V19H9V17H5ZM5 15H9V13H5V15ZM15 17V19H19V17H15Z" fill="currentColor"/>
              </svg>
              License Management
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
