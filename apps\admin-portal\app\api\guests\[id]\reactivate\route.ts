import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Tạo Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }

  return createClient(supabaseUrl, supabaseKey);
};

// POST: <PERSON><PERSON><PERSON> hoạt lại guest (từ check-out về check-in)
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const guestId = params.id;
    const { room_number } = await request.json();

    if (!room_number) {
      return NextResponse.json(
        { error: 'Room number is required for reactivation' },
        { status: 400 }
      );
    }

    // Tạo Supabase client
    const supabase = createSupabaseClient();

    // Lấy thông tin guest hiện tại
    const { data: guest, error: fetchError } = await supabase
      .from('tenant_guests')
      .select('tenant_id, is_active')
      .eq('id', guestId)
      .single();

    if (fetchError) {
      return NextResponse.json(
        { error: 'Guest not found' },
        { status: 404 }
      );
    }

    if (guest.is_active) {
      return NextResponse.json(
        { error: 'Guest is already active' },
        { status: 400 }
      );
    }

    // Cập nhật guest để kích hoạt lại
    const { data, error } = await supabase
      .from('tenant_guests')
      .update({
        is_active: true,
        room_number,
        check_in: new Date().toISOString(),
        check_out: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', guestId)
      .select()
      .single();

    if (error) {
      console.error('Error reactivating guest:', error);
      throw error;
    }

    // Cập nhật trạng thái phòng
    await supabase
      .from('tenant_rooms')
      .update({
        status: 'occupied',
        last_checkin: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('tenant_id', guest.tenant_id)
      .eq('room_number', room_number);

    // Trả về kết quả
    return NextResponse.json({
      data,
      message: 'Guest reactivated successfully'
    });

  } catch (error) {
    console.error('Error in reactivate guest:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
