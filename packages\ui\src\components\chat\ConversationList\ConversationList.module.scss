@import '../../../styles/variables';

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  border-right: 1px solid var(--color-border, #E1E1E1);
}

.header {
  padding: 16px;
  border-bottom: 1px solid var(--color-border, #E1E1E1);
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.searchContainer {
  padding: 8px 16px;
  border-bottom: 1px solid var(--color-border, #E1E1E1);
}

.list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  color: var(--color-gray, #7D8491);
  font-size: 0.875rem;
  text-align: center;
  padding: 2rem;
}
