.messageContainer {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  
  &.guest {
    align-items: flex-start;
    
    .messageContent {
      background: #f3f4f6;
      border: 1px solid #e5e7eb;
    }
  }
  
  &.staff {
    align-items: flex-end;
    
    .messageContent {
      background: #fef3e2;
      border: 1px solid #fed7aa;
    }
  }
}

.messageHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
  color: #6b7280;
  
  .senderInfo {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .senderName {
      font-weight: 600;
      color: #374151;
    }
    
    .translationInfo {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .languageIndicator {
        font-size: 10px;
        background: #e0e7ff;
        color: #3730a3;
        padding: 2px 4px;
        border-radius: 4px;
        font-weight: 500;
      }
      
      .confidence {
        font-size: 10px;
        background: #dcfce7;
        color: #166534;
        padding: 2px 4px;
        border-radius: 4px;
        font-weight: 500;
      }
    }
  }
  
  .timestamp {
    font-size: 11px;
    color: #9ca3af;
  }
}

.messageContent {
  max-width: 70%;
  border-radius: 12px;
  padding: 12px 16px;
  position: relative;
  
  .messageText {
    font-size: 14px;
    line-height: 1.5;
    color: #111827;
    word-wrap: break-word;
  }
  
  .translationActions {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    
    .toggleButton {
      font-size: 11px;
      color: #6366f1;
      background: none;
      border: none;
      cursor: pointer;
      text-decoration: underline;
      padding: 0;
      
      &:hover {
        color: #4f46e5;
      }
    }
    
    .originalText {
      margin-top: 8px;
      padding: 8px;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 6px;
      border-left: 3px solid #6366f1;
      
      small {
        font-size: 10px;
        color: #6b7280;
        text-transform: uppercase;
        font-weight: 600;
        letter-spacing: 0.05em;
      }
      
      p {
        margin: 4px 0 0 0;
        font-size: 13px;
        color: #374151;
        font-style: italic;
      }
    }
  }
}

// Message bubble tails - Đuôi tin nhắn
.guest .messageContent::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 12px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #f3f4f6;
}

.staff .messageContent::before {
  content: '';
  position: absolute;
  right: -8px;
  top: 12px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left: 8px solid #fef3e2;
}
