LoaLoa Admin Portal - User Management System
Tổng quan
Module User Management trong Admin Portal của LoaLoa cung cấp giao diện để quản lý người dùng trong hệ thống. Với module này, quản trị viên có thể tạo, xem, chỉnh sửa và xóa người dùng, đồng thời phân vai trò và quyền hạn cho từng người dùng.

Cấu trúc thư mục
users/
├── page.tsx                 # Trang danh sách người dùng
├── users.module.scss        # CSS cho trang danh sách
├── create/                  # Quản lý tạo người dùng mới
│   ├── page.tsx             # Trang tạo người dùng
│   └── create-user.module.scss  # CSS cho trang tạo người dùng
└── [id]/                    # Chi tiết & chỉnh sửa người dùng
    ├── page.tsx             # Trang chi tiết người dùng
    ├── user-detail.module.scss  # CSS cho trang chi tiết
    └── edit/                # Trang chỉnh sửa người dùng
        ├── page.tsx         # Form chỉnh sửa người dùng
        └── edit-user.module.scss  # CSS cho trang chỉnh sửa
Cấu trúc Database
Module này sử dụng các bảng sau:

tenant_users - Lưu trữ thông tin người dùng trong tenant:

id: UUID, khóa chính
tenant_id: UUID, liên kết đến bảng tenants
user_id: UUID, liên kết đến bảng users (auth.users)
role: ENUM ('admin', 'manager', 'user')
is_primary_tenant: boolean, xác định người dùng chính của tenant
is_active: boolean, trạng thái hoạt động của người dùng
joined_at: timestamp, thời điểm tham gia
last_login_at: timestamp, thời điểm đăng nhập gần nhất
expiry_date: timestamp, ngày hết hạn (nếu có)
permissions: jsonb, quyền hạn tùy chỉnh
created_at: timestamp, thời điểm tạo
updated_at: timestamp, thời điểm cập nhật gần nhất
metadata: jsonb, dữ liệu bổ sung
tenant_users_details - Lưu trữ thông tin chi tiết người dùng:

id: UUID, khóa chính
tenant_user_id: UUID, liên kết đến tenant_users
email: varchar, email của người dùng
display_name: varchar, tên hiển thị
avatar_url: text, URL ảnh đại diện
phone: varchar, số điện thoại
created_at: timestamp, thời điểm tạo
updated_at: timestamp, thời điểm cập nhật gần nhất
tenant_permissions - Lưu trữ các quyền hạn trong hệ thống:

id: UUID, khóa chính
tenant_id: UUID, liên kết đến tenants
name: varchar, tên quyền
description: text, mô tả quyền
resource: varchar, tài nguyên áp dụng
action: varchar, hành động được phép
created_at: timestamp, thời điểm tạo
updated_at: timestamp, thời điểm cập nhật gần nhất
tenant_role_permissions - Liên kết giữa vai trò và quyền:

id: UUID, khóa chính
tenant_id: UUID, liên kết đến tenants
role: ENUM ('admin', 'manager', 'user')
permission_id: UUID, liên kết đến tenant_permissions
created_at: timestamp, thời điểm tạo
updated_at: timestamp, thời điểm cập nhật gần nhất
API Endpoints
Module cung cấp các API endpoints sau:

GET /api/users
Mô tả: Lấy danh sách người dùng với filter và phân trang
Query params:
page: Số trang (mặc định: 1)
limit: Số lượng kết quả trên mỗi trang (mặc định: 10)
search: Tìm kiếm theo tên, email, số điện thoại
role: Lọc theo vai trò (admin, manager, user)
status: Lọc theo trạng thái (active, inactive)
Response: Danh sách người dùng và metadata phân trang
POST /api/users
Mô tả: Tạo người dùng mới hoặc liên kết người dùng hiện có với tenant
Request Body:
email: Email người dùng (bắt buộc)
password: Mật khẩu (khi tạo mới)
display_name: Tên hiển thị
phone: Số điện thoại (tùy chọn)
role: Vai trò (mặc định: user)
is_active: Trạng thái hoạt động (mặc định: true)
expiry_date: Ngày hết hạn (tùy chọn)
Response: Thông tin người dùng đã tạo
GET /api/users/[id]
Mô tả: Lấy thông tin chi tiết của một người dùng
Path params:
id: ID của tenant_user
Response: Thông tin chi tiết của người dùng
PUT /api/users/[id]
Mô tả: Cập nhật thông tin người dùng
Path params:
id: ID của tenant_user
Request Body: Các trường cần cập nhật
Response: Thông tin người dùng sau khi cập nhật
DELETE /api/users/[id]
Mô tả: Xóa người dùng khỏi tenant
Path params:
id: ID của tenant_user
Response: Thông báo thành công
Giao diện người dùng
Trang danh sách người dùng (/users)
Hiển thị danh sách người dùng với thông tin: tên, email, vai trò, trạng thái, ngày tham gia
Cung cấp tính năng tìm kiếm và lọc người dùng
Cho phép xem chi tiết, chỉnh sửa và xóa người dùng
Có nút để tạo người dùng mới
Trang tạo người dùng (/users/create)
Form để nhập thông tin người dùng mới
Kiểm tra hợp lệ dữ liệu đầu vào
Hiển thị thông báo thành công sau khi tạo
Trang chi tiết người dùng (/users/[id])
Hiển thị tất cả thông tin về người dùng
Cho phép chuyển đến trang chỉnh sửa
Trang chỉnh sửa người dùng (/users/[id]/edit)
Form để chỉnh sửa thông tin người dùng
Cho phép đặt lại mật khẩu
Hiển thị thông báo thành công sau khi lưu
Phân quyền
Module này định nghĩa ba vai trò chính:

Admin: Có toàn quyền truy cập và quản lý trong tenant
Manager: Có thể xem báo cáo, không có quyền tạo QR code, room, area, user
User: Chỉ có thể đăng nhập vào staff-dashboard để nhận tin nhắn chat
Ghi chú triển khai
Module sử dụng DashboardLayout để duy trì tính nhất quán với các phần khác của ứng dụng
Các component UI như Button, SearchBar, StatusBadge, Pagination, DeleteConfirmModal được import từ package chung @loaloa/ui
Module được thiết kế để hoạt động trong môi trường multi-tenant
Hướng dẫn sử dụng
Xem danh sách người dùng
Truy cập trang /users
Sử dụng thanh tìm kiếm và bộ lọc để lọc danh sách
Sử dụng phân trang nếu có nhiều kết quả
Tạo người dùng mới
Từ trang /users, nhấn nút "Add User"
Điền thông tin người dùng vào form
Nhấn "Create User" để lưu
Chỉnh sửa người dùng
Từ danh sách người dùng, nhấn nút "Edit" trên người dùng cần chỉnh sửa
Hoặc, từ trang chi tiết người dùng, nhấn nút "Edit User"
Cập nhật thông tin và nhấn "Save Changes" để lưu
Xóa người dùng
Từ danh sách người dùng, nhấn nút "Delete" trên người dùng cần xóa
Xác nhận hành động xóa trong hộp thoại hiện ra