-- Trigger tạo schema khi tạo tenant mới
DROP TRIGGER IF EXISTS create_tenant_schema_trigger ON public.tenants;
CREATE TRIGGER create_tenant_schema_trigger
AFTER INSERT ON public.tenants
FOR EACH ROW EXECUTE FUNCTION public.create_tenant_schema();

-- Tri<PERSON> quản lý dịch tự động (áp dụng cho tất cả schema tenant)
CREATE OR REPLACE FUNCTION public.setup_translation_triggers() RETURNS VOID AS $$
DECLARE
    r RECORD;
BEGIN
    -- <PERSON><PERSON><PERSON><PERSON> qua tất cả schema tenant
    FOR r IN SELECT schema_name FROM public.tenant_schemas WHERE is_active = TRUE LOOP
        -- Tạo trigger cho mỗi schema
        EXECUTE format(
            'DROP TRIGGER IF EXISTS trigger_automatic_translations ON %I.chat_messages;
             CREATE TRIGGER trigger_automatic_translations
             AFTER INSERT ON %I.chat_messages
             FOR EACH ROW EXECUTE FUNCTION public.manage_automatic_translations()', 
            r.schema_name, r.schema_name
        );
    E<PERSON> LOOP;
END;

$$ LANGUAGE plpgsql;

-- <PERSON>gger cập nhật timestamp khi thay đổi thông tin tenant
DROP TRIGGER IF EXISTS update_tenant_timestamp ON public.tenants;
CREATE TRIGGER update_tenant_timestamp
BEFORE UPDATE ON public.tenants
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Hàm cập nhật trường updated_at
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;

$$ LANGUAGE plpgsql;
