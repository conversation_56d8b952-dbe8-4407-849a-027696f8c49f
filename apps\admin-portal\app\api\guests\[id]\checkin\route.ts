import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }

  return createClient(supabaseUrl, supabaseKey);
};

// POST: Check-in guest
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const guestId = params.id;
    const { room_number } = await request.json();

    if (!room_number) {
      return NextResponse.json(
        { error: 'Room number is required for check-in' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = createSupabaseClient();

    // Get guest information and tenant_id
    const { data: guest, error: fetchError } = await supabase
      .from('tenant_guests')
      .select('tenant_id')
      .eq('id', guestId)
      .single();

    if (fetchError || !guest) {
      return NextResponse.json(
        { error: 'Guest not found' },
        { status: 404 }
      );
    }

    // Verify the room exists and is available
    const { data: room, error: roomError } = await supabase
      .from('tenant_rooms')
      .select('status')
      .eq('room_number', room_number)
      .eq('tenant_id', guest.tenant_id)
      .single();

    if (roomError) {
      return NextResponse.json(
        { error: 'Room not found or not in the same tenant' },
        { status: 404 }
      );
    }

    if (room.status !== 'available') {
      return NextResponse.json(
        { error: `Room ${room_number} is not available for check-in. Current status: ${room.status}` },
        { status: 400 }
      );
    }

    // Update check-in information
    const { data, error } = await supabase
      .from('tenant_guests')
      .update({
        room_number,
        check_in: new Date().toISOString(),
        is_active: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', guestId)
      .select();

    if (error) {
      console.error('Error checking in guest:', error);
      throw error;
    }

    // Update room status
    await supabase
      .from('tenant_rooms')
      .update({
        status: 'occupied',
        last_checkin: new Date().toISOString()
      })
      .eq('tenant_id', guest.tenant_id)
      .eq('room_number', room_number);

    // Return result
    return NextResponse.json({
      data,
      message: 'Guest checked in successfully'
    });

  } catch (error) {
    console.error('Error in check-in guest:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
