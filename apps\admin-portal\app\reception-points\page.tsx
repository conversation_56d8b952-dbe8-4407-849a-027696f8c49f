'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import DashboardLayout from '../dashboard-layout';
import styles from './reception-points.module.scss';
import { But<PERSON>, Alert, SearchBar } from '@ui';
import DeleteConfirmModal from '../components/modals/DeleteConfirmModal';

interface ReceptionPoint {
  id: string;
  name: string;
  code: string;
  description?: string;
  icon_url?: string;
  is_active: boolean;
  priority: number;
  created_at: string;
}

export default function ReceptionPointsPage() {
  const [points, setPoints] = useState<ReceptionPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  // Delete modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [pointToDelete, setPointToDelete] = useState<ReceptionPoint | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  
  const fetchReceptionPoints = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Build query params
      const params = new URLSearchParams();
      if (statusFilter === 'active') params.append('is_active', 'true');
      if (statusFilter === 'inactive') params.append('is_active', 'false');
      params.append('page', page.toString());
      
      const response = await fetch(`/api/reception-points?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch reception points');
      }
      
      const data = await response.json();
      setPoints(data.data || []);
      setTotalPages(Math.ceil((data.meta?.total || 0) / (data.meta?.limit || 10)));
    } catch (err) {
      console.error('Error fetching reception points:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchReceptionPoints();
  }, [statusFilter, page]);
  
  // Filter points by search term
  const filteredPoints = points.filter(point => 
    point.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    point.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (point.description && point.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );
  
  // Handle point deletion
  const handleDeleteClick = (point: ReceptionPoint) => {
    setPointToDelete(point);
    setShowDeleteModal(true);
  };
  
  const handleConfirmDelete = async () => {
    if (!pointToDelete) return;
    
    try {
      setDeleteError(null);
      const response = await fetch(`/api/reception-points/${pointToDelete.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete reception point');
      }
      
      // Refresh the list
      fetchReceptionPoints();
      setShowDeleteModal(false);
      setPointToDelete(null);
    } catch (err) {
      console.error('Error deleting reception point:', err);
      setDeleteError(err instanceof Error ? err.message : 'Failed to delete reception point');
    }
  };
  
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };
  
  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <div>
            <h1 className={styles.title}>Message Reception Points</h1>
            <p className={styles.description}>
              Manage where messages from guests are routed to staff
            </p>
          </div>
          <Link href="/reception-points/create">
            <Button variant="primary" label ="Add new">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M8 3.33334V12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3.33334 8H12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Create Reception Point
            </Button>
          </Link>
        </div>
        
        {error && (
          <Alert variant="error" title="Error" closable onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        <div className={styles.filtersContainer}>
          <SearchBar 
            placeholder="Search reception points..." 
            onChange={(e) => setSearchTerm(e.target.value)}
            className={styles.searchBar}
          />
          
          <div className={styles.statusFilter}>
            <select 
              value={statusFilter} 
              onChange={(e) => { 
                setStatusFilter(e.target.value); 
                setPage(1); 
              }}
              className={styles.select}
            >
              <option value="">All Statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
        
        {loading ? (
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Loading reception points...</p>
          </div>
        ) : filteredPoints.length === 0 ? (
          <div className={styles.emptyState}>
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="8" y1="12" x2="16" y2="12"></line>
              <line x1="8" y1="16" x2="16" y2="16"></line>
              <line x1="8" y1="8" x2="16" y2="8"></line>
            </svg>
            <h3>No reception points found</h3>
            <p>
              {searchTerm || statusFilter ? 
                'No reception points match your filters.' : 
                'No reception points have been created yet.'}
            </p>
            {!searchTerm && !statusFilter && (
              <Link href="/reception-points/create" className={styles.createButton}>
                Create Reception Point
              </Link>
            )}
          </div>
        ) : (
          <div className={styles.tableContainer}>
            <table className={styles.table}>
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Code</th>
                  <th>Priority</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredPoints.map((point) => (
                  <tr key={point.id}>
                    <td className={styles.nameCell}>
                      <Link href={`/reception-points/${point.id}`} className={styles.nameLink}>
                        {point.name}
                      </Link>
                      {point.description && (
                        <div className={styles.description}>
                          {point.description.length > 50 ? `${point.description.slice(0, 50)}...` : point.description}
                        </div>
                      )}
                    </td>
                    <td className={styles.codeCell}>{point.code}</td>
                    <td className={styles.priorityCell}>{point.priority}</td>
                    <td>
                      <span className={`${styles.statusBadge} ${point.is_active ? styles.active : styles.inactive}`}>
                        {point.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td>{formatDate(point.created_at)}</td>
                    <td className={styles.actionsCell}>
                      <div className={styles.actions}>
                        <Link href={`/reception-points/${point.id}/edit`} className={styles.editButton}>
                          Edit
                        </Link>
                        <button 
                          onClick={() => handleDeleteClick(point)} 
                          className={styles.deleteButton}
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        
        {totalPages > 1 && (
          <div className={styles.pagination}>
            <button 
              onClick={() => setPage(p => Math.max(p - 1, 1))} 
              disabled={page === 1}
              className={styles.paginationButton}
            >
              Previous
            </button>
            <span className={styles.pageInfo}>
              Page {page} of {totalPages}
            </span>
            <button 
              onClick={() => setPage(p => Math.min(p + 1, totalPages))} 
              disabled={page === totalPages}
              className={styles.paginationButton}
            >
              Next
            </button>
          </div>
        )}
      </div>
      
      {/* Delete Confirmation Modal */}
      {pointToDelete && (
        <DeleteConfirmModal
          isOpen={showDeleteModal}
          title="Delete Reception Point"
          message={`Are you sure you want to delete "${pointToDelete.name}" (${pointToDelete.code})? This action cannot be undone.`}
          onConfirm={handleConfirmDelete}
          onCancel={() => {
            setShowDeleteModal(false);
            setPointToDelete(null);
            setDeleteError(null);
          }}
          error={deleteError}
        />
      )}
    </DashboardLayout>
  );
}
