// D:\loaloa\apps\admin-portal\app\api\guests\route.ts
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Tạo Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// GET: <PERSON><PERSON><PERSON> danh s<PERSON>ch guest với filter và phân trang
export async function GET(request: Request) {
  try {
    // Trích xuất query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const room = searchParams.get('room') || '';
    const status = searchParams.get('status') || 'active';
    const tenant = searchParams.get('tenant') || '';
    
    // Tính toán offset cho phân trang
    const offset = (page - 1) * limit;
    
    // Tạo Supabase client
    const supabase = createSupabaseClient();
    
    // Xây dựng query
    let query = supabase.from('tenant_guests').select('*', { count: 'exact' });
    
    // Thêm điều kiện tìm kiếm
    if (search) {
      query = query.or(`full_name.ilike.%${search}%,email.ilike.%${search}%,phone.ilike.%${search}%`);
    }
    
    // Lọc theo phòng
    if (room) {
      query = query.eq('room_number', room);
    }
    
    // Lọc theo trạng thái
    if (status === 'active') {
      query = query.eq('is_active', true);
    } else if (status === 'inactive') {
      query = query.eq('is_active', false);
    }
    
    // Lọc theo tenant
    if (tenant) {
      query = query.eq('tenant_id', tenant);
    }
    
    // Phân trang và sắp xếp
    query = query.order('created_at', { ascending: false }).range(offset, offset + limit - 1);
    
    // Thực hiện truy vấn
    const { data, error, count } = await query;
    
    if (error) {
      console.error('Error fetching guests:', error);
      throw error;
    }
    
    // Trả về kết quả
    return NextResponse.json({
      data,
      meta: {
        total: count || 0,
        page,
        limit,
        pageCount: Math.ceil((count || 0) / limit)
      }
    });
    
  } catch (error) {
    console.error('Error in GET guests:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST: Tạo guest mới 
export async function POST(request: Request) {
  try {
    // Lấy dữ liệu từ request body
    const guestData = await request.json();
    
    // Kiểm tra dữ liệu bắt buộc
    if (!guestData.full_name || !guestData.tenant_id) {
      return NextResponse.json(
        { error: 'Missing required fields: full_name and tenant_id' },
        { status: 400 }
      );
    }
    
    // Tạo Supabase client
    const supabase = createSupabaseClient();
    
    // Chuyển đổi tenant_id thành UUID nếu cần
    // Thêm các giá trị mặc định nếu không được cung cấp
    const newGuest = {
      ...guestData,
      tenant_id: guestData.tenant_id, // Đảm bảo tenant_id là UUID hợp lệ
      is_active: guestData.is_active !== undefined ? guestData.is_active : true,
      check_in: guestData.room_number ? new Date().toISOString() : null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Thêm guest mới
    const { data, error } = await supabase
      .from('tenant_guests')
      .insert(newGuest)
      .select()
      .single();

    if (error) {
      console.error('Error creating guest:', error);
      throw error;
    }
    
    // Nếu đã chỉ định phòng, cập nhật trạng thái phòng
    if (guestData.room_number) {
      await supabase
        .from('tenant_rooms')
        .update({
          status: 'occupied',
          last_checkin: new Date().toISOString()
        })
        .eq('tenant_id', guestData.tenant_id)
        .eq('room_number', guestData.room_number);
    }

    // Trả về guest đã tạo
    return NextResponse.json(
      { data, message: 'Guest created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST guests:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}