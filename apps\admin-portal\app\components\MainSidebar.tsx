'use client';
import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import styles from './MainSidebar.module.scss';

export default function MainSidebar() {
  const [collapsed, setCollapsed] = useState(false);
  const pathname = usePathname();

  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`);
  };

  return (
    <aside className={`${styles.sidebar} ${collapsed ? styles.collapsed : ''}`}>
      <div className={styles.logoContainer}>
        <Link href="/dashboard" className={styles.logo}>
          <img src="/logo.png" alt="LoaLoa" className={styles.logoImage} />
          {!collapsed && <span className={styles.logoText}>LoaLoa</span>}
        </Link>
        <button 
          className={styles.collapseButton} 
          onClick={() => setCollapsed(!collapsed)} 
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path 
              d={collapsed ? "M8.33333 5.83333L12.5 10L8.33333 14.1667" : "M11.6667 5.83333L7.5 10L11.6667 14.1667"} 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              strokeLinejoin="round" 
            />
          </svg>
        </button>
      </div>

      <nav className={styles.navigation}>
        <Link 
          href="/dashboard" 
          className={`${styles.navLink} ${isActive('/dashboard') ? styles.active : ''}`}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M3.33334 10.8333V16.6667H8.33334V12.5H11.6667V16.6667H16.6667V10.8333L10 5.83333L3.33334 10.8333Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M1.66666 9.16667L10 2.5L18.3333 9.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          {!collapsed && <span>Dashboard</span>}
        </Link>

        <Link 
          href="/guests" 
          className={`${styles.navLink} ${isActive('/guests') ? styles.active : ''}`}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M13.3333 5.83333C13.3333 7.67428 11.8409 9.16667 10 9.16667C8.15905 9.16667 6.66666 7.67428 6.66666 5.83333C6.66666 3.99238 8.15905 2.5 10 2.5C11.8409 2.5 13.3333 3.99238 13.3333 5.83333Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M10 11.6667C6.66666 11.6667 3.33334 13.3333 3.33334 17.5H16.6667C16.6667 13.3333 13.3333 11.6667 10 11.6667Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          {!collapsed && <span>Guests</span>}
        </Link>

        <Link 
          href="/qr-codes" 
          className={`${styles.navLink} ${isActive('/qr-codes') ? styles.active : ''}`}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M8.33333 2.5H2.5V8.33333H8.33333V2.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M17.5 2.5H11.6667V8.33333H17.5V2.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M17.5 11.6667H11.6667V17.5H17.5V11.6667Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8.33333 11.6667H2.5V17.5H8.33333V11.6667Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          {!collapsed && <span>QR Codes</span>}
        </Link>

        {/* Thêm liên kết đến Reception Points */}
        <Link 
          href="/reception-points" 
          className={`${styles.navLink} ${isActive('/reception-points') ? styles.active : ''}`}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M14.1667 10.8333C14.1667 12.7663 12.599 14.3333 10.6667 14.3333C8.7343 14.3333 7.16667 12.7663 7.16667 10.8333C7.16667 8.90037 8.7343 7.33334 10.6667 7.33334C12.599 7.33334 14.1667 8.90037 14.1667 10.8333Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M10.6667 14.3333V17.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M3.83333 14.9583L5.63542 13.1562C5.875 12.9167 6.25 12.9167 6.48958 13.1562L7.02083 13.6875" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M14.3125 13.6875L14.8438 13.1562C15.0833 12.9167 15.4583 12.9167 15.6979 13.1562L17.5 14.9583" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M7.02083 8.02083L6.48958 8.55208C6.25 8.79167 5.875 8.79167 5.63542 8.55208L3.83333 6.75" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M17.5 6.75L15.6979 8.55208C15.4583 8.79167 15.0833 8.79167 14.8438 8.55208L14.3125 8.02083" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M10.6667 4.16667V7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          {!collapsed && <span>Reception Points</span>}
        </Link>
	<Link 
  href="/chat-routing-rules" 
  className={`${styles.navLink} ${isActive('/chat-routing-rules') ? styles.active : ''}`}
>
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
    <path d="M10 12.5L2.5 7.5L10 2.5L17.5 7.5L10 12.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M2.5 12.5L10 17.5L17.5 12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
  {!collapsed && <span>Chat Routing</span>}
</Link>

<Link 
  href="/staff-assignments" 
  className={`${styles.navLink} ${isActive('/staff-assignments') ? styles.active : ''}`}
>
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
    <path d="M9.16666 10C11.0076 10 12.5 8.5076 12.5 6.66666C12.5 4.82573 11.0076 3.33333 9.16666 3.33333C7.32573 3.33333 5.83333 4.82573 5.83333 6.66666C5.83333 8.5076 7.32573 10 9.16666 10Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M15 3.33334C15.884 3.33334 16.6667 4.17334 16.6667 5.16667C16.6667 6.12834 15.9713 6.94834 15.0907 7C15.0305 6.98644 14.9697 6.97811 14.9087 6.97501M15 16.6667C13.328 14.9447 11.044 13.9593 8.51934 13.842C8.337 13.8313 8.16634 13.8333 8 13.8333C7.83366 13.8333 7.663 13.8313 7.48066 13.842C4.956 13.9593 2.672 14.9447 1 16.6667M14.9087 6.97501C14.726 6.96475 14.5412 6.96178 14.356 6.966C11.876 6.99267 9.89667 8.10067 8.73267 9.84934C8.618 10.03 8.31067 10.03 8.19667 9.84934C7.032 8.09934 5.052 6.99134 2.56867 6.966C2.38333 6.96178 2.19854 6.96475 2.01667 6.97501" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
  {!collapsed && <span>Staff Assignments</span>}
</Link>

	
        <Link 
          href="/rooms-areas" 
          className={`${styles.navLink} ${isActive('/rooms-areas') ? styles.active : ''}`}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M17.5 7.5V5.83333C17.5 5.09695 17.0083 4.58333 16.25 4.58333H3.75C2.99167 4.58333 2.5 5.09695 2.5 5.83333V7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
	    <path d="M17.5 12.5V14.1667C17.5 14.9031 17.0083 15.4167 16.25 15.4167H3.75C2.99167 15.4167 2.5 14.9031 2.5 14.1667V12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <rect x="7.5" y="7.5" width="5" height="5" rx="1" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M5 7.5V12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M15 7.5V12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          {!collapsed && <span>Rooms & Areas</span>}
        </Link>

        <Link 
          href="/users" 
          className={`${styles.navLink} ${isActive('/users') ? styles.active : ''}`}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M13.3333 5.83333C13.3333 7.67428 11.8409 9.16667 10 9.16667C8.15905 9.16667 6.66666 7.67428 6.66666 5.83333C6.66666 3.99238 8.15905 2.5 10 2.5C11.8409 2.5 13.3333 3.99238 13.3333 5.83333Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M5 17.5C5 14.5 7.5 12.5 10 12.5C12.5 12.5 15 14.5 15 17.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M17.5 10.8333C18.0833 12 18.3333 13.3333 18.3333 15C18.3333 15.9167 18.1667 16.75 17.9167 17.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M2.08334 17.5C1.83334 16.75 1.66667 15.9167 1.66667 15C1.66667 13.3333 1.91667 12 2.5 10.8333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          {!collapsed && <span>Users</span>}
        </Link>

        <Link 
          href="/reports" 
          className={`${styles.navLink} ${isActive('/reports') ? styles.active : ''}`}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M17.5 16.6667V8.33333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M10 16.6667V3.33333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M2.5 16.6667V11.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          {!collapsed && <span>Reports</span>}
        </Link>

        <Link 
          href="/settings" 
          className={`${styles.navLink} ${isActive('/settings') ? styles.active : ''}`}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M16.1667 10C16.1667 10.8333 15.8333 11.9167 15.4167 12.5833L17.0833 14.25C16.25 15.5 15.0833 16.5 13.75 17.1667L12.0833 15.5C11.4167 15.8333 10.8333 16.1667 10 16.1667C9.16667 16.1667 8.58333 15.8333 7.91667 15.5L6.25 17.1667C4.91667 16.5 3.75 15.5 2.91667 14.25L4.58333 12.5833C4.16667 11.9167 3.83333 10.8333 3.83333 10C3.83333 9.16667 4.16667 8.08333 4.58333 7.41667L2.91667 5.75C3.75 4.5 4.91667 3.5 6.25 2.83333L7.91667 4.5C8.58333 4.16667 9.16667 3.83333 10 3.83333C10.8333 3.83333 11.4167 4.16667 12.0833 4.5L13.75 2.83333C15.0833 3.5 16.25 4.5 17.0833 5.75L15.4167 7.41667C15.8333 8.08333 16.1667 9.16667 16.1667 10Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          {!collapsed && <span>Settings</span>}
        </Link>
      </nav>

      <div className={styles.userSection}>
        <div className={styles.userInfo}>
          <div className={styles.userAvatar}>A</div>
          {!collapsed && (
            <div className={styles.userDetails}>
              <p className={styles.userName}>Admin</p>
              <p className={styles.userRole}>Administrator</p>
            </div>
          )}
        </div>
        <Link 
          href="/logout" 
          className={styles.logoutButton}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M7.5 17.5H4.16667C3.72464 17.5 3.30072 17.3244 2.98816 17.0118C2.67559 16.6993 2.5 16.2754 2.5 15.8333V4.16667C2.5 3.72464 2.67559 3.30072 2.98816 2.98816C3.30072 2.67559 3.72464 2.5 4.16667 2.5H7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M13.3333 14.1667L17.5 10L13.3333 5.83337" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M17.5 10H7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          {!collapsed && <span>Logout</span>}
        </Link>
      </div>
    </aside>
  );
}