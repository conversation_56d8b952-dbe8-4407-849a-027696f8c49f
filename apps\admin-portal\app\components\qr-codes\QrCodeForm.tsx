import React, { useState, useEffect } from 'react';
import styles from './QrCodeForm.module.scss';
import { Button } from '@ui';

interface QrCodeFormProps {
  initialData?: any;
  onSubmit: (formData: any) => Promise<void>;
  isSubmitting?: boolean;
  isEditing?: boolean;
}

export default function QrCodeForm({
  initialData = {},
  onSubmit,
  isSubmitting = false,
  isEditing = false
}: QrCodeFormProps) {
  // Form state
  const [formData, setFormData] = useState({
    name: initialData.name || '',
    description: initialData.description || '',
    qr_type_id: initialData.qr_type_id || '',
    target_type: initialData.target_type || 'none', // Changed from link_type
    target_id: initialData.target_id || '', // Changed from room_id/area_id
    room_id: initialData.room_id || '',  // Keep for backward compatibility
    target_department: initialData.target_department || '',
    custom_action: initialData.custom_action || {},
    location: initialData.location || '',
    reception_point_id: initialData.reception_point_id || '',
    is_active: initialData.is_active !== undefined ? initialData.is_active : true
  });

  // Form validation and active tab
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState('basic');
  const [previewQrUrl, setPreviewQrUrl] = useState('');

  // Load data for selects
  const [qrTypes, setQrTypes] = useState<any[]>([]);
  const [rooms, setRooms] = useState<any[]>([]);
  const [areas, setAreas] = useState<any[]>([]);
  const [receptionPoints, setReceptionPoints] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Load data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch all required data in parallel
        const [typesRes, roomsRes, areasRes, pointsRes] = await Promise.all([
          fetch('/api/qr-code-types'),
          fetch('/api/rooms?limit=100'),
          fetch('/api/areas?limit=100'),
          fetch('/api/reception-points?limit=100')
        ]);
        
        const [typesData, roomsData, areasData, pointsData] = await Promise.all([
          typesRes.json(),
          roomsRes.json(),
          areasRes.json(),
          pointsRes.json()
        ]);
        
        setQrTypes(typesData.data || []);
        setRooms(roomsData.data || []);
        setAreas(areasData.data || []);
        setReceptionPoints(pointsData.data || []);
        
        // Generate preview QR if editing
        if (isEditing && initialData.id) {
          setPreviewQrUrl(`/api/qr-codes/${initialData.id}/image?size=200`);
        }
      } catch (err) {
        console.error('Error fetching form data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [isEditing, initialData.id]);

  // Handle form field changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // Handle target type change
    if (name === 'target_type') {
      // Reset target_id when type changes
      setFormData(prev => ({ ...prev, target_id: '' }));
      
      // For backward compatibility
      if (value === 'room') {
        setFormData(prev => ({ ...prev, room_id: '' }));
      }
    }
    
    // Handle QR type change
    if (name === 'qr_type_id') {
      const selectedType = qrTypes.find(type => type.id === value);
      if (selectedType) {
        // Update custom action based on type
        let updatedCustomAction = { ...formData.custom_action };
        
        if (selectedType.default_action === 'chat') {
          updatedCustomAction = { action: 'chat' };
        } else if (selectedType.default_action === 'info') {
          updatedCustomAction = { 
            action: 'info', 
            content: formData.custom_action.content || '' 
          };
        } else if (selectedType.default_action === 'service') {
          updatedCustomAction = { 
            action: 'service', 
            service_type: formData.custom_action.service_type || 'general' 
          };
        } else if (selectedType.default_action === 'feedback') {
          updatedCustomAction = { 
            action: 'feedback', 
            feedback_template: formData.custom_action.feedback_template || 'default' 
          };
        }
        
        setFormData(prev => ({ ...prev, custom_action: updatedCustomAction }));
      }
    }
  };

  // Handle custom action changes
  const handleCustomActionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      custom_action: { ...prev.custom_action, [name]: value }
    }));
  };

  // Form validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // Basic validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.qr_type_id) {
      newErrors.qr_type_id = 'QR Code Type is required';
    }
    
    if (!formData.location.trim()) {
      newErrors.location = 'Physical location is required';
    }
    
    // Validate target selection
    if (formData.target_type === 'room' && !formData.target_id) {
      newErrors.target_id = 'Please select a room';
    }
    
    if (formData.target_type === 'area' && !formData.target_id) {
      newErrors.target_id = 'Please select an area';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) {
      // If there are errors, switch to the tab that has errors
      if (errors.name || errors.qr_type_id || errors.location) {
        setActiveTab('basic');
      } else if (errors.target_id) {
        setActiveTab('association');
      }
      return;
    }

    // Clean up formData before submission
    const submissionData = { ...formData };
    
    // Set room_id for backward compatibility if target_type is room
    if (formData.target_type === 'room') {
      submissionData.room_id = formData.target_id;
    } else {
      delete submissionData.room_id;
    }
    
    // Delete unnecessary fields
    delete submissionData.target_type;
    
    // Submit form
    await onSubmit(submissionData);
  };

  // Get selected QR type
  const selectedType = formData.qr_type_id
    ? qrTypes.find(type => type.id === formData.qr_type_id)
    : null;

  // Render the form
  return (
    <div className={styles.formContainer}>
      {loading ? (
        <div className={styles.loading}>Loading form data...</div>
      ) : (
        <form onSubmit={handleSubmit} className={styles.form}>
          {/* Tabs navigation */}
          <div className={styles.tabs}>
            <button 
              type="button"
              onClick={() => setActiveTab('basic')}
              className={`${styles.tab} ${activeTab === 'basic' ? styles.active : ''}`}
            >
              Basic Information
            </button>
            <button 
              type="button"
              onClick={() => setActiveTab('association')}
              className={`${styles.tab} ${activeTab === 'association' ? styles.active : ''}`}
            >
              Location & Association
            </button>
            <button 
              type="button"
              onClick={() => setActiveTab('routing')}
              className={`${styles.tab} ${activeTab === 'routing' ? styles.active : ''}`}
            >
              Message Routing
            </button>
            {selectedType && (
              <button 
                type="button"
                onClick={() => setActiveTab('actions')}
                className={`${styles.tab} ${activeTab === 'actions' ? styles.active : ''}`}
              >
                QR Actions
              </button>
            )}
            {isEditing && (
              <button 
                type="button"
                onClick={() => setActiveTab('preview')}
                className={`${styles.tab} ${activeTab === 'preview' ? styles.active : ''}`}
              >
                Preview
              </button>
            )}
          </div>

          {/* Basic Information Tab */}
          <div className={`${styles.tabContent} ${activeTab === 'basic' ? styles.active : ''}`}>
            <div className={styles.formSection}>
              <h2 className={styles.sectionTitle}>Basic Information</h2>
              <div className={styles.helpText}>
                Enter the basic details for your QR code
              </div>
              
              <div className={styles.formGrid}>
                <div className={styles.formGroup}>
                  <label htmlFor="name">
                    QR Code Name <span className={styles.required}>*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={errors.name ? styles.inputError : ''}
                    placeholder="Enter a descriptive name (e.g., Restaurant Menu QR)"
                  />
                  {errors.name && <div className={styles.errorText}>{errors.name}</div>}
                  <div className={styles.fieldDescription}>
                    A clear name to identify this QR code in the admin panel
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="qr_type_id">
                    QR Code Type <span className={styles.required}>*</span>
                  </label>
                  <select
                    id="qr_type_id"
                    name="qr_type_id"
                    value={formData.qr_type_id}
                    onChange={handleInputChange}
                    className={errors.qr_type_id ? styles.inputError : ''}
                  >
                    <option value="">Select a type</option>
                    {qrTypes.map(type => (
                      <option key={type.id} value={type.id}>
                        {type.name} - {type.description || type.default_action}
                      </option>
                    ))}
                  </select>
                  {errors.qr_type_id && <div className={styles.errorText}>{errors.qr_type_id}</div>}
                  <div className={styles.fieldDescription}>
                    This determines what happens when a guest scans this QR code
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="description">Description</label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={3}
                    placeholder="Enter a detailed description for internal use"
                  />
                  <div className={styles.fieldDescription}>
                    Additional details about this QR code (only visible to admin)
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="is_active">Status</label>
                  <div className={styles.checkboxContainer}>
                    <input
                      type="checkbox"
                      id="is_active"
                      name="is_active"
                      checked={formData.is_active}
                      onChange={handleInputChange}
                    />
                    <label htmlFor="is_active" className={styles.checkboxLabel}>
                      Active
                    </label>
                  </div>
                  <div className={styles.fieldDescription}>
                    Inactive QR codes will not function when scanned
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Location & Association Tab */}
          <div className={`${styles.tabContent} ${activeTab === 'association' ? styles.active : ''}`}>
            <div className={styles.formSection}>
              <h2 className={styles.sectionTitle}>Physical Location & Association</h2>
              <div className={styles.helpText}>
                Specify where this QR code is physically located and what it's associated with
              </div>
              
              <div className={styles.formGroup}>
                <label htmlFor="location">
                  Physical Placement <span className={styles.required}>*</span>
                </label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  className={errors.location ? styles.inputError : ''}
                  placeholder="E.g., Restaurant entrance, Room #101 desk, Pool area sign"
                />
                {errors.location && <div className={styles.errorText}>{errors.location}</div>}
                <div className={styles.fieldDescription}>
                  The exact physical location where this QR code will be placed
                </div>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="target_type">Associate With</label>
                <div className={styles.radioGroup}>
                  <div className={styles.radioOption}>
                    <input
                      type="radio"
                      id="target_none"
                      name="target_type"
                      value="none"
                      checked={formData.target_type === 'none'}
                      onChange={handleInputChange}
                    />
                    <label htmlFor="target_none">None (General QR)</label>
                  </div>
                  <div className={styles.radioOption}>
                    <input
                      type="radio"
                      id="target_room"
                      name="target_type"
                      value="room"
                      checked={formData.target_type === 'room'}
                      onChange={handleInputChange}
                    />
                    <label htmlFor="target_room">Room</label>
                  </div>
                  <div className={styles.radioOption}>
                    <input
                      type="radio"
                      id="target_area"
                      name="target_type"
                      value="area"
                      checked={formData.target_type === 'area'}
                      onChange={handleInputChange}
                    />
                    <label htmlFor="target_area">Area/Facility</label>
                  </div>
                </div>
                <div className={styles.fieldDescription}>
                  Link this QR code to a specific room or area in your property
                </div>
              </div>

              {formData.target_type === 'room' && (
                <div className={styles.formGroup}>
                  <label htmlFor="target_id">
                    Select Room <span className={styles.required}>*</span>
                  </label>
                  <select
                    id="target_id"
                    name="target_id"
                    value={formData.target_id}
                    onChange={handleInputChange}
                    className={errors.target_id ? styles.inputError : ''}
                  >
                    <option value="">Select a room</option>
                    {rooms.map(room => (
                      <option key={room.id} value={room.id}>
                        Room {room.room_number} - {room.room_type} (Floor {room.floor})
                      </option>
                    ))}
                  </select>
                  {errors.target_id && <div className={styles.errorText}>{errors.target_id}</div>}
                  <div className={styles.fieldDescription}>
                    Messages from this QR will include room details
                  </div>
                </div>
              )}

              {formData.target_type === 'area' && (
                <div className={styles.formGroup}>
                  <label htmlFor="target_id">
                    Select Area <span className={styles.required}>*</span>
                  </label>
                  <select
                    id="target_id"
                    name="target_id"
                    value={formData.target_id}
                    onChange={handleInputChange}
                    className={errors.target_id ? styles.inputError : ''}
                  >
                    <option value="">Select an area</option>
                    {areas.map(area => (
                      <option key={area.id} value={area.id}>
                        {area.name} - {area.area_type} {area.floor ? `(Floor ${area.floor})` : ''}
                      </option>
                    ))}
                  </select>
                  {errors.target_id && <div className={styles.errorText}>{errors.target_id}</div>}
                  <div className={styles.fieldDescription}>
                    Messages from this QR will include area details
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Message Routing Tab */}
          <div className={`${styles.tabContent} ${activeTab === 'routing' ? styles.active : ''}`}>
            <div className={styles.formSection}>
              <h2 className={styles.sectionTitle}>Message Routing</h2>
              <div className={styles.helpText}>
                Configure where messages from this QR code will be directed
              </div>
              
              <div className={styles.formGroup}>
                <label htmlFor="reception_point_id">Reception Point</label>
                <select
                  id="reception_point_id"
                  name="reception_point_id"
                  value={formData.reception_point_id || ''}
                  onChange={handleInputChange}
                >
                  <option value="">No specific reception point</option>
                  {receptionPoints.map(point => (
                    <option key={point.id} value={point.id}>
                      {point.name} {point.description ? `- ${point.description}` : ''}
                    </option>
                  ))}
                </select>
                <div className={styles.fieldDescription}>
                  Messages will be routed to staff assigned to this reception point
                </div>
              </div>
              
              {selectedType && selectedType.default_action === 'chat' && (
                <div className={styles.formGroup}>
                  <label htmlFor="target_department">Alternative Department</label>
                  <select
                    id="target_department"
                    name="target_department"
                    value={formData.target_department}
                    onChange={handleInputChange}
                  >
                    <option value="">No specific department</option>
                    <option value="reception">Reception</option>
                    <option value="housekeeping">Housekeeping</option>
                    <option value="restaurant">Restaurant</option>
                    <option value="spa">Spa</option>
                    <option value="concierge">Concierge</option>
                    <option value="maintenance">Maintenance</option>
                  </select>
                  <div className={styles.fieldDescription}>
                    Only used if no reception point is specified or available
                  </div>
                </div>
              )}
              
              <div className={styles.infoBox}>
                <div className={styles.infoIcon}>i</div>
                <div>
                  <strong>How message routing works:</strong>
                  <ol>
                    <li>When a guest scans this QR, the system first checks if a Reception Point is assigned</li>
                    <li>If yes, the message is routed to staff assigned to that reception point</li>
                    <li>If no Reception Point or no staff available, the system uses the department</li>
                    <li>For more complex routing (by time, language, etc.), create Chat Routing Rules</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>

          {/* QR Actions Tab */}
          {selectedType && (
            <div className={`${styles.tabContent} ${activeTab === 'actions' ? styles.active : ''}`}>
              <div className={styles.formSection}>
                <h2 className={styles.sectionTitle}>
                  {selectedType.default_action.charAt(0).toUpperCase() + selectedType.default_action.slice(1)} Settings
                </h2>
                <div className={styles.helpText}>
                  Configure what happens when a guest scans this QR code
                </div>
                
                {selectedType.default_action === 'info' && (
                  <div className={styles.formGroup}>
                    <label htmlFor="content">Information Content</label>
                    <textarea
                      id="content"
                      name="content"
                      value={formData.custom_action.content || ''}
                      onChange={handleCustomActionChange}
                      rows={5}
                      placeholder="Enter information to display when this QR code is scanned"
                    />
                    <div className={styles.fieldDescription}>
                      This text will be displayed to guests when they scan this QR code.
                      You can include welcome messages, operating hours, available services, etc.
                    </div>
                  </div>
                )}
                
                {selectedType.default_action === 'service' && (
                  <div className={styles.formGroup}>
                    <label htmlFor="service_type">Service Type</label>
                    <select
                      id="service_type"
                      name="service_type"
                      value={formData.custom_action.service_type || 'general'}
                      onChange={handleCustomActionChange}
                    >
                      <option value="general">General Service Request</option>
                      <option value="room_service">Room Service</option>
                      <option value="housekeeping">Housekeeping</option>
                      <option value="maintenance">Maintenance</option>
                      <option value="transportation">Transportation</option>
                    </select>
                    <div className={styles.fieldDescription}>
                      Defines the type of service a guest is requesting when they scan this QR code
                    </div>
                  </div>
                )}
                
                {selectedType.default_action === 'feedback' && (
                  <div className={styles.formGroup}>
                    <label htmlFor="feedback_template">Feedback Template</label>
                    <select
                      id="feedback_template"
                      name="feedback_template"
                      value={formData.custom_action.feedback_template || 'default'}
                      onChange={handleCustomActionChange}
                    >
                      <option value="default">General Feedback</option>
                      <option value="room">Room Experience</option>
                      <option value="restaurant">Restaurant Experience</option>
                      <option value="service">Service Quality</option>
                      <option value="checkout">Checkout Experience</option>
                    </select>
                    <div className={styles.fieldDescription}>
                      Choose the feedback form that will be presented when guests scan this QR code
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* Preview Tab (Only when editing) */}
          {isEditing && (
            <div className={`${styles.tabContent} ${activeTab === 'preview' ? styles.active : ''}`}>
              <div className={styles.formSection}>
                <h2 className={styles.sectionTitle}>QR Code Preview</h2>
                
                <div className={styles.qrPreviewContainer}>
                  {previewQrUrl ? (
                    <>
                      <div className={styles.qrImage}>
                        <img src={previewQrUrl} alt="QR Code Preview" />
                      </div>
                      <div className={styles.qrActions}>
                        <a 
                          href={`/api/qr-codes/${initialData.id}/download`}
                          download={`qrcode-${formData.name.toLowerCase().replace(/\s+/g, '-')}.png`}
                          className={styles.downloadButton}
                        >
                          Download QR Code
                        </a>
                        <p className={styles.qrCodeValue}>
                          Code: <span>{initialData.code_value}</span>
                        </p>
                        <p className={styles.scanCount}>
                          Total scans: <span>{initialData.scan_count || 0}</span>
                        </p>
                      </div>
                    </>
                  ) : (
                    <div className={styles.noPreview}>
                      QR code preview not available
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Form actions */}
          <div className={styles.formActions}>
            <Button
              type="button"
              variant="secondary"
              label="Cancel"
              onClick={() => window.history.back()}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              label={isEditing ? 'Update QR Code' : 'Create QR Code'}
              disabled={isSubmitting}
              loading={isSubmitting}
            >
              {isEditing ? 'Update QR Code' : 'Create QR Code'}
            </Button>
          </div>
        </form>
      )}
    </div>
  );
}
