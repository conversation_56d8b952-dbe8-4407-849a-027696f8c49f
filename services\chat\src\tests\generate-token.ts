import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'default_secret_key';

const userId = '49bd458c-45b6-4791-a3af-0ddf710d3a25'; // ID của bạn
const email = '<EMAIL>'; // Email của bạn
const roles = ['admin', 'staff']; // Các vai trò của bạn

const payload = {
  userId,
  email,
  roles,
  preferredLanguage: 'vi'
};

const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '1h' });

console.log('=== JWT TOKEN ===');
console.log(token);
console.log('=================');
