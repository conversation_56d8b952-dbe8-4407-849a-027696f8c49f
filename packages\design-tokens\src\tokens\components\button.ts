import { spacing } from '../primitives/spacing';
import { borders } from '../primitives/borders';
import { typography } from '../primitives/typography';
import { shadows } from '../primitives/shadows';

export const buttonTokens = {
  // Kích thước
  sizes: {
    sm: {
      padding: `${spacing[1.5]} ${spacing[3]}`,
      fontSize: typography.fontSizes.sm,
      lineHeight: typography.lineHeights.normal
    },
    md: {
      padding: `${spacing[2]} ${spacing[4]}`,
      fontSize: typography.fontSizes.base,
      lineHeight: typography.lineHeights.normal
    },
    lg: {
      padding: `${spacing[2.5]} ${spacing[5]}`,
      fontSize: typography.fontSizes.md,
      lineHeight: typography.lineHeights.normal
    }
  },
  
  // Border radius
  borderRadius: borders.radii.md,
  
  // Font weight
  fontWeight: typography.fontWeights.medium,
  
  // Hiệu ứng shadow
  shadow: shadows.sm,
  shadowHover: shadows.md,
  
  // Transition
  transition: 'all 0.2s ease-in-out'
};
