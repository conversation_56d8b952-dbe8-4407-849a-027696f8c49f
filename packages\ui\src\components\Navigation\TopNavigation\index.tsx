import React, { useState } from 'react';

export interface NavLink {
  /**
   * Link label
   */
  label: string;
  /**
   * Link URL or path
   */
  href: string;
  /**
   * Is this the active link
   */
  active?: boolean;
  /**
   * Optional icon
   */
  icon?: React.ReactNode;
}

export interface TopNavigationProps {
  /**
   * Logo component or text
   */
  logo: React.ReactNode;
  /**
   * Navigation links
   */
  links: NavLink[];
  /**
   * Additional items to display on the right side (e.g., button, profile)
   */
  rightItems?: React.ReactNode;
  /**
   * Theme variant
   */
  variant?: 'light' | 'dark' | 'studio';
  /**
   * Optional shadow
   */
  shadow?: boolean;
  /**
   * Whether to make the navigation sticky
   */
  sticky?: boolean;
  /**
   * Additional CSS properties
   */
  style?: React.CSSProperties;
  /**
   * Optional CSS class name
   */
  className?: string;
}

export const TopNavigation: React.FC<TopNavigationProps> = ({
  logo,
  links,
  rightItems,
  variant = 'light',
  shadow = true,
  sticky = false,
  style,
  className,
  ...props
}) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Theme-based colors
  const themeColors = {
    light: {
      background: '#FFFFFF',
      text: '#010103',
      activeText: '#FF4D00',
      border: '#EBEBEB',
      mobileMenuBackground: '#FFFFFF',
    },
    dark: {
      background: '#1E1E1E',
      text: '#EBEBEB',
      activeText: '#F9F871',
      border: '#161616',
      mobileMenuBackground: '#161616',
    },
    studio: {
      background: '#16262E',
      text: '#EBEBEB',
      activeText: '#FF4D00',
      border: '#2E4756',
      mobileMenuBackground: '#16262E',
    },
  };

  // Container style
  const containerStyle: React.CSSProperties = {
    position: sticky ? 'sticky' : 'relative',
    top: sticky ? 0 : 'auto',
    zIndex: 1000,
    width: '100%',
    backgroundColor: themeColors[variant].background,
    borderBottom: `1px solid ${themeColors[variant].border}`,
    boxShadow: shadow ? '0 2px 4px rgba(0,0,0,0.08)' : 'none',
    ...style,
  };

  // Nav style
  const navStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '12px 24px',
    maxWidth: '1200px',
    margin: '0 auto',
  };

  // Logo container style
  const logoStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    color: themeColors[variant].text,
    fontWeight: 600,
    fontSize: '20px',
    marginRight: '24px',
  };

  // Links container style
  const linksContainerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    listStyle: 'none',
    margin: 0,
    padding: 0,
    gap: '24px',
    '@media (max-width: 768px)': {
      display: 'none',
    },
  };

  // Link style
  const getLinkStyle = (active: boolean = false): React.CSSProperties => ({
    color: active ? themeColors[variant].activeText : themeColors[variant].text,
    textDecoration: 'none',
    fontWeight: active ? 600 : 500,
    fontSize: '16px',
    padding: '6px 0',
    borderBottom: active ? `2px solid ${themeColors[variant].activeText}` : '2px solid transparent',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  });

  // Right items container style
  const rightItemsStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '16px',
    '@media (max-width: 768px)': {
      display: 'none',
    },
  };

  // Mobile menu button style
  const mobileMenuButtonStyle: React.CSSProperties = {
    display: 'none',
    '@media (max-width: 768px)': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent',
      border: 'none',
      color: themeColors[variant].text,
      fontSize: '24px',
      cursor: 'pointer',
      padding: '8px',
    },
  };

  // Mobile menu style
  const mobileMenuStyle: React.CSSProperties = {
    display: mobileMenuOpen ? 'flex' : 'none',
    flexDirection: 'column',
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: themeColors[variant].mobileMenuBackground,
    borderTop: `1px solid ${themeColors[variant].border}`,
    boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
    zIndex: 1000,
    padding: '16px',
    '@media (min-width: 769px)': {
      display: 'none',
    },
  };

  // Mobile link style
  const mobileLinkStyle: React.CSSProperties = {
    padding: '12px 16px',
    color: themeColors[variant].text,
    textDecoration: 'none',
    fontSize: '16px',
    fontWeight: 500,
    borderRadius: '4px',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  };

  // Active mobile link style
  const activeMobileLinkStyle: React.CSSProperties = {
    ...mobileLinkStyle,
    backgroundColor: 'rgba(255,77,0,0.1)',
    color: themeColors[variant].activeText,
    fontWeight: 600,
  };

  // Hamburger icon for mobile menu
  const HamburgerIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  // Close icon for mobile menu
  const CloseIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6 18L18 6M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <div style={containerStyle} className={className} {...props}>
      <nav style={navStyle}>
        <div style={logoStyle}>{logo}</div>
        
        <ul style={linksContainerStyle}>
          {links.map((link, index) => (
            <li key={index}>
              <a 
                href={link.href} 
                style={getLinkStyle(link.active)}
              >
                {link.icon && <span>{link.icon}</span>}
                {link.label}
              </a>
            </li>
          ))}
        </ul>
        
        {rightItems && (
          <div style={rightItemsStyle}>
            {rightItems}
          </div>
        )}
        
        <button 
          style={mobileMenuButtonStyle} 
          onClick={toggleMobileMenu}
          aria-label={mobileMenuOpen ? 'Close menu' : 'Open menu'}
        >
          {mobileMenuOpen ? <CloseIcon /> : <HamburgerIcon />}
        </button>
      </nav>
      
      {/* Mobile menu */}
      <div style={mobileMenuStyle}>
        {links.map((link, index) => (
          <a 
            key={index}
            href={link.href} 
            style={link.active ? activeMobileLinkStyle : mobileLinkStyle}
          >
            {link.icon && <span>{link.icon}</span>}
            {link.label}
          </a>
        ))}
        
        {rightItems && <div style={{ padding: '16px 0' }}>{rightItems}</div>}
      </div>
    </div>
  );
};

export default TopNavigation;
