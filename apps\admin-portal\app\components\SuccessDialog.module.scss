.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  width: 100%;
  max-width: 500px;
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.icon {
  margin-bottom: 16px;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.message {
  text-align: center;
  margin-bottom: 24px;
  color: #4b5563;
  font-size: 1rem;
  line-height: 1.5;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.backButton {
  padding: 10px 20px;
  border-radius: 6px;
  background-color: #f3f4f6;
  color: #1f2937;
  font-weight: 500;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  text-decoration: none;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #e5e7eb;
  }
}

.continueButton {
  padding: 10px 20px;
  border-radius: 6px;
  background-color: #2563eb;
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  text-decoration: none;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #1d4ed8;
  }
}
