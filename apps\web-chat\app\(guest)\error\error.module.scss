.errorPage {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  padding: 1rem;
}

.errorContainer {
  background: white;
  border-radius: 1rem;
  padding: 3rem 2rem;
  text-align: center;
  max-width: 28rem;
  width: 100%;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.6s ease-out;
  
  @media (max-width: 640px) {
    padding: 2rem 1.5rem;
  }
}

.errorIcon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  animation: bounce 2s infinite;
}

.errorTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #dc2626;
  margin-bottom: 1rem;
  
  @media (max-width: 640px) {
    font-size: 1.25rem;
  }
}

.errorMessage {
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.errorCode {
  background: #f3f4f6;
  color: #374151;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  margin-bottom: 2rem;
}

.buttonGroup {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 0.75rem;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px rgba(220, 38, 38, 0.3);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
  }
}

.secondaryButton {
  background: white;
  color: #374151;
  padding: 0.75rem 2rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #dc2626;
    color: #dc2626;
    transform: translateY(-2px);
  }
}

// Animations
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-30px);
  }
  70% {
    transform: translateY(-15px);
  }
  90% {
    transform: translateY(-4px);
  }
}
