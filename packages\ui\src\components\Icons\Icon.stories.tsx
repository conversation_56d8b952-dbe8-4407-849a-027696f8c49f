import React from 'react';
import { StoryObj, Meta } from '@storybook/react';
import { Icon } from './Icon';
import * as Icons from './icons';

export default {
  title: 'UI/Icons',
  component: Icon,
  argTypes: {
    icon: {
      control: 'select',
      options: ['home', 'arrow', 'search', 'notification', 'settings'],
      description: 'Icon name'
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large', 16, 24, 32, 48],
      description: 'Icon size'
    },
    color: {
      control: 'color',
      description: 'Icon color'
    },
    variant: {
      control: 'select',
      options: ['filled', 'outlined', 'twoTone'],
      description: 'Icon variant'
    },
    theme: {
      control: 'select',
      options: ['light', 'dark', 'studio'],
      description: 'Theme'
    }
  },
  parameters: {
    componentSubtitle: 'Icons System cho LoaLoa UI',
  },
} satisfies Meta<typeof Icon>;

type Story = StoryObj<typeof Icon>;

// Basic usage
export const Basic: Story = {
  args: {
    icon: 'home',
    size: 'medium',
    theme: 'light',
  },
};

// Sizes showcase
export const Sizes: Story = {
  render: () => (
    <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
        <Icon icon="home" size="small" />
        <span>Small (16px)</span>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
        <Icon icon="home" size="medium" />
        <span>Medium (24px)</span>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
        <Icon icon="home" size="large" />
        <span>Large (32px)</span>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
        <Icon icon="home" size={48} />
        <span>Custom (48px)</span>
      </div>
    </div>
  ),
};

// Variants showcase
export const Variants: Story = {
  render: () => (
    <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
        <Icon icon="home" variant="filled" />
        <span>Filled</span>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
        <Icon icon="home" variant="outlined" />
        <span>Outlined</span>
      </div>
    </div>
  ),
};

// Theme integration
export const ThemeIntegration: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '24px' }}>
      <div 
        style={{ 
          padding: '16px', 
          background: '#FFFFFF', 
          borderRadius: '8px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '8px'
        }}
      >
        <Icon icon="home" theme="light" />
        <span>Light Theme</span>
      </div>
      <div 
        style={{ 
          padding: '16px', 
          background: '#333333', 
          borderRadius: '8px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '8px',
          color: '#FFFFFF'
        }}
      >
        <Icon icon="home" theme="dark" />
        <span>Dark Theme</span>
      </div>
      <div 
        style={{ 
          padding: '16px', 
          background: '#1A2B3C', 
          borderRadius: '8px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '8px',
          color: '#FFFFFF'
        }}
      >
        <Icon icon="home" theme="studio" />
        <span>Studio Theme</span>
      </div>
    </div>
  ),
};

// Colors showcase
export const Colors: Story = {
  render: () => (
    <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
      <Icon icon="home" color="#FF5733" />
      <Icon icon="home" color="#33A1FF" />
      <Icon icon="home" color="#33FF57" />
      <Icon icon="home" color="#F033FF" />
      <Icon icon="home" color="#FFD133" />
    </div>
  ),
};

// Icon Gallery
export const IconGallery: Story = {
  render: () => {
    // Extract icon names
    const iconNames = ['home', 'arrow', 'search'];
    
    // Create a grid of all available icons
    return (
      <div>
        <h3>Available Icons</h3>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',
          gap: '24px',
          margin: '16px 0'
        }}>
          {iconNames.map(iconName => (
            <div 
              key={iconName}
              style={{ 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center',
                padding: '16px',
                border: '1px solid #EEEEEE',
                borderRadius: '8px',
                transition: 'all 0.2s ease',
                cursor: 'pointer',
              }}
            >
              <Icon icon={iconName} size="large" />
              <span style={{ marginTop: '8px', fontSize: '14px' }}>{iconName}</span>
            </div>
          ))}
        </div>

        <h3>Variants</h3>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',
          gap: '24px',
          margin: '16px 0'
        }}>
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center',
            padding: '16px',
            border: '1px solid #EEEEEE',
            borderRadius: '8px'
          }}>
            <Icon icon="home" variant="filled" />
            <span style={{ marginTop: '8px', fontSize: '14px' }}>filled</span>
          </div>
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center',
            padding: '16px',
            border: '1px solid #EEEEEE',
            borderRadius: '8px'
          }}>
            <Icon icon="home" variant="outlined" />
            <span style={{ marginTop: '8px', fontSize: '14px' }}>outlined</span>
          </div>
        </div>
      </div>
    );
  },
};

// Usage with Button (Integration example)
export const IntegrationWithComponents: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        padding: '8px 16px',
        background: '#2196F3',
        color: 'white',
        borderRadius: '4px',
        cursor: 'pointer',
        width: 'fit-content',
        gap: '8px'
      }}>
        <Icon icon="home" color="white" size="small" />
        <span>Button with Icon</span>
      </div>

      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        padding: '8px 16px',
        background: 'rgba(33, 150, 243, 0.1)',
        color: '#2196F3',
        borderRadius: '4px',
        width: 'fit-content',
        gap: '8px'
      }}>
        <Icon icon="notification" color="#2196F3" size="small" />
        <span>Info Alert with Icon</span>
      </div>

      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        padding: '8px 0',
        borderBottom: '1px solid #EEEEEE',
        width: 'fit-content',
        gap: '8px',
        cursor: 'pointer'
      }}>
        <Icon icon="arrow" size="small" />
        <span>Navigation Item with Icon</span>
      </div>
    </div>
  ),
};