import express from 'express';
import activationRoutes from './activation';
import validationRoutes from './validation';
import licensesRoutes from './licenses';

const router = express.Router();

// Public routes for license activation and validation
router.use('/', activationRoutes);
router.use('/', validationRoutes);

// Admin routes for license management
router.use('/licenses', licensesRoutes);

export default router;