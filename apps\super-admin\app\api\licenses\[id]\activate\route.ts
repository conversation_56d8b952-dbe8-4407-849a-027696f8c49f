import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '../../../../../utils/supabase/server';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const supabase = await createClient();
    
    // Kiểm tra license có tồn tại không
    const { data: license, error: fetchError } = await supabase
      .from('licenses')
      .select('*')
      .eq('id', id)
      .single();
      
    if (fetchError || !license) {
      return NextResponse.json(
        { error: 'License not found' },
        { status: 404 }
      );
    }
    
    // Kiểm tra xem license đã hết hạn chưa
    const expiryDate = new Date(license.expiry_date);
    if (expiryDate < new Date()) {
      return NextResponse.json(
        { error: 'Cannot activate an expired license' },
        { status: 400 }
      );
    }
    
    // Kích hoạt license
    const { data: updatedLicense, error } = await supabase
      .from('licenses')
      .update({
        is_active: true,
        revocation_reason: null
      })
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      console.error('Error activating license:', error);
      return NextResponse.json(
        { error: 'Failed to activate license' },
        { status: 500 }
      );
    }
    
    // Ghi log hoạt động
    await supabase
      .from('license_activities')
      .insert({
        license_id: id,
        activity_type: 'ACTIVATION',
        details: { message: 'License manually activated by admin' }
      });
      
    return NextResponse.json({
      data: updatedLicense,
      message: 'License activated successfully'
    });
    
  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while activating the license' },
      { status: 500 }
    );
  }
}
