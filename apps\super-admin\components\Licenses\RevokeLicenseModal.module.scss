.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  width: 500px;
  max-width: 90%;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  
  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #b91c1c;
  }
  
  .closeButton {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #6b7280;
    
    &:hover {
      color: #111827;
    }
  }
}

.modalBody {
  padding: 24px;
  
  .warning {
    margin-bottom: 24px;
    padding: 16px;
    background-color: #fef2f2;
    border-left: 4px solid #b91c1c;
    border-radius: 4px;
    
    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
    }
    
    code {
      font-family: monospace;
      background-color: #fee2e2;
      padding: 2px 4px;
      border-radius: 4px;
    }
  }
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
}

.errorMessage {
  background-color: #fef2f2;
  color: #b91c1c;
  padding: 12px 24px;
  border-bottom: 1px solid #fee2e2;
}