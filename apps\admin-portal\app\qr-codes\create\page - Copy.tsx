'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Alert } from '@loaloa/ui';
import LimitIndicator from '../../components/LimitIndicator';
import styles from './qrCodeForm.module.scss';
import SuccessDialog from '../../components/SuccessDialog';

interface Room {
  id: string;
  room_number: string;
  room_type: string;
  floor: string;
}

interface Area {
  id: string;
  name: string;
  area_type: string;
  floor: string;
}

interface LimitInfo {
  current_count: number;
  max_allowed: number | null;
  is_enforced: boolean;
  usage_percentage: number;
  remaining: number | null;
}

export default function CreateQRCodePage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    location: '',
    description: '',
    target_type: '', // 'room' or 'area'
    room_number: '',
    target_id: '', // For area
    qr_type: 'TENANT',
  });
  const [rooms, setRooms] = useState<Room[]>([]);
  const [areas, setAreas] = useState<Area[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [limitInfo, setLimitInfo] = useState<LimitInfo | null>(null);
  const [loadingData, setLoadingData] = useState(true);
  const [showSuccess, setShowSuccess] = useState(false);
  const [createdQRId, setCreatedQRId] = useState<string | null>(null);

  // Fetch rooms, areas and limit information
  useEffect(() => {
    const fetchInitialData = async () => {
  try {
    setLoadingData(true);
    setError(null);
    
    // Fetch rooms
    console.log('Fetching available rooms...');
    const roomsResponse = await fetch('/api/rooms?status=available');
    
    if (!roomsResponse.ok) {
      const errorText = await roomsResponse.text();
      console.error('Rooms fetch error:', errorText);
      throw new Error(`Failed to fetch rooms: ${roomsResponse.status} ${roomsResponse.statusText}`);
    }
    
    const roomsData = await roomsResponse.json();
    console.log('Rooms fetch result:', roomsData);
    setRooms(roomsData.data || []);

    // Fetch areas
    console.log('Fetching areas...');
    const areasResponse = await fetch('/api/areas');
    
    if (!areasResponse.ok) {
      const errorText = await areasResponse.text();
      console.error('Areas fetch error:', errorText);
      throw new Error(`Failed to fetch areas: ${areasResponse.status} ${areasResponse.statusText}`);
    }
    
    const areasData = await areasResponse.json();
    console.log('Areas fetch result:', areasData);
    setAreas(areasData.data || []);

    // Fetch limit info - make this optional
    try {
      const limitResponse = await fetch('/api/qr-codes?limit=1');
      if (limitResponse.ok) {
        const limitData = await limitResponse.json();
        setLimitInfo(limitData.limits);
      }
    } catch (limitErr) {
      console.error('Error fetching limit information:', limitErr);
      // Don't throw error for limit info - this is non-critical
    }
  } catch (err) {
    console.error('Error fetching initial data:', err);
    setError('Could not load data. Please try again later.');
  } finally {
    setLoadingData(false);
  }
};

    fetchInitialData();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Reset related fields when target type changes
    if (name === 'target_type') {
      setFormData(prev => ({
        ...prev,
        [name]: value,
        room_number: value === 'room' ? prev.room_number : '',
        target_id: value === 'area' ? prev.target_id : '',
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  // Validate required fields
  if (!formData.location) {
    setError('Please enter a location for the QR code');
    return;
  }

  try {
    setLoading(true);
    setError(null);
    
    // Prepare payload based on target_type
    const payload = {
      location: formData.location,
      description: formData.description,
      qr_type: formData.qr_type,
      target_type: formData.target_type || null
    };
    
    // Add room_number only if target_type is 'room' and room_number is selected
    if (formData.target_type === 'room' && formData.room_number) {
      payload.room_number = formData.room_number;
    }
    
    // Add target_id only if target_type is 'area' and target_id is selected
    if (formData.target_type === 'area' && formData.target_id) {
      payload.target_id = formData.target_id;
    }
    
    console.log('Submitting QR code creation payload:', payload);
    
    const response = await fetch('/api/qr-codes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
    
    const result = await response.json();
    
    if (!response.ok) {
      console.error('QR code creation failed:', result);
      throw new Error(result.error || 'An error occurred while creating the QR code');
    }
    
    console.log('QR code created successfully:', result);
    
    // Save ID of newly created QR code and show success dialog
    setCreatedQRId(result.data.id);
    setShowSuccess(true);
  } catch (err) {
    console.error('Error creating QR code:', err);
    setError(err instanceof Error ? err.message : 'An error occurred while creating the QR code');
  } finally {
    setLoading(false);
  }
};

  // Check if limit has been reached
  const hasReachedLimit = limitInfo?.remaining === 0 && limitInfo.is_enforced;

  return (
    <div className={styles.container}>
      <div className={styles.pageHeader}>
        <div className={styles.titleSection}>
          <Link href="/qr-codes" className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8.33333 5.83331L4.16666 9.99998L8.33333 14.1666" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to QR Codes
          </Link>
          <h1 className={styles.pageTitle}>Create New QR Code</h1>
        </div>
      </div>

      {error && (
        <Alert 
          variant="error" 
          title="Error" 
          closable 
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {hasReachedLimit && (
        <Alert 
          variant="warning" 
          title="Limit Reached" 
          closable={false}
        >
          You have reached the maximum number of QR codes allowed. Please delete some existing QR codes or upgrade your service plan.
        </Alert>
      )}

      {limitInfo && (
        <div className={styles.limitSection}>
          <LimitIndicator
            current={limitInfo.current_count}
            maximum={limitInfo.max_allowed}
            isEnforced={limitInfo.is_enforced}
            label="QR Codes"
            showDetails={true}
          />
        </div>
      )}

      <div className={styles.formCard}>
        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="location" className={styles.label}>
              Location <span className={styles.required}>*</span>
            </label>
            <input
              type="text"
              id="location"
              name="location"
              value={formData.location}
              onChange={handleChange}
              className={styles.input}
              placeholder="Enter the location for the QR code (e.g., Lobby, Restaurant, Pool)"
              required
              disabled={loading || hasReachedLimit}
            />
            <p className={styles.fieldHelp}>
              This location will be displayed on the QR code and help identify where it is placed.
            </p>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description" className={styles.label}>
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className={styles.textarea}
              placeholder="Optional description for this QR code"
              rows={3}
              disabled={loading || hasReachedLimit}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="target_type" className={styles.label}>
              Link To
            </label>
            <select
              id="target_type"
              name="target_type"
              value={formData.target_type}
              onChange={handleChange}
              className={styles.select}
              disabled={loading || loadingData || hasReachedLimit}
            >
              <option value="">-- Not linked to any specific item --</option>
              <option value="room">Link to Room</option>
              <option value="area">Link to Area</option>
            </select>
          </div>

          {formData.target_type === 'room' && (
            <div className={styles.formGroup}>
              <label htmlFor="room_number" className={styles.label}>
                Room
              </label>
              <select
                id="room_number"
                name="room_number"
                value={formData.room_number}
                onChange={handleChange}
                className={styles.select}
                disabled={loading || loadingData || hasReachedLimit}
              >
                <option value="">-- Select a Room --</option>
                {loadingData ? (
                  <option value="" disabled>Loading rooms...</option>
                ) : (
                  rooms.map(room => (
                    <option key={room.id} value={room.room_number}>
                      Room {room.room_number} ({room.room_type}, Floor {room.floor})
                    </option>
                  ))
                )}
              </select>
              <p className={styles.fieldHelp}>
                {loadingData 
                     ? 'Loading room list...' 
                  : 'Link this QR code to a specific room. Only available rooms are shown.'
                }
              </p>
            </div>
          )}

          {formData.target_type === 'area' && (
            <div className={styles.formGroup}>
              <label htmlFor="target_id" className={styles.label}>
                Area
              </label>
              <select
                id="target_id"
                name="target_id"
                value={formData.target_id}
                onChange={handleChange}
                className={styles.select}
                disabled={loading || loadingData || hasReachedLimit}
              >
                <option value="">-- Select an Area --</option>
                {loadingData ? (
                  <option value="" disabled>Loading areas...</option>
                ) : (
                  areas.map(area => (
                    <option key={area.id} value={area.id}>
                      {area.name} ({area.area_type}, Floor {area.floor})
                    </option>
                  ))
                )}
              </select>
              <p className={styles.fieldHelp}>
                {loadingData 
                  ? 'Loading area list...' 
                  : 'Link this QR code to a specific area in your property.'
                }
              </p>
            </div>
          )}

          <div className={styles.formActions}>
            <Link href="/qr-codes" className={styles.cancelButton}>
              Cancel
            </Link>
            <button 
              className={styles.submitButton} 
              disabled={loading || hasReachedLimit}
            >
              {loading ? 'Creating...' : 'Create QR Code'}
            </button>
          </div>
        </form>
      </div>

      {showSuccess && (
        <SuccessDialog 
          title="QR Code Created Successfully!"
          message="Your QR code has been created successfully. Would you like to create another QR code or return to the QR code list?"
          continueText="Create Another QR Code"
          backText="Return to QR Code List"
          continueHref="/qr-codes/create"
          backHref="/qr-codes"
          onClose={() => setShowSuccess(false)}
        />
      )}
    </div>
  );
}