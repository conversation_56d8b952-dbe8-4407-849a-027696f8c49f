import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, Button } from '@ui';
import styles from './QrCodeList.module.scss';

interface QrCodeListProps {
  qrCodes: any[];
  loading: boolean;
  error: string | null;
  onDelete: (qrCode: any) => void;
  onPageChange: (page: number) => void;
  currentPage: number;
  totalPages: number;
}

export default function QrCodeList({
  qrCodes,
  loading,
  error,
  onDelete,
  onPageChange,
  currentPage,
  totalPages
}: QrCodeListProps) {
  
  // Helper function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Show loading state
  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading QR codes...</p>
      </div>
    );
  }
  
  // Show error state
  if (error) {
    return (
      <div className={styles.error}>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>
        <p>Error loading QR codes: {error}</p>
        <button onClick={() => onPageChange(currentPage)} className={styles.retryButton}>Retry</button>
      </div>
    );
  }
  
  // Show empty state
  if (qrCodes.length === 0) {
    return (
      <div className={styles.emptyState}>
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          <path d="M7 7h.01"></path>
          <path d="M17 7h.01"></path>
          <path d="M7 17h.01"></path>
          <path d="M17 17h.01"></path>
          <path d="M12 12h.01"></path>
          <path d="M7 12h.01"></path>
          <path d="M17 12h.01"></path>
          <path d="M12 7h.01"></path>
          <path d="M12 17h.01"></path>
        </svg>
        <h3>No QR Codes Found</h3>
        <p>No QR codes match your search criteria or none have been created yet.</p>
        <Link href="/qr-codes/create" className={styles.createButton}>
          Create QR Code
        </Link>
      </div>
    );
  }
  
  // Render QR code list
  return (
    <>
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>Name</th>
              <th>Location</th>
              <th>Type</th>
              <th>Reception Point</th>
              <th>Status</th>
              <th>Scans</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {qrCodes.map((qrCode) => (
              <tr key={qrCode.id}>
                <td className={styles.nameCell}>
                  <Link href={`/qr-codes/${qrCode.id}`} className={styles.nameLink}>
                    {qrCode.name || 'Unnamed QR Code'}
                  </Link>
                  {qrCode.description && (
                    <div className={styles.description}>
                      {qrCode.description.length > 50 
                        ? `${qrCode.description.substring(0, 50)}...` 
                        : qrCode.description}
                    </div>
                  )}
                </td>
                <td>{qrCode.location}</td>
                <td>{qrCode.qr_type}</td>
                <td>
                  {qrCode.reception_point ? (
                    <span className={styles.receptionPointBadge}>
                      {qrCode.reception_point.name}
                    </span>
                  ) : (
                    <span className={styles.noReceptionPoint}>None</span>
                  )}
                </td>
                <td>
                  <span className={`${styles.statusBadge} ${qrCode.status === 'active' ? styles.active : styles.inactive}`}>
                    {qrCode.status === 'active' ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>{qrCode.scan_count || 0}</td>
                <td className={styles.actionsCell}>
                  <div className={styles.actions}>
                    <Link href={`/qr-codes/${qrCode.id}/edit`} className={styles.editButton}>
                      Edit
                    </Link>
                    <button
                      onClick={() => onDelete(qrCode)}
                      className={styles.deleteButton}
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      {totalPages > 1 && (
        <div className={styles.pagination}>
          <button
            onClick={() => onPageChange(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className={styles.paginationButton}
          >
            Previous
          </button>
          <span className={styles.pageInfo}>
            Page {currentPage} of {totalPages}
          </span>
          <button
            onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className={styles.paginationButton}
          >
            Next
          </button>
        </div>
      )}
    </>
  );
}