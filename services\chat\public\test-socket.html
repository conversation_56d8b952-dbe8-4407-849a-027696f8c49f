<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LoaLoa Chat Test</title>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    .container { display: flex; flex-direction: column; height: 90vh; }
    .chat-container { display: flex; flex: 1; }
    .rooms { width: 250px; border-right: 1px solid #ccc; padding-right: 15px; }
    .chat { flex: 1; padding-left: 15px; display: flex; flex-direction: column; }
    .messages { flex: 1; overflow-y: auto; border: 1px solid #ccc; border-radius: 4px; padding: 10px; margin-bottom: 10px; }
    .input-container { display: flex; }
    input[type="text"] { flex: 1; padding: 8px; }
    button { padding: 8px 15px; background: #4CAF50; color: white; border: none; cursor: pointer; margin-left: 5px; }
    button:hover { background: #45a049; }
    .message { margin-bottom: 10px; padding: 8px; border-radius: 4px; }
    .sent { background-color: #e3f2fd; text-align: right; }
    .received { background-color: #f1f1f1; }
    .room-item { padding: 8px; cursor: pointer; border-radius: 4px; }
    .room-item:hover { background-color: #f1f1f1; }
    .active-room { background-color: #e3f2fd; font-weight: bold; }
    h2 { margin-top: 0; }
    .connection-form { margin-bottom: 20px; padding: 10px; border: 1px solid #ccc; border-radius: 4px; }
    .status { margin-bottom: 10px; }
    .success { color: green; }
    .error { color: red; }
  </style>
</head>
<body>
  <h1>LoaLoa Chat Test</h1>
  
  <div class="connection-form">
    <h3>Kết nối</h3>
    <div>
      <label for="userId">User ID:</label>
      <input type="text" id="userId" placeholder="User ID (để trống nếu là temporary user)">
    </div>
    <div>
      <label for="temporaryUserId">Temporary User ID:</label>
      <input type="text" id="temporaryUserId" placeholder="Temporary User ID">
    </div>
    <div>
      <label for="language">Preferred Language:</label>
      <select id="language">
        <option value="en">English</option>
        <option value="vi">Vietnamese</option>
        <option value="ja">Japanese</option>
        <option value="ko">Korean</option>
        <option value="zh">Chinese</option>
        <option value="fr">French</option>
      </select>
    </div>
    <div>
      <label for="token">JWT Token (nếu có):</label>
      <input type="text" id="token" placeholder="JWT Token">
    </div>
    <div>
      <button id="connectBtn">Connect</button>
    </div>
    <div class="status" id="connectionStatus"></div>
  </div>
  
  <div class="container">
    <div class="chat-container">
      <div class="rooms">
        <h2>Phòng chat</h2>
        <div id="roomsList"></div>
        <div style="margin-top: 15px;">
          <input type="text" id="roomId" placeholder="Room ID">
          <button id="joinRoomBtn">Join Room</button>
        </div>
      </div>
      
      <div class="chat">
        <h2 id="currentRoomName">Chưa chọn phòng</h2>
        <div class="messages" id="messages"></div>
        <div class="input-container">
          <input type="text" id="messageInput" placeholder="Type a message...">
          <button id="sendBtn">Send</button>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    let socket;
    let currentRoomId = null;
    const messagesContainer = document.getElementById('messages');
    const messageInput = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendBtn');
    const connectBtn = document.getElementById('connectBtn');
    const joinRoomBtn = document.getElementById('joinRoomBtn');
    const roomsList = document.getElementById('roomsList');
    const connectionStatus = document.getElementById('connectionStatus');
    const currentRoomName = document.getElementById('currentRoomName');
    
    // Connect to WebSocket server
    connectBtn.addEventListener('click', () => {
      const userId = document.getElementById('userId').value;
      const temporaryUserId = document.getElementById('temporaryUserId').value;
      const language = document.getElementById('language').value;
      const token = document.getElementById('token').value;
      
      if (!userId && !temporaryUserId) {
        showStatus('Cần nhập User ID hoặc Temporary User ID', 'error');
        return;
      }
      
      // Connect to server
      socket = io('http://localhost:3002', {
        auth: token ? { token } : {}
      });
      
      // Handle connection events
      socket.on('connect', () => {
        showStatus('Kết nối thành công!', 'success');
        
        // Setup user information
        socket.emit('setup', {
          userId,
          temporaryUserId,
          preferredLanguage: language,
          deviceId: `test-device-${Date.now()}`
        });
      });
      
      socket.on('setup_success', (response) => {
        showStatus(`Thiết lập thành công! Socket ID: ${response.data.socketId}`, 'success');
        loadRooms();
      });
      
      socket.on('disconnect', () => {
        showStatus('Mất kết nối!', 'error');
      });
      
      socket.on('error', (error) => {
        showStatus(`Lỗi: ${error.error}`, 'error');
      });
      
      // Handle message events
      socket.on('message_received', (data) => {
        if (currentRoomId === data.message.chat_room_id) {
          addMessage(data.message, false);
        }
      });
      
     socket.on('translation_received', (data) => {
  const { messageId, language: targetLanguage, translatedContent } = data;
  
  // Hiển thị bản dịch với định dạng rõ ràng hơn
  const messageEl = document.querySelector(`[data-message-id="${messageId}"]`);
  if (messageEl) {
    // Kiểm tra xem đã có bản dịch cho ngôn ngữ này chưa
    const existingTranslation = messageEl.querySelector(`.translation-${targetLanguage}`);
    if (existingTranslation) {
      existingTranslation.textContent = translatedContent;
    } else {
      const translationEl = document.createElement('div');
      translationEl.className = `translation translation-${targetLanguage}`;
      translationEl.style.fontStyle = 'italic';
      translationEl.style.color = '#666';
      translationEl.style.marginTop = '4px';
      translationEl.style.padding = '2px 5px';
      translationEl.style.borderLeft = '2px solid #ccc';
      translationEl.textContent = `${targetLanguage}: ${translatedContent}`;
      messageEl.appendChild(translationEl);
    }
  }
});
      
      // Handle room events
      socket.on('room_joined', (data) => {
        console.log('Someone joined the room:', data);
      });
      
      socket.on('room_left', (data) => {
        console.log('Someone left the room:', data);
      });
    });
    
    // Join a room
    joinRoomBtn.addEventListener('click', () => {
      const roomId = document.getElementById('roomId').value;
      
      if (!roomId) {
                showStatus('Vui lòng nhập Room ID', 'error');
        return;
      }
      
      socket.emit('join_room', roomId, (response) => {
        if (response.success) {
          currentRoomId = roomId;
          currentRoomName.textContent = response.data.room.name || `Room ${roomId}`;
          showStatus(`Đã tham gia phòng: ${response.data.room.name || roomId}`, 'success');
          messagesContainer.innerHTML = '';
          loadMessages(roomId);
          
          // Update active room in UI
          const roomElements = document.querySelectorAll('.room-item');
          roomElements.forEach(el => el.classList.remove('active-room'));
          const roomElement = document.querySelector(`[data-room-id="${roomId}"]`);
          if (roomElement) {
            roomElement.classList.add('active-room');
          }
        } else {
          showStatus(`Không thể tham gia phòng: ${response.error}`, 'error');
        }
      });
    });
    
    // Send a message
    sendBtn.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessage();
      }
    });
    
    function sendMessage() {
      const message = messageInput.value.trim();
      
      if (!message || !currentRoomId) {
        return;
      }
      
      const language = document.getElementById('language').value;
      
      socket.emit('send_message', {
        roomId: currentRoomId,
        content: message,
        originalLanguage: language
      }, (response) => {
        if (response.success) {
          addMessage(response.data.message, true);
          messageInput.value = '';
        } else {
          showStatus(`Không gửi được tin nhắn: ${response.error}`, 'error');
        }
      });
    }
    
    // Show status message
    function showStatus(message, type = 'success') {
      connectionStatus.textContent = message;
      connectionStatus.className = `status ${type}`;
    }
    
    // Add message to chat
    function addMessage(message, isSent) {
      const messageEl = document.createElement('div');
      messageEl.className = `message ${isSent ? 'sent' : 'received'}`;
      messageEl.dataset.messageId = message.id;
      
      const contentEl = document.createElement('div');
      contentEl.className = 'content';
      contentEl.textContent = message.content;
      
      const metaEl = document.createElement('div');
      metaEl.className = 'meta';
      metaEl.style.fontSize = '0.8em';
      metaEl.style.color = '#666';
      
      // Format timestamp
      const timestamp = new Date(message.sent_at).toLocaleTimeString();
      metaEl.textContent = `[${message.original_language}] ${timestamp}`;
      
      messageEl.appendChild(contentEl);
      messageEl.appendChild(metaEl);
      messagesContainer.appendChild(messageEl);
      
      // Scroll to bottom
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Load rooms for current user
    function loadRooms() {
      fetch('http://localhost:3002/api/rooms', {
        headers: {
          'Authorization': `Bearer ${document.getElementById('token').value}`
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          roomsList.innerHTML = '';
          
          if (data.data.length === 0) {
            roomsList.innerHTML = '<div>Không có phòng chat</div>';
            return;
          }
          
          data.data.forEach(room => {
            const roomEl = document.createElement('div');
            roomEl.className = 'room-item';
            roomEl.textContent = room.name || `Room ${room.id}`;
            roomEl.dataset.roomId = room.id;
            
            roomEl.addEventListener('click', () => {
              document.getElementById('roomId').value = room.id;
              joinRoomBtn.click();
            });
            
            roomsList.appendChild(roomEl);
          });
        } else {
          showStatus('Không thể tải danh sách phòng', 'error');
        }
      })
      .catch(error => {
        console.error('Error loading rooms:', error);
        showStatus('Lỗi khi tải danh sách phòng', 'error');
      });
    }
    
    // Load messages for a room
    function loadMessages(roomId) {
      fetch(`http://localhost:3002/api/messages/room/${roomId}`, {
        headers: {
          'Authorization': `Bearer ${document.getElementById('token').value}`
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          messagesContainer.innerHTML = '';
          
          if (data.data.length === 0) {
            messagesContainer.innerHTML = '<div style="text-align:center;color:#666;">Không có tin nhắn</div>';
            return;
          }
          
          // Sort messages by sent_at
          const messages = data.data.sort((a, b) => 
            new Date(a.sent_at).getTime() - new Date(b.sent_at).getTime()
          );
          
          // Add each message to the chat
          messages.forEach(message => {
            // Check if the message is sent by current user
            const userId = document.getElementById('userId').value;
            const temporaryUserId = document.getElementById('temporaryUserId').value;
            
            // In a real app, you'd compare the participant's user_id with current user's ID
            // Here we just set all messages as received for demo
            addMessage(message, false);
          });
          
          // Scroll to bottom
          messagesContainer.scrollTop = messagesContainer.scrollHeight;
        } else {
          showStatus('Không thể tải tin nhắn', 'error');
        }
      })
      .catch(error => {
        console.error('Error loading messages:', error);
        showStatus('Lỗi khi tải tin nhắn', 'error');
      });
    }
  </script>
</body>
</html>
