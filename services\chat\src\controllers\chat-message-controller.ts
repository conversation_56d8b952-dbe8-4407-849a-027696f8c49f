import { Request, Response } from 'express';
import * as chatMessageService from '../services/chat-message-service';
import * as translationService from '../services/translation-service';

/**
 * L<PERSON>y danh sách tin nhắn trong phòng chat
 */
export const getRoomMessages = async (req: Request, res: Response) => {
  try {
    const roomId = req.params.roomId;
    const limit = parseInt(req.query.limit as string, 10) || 50;
    const beforeTimestamp = req.query.before as string;
    
    const messages = await chatMessageService.getMessagesInRoom(
      roomId,
      limit,
      beforeTimestamp
    );
    
    return res.json({
      success: true,
      data: messages
    });
  } catch (error) {
    console.error('Error getting room messages:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Lấy chi tiết tin nhắn
 */
export const getMessageById = async (req: Request, res: Response) => {
  try {
    const messageId = req.params.id;
    
    const message = await chatMessageService.getMessageById(messageId);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }
    
    return res.json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Error getting message by ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Tạo tin nhắn mới
 */
export const createMessage = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const userId = req.user?.userId;
    const temporaryUserId = req.body.temporary_user_id;
    
    if (!userId && !temporaryUserId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    const {
      room_id,
      content,
      content_type,
      original_language,
      metadata
    } = req.body;
    
    if (!room_id || !content) {
      return res.status(400).json({
        success: false,
        message: 'Room ID and content are required'
      });
    }
    
    // Kiểm tra quyền gửi tin nhắn
    const canSendMessage = await chatMessageService.canSendMessage(
      room_id,
      userId,
      temporaryUserId
    );
    
    if (!canSendMessage) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to send messages in this room'
      });
    }
    
    const message = await chatMessageService.createMessage({
      roomId: room_id,
      userId,
      temporaryUserId,
      content,
      contentType: content_type || 'text',
      originalLanguage: original_language || 'en',
      metadata
    });
    
    if (!message) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create message'
      });
    }
    
    // Khi tạo qua REST API, không xử lý dịch tự động ở đây
    // Translation sẽ được xử lý thông qua WebSocket hoặc endpoint riêng
    
    return res.status(201).json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Error creating message:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Cập nhật tin nhắn
 */
export const updateMessage = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    const messageId = req.params.id;
    const { content, metadata } = req.body;
    
    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'Content is required'
      });
    }
    
    // TODO: Kiểm tra xem người dùng có phải là người gửi tin nhắn không
    
    const message = await chatMessageService.updateMessage(
      messageId,
      content,
      metadata
    );
    
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found or update failed'
      });
    }
    
    return res.json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Error updating message:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Xóa tin nhắn
 */
export const deleteMessage = async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    const messageId = req.params.id;
    
    // TODO: Kiểm tra xem người dùng có phải là người gửi tin nhắn không
    
    const success = await chatMessageService.deleteMessage(messageId);
    
    if (!success) {
      return res.status(400).json({
        success: false,
        message: 'Failed to delete message'
      });
    }
    
    return res.json({
      success: true,
      message: 'Message deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting message:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Dịch tin nhắn
 */
export const translateMessage = async (req: Request, res: Response) => {
  try {
    const messageId = req.params.id;
    const { target_language } = req.body;
    
    if (!target_language) {
      return res.status(400).json({
        success: false,
        message: 'Target language is required'
      });
    }
    
    const translation = await translationService.translateMessage(
      messageId,
      target_language
    );
    
    if (!translation) {
      return res.status(400).json({
        success: false,
        message: 'Failed to translate message'
      });
    }
    
    return res.json({
      success: true,
      data: translation
    });
  } catch (error) {
    console.error('Error translating message:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
