'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import DashboardLayout from '../dashboard-layout';
import styles from './chat-routing-rules.module.scss';
import { <PERSON><PERSON>, Alert, SearchBar } from '@ui';
import DeleteConfirmModal from '../components/modals/DeleteConfirmModal';

interface ChatRoutingRule {
  id: string;
  rule_name: string;
  rule_type: string;
  rule_condition: any;
  priority: number;
  target_department?: string;
  target_user?: {
    id: string;
    tenant_users_details: {
      display_name: string;
      email: string;
    }
  };
  target_reception_point_id?: string;
  is_active: boolean;
  created_at: string;
}

export default function ChatRoutingRulesPage() {
  const [rules, setRules] = useState<ChatRoutingRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  // Delete modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [ruleToDelete, setRuleToDelete] = useState<ChatRoutingRule | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  const fetchRules = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Build query params
      const params = new URLSearchParams();
      if (typeFilter) params.append('rule_type', typeFilter);
      if (statusFilter === 'active') params.append('is_active', 'true');
      if (statusFilter === 'inactive') params.append('is_active', 'false');
      params.append('page', page.toString());
      
      const response = await fetch(`/api/chat-routing-rules?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch chat routing rules');
      }
      
      const data = await response.json();
      setRules(data.data || []);
      setTotalPages(Math.ceil((data.meta?.total || 0) / (data.meta?.limit || 10)));
    } catch (err) {
      console.error('Error fetching chat routing rules:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRules();
  }, [typeFilter, statusFilter, page]);

  // Filter rules by search term
  const filteredRules = rules.filter(rule => 
    rule.rule_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle rule deletion
  const handleDeleteClick = (rule: ChatRoutingRule) => {
    setRuleToDelete(rule);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!ruleToDelete) return;
    
    try {
      setDeleteError(null);
      const response = await fetch(`/api/chat-routing-rules/${ruleToDelete.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete routing rule');
      }
      
      // Refresh the list
      fetchRules();
      setShowDeleteModal(false);
      setRuleToDelete(null);
    } catch (err) {
      console.error('Error deleting routing rule:', err);
      setDeleteError(err instanceof Error ? err.message : 'Failed to delete routing rule');
    }
  };

  // Format rule type for display
  const formatRuleType = (type: string) => {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <div>
            <h1 className={styles.title}>Chat Routing Rules</h1>
            <p className={styles.description}>
              Manage how incoming chats are routed to staff members
            </p>
          </div>
          <Link href="/chat-routing-rules/create">
            <Button variant="primary" label="New Rule">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M8 3.33334V12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3.33334 8H12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Create Routing Rule
            </Button>
          </Link>
        </div>
        
        {error && (
          <Alert variant="error" title="Error" closable onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        <div className={styles.filtersContainer}>
          <SearchBar 
            placeholder="Search routing rules..." 
            onChange={(e) => setSearchTerm(e.target.value)}
            className={styles.searchBar}
          />
          
          <div className={styles.filters}>
            <div className={styles.filterGroup}>
              <select 
                value={typeFilter} 
                onChange={(e) => { 
                  setTypeFilter(e.target.value); 
                  setPage(1); 
                }}
                className={styles.select}
              >
                <option value="">All Types</option>
                <option value="qr_code">QR Code</option>
                <option value="guest">Guest</option>
                <option value="time">Time</option>
                <option value="language">Language</option>
                <option value="custom">Custom</option>
              </select>
            </div>
            
            <div className={styles.filterGroup}>
              <select 
                value={statusFilter} 
                onChange={(e) => { 
                  setStatusFilter(e.target.value); 
                  setPage(1); 
                }}
                className={styles.select}
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </div>
        
        {loading ? (
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Loading routing rules...</p>
          </div>
        ) : filteredRules.length === 0 ? (
          <div className={styles.emptyState}>
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"></path>
              <path d="M12 8V16"></path>
              <path d="M8 12H16"></path>
            </svg>
            <h3>No routing rules found</h3>
            <p>
              {searchTerm || typeFilter || statusFilter ? 
                'No routing rules match your filters.' : 
                'No routing rules have been created yet.'}
            </p>
            {!searchTerm && !typeFilter && !statusFilter && (
              <Link href="/chat-routing-rules/create" className={styles.createButton}>
                Create Routing Rule
              </Link>
            )}
          </div>
        ) : (
          <div className={styles.tableContainer}>
            <table className={styles.table}>
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Type</th>
                  <th>Route To</th>
                  <th>Priority</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredRules.map((rule) => (
                  <tr key={rule.id}>
                    <td>
                      <Link href={`/chat-routing-rules/${rule.id}`} className={styles.ruleLink}>
                        {rule.rule_name}
                      </Link>
                    </td>
                    <td>{formatRuleType(rule.rule_type)}</td>
                    <td>
                      {rule.target_user ? (
                        <div className={styles.routeToUser}>
                          <span className={styles.userName}>{rule.target_user.tenant_users_details.display_name}</span>
                          <span className={styles.userEmail}>{rule.target_user.tenant_users_details.email}</span>
                        </div>
                      ) : rule.target_department ? (
                        <div className={styles.routeToDepartment}>
                          <span className={styles.departmentBadge}>{rule.target_department}</span>
                        </div>
                      ) : rule.target_reception_point_id ? (
                        <div className={styles.routeToPoint}>
                          <span className={styles.pointBadge}>Reception Point</span>
                        </div>
                      ) : (
                        <span className={styles.noRoute}>Not specified</span>
                      )}
                    </td>
                    <td>
                      <span className={styles.priorityBadge}>{rule.priority}</span>
                    </td>
                    <td>
                      <span className={`${styles.statusBadge} ${rule.is_active ? styles.active : styles.inactive}`}>
                        {rule.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td>{formatDate(rule.created_at)}</td>
                    <td className={styles.actions}>
                      <Link href={`/chat-routing-rules/${rule.id}/edit`} className={styles.editButton}>
                        Edit
                      </Link>
                      <button 
                        onClick={() => handleDeleteClick(rule)} 
                        className={styles.deleteButton}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        
        {totalPages > 1 && (
          <div className={styles.pagination}>
            <button 
              onClick={() => setPage(p => Math.max(p - 1, 1))} 
              disabled={page === 1}
              className={styles.paginationButton}
            >
              Previous
            </button>
            <span className={styles.pageInfo}>
              Page {page} of {totalPages}
            </span>
            <button 
              onClick={() => setPage(p => Math.min(p + 1, totalPages))} 
              disabled={page === totalPages}
              className={styles.paginationButton}
            >
              Next
            </button>
          </div>
        )}
      </div>
      
      {/* Delete Confirmation Modal */}
      {ruleToDelete && (
        <DeleteConfirmModal
          isOpen={showDeleteModal}
          title="Delete Routing Rule"
          message={`Are you sure you want to delete "${ruleToDelete.rule_name}"? This action cannot be undone.`}
          onConfirm={handleConfirmDelete}
          onCancel={() => {
            setShowDeleteModal(false);
            setRuleToDelete(null);
            setDeleteError(null);
          }}
          error={deleteError}
        />
      )}
    </DashboardLayout>
  );
}
