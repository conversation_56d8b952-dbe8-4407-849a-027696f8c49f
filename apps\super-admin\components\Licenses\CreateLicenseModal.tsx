import React, { useState } from 'react';
import { Button } from '@loaloa/ui';
import { FormGroup, Input, Select } from '@loaloa/ui';

// Định nghĩa inline styles
const styles = {
  modalOverlay: {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modal: {
    backgroundColor: 'white',
    borderRadius: '8px',
    overflow: 'hidden',
    width: '500px',
    maxWidth: '90%',
  },
  modalHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '16px 24px',
    borderBottom: '1px solid #e5e7eb',
  },
  modalTitle: {
    margin: 0,
    fontSize: '18px',
    fontWeight: 600,
  },
  closeButton: {
    background: 'none',
    border: 'none',
    fontSize: '20px',
    cursor: 'pointer',
    color: '#6b7280',
  },
  modalBody: {
    padding: '24px',
    maxHeight: '60vh',
    overflow: 'auto',
  },
  formGroup: {
    marginBottom: '16px',
  },
  label: {
    display: 'block',
    marginBottom: '4px',
    fontSize: '14px',
    fontWeight: 500,
  },
  input: {
    width: '100%',
    padding: '8px 12px',
    fontSize: '14px',
    border: '1px solid #d1d5db',
    borderRadius: '4px',
    marginBottom: '8px',
  },
  select: {
    width: '100%',
    padding: '8px 12px',
    fontSize: '14px',
    border: '1px solid #d1d5db',
    borderRadius: '4px',
    marginBottom: '8px',
  },
  textarea: {
    width: '100%',
    padding: '8px 12px',
    fontSize: '14px',
    border: '1px solid #d1d5db',
    borderRadius: '4px',
    marginBottom: '8px',
    minHeight: '80px',
  },
  checkbox: {
    marginRight: '8px',
  },
  checkboxLabel: {
    fontSize: '14px',
  },
  modalFooter: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '12px',
    padding: '16px 24px',
    borderTop: '1px solid #e5e7eb',
  },
  errorMessage: {
    backgroundColor: '#fef2f2',
    color: '#b91c1c',
    padding: '12px 24px',
    borderBottom: '1px solid #fee2e2',
  },
  successMessage: {
    backgroundColor: '#f0fdf4',
    color: '#166534',
    padding: '12px 24px',
    borderBottom: '1px solid #dcfce7',
  },
  metadataEditor: {
    width: '100%',
    padding: '8px 12px',
    fontSize: '14px',
    fontFamily: 'monospace',
    border: '1px solid #d1d5db',
    borderRadius: '4px',
    marginBottom: '8px',
    minHeight: '120px',
  },
};

interface CreateLicenseModalProps {
  onClose: () => void;
  onSuccess: () => void;
}

export const CreateLicenseModal: React.FC<CreateLicenseModalProps> = ({ 
  onClose, 
  onSuccess 
}) => {
  // Form state
  const [customerName, setCustomerName] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [productId, setProductId] = useState('loaloa-hotel');
  const [expiryDays, setExpiryDays] = useState<number>(365);
  const [isActive, setIsActive] = useState(true);
  const [metadata, setMetadata] = useState('{\n  "notes": "",\n  "features": {\n    "maxUsers": 10\n  }\n}');
  
  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Validate JSON
  const validateJSON = (json: string): boolean => {
    try {
      JSON.parse(json);
      return true;
    } catch (e) {
      return false;
    }
  };
  
  // Handle submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!customerName) {
      setError('Customer name is required');
      return;
    }
    
    if (!expiryDays || expiryDays < 1) {
      setError('Valid expiry days are required');
      return;
    }
    
    if (metadata && !validateJSON(metadata)) {
      setError('Metadata must be valid JSON');
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('Submitting license data:', { 
        customerName, 
        expiryDays,
        productId,
        isActive,
        metadata: metadata ? JSON.parse(metadata) : undefined,
        customerEmail,
        customerPhone
      });
      
      const response = await fetch('/api/licenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerName,
          customerEmail,
          customerPhone,
          expiryDays,
          productId,
          isActive,
          metadata: metadata ? JSON.parse(metadata) : undefined
        }),
      });
      
      // Lấy response text trước
      const responseText = await response.text();
      let responseData;
      
      try {
        // Thử parse nó thành JSON
        responseData = JSON.parse(responseText);
      } catch (e) {
        console.error('Failed to parse response as JSON:', responseText);
        throw new Error('Invalid response format');
      }
      
      console.log('API response:', responseData);
      
      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to create license');
      }
      
      console.log('License created successfully:', responseData.data);
      
      setSuccess('License created successfully!');
      
      // Đợi 2 giây trước khi đóng modal
      setTimeout(() => {
        onSuccess();
      }, 2000);
    } catch (err: any) {
      console.error('Error creating license:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div style={styles.modalOverlay}>
      <div style={styles.modal}>
        <div style={styles.modalHeader}>
          <h2 style={styles.modalTitle}>Create New License</h2>
          <button style={styles.closeButton} onClick={onClose} type="button">×</button>
        </div>
        
        {error && (
          <div style={styles.errorMessage}>{error}</div>
        )}
        
        {success && (
          <div style={styles.successMessage}>{success}</div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div style={styles.modalBody}>
            {/* Basic Information */}
            <div style={styles.formGroup}>
              <label style={styles.label} htmlFor="customerName">
                Customer Name *
              </label>
              <input
                id="customerName"
                style={styles.input}
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                placeholder="Enter customer name"
                required
              />
            </div>
            
            <div style={styles.formGroup}>
              <label style={styles.label} htmlFor="customerEmail">
                Customer Email
              </label>
              <input
                id="customerEmail"
                type="email"
                style={styles.input}
                value={customerEmail}
                onChange={(e) => setCustomerEmail(e.target.value)}
                placeholder="Enter customer email"
              />
            </div>
            
            <div style={styles.formGroup}>
              <label style={styles.label} htmlFor="customerPhone">
                Customer Phone
              </label>
              <input
                id="customerPhone"
                style={styles.input}
                value={customerPhone}
                onChange={(e) => setCustomerPhone(e.target.value)}
                placeholder="Enter customer phone"
              />
            </div>
            
            <div style={styles.formGroup}>
              <label style={styles.label} htmlFor="productId">
                Product ID
              </label>
              <select
                id="productId"
                style={styles.select}
                value={productId}
                onChange={(e) => setProductId(e.target.value)}
              >
                <option value="loaloa-hotel">LoaLoa Hotel</option>
                <option value="loaloa-resort">LoaLoa Resort</option>
                <option value="loaloa-premium">LoaLoa Premium</option>
                <option value="loaloa-enterprise">LoaLoa Enterprise</option>
              </select>
            </div>
            
            <div style={styles.formGroup}>
              <label style={styles.label} htmlFor="expiryDays">
                License Duration (days) *
              </label>
              <select
                id="expiryDays"
                style={styles.select}
                value={expiryDays.toString()}
                onChange={(e) => setExpiryDays(Number(e.target.value))}
              >
                <option value="30">30 days</option>
                <option value="90">90 days</option>
                <option value="180">180 days</option>
                <option value="365">1 year (365 days)</option>
                <option value="730">2 years (730 days)</option>
              </select>
            </div>
            
            <div style={styles.formGroup}>
              <label style={{...styles.label, display: 'flex', alignItems: 'center'}}>
                <input
                  type="checkbox"
                  style={styles.checkbox}
                  checked={isActive}
                  onChange={(e) => setIsActive(e.target.checked)}
                />
                <span style={styles.checkboxLabel}>
                  License Active
                </span>
              </label>
            </div>
            
            <div style={styles.formGroup}>
              <label style={styles.label} htmlFor="metadata">
                Metadata (JSON)
              </label>
              <textarea
                id="metadata"
                style={styles.metadataEditor}
                value={metadata}
                onChange={(e) => setMetadata(e.target.value)}
                placeholder="Enter metadata as JSON"
              />
            </div>
          </div>
          
          <div style={styles.modalFooter}>
            <Button 
              variant="outline" 
              onClick={onClose} 
              disabled={isLoading}
              type="button"
	      label="Close"
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              variant="primary"
	      label="Submit"
              disabled={isLoading}	      
            >
              {isLoading ? 'Creating...' : 'Submit'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
