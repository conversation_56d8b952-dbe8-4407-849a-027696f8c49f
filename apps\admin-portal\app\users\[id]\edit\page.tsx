'use client';
import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '../../../dashboard-layout';
import styles from './edit-user.module.scss';
import { Button, Alert, Skeleton, Tab, TabList, TabPanel, TabPanels, Tabs } from '@ui';
import UserReceptionPointsList from '../../../components/users/UserReceptionPointsList';
import AddReceptionPointModal from '../../../components/users/AddReceptionPointModal';

export default function EditUserPage() {
  const router = useRouter();
  const { id: userId } = useParams<{ id: string }>();
  const [user, setUser] = useState<any>(null);
  const [formData, setFormData] = useState({
    display_name: '',
    role: 'user',
    phone: '',
    is_active: true,
    department: '',
    title: '',
    preferred_language: 'en'
  });
  
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddReceptionPointModal, setShowAddReceptionPointModal] = useState(false);

  // Fetch user data
  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/users/${userId}`);
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('User not found');
          }
          throw new Error('Failed to fetch user details');
        }
        
        const data = await response.json();
        setUser(data.data);
        
        // Set form data based on user details
        const details = data.data.tenant_users_details || {};
        setFormData({
          display_name: details.display_name || '',
          role: data.data.role || 'user',
          phone: details.phone || '',
          is_active: data.data.is_active !== undefined ? data.data.is_active : true,
          department: details.department || '',
          title: details.title || '',
          preferred_language: details.preferred_language || 'en'
        });
      } catch (err) {
        console.error('Error fetching user:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchUser();
    }
  }, [userId]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  // Handle reception point update
  const handleReceptionPointsChange = () => {
    // This function will be called after a reception point is added, updated, or removed
    // We don't need to refresh the whole user data, just the reception points list component will handle it
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setUpdating(true);
      setError(null);
      
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user');
      }
      
      // Redirect to user detail page
      router.push(`/users/${userId}`);
    } catch (err) {
      console.error('Error updating user:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className={styles.container}>
          <div className={styles.header}>
            <Skeleton height="2rem" width="50%" />
          </div>
          <div className={styles.content}>
            <Skeleton height="20rem" />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !user) {
    return (
      <DashboardLayout>
        <div className={styles.container}>
          <Alert variant="error" title="Error" closable={false}>
            {error || 'Failed to load user details'}
          </Alert>
          <div className={styles.buttonContainer}>
            <Link href="/users">
              <Button variant="secondary" label="Back to Users">
                Back to Users
              </Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const userDetails = user.tenant_users_details || {};

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Link href={`/users/${userId}`} className={styles.backButton}>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M10.6667 2.66667L5.33333 8.00001L10.6667 13.3333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to User Details
          </Link>
          <h1 className={styles.title}>Edit User</h1>
        </div>
        
        <div className={styles.content}>
          {/* Sidebar Navigation */}
          <div className={styles.sidebar}>
            <ul className={styles.sideNav}>
              <li className={styles.sideNavItem}>
                <a href="#user-info" className={styles.active}>User Information</a>
              </li>
              <li className={styles.sideNavItem}>
                <a href="#reception-points">Reception Points</a>
              </li>
              <li className={styles.sideNavItem}>
                <a href="#permissions">Permissions</a>
              </li>
              <li className={styles.sideNavItem}>
                <a href="#audit-log">Audit Log</a>
              </li>
            </ul>
          </div>
          
          {/* Main Content */}
          <div className={styles.mainContent}>
            <Tabs>
              <TabList>
                <Tab index={0}>User Information</Tab>
                <Tab index={1}>Reception Points</Tab>
              </TabList>
              
              <TabPanels>
                <TabPanel index={0}>
                  {error && (
                    <Alert variant="error" title="Error" onClose={() => setError(null)}>
                      {error}
                    </Alert>
                  )}
                  
                  <form onSubmit={handleSubmit} className={styles.form}>
                    <div className={styles.formGroup}>
                      <label htmlFor="display_name">Name <span className={styles.required}>*</span></label>
                      <input
                        type="text"
                        id="display_name"
                        name="display_name"
                        value={formData.display_name}
                        onChange={handleInputChange}
                        required
                        disabled={updating}
                      />
                    </div>
                    
                    <div className={styles.formGroup}>
                      <label htmlFor="email">Email</label>
                      <input
                        type="email"
                        id="email"
                        value={userDetails.email || ''}
                        disabled
                        className={styles.disabledInput}
                      />
                      <p className={styles.helpText}>Email cannot be changed</p>
                    </div>
                    
                    <div className={styles.formGroup}>
                      <label htmlFor="phone">Phone Number</label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        disabled={updating}
                      />
                    </div>
                    
                    {/* Thêm trường Department */}
                    <div className={styles.formGroup}>
                      <label htmlFor="department">Department</label>
                      <input
                        type="text"
                        id="department"
                        name="department"
                        value={formData.department}
                        onChange={handleInputChange}
                        placeholder="Enter department"
                        disabled={updating}
                      />
                    </div>
                    
                    {/* Thêm trường Title */}
                    <div className={styles.formGroup}>
                      <label htmlFor="title">Title</label>
                      <input
                        type="text"
                        id="title"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="Enter job title"
                        disabled={updating}
                      />
                    </div>
                    
                    {/* Thêm trường Preferred Language */}
                    <div className={styles.formGroup}>
                      <label htmlFor="preferred_language">Preferred Language</label>
                      <select
                        id="preferred_language"
                        name="preferred_language"
                        value={formData.preferred_language}
                        onChange={handleInputChange}
                        disabled={updating}
                      >
                        <option value="en">English</option>
                        <option value="vi">Vietnamese</option>
			<option value="fr">French</option>
                        <option value="ja">Japanese</option>
                        <option value="zh">Chinese</option>
                        <option value="ko">Korean</option>
                        <option value="ru">Russian</option>
                      </select>
                    </div>
                    
                    <div className={styles.formGroup}>
                      <label htmlFor="role">Role <span className={styles.required}>*</span></label>
                      <select
                        id="role"
                        name="role"
                        value={formData.role}
                        onChange={handleInputChange}
                        disabled={updating}
                      >
                        <option value="admin">Admin</option>
                        <option value="manager">Manager</option>
                        <option value="user">User</option>
                      </select>
                    </div>
                    
                    <div className={styles.formGroup}>
                      <div className={styles.checkboxGroup}>
                        <input
                          type="checkbox"
                          id="is_active"
                          name="is_active"
                          checked={formData.is_active}
                          onChange={handleInputChange}
                          disabled={updating}
                        />
                        <label htmlFor="is_active">Active User</label>
                      </div>
                    </div>
                    
                    <div className={styles.actions}>
                      <Link href={`/users/${userId}`}>
                        <Button variant="secondary" disabled={updating} label="Cancel">
                          Cancel
                        </Button>
                      </Link>
                      <Button
                        type="submit"
                        variant="primary"
                        loading={updating}
                        disabled={updating}
                        label="Save Changes"
                      >
                        Save Changes
                      </Button>
                    </div>
                  </form>
                </TabPanel>
                
                <TabPanel index={1}>
                  <div className={styles.receptionPointsSection}>
                    <div className={styles.sectionHeader}>
                      <h3 className={styles.sectionTitle}>Assigned Reception Points</h3>
                      <Button
                        variant="primary"
                        onClick={() => setShowAddReceptionPointModal(true)}
                        label="Assign Reception Point"
                      >
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path
                            d="M8 3.33334V12.6667"
                            stroke="currentColor"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M3.33334 8H12.6667"
                            stroke="currentColor"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        Assign Reception Point
                      </Button>
                    </div>
                    
                    <UserReceptionPointsList
                      userId={userId as string}
                      onReceptionPointsChange={handleReceptionPointsChange}
                    />
                  </div>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </div>
        </div>
      </div>
      
      {showAddReceptionPointModal && (
        <AddReceptionPointModal
          userId={userId as string}
          onClose={() => setShowAddReceptionPointModal(false)}
          onSave={() => {
            setShowAddReceptionPointModal(false);
            handleReceptionPointsChange();
          }}
        />
      )}
    </DashboardLayout>
  );
}