'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '../../dashboard-layout';
import styles from './create-rule.module.scss';
import { <PERSON><PERSON>, Button } from '@ui';
import ChatRoutingRuleForm from '../../components/chat-routing/ChatRoutingRuleForm';

export default function CreateChatRoutingRulePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (formData: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/chat-routing-rules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create routing rule');
      }

      // Navigate to the rule list page on success
      router.push('/chat-routing-rules');
    } catch (err: any) {
      console.error('Error creating rule:', err);
      setError(err.message || 'An error occurred while creating the routing rule');
      // Don't set loading to false here, the form component will handle it
      throw err; // Re-throw to let the form component know about the error
    }
  };

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Link href="/chat-routing-rules" className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M10 15.8333L4.16667 10L10 4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to Routing Rules
          </Link>
          <h1 className={styles.title}>Create Chat Routing Rule</h1>
        </div>

        {error && (
          <Alert variant="error" title="Error" closable onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <div className={styles.formContainer}>
          <ChatRoutingRuleForm 
            onSubmit={handleSubmit}
            isSubmitting={loading}
          />
        </div>
      </div>
    </DashboardLayout>
  );
}
