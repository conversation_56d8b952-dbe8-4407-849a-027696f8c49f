import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function để lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Lấy chi tiết loại QR code theo ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const typeId = params.id;

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Truy vấn chi tiết loại QR code
    const { data, error } = await supabase
      .from('tenant_qr_code_types')
      .select('*')
      .eq('id', typeId)
      .eq('tenant_id', tenant_id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'QR code type not found' }, { status: 404 });
      }
      console.error('Error fetching QR code type:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Error in GET QR code type:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}

// PUT: Cập nhật loại QR code
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const typeId = params.id;
    const updateData = await request.json();

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra loại QR code có tồn tại không
    const { data: existingType, error: checkError } = await supabase
      .from('tenant_qr_code_types')
      .select('id, name')
      .eq('id', typeId)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'QR code type not found' }, { status: 404 });
      }
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    // Nếu cập nhật tên, kiểm tra xem tên mới có trùng với tên của loại khác không
    if (updateData.name && updateData.name !== existingType.name) {
      const { data: existingName, error: nameError } = await supabase
        .from('tenant_qr_code_types')
        .select('id')
        .eq('tenant_id', tenant_id)
        .eq('name', updateData.name)
        .neq('id', typeId)
        .maybeSingle();

      if (nameError && nameError.code !== 'PGRST116') {
        console.error('Error checking name uniqueness:', nameError);
        return NextResponse.json({ error: nameError.message }, { status: 500 });
      }

      if (existingName) {
        return NextResponse.json({ error: `QR code type with name "${updateData.name}" already exists` }, { status: 409 });
      }
    }

    // Kiểm tra default_action hợp lệ nếu có cập nhật
    if (updateData.default_action) {
      const validActions = ['chat', 'info', 'service', 'feedback'];
      if (!validActions.includes(updateData.default_action)) {
        return NextResponse.json({ 
          error: `Invalid default action. Must be one of: ${validActions.join(', ')}` 
        }, { status: 400 });
      }
    }

    // Chuẩn bị dữ liệu cập nhật
    const typeUpdateData: any = {
      updated_at: new Date().toISOString()
    };

    // Chỉ cập nhật các trường có cung cấp
    if (updateData.name !== undefined) typeUpdateData.name = updateData.name;
    if (updateData.description !== undefined) typeUpdateData.description = updateData.description;
    if (updateData.default_action !== undefined) typeUpdateData.default_action = updateData.default_action;
    if (updateData.is_active !== undefined) typeUpdateData.is_active = updateData.is_active;

    // Cập nhật loại QR code
    const { data, error } = await supabase
      .from('tenant_qr_code_types')
      .update(typeUpdateData)
      .eq('id', typeId)
      .eq('tenant_id', tenant_id)
      .select()
      .single();

    if (error) {
      console.error('Error updating QR code type:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({ data, message: 'QR code type updated successfully' });
  } catch (error: any) {
    console.error('Error in PUT QR code type:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}

// DELETE: Xóa loại QR code
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const typeId = params.id;

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra loại QR code có tồn tại không
    const { data: existingType, error: checkError } = await supabase
      .from('tenant_qr_code_types')
      .select('id')
      .eq('id', typeId)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'QR code type not found' }, { status: 404 });
      }
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    // Kiểm tra xem có QR code nào đang sử dụng loại này không
    const { data: usedQrCodes, error: usageError } = await supabase
      .from('tenant_qr_codes')
      .select('id')
      .eq('tenant_id', tenant_id)
      .eq('qr_type_id', typeId)
      .limit(1);

    if (usageError) {
      console.error('Error checking QR code usage:', usageError);
      return NextResponse.json({ error: usageError.message }, { status: 500 });
    }

    if (usedQrCodes && usedQrCodes.length > 0) {
      return NextResponse.json({ error: 'Cannot delete QR code type that is in use' }, { status: 409 });
    }

    // Xóa loại QR code
    const { error } = await supabase
      .from('tenant_qr_code_types')
      .delete()
      .eq('id', typeId)
      .eq('tenant_id', tenant_id);

    if (error) {
      console.error('Error deleting QR code type:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({ message: 'QR code type deleted successfully' });
  } catch (error: any) {
    console.error('Error in DELETE QR code type:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}
