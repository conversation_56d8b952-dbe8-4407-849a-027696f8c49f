@import '../../styles/variables';

.tableContainer {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 1.5rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
  
  th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--color-border, #D4D4D4);
  }
  
  th {
    font-weight: 600;
    color: var(--color-text-secondary, #464646);
    background-color: var(--color-background-secondary, #EBEBEB);
    position: sticky;
    top: 0;
    z-index: 1;
  }
  
  tbody tr {
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: var(--color-background-hover, #F9F9F9);
    }
  }
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sortableHeader {
  cursor: pointer;
  user-select: none;
  
  &:hover {
    background-color: var(--color-background-hover, #F0F0F0);
  }
}

.sortIcon {
  font-size: 0.75rem;
  margin-left: 4px;
  display: inline-block;
  transform: translateY(-1px);
  
  &.sortDesc {
    transform: rotate(180deg) translateY(1px);
  }
}

.clickable {
  cursor: pointer;
}

.emptyState {
  text-align: center;
  padding: 32px;
  color: var(--color-text-secondary, #7D8491);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  
  p {
    margin-top: 16px;
    color: var(--color-text-secondary, #7D8491);
  }
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary, #FF4D00);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  gap: 8px;
}

.pageNumbers {
  display: flex;
  gap: 4px;
}

.pageButton {
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: 1px solid var(--color-border, #D4D4D4);
  background: transparent;
  cursor: pointer;
  
  &:hover {
    background-color: var(--color-background-hover, #F9F9F9);
  }
  
  &.activePage {
    background-color: var(--color-primary, #FF4D00);
    color: white;
    border-color: var(--color-primary, #FF4D00);
  }
}
