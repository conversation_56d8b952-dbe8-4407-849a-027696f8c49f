import { NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/admin';
import { getCurrentTenantIdForWeb } from '@/license-client';
import { verifyPassword, generateSessionToken } from '@/lib/auth/password';

export async function POST(request: Request) {
  console.log('🚀 Staff login API called');
  
  try {
    const { email, password } = await request.json();
    console.log('📧 Login attempt for:', email);

    // Validate input - Xác thực đầu vào
    if (!email || !password) {
      console.log('❌ Missing email or password');
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Get tenant ID from license - Lấy tenant ID từ license
    console.log('🔍 Getting tenant ID...');
    const tenant_id = await getCurrentTenantIdForWeb();
    console.log('🏢 Using tenant ID:', tenant_id);
    
    if (!tenant_id) {
      console.log('❌ No tenant ID found');
      return NextResponse.json(
        { error: 'Tenant ID not found. Please activate your license.' },
        { status: 400 }
      );
    }

    // Create Supabase service client - Tạo Supabase service client
    console.log('🔧 Creating Supabase client...');
    const supabase = createServiceClient();
    console.log('✅ Supabase client created');

    // Debug: Get all users first - Debug: Lấy tất cả users trước
    console.log('🔍 Getting all users for debug...');
    const { data: allUsers, error: allUsersError } = await supabase
      .from('tenant_users_details')
      .select(`
        email,
        display_name,
        password_hash,
        tenant_users!inner (
          id,
          role,
          tenant_id,
          is_active
        )
      `)
      .eq('tenant_users.tenant_id', tenant_id)
      .eq('tenant_users.is_active', true);

    console.log('📊 All users found:', allUsers?.length || 0);
    if (allUsers) {
      allUsers.forEach((u, i) => {
        console.log(`   ${i + 1}. ${u.email} (${u.display_name})`);
      });
    }

    // Find user by email - Tìm user theo email
    console.log('👤 Looking for user with email:', email.toLowerCase());
    const user = allUsers?.find(u => u.email?.toLowerCase() === email.toLowerCase());

    if (!user) {
      console.log('❌ User not found with email:', email);
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    console.log('✅ User found:', {
      email: user.email,
      display_name: user.display_name,
      role: user.tenant_users.role,
      hasPasswordHash: !!user.password_hash
    });

    // Verify password using bcrypt - Xác thực password bằng bcrypt
    console.log('🔑 Verifying password...');
    if (!user.password_hash) {
      console.log('❌ No password hash found for user');
      return NextResponse.json(
        { error: 'Account not properly configured. Please contact admin.' },
        { status: 401 }
      );
    }

    const isPasswordValid = await verifyPassword(password, user.password_hash);
    
    if (!isPasswordValid) {
      console.log('❌ Invalid password');
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    console.log('✅ Password verified successfully');

    // Update last login time - Cập nhật thời gian đăng nhập cuối
    console.log('⏰ Updating last login time...');
    const { error: updateError } = await supabase
      .from('tenant_users')
      .update({ 
        last_login_at: new Date().toISOString() 
      })
      .eq('id', user.tenant_users.id);

    if (updateError) {
      console.log('⚠️ Warning: Could not update last login time:', updateError);
    } else {
      console.log('✅ Last login time updated');
    }

    // Generate session token - Tạo session token
    const sessionToken = generateSessionToken(user.tenant_users.id);
    console.log('🎫 Session token created:', sessionToken.substring(0, 20) + '...');

    // Prepare user response - Chuẩn bị response user
    const userResponse = {
      id: user.tenant_users.id,
      email: user.email,
      display_name: user.display_name || user.email?.split('@')[0] || 'Staff User',
      role: user.tenant_users.role,
      tenant_id: tenant_id,
      reception_points: [], // Will be populated later
      last_login_at: new Date().toISOString()
    };

    console.log('🎉 Login successful for:', userResponse.display_name);
    console.log('📤 Sending response...');

    return NextResponse.json({
      success: true,
      token: sessionToken,
      user: userResponse,
      message: 'Login successful'
    });

  } catch (error) {
    console.error('💥 Staff login error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
