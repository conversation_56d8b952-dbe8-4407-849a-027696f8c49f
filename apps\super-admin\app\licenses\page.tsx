'use client';

import React, { useState, useEffect } from 'react';
import { DashboardLayout, Button, Table, Card, Badge } from '@loaloa/ui';
import { HomeIcon, BuildingIcon, UsersIcon, LicenseIcon, SettingsIcon } from '@loaloa/ui/src/components/Icons/icons';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import styles from './licenses.module.scss';

// Components
import LicenseStatusBadge from '../../components/licenses/LicenseStatusBadge';
import { CreateLicenseModal } from '../../components/licenses/CreateLicenseModal';

// Hooks
import { useLicenses } from '../../hooks/useLicenses';
import { useDebounce } from '../../hooks/useDebounce';

export default function Licenses() {
  const router = useRouter();
  
  // State
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [status, setStatus] = useState<'ACTIVE' | 'EXPIRED' | 'REVOKED' | 'NOT_ACTIVATED' | 'EXPIRING_SOON' | 'ALL'>('ALL');
  const [search, setSearch] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  
  const debouncedSearch = useDebounce(searchInput, 500);
  
  // Fetch license data
  const { licenses, meta, isLoading, isError, mutate } = useLicenses(
    { page, limit },
    { status, search }
  );

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };
  
  // Update search when debounced search changes
  useEffect(() => {
    setSearch(debouncedSearch);
  }, [debouncedSearch]);
  
  // Sidebar items
  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', href: '/', icon: <HomeIcon /> },
    { id: 'tenants', label: 'Tenants', href: '/tenants', icon: <BuildingIcon /> },
    { id: 'users', label: 'Users', href: '/users', icon: <UsersIcon /> },
    { id: 'licenses', label: 'Licenses', href: '/licenses', icon: <LicenseIcon />, active: true },
    { id: 'settings', label: 'Settings', href: '/settings', icon: <SettingsIcon /> },
  ];
  
  // Table columns
  const columns = [
    {
      header: 'License Key',
      accessor: (license) => (
        <div className={styles.licenseKeyCell}>
          <Link href={`/licenses/${license.id}`} className={styles.licenseLink}>
            {license.license_key}
          </Link>
          <span className={styles.customerName}>{license.customer_name}</span>
        </div>
      ),
      width: '25%',
    },
    {
      header: 'Status',
      accessor: (license) => <LicenseStatusBadge status={license.status} />,
      width: '15%',
    },
    {
      header: 'Expiry',
      accessor: (license) => (
        <div className={styles.expiryCell}>
          <div>{new Date(license.expiry_date).toLocaleDateString()}</div>
          <div className={styles.daysRemaining}>
            {license.days_remaining > 0 
              ? `${license.days_remaining} days remaining` 
              : 'Expired'}
          </div>
        </div>
      ),
      width: '15%',
    },
    {
      header: 'Activated',
      accessor: (license) => license.activation_date 
        ? new Date(license.activation_date).toLocaleDateString() 
        : 'Not activated',
      width: '15%',
    },
    {
      header: 'Last Check-in',
      accessor: (license) => license.last_check_in 
        ? new Date(license.last_check_in).toLocaleDateString() 
        : 'Never',
      width: '15%',
    },
    {
      header: 'Actions',
      accessor: (license) => (
        <div className={styles.actions}>
          <Button 
            size="small" 
            variant="outline" 
	    label ="View"	   
            onClick={() => router.push(`/licenses/${license.id}`)}
          >
            View
          </Button>
          {license.unreviewed_clone_alerts > 0 && (
            <Badge 
              className={styles.alertBadge} 
              variant="danger"
            >
              {license.unreviewed_clone_alerts}
            </Badge>
          )}
        </div>
      ),
      width: '15%',
    },
  ];
  
  // License stats
  const licenseStats = {
    total: meta?.total || 0,
    active: licenses?.filter(l => l.status === 'ACTIVE').length || 0,
    expiringSoon: licenses?.filter(l => l.status === 'EXPIRING_SOON').length || 0,
    expired: licenses?.filter(l => l.status === 'EXPIRED').length || 0,
  };
  console.log('Rendering Licenses page with data:', licenses);

  return (
    <DashboardLayout 
      sidebarItems={sidebarItems}
      title="License Management"
      username="Admin User"
      breadcrumbs={[
        { label: 'Dashboard', href: '/' },
        { label: 'Licenses' }
      ]}
    >
      <div className={styles.licensesContainer}>
        <div className={styles.pageHeader}>
          <div className={styles.titleSection}>
            <h1 className={styles.pageTitle}>Licenses</h1>
            <p className={styles.pageDescription}>
              Manage all license keys for on-premise deployments
            </p>
          </div>
          <div className={styles.actions}>
            <Button 
              variant="primary" 
	       label ="New License"
              onClick={() => setShowCreateModal(true)}
            >
              Create New License
            </Button>
          </div>
        </div>
        
        {/* Search and filters */}
        <div className={styles.filtersContainer}>
          <div className={styles.searchBar}>
            <input 
              type="text" 
              placeholder="Search licenses..." 
              value={searchInput} 
              onChange={(e) => setSearchInput(e.target.value)} 
              className={styles.searchInput}
            />
          </div>
          <div className={styles.filters}>
            <select 
              value={status} 
              onChange={(e) => setStatus(e.target.value as any)} 
              className={styles.filterSelect}
            >
              <option value="ALL">All Status</option>
              <option value="ACTIVE">Active</option>
              <option value="EXPIRING_SOON">Expiring Soon</option>
              <option value="EXPIRED">Expired</option>
              <option value="REVOKED">Revoked</option>
              <option value="NOT_ACTIVATED">Not Activated</option>
            </select>
          </div>
        </div>
        
        {/* Stats cards */}
        <div className={styles.statsCards}>
          <Card className={styles.statCard}>
            <h3 className={styles.statTitle}>Total Licenses</h3>
            <div className={styles.statValue}>{licenseStats.total}</div>
          </Card>
          <Card className={styles.statCard}>
            <h3 className={styles.statTitle}>Active Licenses</h3>
            <div className={styles.statValue}>{licenseStats.active}</div>
            {licenseStats.total > 0 && (
              <div className={styles.statFooter}>
                {Math.round((licenseStats.active / licenseStats.total) * 100)}% of total
              </div>
            )}
          </Card>
          <Card className={styles.statCard}>
            <h3 className={styles.statTitle}>Expiring Soon</h3>
            <div className={styles.statValue}>{licenseStats.expiringSoon}</div>
            <div className={styles.statFooter}>Within 30 days</div>
          </Card>
          <Card className={styles.statCard}>
            <h3 className={styles.statTitle}>Expired</h3>
            <div className={styles.statValue}>{licenseStats.expired}</div>
            {licenseStats.total > 0 && (
              <div className={styles.statFooter}>
                {Math.round((licenseStats.expired / licenseStats.total) * 100)}% of total
              </div>
            )}
          </Card>
        </div>
        
        {/* Error message */}
        {isError && (
          <div className={styles.errorMessage}>
            Error loading licenses. Please try again.
          </div>
        )}
        
        {/* Data table */}
        <Card className={styles.tableCard}>
          <Table 
            columns={columns} 
            data={licenses || []} 
            isLoading={isLoading}
            pagination={{
              currentPage: page,
              totalPages: meta?.pageCount || 1,
              onPageChange: handlePageChange
            }}
          />
        </Card>
      </div>
      
      {/* Create license modal */}
      {showCreateModal && (
        <CreateLicenseModal
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false);
            mutate();
          }}
        />
      )}
    </DashboardLayout>
  );
}
