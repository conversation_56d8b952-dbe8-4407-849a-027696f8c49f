'use client';

import Link from 'next/link';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <div className="text-6xl mb-4">🏨</div>
          <h1 className="text-4xl font-bold text-orange-600 mb-4">
            LoaLoa Web Chat
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Multi-language hotel communication platform
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
          {/* Real QR Scan */}
          <Link
            href="/scan"
            className="block bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-2 border-orange-200 hover:border-orange-400"
          >
            <div className="text-center">
              <div className="text-4xl mb-3">📱</div>
              <h3 className="text-xl font-bold text-orange-600 mb-2">Scan QR Code</h3>
              <p className="text-gray-600">
                Scan your room or area QR code to start chatting with hotel staff
              </p>
            </div>
          </Link>

          {/* Demo */}
          <Link
            href="/qr/demo"
            className="block bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-2 border-green-200 hover:border-green-400"
          >
            <div className="text-center">
              <div className="text-4xl mb-3">🎮</div>
              <h3 className="text-xl font-bold text-green-600 mb-2">Try Demo</h3>
              <p className="text-gray-600">
                Experience the chat system with our interactive demo
              </p>
            </div>
          </Link>
        </div>

        {/* Features */}
        <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-2xl mb-2">🌐</div>
            <p className="text-sm text-gray-600">Multi-language</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-2xl mb-2">⚡</div>
            <p className="text-sm text-gray-600">Real-time</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-2xl mb-2">🔒</div>
            <p className="text-sm text-gray-600">Secure</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-2xl mb-2">📱</div>
            <p className="text-sm text-gray-600">Mobile Ready</p>
          </div>
        </div>
      </div>
    </div>
  );
}
