import React from 'react';
import styles from './MessageBubble.module.scss';

export interface MessageBubbleProps {
  /**
   * Message content
   */
  content: string;
  /**
   * Is the message sent by current user
   */
  isSender: boolean;
  /**
   * Message timestamp
   */
  timestamp: string | Date;
  /**
   * Status of the message
   */
  status?: 'sending' | 'sent' | 'delivered' | 'read';
  /**
   * Additional CSS class
   */
  className?: string;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  content,
  isSender,
  timestamp,
  status = 'sent',
  className = '',
}) => {
  // Format timestamp
  const formattedTime = typeof timestamp === 'string' 
    ? new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    : timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  return (
    <div 
      className={`${styles.container} ${isSender ? styles.sender : styles.receiver} ${className}`}
    >
      <div className={styles.bubble}>
        <div className={styles.content}>{content}</div>
      </div>
      <div className={styles.footer}>
        <span className={styles.time}>{formattedTime}</span>
        {isSender && (
          <span className={`${styles.status} ${styles[status]}`}>
            {status === 'sending' && (
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" opacity="0.3" />
                <path d="M12 6V12L16 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
              </svg>
            )}
            {status === 'sent' && (
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <path d="M5 13L9 17L19 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            )}
            {status === 'delivered' && (
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <path d="M4 12L8 16M8 16L12 12M8 16L16 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            )}
            {status === 'read' && (
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <path d="M4 12L8 16L20 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            )}
          </span>
        )}
      </div>
    </div>
  );
};

export default MessageBubble;
