import React, { InputHTMLAttributes, ReactNode } from 'react';

export interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /**
   * Input label
   */
  label?: string;
  /**
   * Helper text displayed below the input
   */
  helperText?: string;
  /**
   * Error message displayed when error is true
   */
  errorText?: string;
  /**
   * Whether the input is in error state
   */
  error?: boolean;
  /**
   * Input size
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Icon displayed at the start of the input
   */
  startIcon?: ReactNode;
  /**
   * Icon displayed at the end of the input
   */
  endIcon?: ReactNode;
  /**
   * Whether the input is full width
   */
  fullWidth?: boolean;
  /**
   * Additional CSS properties for the input container
   */
  containerStyle?: React.CSSProperties;
}

export const Input: React.FC<InputProps> = ({
  id,
  label,
  helperText,
  errorText,
  error = false,
  size = 'medium',
  startIcon,
  endIcon,
  fullWidth = false,
  containerStyle,
  style,
  className,
  ...props
}) => {
  // Generate a random ID if not provided
  const inputId = id || `input-${Math.random().toString(36).substring(2, 9)}`;
  
  // Size styles
  const sizeStyles: Record<string, React.CSSProperties> = {
    small: {
      padding: '6px 12px',
      fontSize: '12px',
      height: '32px',
    },
    medium: {
      padding: '8px 16px',
      fontSize: '14px',
      height: '40px',
    },
    large: {
      padding: '12px 16px',
      fontSize: '16px',
      height: '48px',
    },
  };
  
  // Container style
  const containerBaseStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    marginBottom: '16px',
    width: fullWidth ? '100%' : 'auto',
    ...containerStyle,
  };
  
  // Label style
  const labelStyle: React.CSSProperties = {
    fontSize: '14px',
    fontWeight: 500,
    color: error ? '#B91C1C' : '#464646',
    marginBottom: '4px',
  };
  
  // Input wrapper style (for icons)
  const inputWrapperStyle: React.CSSProperties = {
    position: 'relative',
    display: 'flex',
    width: '100%',
  };
  
  // Input style
  const inputStyle: React.CSSProperties = {
    fontFamily: 'Inter, sans-serif',
    width: '100%',
    borderRadius: '4px',
    border: `1px solid ${error ? '#B91C1C' : '#D1D5DB'}`,
    outline: 'none',
    backgroundColor: props.disabled ? '#F9FAFB' : '#FFFFFF',
    color: props.disabled ? '#9CA3AF' : '#464646',
    transition: 'border-color 0.2s ease, box-shadow 0.2s ease',
    paddingLeft: startIcon ? '40px' : sizeStyles[size].padding.split(' ')[0],
    paddingRight: endIcon ? '40px' : sizeStyles[size].padding.split(' ')[1],
    paddingTop: sizeStyles[size].padding.split(' ')[0],
    paddingBottom: sizeStyles[size].padding.split(' ')[0],
    fontSize: sizeStyles[size].fontSize,
    height: sizeStyles[size].height,
    ...style,
  };
  
  // Focus styles - applied via `:focus-within` but simulated here with CSS
  const inputFocusStyle = !props.disabled ? {
    ':focus': {
      borderColor: error ? '#B91C1C' : '#FF4D00',
      boxShadow: `0 0 0 2px ${error ? 'rgba(185, 28, 28, 0.2)' : 'rgba(255, 77, 0, 0.2)'}`,
    },
  } : {};
  
  // Icon style
  const iconStyle: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    transform: 'translateY(-50%)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: error ? '#B91C1C' : '#6B7280',
    pointerEvents: 'none', // Makes sure the icon doesn't interfere with input interaction
  };
  
  const startIconStyle: React.CSSProperties = {
    ...iconStyle,
    left: '12px',
  };
  
  const endIconStyle: React.CSSProperties = {
    ...iconStyle,
    right: '12px',
  };
  
  // Helper text style
  const helperTextStyle: React.CSSProperties = {
    fontSize: '12px',
    marginTop: '4px',
    color: error ? '#B91C1C' : '#6B7280',
  };

  return (
    <div style={containerBaseStyle} className={className}>
      {label && (
        <label htmlFor={inputId} style={labelStyle}>
          {label}
        </label>
      )}
      
      <div style={inputWrapperStyle}>
        {startIcon && <span style={startIconStyle}>{startIcon}</span>}
        
        <input
          id={inputId}
          style={{...inputStyle, ...inputFocusStyle}}
          {...props}
        />
        
        {endIcon && <span style={endIconStyle}>{endIcon}</span>}
      </div>
      
      {(error && errorText) ? (
        <p style={helperTextStyle}>{errorText}</p>
      ) : helperText ? (
        <p style={helperTextStyle}>{helperText}</p>
      ) : null}
    </div>
  );
};

export default Input;
