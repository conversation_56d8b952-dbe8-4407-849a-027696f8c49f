LoaLoa Super Admin Panel
Tổng quan
Super Admin Panel cho hệ thống LoaLoa là một dashboard được phát triển bằng Next.js để quản lý hệ thống multi-tenant. Hệ thống cho phép quản trị viên quản lý người dùng, tenants, và licenses trong một giao diện thống nhất.

Công nghệ sử dụng
Frontend: Next.js 14.1.3, React 18.2.0
Ngôn ngữ: TypeScript 5.4.5
Backend: Supabase (PostgreSQL)
Xác thực: JWT thông qua Supabase Auth
State Management: SWR cho fetching và caching data
UI Components: Custom UI library (@loaloa/ui)
Styling: CSS Modules (SCSS)
Cấu trúc dự án
D:\loaloa\apps\super-admin\
├── app/
│   ├── api/                  # API routes
│   │   ├── admins/           # Admin management API
│   │   ├── tenants/          # Tenant management API
│   │   └── licenses/         # License management API
│   ├── components/           # Shared components
│   │   ├── ErrorBoundary/    # Error handling component
│   │   ├── LoadingIndicator/ # Global loading indicator
│   │   ├── SkeletonLoader/   # Loading skeleton components
│   │   └── UserTable/        # User listing component
│   ├── hooks/                # Custom React hooks
│   ├── providers/            # Context providers
│   ├── services/             # Service layers for API
│   ├── users/                # User management pages
│   │   ├── [id]/             # User detail page
│   │   │   └── edit/         # User edit page
│   │   ├── new/              # Create user page
│   │   └── page.tsx          # User listing page
│   ├── tenants/              # Tenant management pages
│   ├── licenses/             # License management pages
│   ├── globals.css           # Global styles
│   └── layout.tsx            # Root layout
├── public/                   # Static assets
├── utils/                    # Utility functions
│   └── supabase/             # Supabase client utilities
└── package.json              # Project dependencies
Cấu trúc cơ sở dữ liệu
Bảng chính
admin_profiles

id: UUID (khóa chính, liên kết với auth.users)
full_name: VARCHAR
role: ENUM (super_admin, admin)
phone: VARCHAR
is_active: BOOLEAN
profile_image: TEXT
created_at: TIMESTAMP
last_login: TIMESTAMP
tenants

id: UUID (khóa chính)
name: VARCHAR
domain: VARCHAR
contact_email: VARCHAR
contact_phone: VARCHAR
address: TEXT
logo_url: TEXT
primary_color: VARCHAR
is_active: BOOLEAN
created_at: TIMESTAMP
updated_at: TIMESTAMP
metadata: JSONB
tenant_users

id: UUID (khóa chính)
tenant_id: UUID (khóa ngoại -> tenants.id)
user_id: UUID (khóa ngoại -> auth.users.id)
role: ENUM (owner, admin, member)
is_primary_tenant: BOOLEAN
is_active: BOOLEAN
joined_at: TIMESTAMP
last_login_at: TIMESTAMP
expiry_date: TIMESTAMP
permissions: JSONB
created_at: TIMESTAMP
updated_at: TIMESTAMP
metadata: JSONB
tenant_licenses

id: UUID (khóa chính)
tenant_id: UUID (khóa ngoại -> tenants.id)
license_type: VARCHAR
starts_at: TIMESTAMP
expires_at: TIMESTAMP
is_active: BOOLEAN
user_limit: INT
payment_status: VARCHAR
price: DECIMAL
created_at: TIMESTAMP
updated_at: TIMESTAMP
metadata: JSONB
Views
auth_user_emails

View để truy cập thông tin email từ bảng auth.users
id: UUID
email: VARCHAR
my_tenants

View để lấy danh sách tenant của người dùng hiện tại
Bao gồm thông tin tenant và vai trò của người dùng
Tính năng chính
Quản lý người dùng
Xem danh sách người dùng với phân trang và tìm kiếm
Tạo người dùng mới
Xem chi tiết người dùng
Chỉnh sửa thông tin người dùng
Kích hoạt/vô hiệu hóa người dùng
Reset mật khẩu
Quản lý liên kết với tenants
Quản lý Tenant
Xem danh sách tenants
Tạo tenant mới
Chỉnh sửa thông tin tenant
Kích hoạt/vô hiệu hóa tenant
Quản lý người dùng trong tenant
Quản lý License
Xem danh sách licenses
Tạo license mới
Chỉnh sửa thông tin license
Kích hoạt/vô hiệu hóa license
Theo dõi ngày hết hạn và trạng thái thanh toán
Xác thực và phân quyền
Xác thực: Sử dụng Supabase Auth để quản lý đăng nhập và JWT
Phân quyền: Sử dụng Role-Based Access Control (RBAC)
Super Admin: Có quyền truy cập đầy đủ vào tất cả các tính năng
Admin: Có quyền quản lý trong phạm vi tenant được gán
Row Level Security (RLS): Áp dụng RLS ở cấp độ database để đảm bảo tính bảo mật
Hướng dẫn cài đặt
Thiết lập môi trường
Đảm bảo đã cài đặt Node.js 20.12.2 hoặc cao hơn
Cài đặt PNPM: npm install -g pnpm
Cài đặt dependencies
cd D:\loaloa
pnpm install
Thiết lập biến môi trường
Tạo file .env.local trong thư mục apps/super-admin với nội dung:

NEXT_PUBLIC_SUPABASE_URL=https://your-supabase-project-url.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
Thiết lập cơ sở dữ liệu
Đăng nhập vào Supabase Dashboard
Chạy các script SQL đã được cung cấp để tạo schema và bảng
Thiết lập Row Level Security (RLS) cho các bảng
Tạo một người dùng admin thông qua giao diện Authentication > Users
Khởi chạy ứng dụng
cd D:\loaloa\apps\super-admin
pnpm dev
Ứng dụng sẽ chạy tại địa chỉ: http://localhost:3000

Troubleshooting
Vấn đề xác thực
Đảm bảo JWT token được tạo và sử dụng đúng cách
Kiểm tra RLS policies trong Supabase
Lỗi kết nối cơ sở dữ liệu
Xác minh URL và API key Supabase trong .env.local
Kiểm tra console lỗi để biết thêm chi tiết
Vấn đề về mối quan hệ bảng
Đảm bảo các foreign key được thiết lập đúng
Kiểm tra các truy vấn SQL khi gặp lỗi về mối quan hệ
Đóng góp
Fork repository
Tạo branch mới: git checkout -b feature/your-feature-name
Commit thay đổi: git commit -m 'Add some feature'
Push lên branch: git push origin feature/your-feature-name
Tạo Pull Request
Giấy phép
© 2025 LoaLoa. Tất cả các quyền được bảo lưu.

README này được thiết kế cho Super Admin Panel của LoaLoa - Giải pháp chat đa ngôn ngữ cho ngành dịch vụ, đặc biệt tập trung vào khả năng giao tiếp giữa khách hàng và nhà cung cấp dịch vụ nói các ngôn ngữ khác nhau. Hệ thống được thiết kế với kiến trúc microservices, hỗ trợ multiple tenancy (đa người thuê), dễ dàng mở rộng từ một khách sạn đến nhiều doanh nghiệp khác nhau.