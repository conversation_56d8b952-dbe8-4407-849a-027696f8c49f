# LoaLoa Chat Service

Dịch vụ chat đa ngôn ngữ thời gian thực cho LoaLoa - gi<PERSON>i pháp giao tiếp đa ngôn ngữ cho ngành dịch vụ.

## Cấu trúc thư mục
/services/chat/ 
├── migrations/ # SQL migrations 
├── src/ 
│ ├── controllers/ # API controllers 
│ ├── middlewares/ # Express middlewares 
│ ├── routes/ # API routes 
│ ├── services/ # Business logic 
│ ├── types/ # TypeScript types 
│ ├── utils/ # Utilities 
│ ├── websocket/ # WebSocket handlers 
│ └── index.ts # Entry point 
├── .env # Environment variables 
├── package.json # Project dependencies 
└── tsconfig.json # TypeScript configuration


## Tính năng

- Giao tiếp thời gian thực qua WebSocket
- Phòng chat công khai và riêng tư
- Hỗ trợ nhiều ngôn ngữ với dịch tự động
- Trạng thái tin nhắn (đã gửi, đã đọc)
- Hi<PERSON><PERSON> thị "đang gõ"
- Lưu trữ lịch sử chat

## WebSocket Events

- Connect/Disconnect
- Tham gia/Rời phòng
- Gửi/Nhận tin nhắn
- Cập nhật trạng thái
- Yêu cầu dịch

## API Endpoints

- Quản lý phòng chat
- Quản lý tin nhắn
- Quản lý người tham gia

## Cài đặt và chạy

```bash
# Cài đặt dependencies
npm install

# Khởi động trong chế độ development
npm run dev

# Build và chạy trong chế độ production
npm run build
npm start