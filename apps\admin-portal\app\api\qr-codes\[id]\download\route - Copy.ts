import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import QRCode from 'qrcode';

// Tạo Supabase client
const createSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  return createClient(supabaseUrl, supabaseKey);
};

// GET: Tạo và trả về hình ảnh QR code
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const qrCodeId = params.id;
    const { searchParams } = new URL(request.url);
    const size = parseInt(searchParams.get('size') || '300');
    const format = searchParams.get('format') || 'png';
    const appDomain = process.env.NEXT_PUBLIC_APP_DOMAIN || 'https://app.loaloa.com';

    // Tạo Supabase client
    const supabase = createSupabaseClient();

    // Lấy thông tin QR code
    const { data: qrCode, error } = await supabase
      .from('tenant_qr_codes')  // Sử dụng bảng tenant_qr_codes
      .select('code_value, location, tenant_id')
      .eq('id', qrCodeId)
      .single();

    if (error || !qrCode) {
      return NextResponse.json({ error: 'QR code not found' }, { status: 404 });
    }

    // Tạo URL cho QR code
    const qrUrl = `${appDomain}/scan/${qrCode.code_value}`;

    // Tạo hình ảnh QR code
    const qrOptions = {
      errorCorrectionLevel: 'H',
      margin: 1,
      width: size,
      color: { dark: '#000000', light: '#FFFFFF' }
    };

    // Tạo QR code dưới dạng data URL
    const qrDataUrl = await QRCode.toDataURL(qrUrl, qrOptions);

    // Trả về QR code dưới dạng JSON hoặc hình ảnh
    if (format === 'json') {
      return NextResponse.json({ data_url: qrDataUrl, url: qrUrl, location: qrCode.location });
    } else {
      // Chuyển đổi data URL thành Buffer
      const base64Data = qrDataUrl.replace(/^data:image\/\w+;base64,/, '');
      const buffer = Buffer.from(base64Data, 'base64');
      
      // Trả về hình ảnh
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': 'image/png',
          'Content-Disposition': `attachment; filename="qrcode-${qrCode.location.replace(/[^a-z0-9]/gi, '-').toLowerCase()}.png"`
        }
      });
    }
  } catch (error) {
    console.error('Error in GET QR code download:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
