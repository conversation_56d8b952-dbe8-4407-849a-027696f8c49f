// Load Organization Members
document.getElementById('load-org-members').addEventListener('click', async () => {
  if (!supabaseClient) {
    addDebugMessage('Please connect to Supabase first', true);
    return;
  }
  
  try {
    addDebugMessage('Loading organization members...');
    
    const client = serviceClient || supabaseClient;
    const { data, error } = await client
      .from('organization_members')
      .select(`
        id, 
        user_id,
        organization_id,
        role,
        organizations(name),
        auth.users!user_id(email)
      `)
      .limit(20);
      
    if (error) {
      throw error;
    }
    
    updateStatus('db', 'success');
    addDebugMessage(`Loaded ${data.length} organization members`);
    document.getElementById('org-members-count').textContent = `${data.length} organization members loaded`;
    
    // Define columns for organization members table
    const columns = [
      { field: 'id', label: 'ID' },
      { field: 'user_id', label: 'User ID' },
      { field: 'organization_id', label: 'Org ID' },
      { field: 'role', label: 'Role' },
      { field: 'joined_at', label: 'Joined At', format: 'date' }
    ];
    
    loadTableData('org-members-table', data, columns);
    
    // Cache organization member data for later use
    window.orgMemberData = data;
  } catch (err) {
    updateStatus('db', 'error');
    addDebugMessage(`Failed to load organization members: ${err.message}`, true);
  }
});