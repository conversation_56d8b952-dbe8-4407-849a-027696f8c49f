# Design Specification Guidelines

## Units and Measurements

- All spacing should use the 4px grid system (4, 8, 12, 16px, etc.)
- Font sizes should follow the defined typography scale
- Element heights should be multiples of 4px where possible

## Component Annotation

### Required Information
- Component name
- Component states (default, hover, active, disabled)
- Padding and margin values
- Font styles (size, weight, line height)
- Color values (use design token names)
- Corner radius values

### Example Annotation
Button/Primary

Padding: 12px 24px (3-6 in spacing tokens)
Border-radius: 4px (md in radius tokens)
Typography: 16px/500 (md/medium in typography tokens)
Color: Primary Blue (#3B82F6)
## Design File Organization

- Use named components for all reusable elements
- Organize screens in logical user flows
- Keep all text as actual text (not outlines)
- Use shared styles that match design tokens