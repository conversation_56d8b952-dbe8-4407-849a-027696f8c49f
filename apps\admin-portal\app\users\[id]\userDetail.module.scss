@import '../../styles/_variables.scss';

.container {
  padding: $spacing-md;
  max-width: 1200px;
  margin: 0 auto;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
}

.titleSection {
  display: flex;
  flex-direction: column;
}

.backLink {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $gray;
  text-decoration: none;
  margin-bottom: $spacing-xs;
  font-size: 14px;
  
  &:hover {
    color: $primary-color;
  }
}

.pageTitle {
  font-size: 24px;
  font-weight: 600;
  color: $black;
  margin: 0;
}

.actions {
  display: flex;
  gap: $spacing-md;
}

.primaryButton {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: 8px 16px;
  background-color: $primary-color;
  color: $white;
  border-radius: $border-radius-md;
  text-decoration: none;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    opacity: 0.9;
  }
  
  svg {
    width: 18px;
    height: 18px;
  }
}

.secondaryButton {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: 8px 16px;
  background-color: $secondary-color;
  color: $dark-gray;
  border-radius: $border-radius-md;
  text-decoration: none;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #e0e0e0;
  }
  
  svg {
    width: 18px;
    height: 18px;
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: $primary-color;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: $spacing-md;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.userDetailLayout {
  display: flex;
  gap: $spacing-lg;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.userProfile {
  width: 300px;
  flex-shrink: 0;
  
  @media (max-width: 768px) {
    width: 100%;
  }
}

.userProfileCard {
  background-color: $white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  overflow: hidden;
}

.avatarSection {
  position: relative;
  background-color: #f5f5f5;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid $white;
  box-shadow: $shadow-sm;
}

.avatarPlaceholder {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: $primary-color;
  color: $white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  font-weight: 600;
  border: 4px solid $white;
  box-shadow: $shadow-sm;
}

.statusBadge {
  position: absolute;
  bottom: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  
  &.active {
    background-color: #10b981;
    color: $white;
  }
  
  &.inactive {
    background-color: #ef4444;
    color: $white;
  }
}

.userInfo {
  padding: $spacing-md;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.userName {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 $spacing-sm;
  text-align: center;
}

.roleBadge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: $spacing-md;
  text-transform: uppercase;
  
  &.admin {
    background-color: #475569;
    color: $white;
  }
  
  &.manager {
    background-color: #1d4ed8;
    color: $white;
  }
  
  &.staff {
    background-color: #0891b2;
    color: $white;
  }
  
  &.user {
    background-color: #6b7280;
    color: $white;
  }
}

.contactInfo {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
  margin-top: $spacing-md;
}

.contactItem {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  color: $dark-gray;
  
  svg {
    color: $gray;
    flex-shrink: 0;
  }
  
  span {
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.userDetails {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.detailsCard {
  background-color: $white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  padding: $spacing-lg;
}

.cardTitle {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 $spacing-lg;
  padding-bottom: $spacing-sm;
  border-bottom: 1px solid $secondary-color;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: $spacing-md $spacing-lg;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.infoLabel {
  font-size: 14px;
  color: $gray;
}

.infoValue {
  font-size: 16px;
  font-weight: 500;
}

.languageTags {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-xs;
}

.languageTag {
  display: inline-block;
  padding: 4px 8px;
  background-color: #f3f4f6;
  border-radius: $border-radius-sm;
  font-size: 12px;
}

.activityList {
  display: flex;
  flex-direction: column;
}

.activityItem {
  display: flex;
  padding: $spacing-md 0;
  border-bottom: 1px solid $secondary-color;
  
  &:last-child {
    border-bottom: none;
  }
}

.activityIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #f3f4f6;
  border-radius: 50%;
  margin-right: $spacing-md;
  color: $primary-color;
}

.activityContent {
  display: flex;
  flex-direction: column;
}

.activityText {
  font-weight: 500;
}

.activityTime {
  font-size: 14px;
  color: $gray;
}

.viewMoreButton {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-sm;
  background: none;
  border: none;
  color: $primary-color;
  font-weight: 500;
  cursor: pointer;
  margin: $spacing-md auto 0;
  
  &:hover {
    text-decoration: underline;
  }
}
