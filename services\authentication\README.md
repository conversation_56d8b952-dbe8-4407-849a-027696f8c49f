# LoaLoa Authentication Service

Dịch vụ xác thực cho ứng dụng LoaLoa - giải pháp chat đa ngôn ngữ.

## Cấu trúc thư mục

/services/authentication/ 
├── migrations/ # SQL migrations 
├── src/ 
│ ├── controllers/ # API controllers 
│ ├── middlewares/ # Express middlewares 
│ ├── routes/ # API routes 
│ ├── services/ # Business logic 
│ ├── types/ # TypeScript types 
│ ├── utils/ # Utilities 
│ └── index.ts # Entry point 
├── tests/ # Test files 
├── .env # Environment variables 
├── package.json # Project dependencies 
└── tsconfig.json # TypeScript configuration


## API Endpoints

### Public endpoints:
- POST /api/auth/register - Đăng ký người dùng mới
- POST /api/auth/login - Đăng nhập
- POST /api/auth/forgot-password - Quên mật khẩu
- POST /api/auth/refresh-token - Làm mới token truy cập
- GET /api/auth/verify-email/:userId/:token - <PERSON><PERSON><PERSON> minh email

### Protected endpoints:
- GET /api/auth/profile - <PERSON><PERSON><PERSON> thông tin cá nhân
- PUT /api/auth/profile - <PERSON><PERSON><PERSON> nhật thông tin cá nhân
- POST /api/auth/change-password - Đổi mật khẩu

### Temporary user endpoints:
- POST /api/temporary-users - Tạo người dùng tạm thời và mã QR
- POST /api/temporary-users/activate/:qr_token - Kích hoạt người dùng tạm thời bằng mã QR
- GET /api/temporary-users/device/:device_id - Lấy thông tin người dùng tạm thời từ ID thiết bị
- POST /api/temporary-users/convert/:temporary_user_id - Chuyển đổi người dùng tạm thời thành tài khoản đầy đủ

## Quá trình thiết lập

1. ✅ Thiết kế schema cho user và quyền truy cập trong Supabase
2. ✅ Triển khai API đăng nhập, đăng ký và quản lý phiên
3. ✅ Cài đặt hệ thống JWT cho xác thực
4. ✅ Tạo cơ chế tài khoản tạm thời cho khách qua QR

## Hướng dẫn chạy local

Cài đặt dependencies
npm install

Chạy trong chế độ development
npm run dev

Build và chạy trong chế độ production
npm run build npm start


## Môi trường

file `.env.example` thành `.env` và cập nhật các biến môi trường:

Server
PORT=3001 NODE_ENV=development

Supabase
SUPABASE_URL=your_supabase_url SUPABASE_KEY=your_supabase_key

JWT
JWT_SECRET=your_jwt_secret_key JWT_ACCESS_EXPIRATION=15m JWT_REFRESH_EXPIRATION=7d

QR Code
TEMPORARY_USER_EXPIRATION=24h