import fs from 'fs';
import path from 'path';
import supabase from '../utils/supabase';

const applyMigration = async (sql: string): Promise<{ success: boolean, message: string }> => {
  try {
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      return { success: false, message: `Error executing SQL: ${error.message}` };
    }
    
    return { success: true, message: 'SQL executed successfully' };
  } catch (error: any) {
    return { success: false, message: `Exception: ${error.message || 'Unknown error'}` };
  }
};

const runMigrations = async () => {
  console.log('Starting migration test...');
  
  // Get all migration files in order
  const migrationsPath = path.join(__dirname, '../../migrations');
  const files = fs.readdirSync(migrationsPath)
    .filter(file => file.endsWith('.sql'))
    .sort();
  
  console.log(`Found ${files.length} migration files`);
  
  for (const file of files) {
    console.log(`\nApplying migration: ${file}`);
    
    const filePath = path.join(migrationsPath, file);
    const sql = fs.readFileSync(filePath, 'utf8');
    
    try {
      const result = await applyMigration(sql);
      
      if (result.success) {
        console.log(`✅ Migration '${file}' applied successfully`);
      } else {
        console.error(`❌ Migration '${file}' failed: ${result.message}`);
      }
    } catch (error: any) {
      console.error(`❌ Exception applying migration '${file}': ${error.message || 'Unknown error'}`);
    }
  }
  
  console.log('\nMigration test completed');
};

// Note: You may need to enable the "exec_sql" RPC function in your Supabase project
// This is often disabled by default for security reasons
// Please use Supabase UI or SQL editor to apply migrations in production
runMigrations()
  .then(() => console.log('All migrations completed'))
  .catch(error => console.error('Migration process failed:', error));
