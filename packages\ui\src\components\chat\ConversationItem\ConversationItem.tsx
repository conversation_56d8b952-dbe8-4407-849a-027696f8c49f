import React from 'react';
import styles from './ConversationItem.module.scss';

export interface ConversationItemProps {
  /**
   * ID of the conversation
   */
  id: string;
  /**
   * Title of the conversation (user/group name)
   */
  title: string;
  /**
   * Avatar URL
   */
  avatarUrl?: string;
  /**
   * Last message
   */
  lastMessage?: string;
  /**
   * Time of last message
   */
  lastMessageTime?: string | Date;
  /**
   * Number of unread messages
   */
  unreadCount?: number;
  /**
   * Is the conversation active
   */
  isActive?: boolean;
  /**
   * Language of the conversation
   */
  language?: string;
  /**
   * Click handler
   */
  onClick?: () => void;
  /**
   * Additional CSS class
   */
  className?: string;
}

export const ConversationItem: React.FC<ConversationItemProps> = ({
  id,
  title,
  avatarUrl,
  lastMessage,
  lastMessageTime,
  unreadCount = 0,
  isActive = false,
  language,
  onClick,
  className = '',
}) => {
  // Format time
  const formattedTime = lastMessageTime ? (
    typeof lastMessageTime === 'string'
      ? formatMessageTime(new Date(lastMessageTime))
      : formatMessageTime(lastMessageTime)
  ) : '';

  return (
    <div 
      className={`${styles.container} ${isActive ? styles.active : ''} ${className}`}
      onClick={onClick}
    >
      <div className={styles.avatar}>
        {avatarUrl ? (
          <img src={avatarUrl} alt={title} />
        ) : (
          <div className={styles.avatarPlaceholder}>
            {title.charAt(0).toUpperCase()}
          </div>
        )}
      </div>
      
      <div className={styles.content}>
        <div className={styles.header}>
          <h3 className={styles.title}>{title}</h3>
          {lastMessageTime && (
            <span className={styles.time}>{formattedTime}</span>
          )}
        </div>
        
        <div className={styles.body}>
          {lastMessage && (
            <p className={styles.message}>{lastMessage}</p>
          )}
          
          {language && (
            <span className={styles.language}>{language}</span>
          )}
          
          {unreadCount > 0 && (
            <span className={styles.badge}>{unreadCount}</span>
          )}
        </div>
      </div>
    </div>
  );
};

// Helper function to format message time
function formatMessageTime(date: Date): string {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  if (date >= today) {
    // Today, show time
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } else if (date >= yesterday) {
    // Yesterday
    return 'Yesterday';
  } else {
    // Older, show date
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  }
}

export default ConversationItem;
