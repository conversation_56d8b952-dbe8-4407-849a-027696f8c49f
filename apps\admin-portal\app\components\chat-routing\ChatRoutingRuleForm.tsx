'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import styles from './ChatRoutingRuleForm.module.scss';
import { Button } from '@ui';
import ReceptionPointDropdown from '../ReceptionPointDropdown';

interface ChatRoutingRuleFormProps {
  initialData?: any;
  onSubmit: (formData: any) => Promise<void>;
  isSubmitting?: boolean;
  isEditing?: boolean;
}

export default function ChatRoutingRuleForm({
  initialData = {},
  onSubmit,
  isSubmitting = false,
  isEditing = false
}: ChatRoutingRuleFormProps) {
  // Form state
  const [formData, setFormData] = useState({
    rule_name: initialData.rule_name || '',
    rule_type: initialData.rule_type || 'qr_code',
    rule_condition: initialData.rule_condition || {
      qr_code_id: ''
    },
    target_type: initialData.target_reception_point_id
      ? 'reception_point'
      : initialData.target_user_id
      ? 'user'
      : initialData.target_department
      ? 'department'
      : 'reception_point',
    target_reception_point_id: initialData.target_reception_point_id || '',
    target_department: initialData.target_department || '',
    target_user_id: initialData.target_user_id || '',
    priority: initialData.priority !== undefined ? initialData.priority : 1,
    is_active: initialData.is_active !== undefined ? initialData.is_active : true,
  });

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load data for selectors
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState<any[]>([]);
  const [qrCodes, setQrCodes] = useState<any[]>([]);
  const [receptionPoints, setReceptionPoints] = useState<any[]>([]);

  // Load data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch users
        const usersResponse = await fetch('/api/users?limit=100');
        const usersData = await usersResponse.json();
        setUsers(usersData.data || []);
        
        // Fetch QR codes - Sử dụng API endpoint chính xác
        const qrCodesResponse = await fetch('/api/qr-codes?limit=100');
        const qrCodesData = await qrCodesResponse.json();
        setQrCodes(qrCodesData.data || []);
        
        // Fetch reception points
        const receptionPointsResponse = await fetch('/api/reception-points?limit=100');
        const receptionPointsData = await receptionPointsResponse.json();
        setReceptionPoints(receptionPointsData.data || []);
      } catch (err) {
        console.error('Error fetching form data:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  // Update rule condition based on rule type
  useEffect(() => {
    if (formData.rule_type === 'qr_code' && (!formData.rule_condition || !formData.rule_condition.qr_code_id)) {
      setFormData(prev => ({
        ...prev,
        rule_condition: { qr_code_id: '' }
      }));
    } else if (formData.rule_type === 'guest' && (!formData.rule_condition || !formData.rule_condition.guest_type)) {
      setFormData(prev => ({
        ...prev,
        rule_condition: { guest_type: 'vip' }
      }));
    } else if (formData.rule_type === 'time' && (!formData.rule_condition || !formData.rule_condition.start_time)) {
      setFormData(prev => ({
        ...prev,
        rule_condition: {
          start_time: '22:00',
          end_time: '08:00',
          days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        }
      }));
    } else if (formData.rule_type === 'language' && (!formData.rule_condition || !formData.rule_condition.language)) {
      setFormData(prev => ({
        ...prev,
        rule_condition: { language: 'en' }
      }));
    } else if (formData.rule_type === 'custom' && (!formData.rule_condition || !formData.rule_condition.condition)) {
      setFormData(prev => ({
        ...prev,
        rule_condition: { condition: {} }
      }));
    }
  }, [formData.rule_type]);

  // Handle form field changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
    
    // Handle special cases
    if (name === 'target_type') {
      // Reset other target fields
      if (value === 'reception_point') {
        setFormData(prev => ({
          ...prev,
          target_type: value,
          target_department: '',
          target_user_id: ''
        }));
      } else if (value === 'department') {
        setFormData(prev => ({
          ...prev,
          target_type: value,
          target_reception_point_id: '',
          target_user_id: ''
        }));
      } else if (value === 'user') {
        setFormData(prev => ({
          ...prev,
          target_type: value,
          target_reception_point_id: '',
          target_department: ''
        }));
      }
    }
  };

  // Handle rule condition changes
  const handleConditionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      rule_condition: {
        ...prev.rule_condition,
        [name]: value
      }
    }));
    
    // Clear error for this field in rule_condition
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Form validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.rule_name.trim()) {
      newErrors.rule_name = 'Rule name is required';
    }
    
    if (!formData.rule_type) {
      newErrors.rule_type = 'Rule type is required';
    }
    
    // Validate rule condition based on type
    if (formData.rule_type === 'qr_code' && !formData.rule_condition.qr_code_id) {
      newErrors.qr_code_id = 'QR Code is required';
    }
    
    // Validate target based on target type
    if (formData.target_type === 'reception_point' && !formData.target_reception_point_id) {
      newErrors.target_reception_point_id = 'Reception Point is required';
    } else if (formData.target_type === 'department' && !formData.target_department) {
      newErrors.target_department = 'Department is required';
    } else if (formData.target_type === 'user' && !formData.target_user_id) {
      newErrors.target_user_id = 'User is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    // Đảm bảo rule_condition có giá trị hợp lệ
    let validRuleCondition = { ...formData.rule_condition };
    if (formData.rule_type === 'qr_code' && !validRuleCondition.qr_code_id) {
      setErrors(prev => ({ ...prev, qr_code_id: 'QR Code is required' }));
      return;
    }
    
    // Prepare submission data
    const submissionData = {
      rule_name: formData.rule_name,
      rule_type: formData.rule_type,
      rule_condition: validRuleCondition,
      priority: parseInt(formData.priority.toString()),
      is_active: formData.is_active
    };
    
    // Add target based on selected target type
    if (formData.target_type === 'reception_point') {
      submissionData['target_reception_point_id'] = formData.target_reception_point_id;
    } else if (formData.target_type === 'department') {
      submissionData['target_department'] = formData.target_department;
    } else if (formData.target_type === 'user') {
      submissionData['target_user_id'] = formData.target_user_id;
    }
    
    // Submit form
    try {
      await onSubmit(submissionData);
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  if (loading) {
    return <div className={styles.loading}>Loading data...</div>;
  }

  return (
    <form onSubmit={handleSubmit} className={styles.form}>
      <div className={styles.formSection}>
        <h2 className={styles.sectionTitle}>Basic Information</h2>
        
        <div className={styles.formGroup}>
          <label htmlFor="rule_name">
            Rule Name <span className={styles.required}>*</span>
          </label>
          <input
            type="text"
            id="rule_name"
            name="rule_name"
            value={formData.rule_name}
            onChange={handleInputChange}
            className={errors.rule_name ? styles.inputError : ''}
            placeholder="Enter a name for this routing rule"
          />
          {errors.rule_name && <div className={styles.errorText}>{errors.rule_name}</div>}
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="rule_type">
            Rule Type <span className={styles.required}>*</span>
          </label>
          <select
            id="rule_type"
            name="rule_type"
            value={formData.rule_type}
            onChange={handleInputChange}
            className={errors.rule_type ? styles.inputError : ''}
          >
            <option value="qr_code">QR Code</option>
            <option value="guest">Guest Type</option>
            <option value="time">Time</option>
            <option value="language">Language</option>
            <option value="custom">Custom</option>
          </select>
          {errors.rule_type && <div className={styles.errorText}>{errors.rule_type}</div>}
          <div className={styles.fieldDescription}>
            Defines when this rule will be applied
          </div>
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="priority">Priority</label>
          <input
            type="number"
            id="priority"
            name="priority"
            min="1"
            max="100"
            value={formData.priority}
            onChange={handleInputChange}
          />
          <div className={styles.fieldDescription}>
            Higher values mean higher priority (rules are evaluated from highest to lowest)
          </div>
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="is_active">Status</label>
          <div className={styles.checkboxContainer}>
            <input
              type="checkbox"
              id="is_active"
              name="is_active"
              checked={formData.is_active}
              onChange={handleInputChange}
            />
            <label htmlFor="is_active" className={styles.checkboxLabel}>
              Active
            </label>
          </div>
        </div>
      </div>

      <div className={styles.formSection}>
        <h2 className={styles.sectionTitle}>Rule Condition</h2>
        
        {formData.rule_type === 'qr_code' && (
          <div className={styles.formGroup}>
            <label htmlFor="qr_code_id">
              QR Code <span className={styles.required}>*</span>
            </label>
            <select
              id="qr_code_id"
              name="qr_code_id"
              value={formData.rule_condition.qr_code_id || ''}
              onChange={handleConditionChange}
              className={errors.qr_code_id ? styles.inputError : ''}
            >
              <option value="">Select a QR Code</option>
              {qrCodes.map(qr => (
                <option key={qr.id} value={qr.id}>
                  {qr.name || qr.code_value || qr.location || 'Unnamed QR Code'}
                </option>
              ))}
            </select>
            {errors.qr_code_id && <div className={styles.errorText}>{errors.qr_code_id}</div>}
            <div className={styles.fieldDescription}>
              This rule will apply when this specific QR Code is scanned
            </div>
          </div>
        )}
        
        {formData.rule_type === 'guest' && (
          <div className={styles.formGroup}>
            <label htmlFor="guest_type">Guest Type</label>
            <select
              id="guest_type"
              name="guest_type"
              value={formData.rule_condition.guest_type || 'vip'}
              onChange={handleConditionChange}
            >
              <option value="vip">VIP</option>
              <option value="regular">Regular</option>
              <option value="loyalty">Loyalty</option>
              <option value="new">New</option>
            </select>
            <div className={styles.fieldDescription}>
              This rule will apply for specific guest types
            </div>
          </div>
        )}
        
        {formData.rule_type === 'time' && (
          <>
            <div className={styles.formGroup}>
              <label htmlFor="start_time">Start Time</label>
              <input
                type="time"
                id="start_time"
                name="start_time"
                value={formData.rule_condition.start_time || '22:00'}
                onChange={handleConditionChange}
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="end_time">End Time</label>
              <input
                type="time"
                id="end_time"
                name="end_time"
                value={formData.rule_condition.end_time || '08:00'}
                onChange={handleConditionChange}
              />
              <div className={styles.fieldDescription}>
                This rule will apply during the specified time period
              </div>
            </div>
          </>
        )}
        
        {formData.rule_type === 'language' && (
          <div className={styles.formGroup}>
            <label htmlFor="language">Language</label>
            <select
              id="language"
              name="language"
              value={formData.rule_condition.language || 'en'}
              onChange={handleConditionChange}
            >
              <option value="en">English</option>
              <option value="vi">Vietnamese</option>
              <option value="ja">Japanese</option>
              <option value="ko">Korean</option>
              <option value="zh">Chinese</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="es">Spanish</option>
            </select>
            <div className={styles.fieldDescription}>
              This rule will apply for guests using a specific language
            </div>
          </div>
        )}
        
        {formData.rule_type === 'custom' && (
          <div className={styles.formGroup}>
            <label htmlFor="custom_condition">Custom Condition</label>
            <textarea
              id="custom_condition"
              name="condition"
              value={JSON.stringify(formData.rule_condition.condition || {}, null, 2)}
              onChange={handleConditionChange}
              rows={5}
              placeholder="{}"
            />
            <div className={styles.fieldDescription}>
              Enter a custom JSON condition
            </div>
          </div>
        )}
      </div>

      <div className={styles.formSection}>
        <h2 className={styles.sectionTitle}>Route To</h2>
        
        <div className={styles.formGroup}>
          <label htmlFor="target_type">Route To</label>
          <div className={styles.radioGroup}>
            <div className={styles.radioOption}>
              <input
                type="radio"
                id="target_reception_point"
                name="target_type"
                value="reception_point"
                checked={formData.target_type === 'reception_point'}
                onChange={handleInputChange}
              />
              <label htmlFor="target_reception_point">Reception Point</label>
            </div>
            <div className={styles.radioOption}>
              <input
                type="radio"
                id="target_department"
                name="target_type"
                value="department"
                checked={formData.target_type === 'department'}
                onChange={handleInputChange}
              />
              <label htmlFor="target_department">Department</label>
            </div>
            <div className={styles.radioOption}>
              <input
                type="radio"
                id="target_user"
                name="target_type"
                value="user"
                checked={formData.target_type === 'user'}
                onChange={handleInputChange}
              />
              <label htmlFor="target_user">Specific User</label>
            </div>
          </div>
        </div>
        
        {formData.target_type === 'reception_point' && (
          <div className={styles.formGroup}>
            <label htmlFor="target_reception_point_id">
              Select Reception Point <span className={styles.required}>*</span>
            </label>
            <select
              id="target_reception_point_id"
              name="target_reception_point_id"
              value={formData.target_reception_point_id}
              onChange={handleInputChange}
              className={errors.target_reception_point_id ? styles.inputError : ''}
            >
              <option value="">Select a Reception Point</option>
              {receptionPoints.map(point => (
                <option key={point.id} value={point.id}>
                  {point.name}
                </option>
              ))}
            </select>
            {errors.target_reception_point_id && (
              <div className={styles.errorText}>{errors.target_reception_point_id}</div>
            )}
          </div>
        )}
        
        {formData.target_type === 'department' && (
          <div className={styles.formGroup}>
            <label htmlFor="target_department">
              Select Department <span className={styles.required}>*</span>
            </label>
            <select
              id="target_department"
              name="target_department"
              value={formData.target_department}
              onChange={handleInputChange}
              className={errors.target_department ? styles.inputError : ''}
            >
              <option value="">Select a Department</option>
              <option value="reception">Reception</option>
              <option value="housekeeping">Housekeeping</option>
              <option value="restaurant">Restaurant</option>
              <option value="spa">Spa</option>
              <option value="concierge">Concierge</option>
              <option value="maintenance">Maintenance</option>
            </select>
            {errors.target_department && (
              <div className={styles.errorText}>{errors.target_department}</div>
            )}
          </div>
        )}
        
        {formData.target_type === 'user' && (
          <div className={styles.formGroup}>
            <label htmlFor="target_user_id">
              Select User <span className={styles.required}>*</span>
            </label>
            <select
              id="target_user_id"
              name="target_user_id"
              value={formData.target_user_id}
              onChange={handleInputChange}
              className={errors.target_user_id ? styles.inputError : ''}
            >
              <option value="">Select a User</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.tenant_users_details?.display_name || user.email || user.id}
                </option>
              ))}
            </select>
            {errors.target_user_id && (
              <div className={styles.errorText}>{errors.target_user_id}</div>
            )}
          </div>
        )}
      </div>

      <div className={styles.formActions}>
        <Button
          type="button"
          variant="secondary"
          label="Cancel"
          onClick={() => window.history.back()}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          label={isEditing ? 'Update Rule' : 'Create Rule'}
          disabled={isSubmitting}
          loading={isSubmitting}
        >
          {isEditing ? 'Update Rule' : 'Create Rule'}
        </Button>
      </div>
    </form>
  );
}
