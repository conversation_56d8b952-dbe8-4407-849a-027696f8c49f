//D:\loaloa\apps\admin-portal\app\api\areas
import { createClient } from '../../../lib/supabase/server';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const areaType = searchParams.get('area_type');
    const floor = searchParams.get('floor');
    const isActive = searchParams.get('is_active');
    const limitStr = searchParams.get('limit');
    const limit = limitStr ? parseInt(limitStr) : undefined;
    
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    let query = supabase
      .from('tenant_areas')
      .select('*', { count: 'exact' });
    
    // Áp dụng các bộ lọc
    if (areaType) {
      query = query.eq('area_type', areaType);
    }
    
    if (floor) {
      query = query.eq('floor', floor);
    }
    
    if (isActive === 'true') {
      query = query.eq('is_active', true);
    } else if (isActive === 'false') {
      query = query.eq('is_active', false);
    }
    
    // Áp dụng giới hạn nếu có
    if (limit) {
      query = query.limit(limit);
    }
    
    const { data, error, count } = await query;
    
    if (error && error.code !== 'PGRST116') { // PGRST116 là lỗi "không tìm thấy bảng"
      console.error('Error fetching areas:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    // Nếu bảng chưa tồn tại hoặc không có dữ liệu, trả về mảng trống
    return NextResponse.json({ data: data || [], count: count || 0 });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ data: [], count: 0 }); // Trả về mảng trống nếu có lỗi
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    const body = await request.json();
    
    const { 
      name, 
      area_type, 
      floor, 
      location,
      //description,
      staff_count,
      opening_hours,
      closing_hours,
      image_url 
    } = body;
    
    // Kiểm tra xem khu vực với tên này đã tồn tại chưa
    const { data: existingArea, error: checkError } = await supabase
      .from('tenant_areas')
      .select('id')
      .eq('name', name)
      .maybeSingle();
    
    if (checkError && checkError.code !== 'PGRST116') { // Bỏ qua lỗi bảng không tồn tại
      console.error('Error checking existing area:', checkError);
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }
    
    if (existingArea) {
      return NextResponse.json({ 
        error: `Khu vực với tên "${name}" đã tồn tại` 
      }, { status: 400 });
    }
    
    // Tạo khu vực mới
    const { data, error } = await supabase
      .from('tenant_areas')
      .insert([
        { 
          name, 
          area_type, 
          floor, 
          location,
         // description,
          staff_count,
          opening_hours,
          closing_hours,
          image_url,
          is_active: true
        }
      ])
      .select();
    
    if (error) {
      console.error('Error creating area:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ data: data[0] }, { status: 201 });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}