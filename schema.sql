

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pg_trgm" WITH SCHEMA "public";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."license_type" AS ENUM (
    'free',
    'basic',
    'premium',
    'enterprise',
    'custom'
);


ALTER TYPE "public"."license_type" OWNER TO "postgres";


CREATE TYPE "public"."tenant_db_status" AS ENUM (
    'initializing',
    'active',
    'suspended',
    'migrating',
    'error'
);


ALTER TYPE "public"."tenant_db_status" OWNER TO "postgres";


CREATE TYPE "public"."tenant_user_role" AS ENUM (
    'owner',
    'admin',
    'manager',
    'staff',
    'member',
    'guest'
);


ALTER TYPE "public"."tenant_user_role" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_user_to_tenant"("tenant_id" "uuid", "email" "text", "role" "text" DEFAULT 'member'::"text", "is_primary_tenant" boolean DEFAULT false, "permissions" "jsonb" DEFAULT '{}'::"jsonb", "expiry_date" timestamp with time zone DEFAULT NULL::timestamp with time zone) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
    user_id UUID;
    user_record RECORD;
    tenant_user_record RECORD;
    valid_role BOOLEAN;
    role_value tenant_user_role;
BEGIN
    -- Kiểm tra quyền
    IF NOT (
        (SELECT is_super_admin FROM auth.users WHERE id = auth.uid()) OR 
        EXISTS (
            SELECT 1 FROM public.tenant_users 
            WHERE tenant_id = add_user_to_tenant.tenant_id 
            AND user_id = auth.uid() 
            AND role IN ('owner', 'admin')
        )
    ) THEN
        RAISE EXCEPTION 'Không có quyền thêm người dùng vào tenant';
    END IF;
    
    -- Tìm user_id từ email
    SELECT id INTO user_id FROM auth.users WHERE email = add_user_to_tenant.email;
    
    IF user_id IS NULL THEN
        -- Người dùng chưa tồn tại, chúng ta có thể gửi lời mời hoặc báo lỗi
        RETURN json_build_object(
            'success', false,
            'error', 'Người dùng với email này chưa tồn tại trong hệ thống'
        );
    END IF;
    
    -- Kiểm tra xem người dùng đã thuộc tenant này chưa
    SELECT * INTO tenant_user_record 
    FROM public.tenant_users 
    WHERE tenant_id = add_user_to_tenant.tenant_id 
    AND user_id = user_id;
    
    IF tenant_user_record IS NOT NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Người dùng đã thuộc tenant này'
        );
    END IF;
    
    -- Kiểm tra và chuyển đổi role
    SELECT exists(
        SELECT 1 FROM pg_type 
        WHERE typname = 'tenant_user_role' 
        AND typtype = 'e' 
        AND typelem = 0
    ) INTO valid_role;

    IF valid_role THEN
        -- Nếu đúng là enum, kiểm tra giá trị có hợp lệ
        EXECUTE format('SELECT $1::tenant_user_role', role) INTO role_value;
    ELSE
        RAISE EXCEPTION 'Vai trò không hợp lệ';
    END IF;
    
    -- Kiểm tra license (số lượng user có vượt quá giới hạn không)
    DECLARE
        license_check JSON;
        is_valid BOOLEAN;
    BEGIN
        SELECT * FROM public.check_tenant_license_validity(add_user_to_tenant.tenant_id) INTO license_check;
        is_valid := (license_check->>'is_valid')::BOOLEAN;
        
        IF NOT is_valid THEN
            RETURN json_build_object(
                'success', false,
                'error', 'License không hợp lệ hoặc đã hết hạn: ' || (license_check->>'reason')
            );
        END IF;
    END;
    
    -- Thêm người dùng vào tenant
    INSERT INTO public.tenant_users (
        tenant_id,
        user_id,
        role,
        is_primary_tenant,
        permissions,
        expiry_date
    )
    VALUES (
        add_user_to_tenant.tenant_id,
        user_id,
        role_value,
        add_user_to_tenant.is_primary_tenant,
        add_user_to_tenant.permissions,
        add_user_to_tenant.expiry_date
    );
    
    -- Lấy thông tin người dùng
    SELECT au.email, au.id, au.raw_user_meta_data 
    INTO user_record
    FROM auth.users au 
    WHERE au.id = user_id;
    
    RETURN json_build_object(
        'success', true,
        'message', 'Đã thêm người dùng vào tenant thành công',
        'user', json_build_object(
            'id', user_record.id,
            'email', user_record.email,
            'meta_data', user_record.raw_user_meta_data,
            'role', role,
            'is_primary_tenant', is_primary_tenant,
            'permissions', permissions
        )
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$_$;


ALTER FUNCTION "public"."add_user_to_tenant"("tenant_id" "uuid", "email" "text", "role" "text", "is_primary_tenant" boolean, "permissions" "jsonb", "expiry_date" timestamp with time zone) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_tenant_license_validity"("tenant_id" "uuid") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    license_record RECORD;
    is_license_valid BOOLEAN;
    reason TEXT;
    user_count INTEGER;
BEGIN
    -- Lấy license hiện tại (active và chưa hết hạn)
    SELECT * INTO license_record 
    FROM public.tenant_licenses
    WHERE tenant_id = check_tenant_license_validity.tenant_id
      AND is_active = true
      AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Kiểm tra xem có license hợp lệ không
    IF license_record IS NULL THEN
        is_license_valid := false;
        reason := 'Không tìm thấy license đang active hoặc license đã hết hạn';
    ELSE
        is_license_valid := true;
        
        -- Kiểm tra giới hạn người dùng nếu có
        IF license_record.user_limit IS NOT NULL THEN
            SELECT COUNT(*) INTO user_count 
            FROM public.tenant_users
            WHERE tenant_id = check_tenant_license_validity.tenant_id
              AND is_active = true;
            
            IF user_count > license_record.user_limit THEN
                is_license_valid := false;
                reason := 'Số lượng người dùng đã vượt quá giới hạn license';
            END IF;
        END IF;
    END IF;
    
    RETURN json_build_object(
        'success', true,
        'is_valid', is_license_valid,
        'reason', reason,
        'license', CASE WHEN license_record IS NULL THEN NULL ELSE row_to_json(license_record) END,
        'user_count', user_count
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$$;


ALTER FUNCTION "public"."check_tenant_license_validity"("tenant_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_location_qr_code"("p_location_id" "uuid", "p_title" character varying, "p_description" "text", "p_creator_id" "uuid", "p_organization_id" "uuid", "p_access_type" character varying DEFAULT 'public'::character varying, "p_expire_at" timestamp with time zone DEFAULT NULL::timestamp with time zone, "p_usage_limit" integer DEFAULT NULL::integer) RETURNS "uuid"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    qr_id UUID;
    short_code_value VARCHAR(20);
BEGIN
    -- Kiểm tra quyền (người tạo phải là admin hoặc thuộc tổ chức)
    PERFORM 1 
    FROM public.organization_members 
    WHERE user_id = p_creator_id AND organization_id = p_organization_id AND role IN ('admin', 'owner');
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User does not have permission to create location QR codes for this organization';
    END IF;
    
    -- Tạo mã ngắn duy nhất (kết hợp l_ + 8 ký tự ngẫu nhiên)
    short_code_value := 'l_' || substr(md5(random()::text), 1, 8);
    
    WHILE EXISTS (SELECT 1 FROM public.qr_codes WHERE short_code = short_code_value) LOOP
        short_code_value := 'l_' || substr(md5(random()::text), 1, 8);
    END LOOP;
    
    -- Tạo QR code cho địa điểm
    INSERT INTO public.qr_codes (
        qr_type,
        title,
        description,
        owner_id,
        organization_id,
        reference_id,
        metadata,
        short_code,
        is_dynamic,
        access_type,
        expire_at,
        usage_limit
    ) VALUES (
        'location',
        p_title,
        p_description,
        p_creator_id,
        p_organization_id,
        p_location_id,
        (
            SELECT jsonb_build_object(
                'location_name', name,
                'location_type', location_type,
                'address', address,
                'room_number', room_number
            )
            FROM public.locations
            WHERE id = p_location_id
        ),
        short_code_value,
        true, -- QR động để theo dõi số lần quét
        p_access_type,
        p_expire_at,
        p_usage_limit
    )
    RETURNING id INTO qr_id;
    
    RETURN qr_id;
END;

$$;


ALTER FUNCTION "public"."create_location_qr_code"("p_location_id" "uuid", "p_title" character varying, "p_description" "text", "p_creator_id" "uuid", "p_organization_id" "uuid", "p_access_type" character varying, "p_expire_at" timestamp with time zone, "p_usage_limit" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_tenant"("name" "text", "domain" "text", "contact_email" "text", "contact_phone" "text" DEFAULT NULL::"text", "address" "text" DEFAULT NULL::"text", "logo_url" "text" DEFAULT NULL::"text", "primary_color" "text" DEFAULT '#3498db'::"text", "metadata" "jsonb" DEFAULT '{}'::"jsonb") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    new_tenant_id UUID;
    new_tenant_record RECORD;
BEGIN
    -- Kiểm tra quyền của người gọi function
    IF NOT (SELECT is_super_admin FROM auth.users WHERE id = auth.uid()) THEN
        RAISE EXCEPTION 'Không có quyền tạo tenant mới';
    END IF;

    -- Thêm tenant mới
    INSERT INTO public.tenants (
        name, 
        domain, 
        contact_email, 
        contact_phone, 
        address, 
        logo_url, 
        primary_color,
        metadata
    )
    VALUES (
        name, 
        domain, 
        contact_email, 
        contact_phone, 
        address, 
        logo_url, 
        primary_color,
        metadata
    )
    RETURNING id INTO new_tenant_id;
    
    -- Lấy thông tin tenant vừa tạo
    SELECT * INTO new_tenant_record FROM public.tenants WHERE id = new_tenant_id;
    
    RETURN json_build_object(
        'success', true,
        'tenant', row_to_json(new_tenant_record)
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$$;


ALTER FUNCTION "public"."create_tenant"("name" "text", "domain" "text", "contact_email" "text", "contact_phone" "text", "address" "text", "logo_url" "text", "primary_color" "text", "metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_tenant_database_on_tenant_create"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Thêm bản ghi mới vào tenant_databases
    INSERT INTO public.tenant_databases (tenant_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."create_tenant_database_on_tenant_create"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_tenant_license"("tenant_id" "uuid", "license_type" "text", "user_limit" integer DEFAULT NULL::integer, "storage_limit" bigint DEFAULT NULL::bigint, "features" "jsonb" DEFAULT '{}'::"jsonb", "starts_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP, "expires_at" timestamp with time zone DEFAULT NULL::timestamp with time zone, "payment_status" "text" DEFAULT 'pending'::"text", "payment_method" "text" DEFAULT NULL::"text", "billing_cycle" "text" DEFAULT 'monthly'::"text", "price" numeric DEFAULT NULL::numeric, "currency" "text" DEFAULT 'USD'::"text", "metadata" "jsonb" DEFAULT '{}'::"jsonb") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
    new_license_id UUID;
    new_license_record RECORD;
    valid_license_type BOOLEAN;
BEGIN
    -- Kiểm tra quyền của người gọi function
    IF NOT (SELECT is_super_admin FROM auth.users WHERE id = auth.uid()) THEN
        RAISE EXCEPTION 'Không có quyền tạo license mới';
    END IF;

    -- Kiểm tra license_type có hợp lệ không
    SELECT exists(
        SELECT 1 FROM pg_type 
        WHERE typname = 'license_type' 
        AND typtype = 'e' 
        AND typelem = 0
    ) INTO valid_license_type;

    IF valid_license_type THEN
        -- Nếu đúng là enum, kiểm tra giá trị có hợp lệ
        EXECUTE format('SELECT $1::license_type', license_type);
    ELSE
        RAISE EXCEPTION 'Loại license không hợp lệ';
    END IF;

    -- Thêm license mới
    INSERT INTO public.tenant_licenses (
        tenant_id,
        license_type,
        user_limit,
        storage_limit,
        features,
        starts_at,
        expires_at,
        payment_status,
        payment_method,
        billing_cycle,
        price,
        currency,
        metadata
    )
    VALUES (
        tenant_id,
        license_type::license_type,
        user_limit,
        storage_limit,
        features,
        starts_at,
        expires_at,
        payment_status,
        payment_method,
        billing_cycle,
        price,
        currency,
        metadata
    )
    RETURNING id INTO new_license_id;
    
    -- Lấy thông tin license vừa tạo
    SELECT * INTO new_license_record FROM public.tenant_licenses WHERE id = new_license_id;
    
    RETURN json_build_object(
        'success', true,
        'license', row_to_json(new_license_record)
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$_$;


ALTER FUNCTION "public"."create_tenant_license"("tenant_id" "uuid", "license_type" "text", "user_limit" integer, "storage_limit" bigint, "features" "jsonb", "starts_at" timestamp with time zone, "expires_at" timestamp with time zone, "payment_status" "text", "payment_method" "text", "billing_cycle" "text", "price" numeric, "currency" "text", "metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_tenant_schema"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Tạo schema mới
    EXECUTE format('CREATE SCHEMA IF NOT EXISTS %I', NEW.schema_name);
    
    -- Cấp quyền cho các role cần thiết
    EXECUTE format('GRANT USAGE ON SCHEMA %I TO authenticated', NEW.schema_name);
    EXECUTE format('GRANT USAGE ON SCHEMA %I TO service_role', NEW.schema_name);
    
    -- Cập nhật trạng thái
    UPDATE public.tenant_databases
    SET status = 'active'
    WHERE id = NEW.id;
    
    RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."create_tenant_schema"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_user_qr_code"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Tạo short_code ngẫu nhiên
    DECLARE
        short_code_value VARCHAR(20);
    BEGIN
        -- Tạo mã ngắn duy nhất (kết hợp u_ + 8 ký tự ngẫu nhiên)
        short_code_value := 'u_' || substr(md5(random()::text), 1, 8);
        
        WHILE EXISTS (SELECT 1 FROM public.qr_codes WHERE short_code = short_code_value) LOOP
            short_code_value := 'u_' || substr(md5(random()::text), 1, 8);
        END LOOP;
        
        -- Tạo QR code cho người dùng mới
        INSERT INTO public.qr_codes (
            qr_type,
            title,
            description,
            owner_id,
            reference_id,
            metadata,
            short_code,
            is_dynamic,
            access_type
        ) VALUES (
            'user',
            COALESCE((SELECT email FROM auth.users WHERE id = NEW.id), 'User QR Code'),
            'Personal QR code for chat',
            NEW.id,
            NEW.id,
            jsonb_build_object(
                'email', COALESCE((SELECT email FROM auth.users WHERE id = NEW.id), 'unknown'),
                'created_at', now()::text
            ),
            short_code_value,
            true, -- QR động để có thể theo dõi số lần quét
            'public' -- Mặc định là public để ai cũng có thể quét
        );
    END;
    
    RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."create_user_qr_code"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."delete_tenant"("tenant_id" "uuid") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    tenant_record RECORD;
BEGIN
    -- Chỉ super admin mới có thể xóa tenant
    IF NOT (SELECT is_super_admin FROM auth.users WHERE id = auth.uid()) THEN
        RAISE EXCEPTION 'Chỉ Super Admin mới có thể xóa tenant';
    END IF;
    
    -- Lưu thông tin tenant trước khi xóa
    SELECT * INTO tenant_record FROM public.tenants WHERE id = tenant_id;
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Tenant không tồn tại'
        );
    END IF;
    
    -- Xóa schema của tenant (nếu có)
    BEGIN
        EXECUTE format('DROP SCHEMA IF EXISTS %I CASCADE', 
            (SELECT schema_name FROM public.tenant_databases WHERE tenant_id = delete_tenant.tenant_id)
        );
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Không thể xóa schema: %', SQLERRM;
    END;
    
    -- Xóa tenant (cascade sẽ xóa tất cả bản ghi liên quan)
    DELETE FROM public.tenants WHERE id = tenant_id;
    
    RETURN json_build_object(
        'success', true,
        'message', 'Tenant đã được xóa thành công',
        'deleted_tenant', row_to_json(tenant_record)
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$$;


ALTER FUNCTION "public"."delete_tenant"("tenant_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."generate_tenant_schema_name"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Tạo tên schema theo định dạng 't_' + một phần của UUID (để tránh quá dài)
  NEW.schema_name := 't_' || substring(replace(NEW.tenant_id::text, '-', ''), 1, 16);
  RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."generate_tenant_schema_name"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_current_tenant_id"() RETURNS "uuid"
    LANGUAGE "plpgsql" STABLE SECURITY DEFINER
    AS $$
DECLARE
    _tenant_id UUID;
BEGIN
    -- Lấy tenant_id từ JWT claims hoặc từ request header
    IF current_setting('request.jwt.claims', true)::jsonb ? 'tenant_id' THEN
        _tenant_id := (current_setting('request.jwt.claims', true)::jsonb->>'tenant_id')::UUID;
    ELSIF current_setting('request.headers', true)::jsonb ? 'x-tenant-id' THEN
        _tenant_id := (current_setting('request.headers', true)::jsonb->>'x-tenant-id')::UUID;
    ELSE
        -- Thử lấy từ tenant_users với is_primary_tenant = true
        SELECT tu.tenant_id INTO _tenant_id
        FROM public.tenant_users tu
        WHERE tu.user_id = auth.uid()
        AND tu.is_primary_tenant = true
        LIMIT 1;
    END IF;
    
    RETURN _tenant_id;
END;

$$;


ALTER FUNCTION "public"."get_current_tenant_id"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_current_tenant_info"() RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    tenant_id UUID;
    tenant_record RECORD;
BEGIN
    -- Lấy tenant_id hiện tại
    tenant_id := get_current_tenant_id();
    
    IF tenant_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Không có tenant nào được chọn'
        );
    END IF;
    
    -- Lấy thông tin tenant và quyền của người dùng
    SELECT t.*, tl.license_type, tl.expires_at AS license_expires_at, tu.role, tu.permissions
    INTO tenant_record
    FROM public.tenants t
    LEFT JOIN public.tenant_licenses tl ON tl.tenant_id = t.id AND tl.is_active = true
    JOIN public.tenant_users tu ON tu.tenant_id = t.id AND tu.user_id = auth.uid()
    WHERE t.id = tenant_id;
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Tenant không tồn tại hoặc bạn không có quyền truy cập'
        );
    END IF;
    
    RETURN json_build_object(
        'success', true,
        'tenant', json_build_object(
            'id', tenant_record.id,
            'name', tenant_record.name,
            'domain', tenant_record.domain,
            'logo_url', tenant_record.logo_url,
            'primary_color', tenant_record.primary_color,
            'role', tenant_record.role,
            'permissions', tenant_record.permissions,
            'license_type', tenant_record.license_type,
            'license_expires_at', tenant_record.license_expires_at
        )
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$$;


ALTER FUNCTION "public"."get_current_tenant_info"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_my_tenants"() RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    tenants_result JSON;
BEGIN
    -- Lấy danh sách tenant của người dùng với thông tin bổ sung
    SELECT json_agg(tenant_data) INTO tenants_result
    FROM (
        SELECT 
            t.id,
            t.name,
            t.domain,
            t.logo_url,
            t.primary_color,
            t.is_active,
            tu.role,
            tu.is_primary_tenant,
            tu.joined_at,
            tu.last_login_at,
            tl.license_type,
            tl.expires_at AS license_expires_at
        FROM 
            public.tenant_users tu
            JOIN public.tenants t ON tu.tenant_id = t.id
            LEFT JOIN (
                SELECT DISTINCT ON (tenant_id) *
                FROM public.tenant_licenses
                WHERE is_active = true
                ORDER BY tenant_id, created_at DESC
            ) tl ON tl.tenant_id = t.id
        WHERE 
            tu.user_id = auth.uid()
            AND tu.is_active = true
        ORDER BY 
            tu.is_primary_tenant DESC, t.name
    ) tenant_data;
    
    RETURN json_build_object(
        'success', true,
        'tenants', COALESCE(tenants_result, '[]'::json)
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$$;


ALTER FUNCTION "public"."get_my_tenants"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_tenant_details"("tenant_id" "uuid") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    tenant_info RECORD;
    license_info RECORD;
    user_count INTEGER;
    database_info RECORD;
BEGIN
    -- Kiểm tra quyền
    IF NOT (
        (SELECT is_super_admin FROM auth.users WHERE id = auth.uid()) OR 
        EXISTS (
            SELECT 1 FROM public.tenant_users 
            WHERE tenant_id = get_tenant_details.tenant_id 
            AND user_id = auth.uid()
        )
    ) THEN
        RAISE EXCEPTION 'Không có quyền xem thông tin này';
    END IF;
    
    -- Lấy thông tin tenant
    SELECT * INTO tenant_info FROM public.tenants WHERE id = tenant_id;
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Tenant không tồn tại'
        );
    END IF;
    
    -- Lấy thông tin license gần nhất
    SELECT * INTO license_info 
    FROM public.tenant_licenses 
    WHERE tenant_id = get_tenant_details.tenant_id 
    ORDER BY created_at DESC 
    LIMIT 1;
    
    -- Đếm số lượng người dùng
    SELECT COUNT(*) INTO user_count 
    FROM public.tenant_users 
    WHERE tenant_id = get_tenant_details.tenant_id 
    AND is_active = true;
    
    -- Lấy thông tin database
    SELECT * INTO database_info 
    FROM public.tenant_databases 
    WHERE tenant_id = get_tenant_details.tenant_id;
    
    RETURN json_build_object(
        'success', true,
        'tenant', row_to_json(tenant_info),
        'license', CASE WHEN license_info IS NULL THEN NULL ELSE row_to_json(license_info) END,
        'user_count', user_count,
        'database', CASE WHEN database_info IS NULL THEN NULL ELSE row_to_json(database_info) END
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$$;


ALTER FUNCTION "public"."get_tenant_details"("tenant_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_tenant_licenses"("tenant_id" "uuid") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    licenses_result JSON;
BEGIN
    -- Kiểm tra quyền
    IF NOT (
        (SELECT is_super_admin FROM auth.users WHERE id = auth.uid()) OR 
        EXISTS (
            SELECT 1 FROM public.tenant_users 
            WHERE tenant_id = get_tenant_licenses.tenant_id 
            AND user_id = auth.uid()
            AND role IN ('owner', 'admin')
        )
    ) THEN
        RAISE EXCEPTION 'Không có quyền xem licenses';
    END IF;
    
    -- Lấy tất cả licenses của tenant
    SELECT json_agg(row_to_json(l)) INTO licenses_result
    FROM (
        SELECT * 
        FROM public.tenant_licenses 
        WHERE tenant_id = get_tenant_licenses.tenant_id
        ORDER BY created_at DESC
    ) l;
    
    RETURN json_build_object(
        'success', true,
        'licenses', COALESCE(licenses_result, '[]'::json)
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$$;


ALTER FUNCTION "public"."get_tenant_licenses"("tenant_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_tenant_users"("tenant_id" "uuid") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    users_result JSON;
BEGIN
    -- Kiểm tra quyền
    IF NOT (
        (SELECT is_super_admin FROM auth.users WHERE id = auth.uid()) OR 
        EXISTS (
            SELECT 1 FROM public.tenant_users 
            WHERE tenant_id = get_tenant_users.tenant_id 
            AND user_id = auth.uid()
        )
    ) THEN
        RAISE EXCEPTION 'Không có quyền xem danh sách người dùng trong tenant';
    END IF;
    
    -- Lấy danh sách người dùng với thông tin từ auth.users
    SELECT json_agg(user_data) INTO users_result
    FROM (
        SELECT 
            tu.user_id,
            tu.role,
            tu.is_primary_tenant,
            tu.is_active,
            tu.permissions,
            tu.joined_at,
            tu.last_login_at,
            tu.expiry_date,
            au.email,
            au.raw_user_meta_data
        FROM 
            public.tenant_users tu
            JOIN auth.users au ON tu.user_id = au.id
        WHERE 
            tu.tenant_id = get_tenant_users.tenant_id
        ORDER BY 
            tu.role, au.email
    ) user_data;
    
    RETURN json_build_object(
        'success', true,
        'users', COALESCE(users_result, '[]'::json)
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$$;


ALTER FUNCTION "public"."get_tenant_users"("tenant_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_unread_message_count"("p_user_id" "uuid", "p_room_id" "uuid") RETURNS integer
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    unread_count INTEGER;
BEGIN
    SELECT COUNT(*)
    INTO unread_count
    FROM public.chat_messages m
    WHERE m.chat_room_id = p_room_id
    AND m.sender_id != p_user_id
    AND NOT EXISTS (
        SELECT 1
        FROM public.message_read_status r
        WHERE r.message_id = m.id
        AND r.user_id = p_user_id
    );
    
    RETURN unread_count;
END;

$$;


ALTER FUNCTION "public"."get_unread_message_count"("p_user_id" "uuid", "p_room_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Kiểm tra nếu có dữ liệu tenant_id trong raw_app_meta_data
    DECLARE
        tenant_id UUID;
        tenant_role TEXT;
    BEGIN
        IF NEW.raw_app_meta_data IS NOT NULL AND NEW.raw_app_meta_data ? 'tenant_id' THEN
            tenant_id := (NEW.raw_app_meta_data->>'tenant_id')::UUID;
            tenant_role := COALESCE(NEW.raw_app_meta_data->>'role', 'member');
            
            -- Thêm người dùng vào tenant
            INSERT INTO public.tenant_users (
                tenant_id,
                user_id,
                role,
                is_primary_tenant
            )
            VALUES (
                tenant_id,
                NEW.id,
                tenant_role::tenant_user_role,
                TRUE
            );
        END IF;
    END;
    
    RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_tenant_permission"("permission_name" "text") RETURNS boolean
    LANGUAGE "plpgsql" STABLE SECURITY DEFINER
    AS $$
DECLARE
    current_tenant_id UUID;
    user_role tenant_user_role;
    user_permissions JSONB;
    has_perm BOOLEAN;
BEGIN
    -- Lấy tenant_id hiện tại
    current_tenant_id := get_current_tenant_id();
    
    IF current_tenant_id IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Lấy vai trò và quyền của người dùng
    SELECT tu.role, tu.permissions
    INTO user_role, user_permissions
    FROM public.tenant_users tu
    WHERE tu.tenant_id = current_tenant_id
    AND tu.user_id = auth.uid();
    
    -- Kiểm tra vai trò
    IF user_role = 'owner' OR user_role = 'admin' THEN
        RETURN TRUE;
    END IF;
    
    -- Kiểm tra quyền cụ thể
    IF user_permissions IS NOT NULL AND user_permissions ? permission_name THEN
        has_perm := (user_permissions->>(permission_name))::BOOLEAN;
        RETURN has_perm;
    END IF;
    
    RETURN FALSE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;

$$;


ALTER FUNCTION "public"."has_tenant_permission"("permission_name" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."manage_automatic_translations"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Thực hiện quá trình dịch tự động khi có tin nhắn mới
  INSERT INTO public.message_translations (
    message_id,
    language,
    translated_content,
    translation_provider,
    is_automatic
  )
  -- Đây là một ví dụ giả định, bạn sẽ thay thế bằng logic dịch thực tế
  SELECT 
    NEW.id,
    lang.code,
    NEW.content, -- Thay bằng logic dịch thực tế hoặc gọi API dịch
    'auto',
    true
  FROM (
    SELECT DISTINCT preferred_language AS code
    FROM public.chat_participants
    WHERE chat_room_id = NEW.chat_room_id
      AND preferred_language != NEW.original_language
      AND is_active = true
  ) AS lang;
  
  -- Đánh dấu tin nhắn đã được xử lý dịch thuật
  UPDATE public.chat_messages
  SET is_translated = (
    SELECT COUNT(*) > 0 
    FROM public.message_translations 
    WHERE message_id = NEW.id
  )
  WHERE id = NEW.id;
  
  RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."manage_automatic_translations"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_message_as_read_for_sender"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    -- Tự động đánh dấu tin nhắn đã đọc cho người gửi
    INSERT INTO public.message_read_status (
        message_id,
        user_id,
        participant_id,
        read_at
    ) VALUES (
        NEW.id,
        NEW.sender_id,
        NEW.participant_id,
        NEW.sent_at
    ) ON CONFLICT (message_id, user_id) DO NOTHING;
    
    RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."mark_message_as_read_for_sender"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."process_qr_code_scan"("p_qr_code_id" "uuid", "p_user_id" "uuid" DEFAULT NULL::"uuid", "p_anonymous_id" character varying DEFAULT NULL::character varying, "p_ip_address" character varying DEFAULT NULL::character varying, "p_user_agent" "text" DEFAULT NULL::"text", "p_device_info" "jsonb" DEFAULT NULL::"jsonb", "p_location_info" "jsonb" DEFAULT NULL::"jsonb") RETURNS "jsonb"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    qr_record public.qr_codes;
    scan_id UUID;
    result_json JSONB;
    chat_room_id UUID;
BEGIN
    -- Lấy thông tin QR code
    SELECT * INTO qr_record
    FROM public.qr_codes
    WHERE id = p_qr_code_id;
    
    -- Kiểm tra QR code có tồn tại không
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'QR code not found',
            'error_code', 'QR_NOT_FOUND'
        );
    END IF;
    
    -- Kiểm tra QR code có còn active không
    IF NOT qr_record.is_active THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'QR code is inactive',
            'error_code', 'QR_INACTIVE'
        );
    END IF;
    
    -- Kiểm tra hạn sử dụng
    IF qr_record.expire_at IS NOT NULL AND qr_record.expire_at < NOW() THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'QR code has expired',
            'error_code', 'QR_EXPIRED'
        );
    END IF;
    
    -- Kiểm tra giới hạn lượt quét
    IF qr_record.usage_limit IS NOT NULL AND qr_record.usage_count >= qr_record.usage_limit THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'QR code usage limit exceeded',
            'error_code', 'QR_LIMIT_EXCEEDED'
        );
    END IF;
    
    -- Ghi lại lượt quét
    INSERT INTO public.qr_code_scans (
        qr_code_id,
        scanned_by_user_id,
        scanned_by_anonymous_id,
        ip_address,
        user_agent,
        device_info,
        location_info
    ) VALUES (
        p_qr_code_id,
        p_user_id,
        p_anonymous_id,
        p_ip_address,
        p_user_agent,
        COALESCE(p_device_info, '{}'::jsonb),
        COALESCE(p_location_info, '{}'::jsonb)
    )
    RETURNING id INTO scan_id;
    
    -- Tăng số lượt quét
    UPDATE public.qr_codes
    SET usage_count = usage_count + 1
    WHERE id = p_qr_code_id;
    
    -- Xử lý theo loại QR code
    CASE qr_record.qr_type
        -- QR code người dùng - tạo phòng chat mới
        WHEN 'user' THEN
            -- Chỉ tạo phòng chat khi người quét đã đăng nhập
            IF p_user_id IS NOT NULL THEN
                -- Tìm ID của chủ QR code
                DECLARE
                    qr_owner_id UUID;
                BEGIN
                    qr_owner_id := qr_record.reference_id;
                    
                    -- Tạo phòng chat mới giữa người quét và chủ QR
                    INSERT INTO public.chat_rooms (
                        name,
                        room_type,
                        created_by,
                        organization_id,
                        metadata
                    ) VALUES (
                        'Direct chat via QR',
                        'direct',
                        p_user_id,
                        qr_record.organization_id,
                        jsonb_build_object(
                            'created_via', 'qr_scan',
                            'qr_code_id', p_qr_code_id,
                            'scan_id', scan_id
                        )
                    )
                    RETURNING id INTO chat_room_id;
                    
                    -- Thêm người quét làm participant
                    INSERT INTO public.chat_participants (
                        chat_room_id,
                        user_id,
                        participant_role,
                        is_active
                    ) VALUES (
                        chat_room_id,
                        p_user_id,
                        'member',
                        true
                    );
                    
                    -- Thêm chủ QR làm participant
                    INSERT INTO public.chat_participants (
                        chat_room_id,
                        user_id,
                        participant_role,
                        is_active
                    ) VALUES (
                        chat_room_id,
                        qr_owner_id,
                        'member',
                        true
                    );
                    
                    -- Cập nhật trạng thái quét
                    UPDATE public.qr_code_scans
                    SET 
                        action_taken = 'chat_started',
                        action_result = 'success'
                    WHERE id = scan_id;
                    
                    result_json := jsonb_build_object(
                        'success', true,
                        'message', 'Chat room created successfully',
                        'qr_type', qr_record.qr_type,
                        'action', 'chat_created',
                        'chat_room_id', chat_room_id,
                        'scan_id', scan_id
                    );
                END;
            ELSE
                -- Người dùng chưa đăng nhập
                UPDATE public.qr_code_scans
                SET 
                    action_taken = 'login_required',
                    action_result = 'pending'
                WHERE id = scan_id;
                
                result_json := jsonb_build_object(
                    'success', true,
                    'message', 'Login required to chat with this user',
                    'qr_type', qr_record.qr_type,
                    'action', 'login_required',
                    'scan_id', scan_id
                );
            END IF;
            
        -- QR code địa điểm - kết nối với dịch vụ tại địa điểm
        WHEN 'location' THEN
            DECLARE
                location_id UUID;
                location_name VARCHAR(255);
                service_account_id UUID;
            BEGIN
                -- Lấy thông tin địa điểm
                location_id := qr_record.reference_id;
                
                SELECT name INTO location_name 
                FROM public.locations 
                WHERE id = location_id;
                
                -- Tìm tài khoản dịch vụ phụ trách địa điểm này
                SELECT user_id INTO service_account_id
                FROM public.location_services
                WHERE location_id = qr_record.reference_id
                AND is_active = true
                LIMIT 1;
                
                -- Nếu người dùng đã đăng nhập
                IF p_user_id IS NOT NULL THEN
                    -- Tạo phòng chat mới với dịch vụ
                    INSERT INTO public.chat_rooms (
                        name,
                        room_type,
                        created_by,
                        organization_id,
                        metadata
                    ) VALUES (
                        'Support for ' || location_name,
                        'support',
                        p_user_id,
                        qr_record.organization_id,
                        jsonb_build_object(
                            'created_via', 'location_qr_scan',
                            'qr_code_id', p_qr_code_id,
                            'scan_id', scan_id,
                            'location_id', location_id,
                            'location_name', location_name
                        )
                    )
                    RETURNING id INTO chat_room_id;
                    
                    -- Thêm người quét làm participant
                    INSERT INTO public.chat_participants (
                        chat_room_id,
                        user_id,
                        participant_role,
                        is_active
                    ) VALUES (
                        chat_room_id,
                        p_user_id,
                        'member',
                        true
                    );
                    
                    -- Thêm tài khoản dịch vụ làm participant
                    IF service_account_id IS NOT NULL THEN
                        INSERT INTO public.chat_participants (
                            chat_room_id,
                            user_id,
                            participant_role,
                            is_active
                        ) VALUES (
                            chat_room_id,
                            service_account_id,
                            'support_agent',
                            true
                        );
                    END IF;
                    
                    -- Cập nhật trạng thái quét
                    UPDATE public.qr_code_scans
                    SET 
                        action_taken = 'location_chat_started',
                        action_result = 'success'
                    WHERE id = scan_id;
                    
                    result_json := jsonb_build_object(
                        'success', true,
                        'message', 'Location support chat created successfully',
                        'qr_type', qr_record.qr_type,
                        'action', 'location_chat_created',
                        'chat_room_id', chat_room_id,
                        'location_name', location_name,
                        'scan_id', scan_id
                    );
                ELSE
                    -- Người dùng chưa đăng nhập
                    UPDATE public.qr_code_scans
                    SET 
                        action_taken = 'login_required',
                        action_result = 'pending'
                    WHERE id = scan_id;
                    
                    result_json := jsonb_build_object(
                        'success', true,
                        'message', 'Login required for location services',
                        'qr_type', qr_record.qr_type,
                        'action', 'login_required',
                        'location_name', location_name,
                        'scan_id', scan_id
                    );
                END IF;
            END;
            
        -- Các loại QR khác nếu có
        ELSE
            result_json := jsonb_build_object(
                'success', true,
                'message', 'QR code scanned successfully',
                'qr_type', qr_record.qr_type,
                'action', 'generic_scan',
                'scan_id', scan_id
            );
    END CASE;
    
    RETURN result_json;
    
EXCEPTION WHEN OTHERS THEN
    -- Ghi lại lỗi nếu có
    IF scan_id IS NOT NULL THEN
        UPDATE public.qr_code_scans
        SET 
            action_taken = 'error',
            action_result = 'failure'
        WHERE id = scan_id;
    END IF;
    
    RETURN jsonb_build_object(
        'success', false,
        'message', 'Error processing QR code scan: ' || SQLERRM,
        'error_code', 'PROCESSING_ERROR'
    );
END;

$$;


ALTER FUNCTION "public"."process_qr_code_scan"("p_qr_code_id" "uuid", "p_user_id" "uuid", "p_anonymous_id" character varying, "p_ip_address" character varying, "p_user_agent" "text", "p_device_info" "jsonb", "p_location_info" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."remove_user_from_tenant"("tenant_id" "uuid", "user_id" "uuid") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    tenant_user_record RECORD;
    tenant_owner_count INTEGER;
BEGIN
    -- Kiểm tra quyền
    IF NOT (
        (SELECT is_super_admin FROM auth.users WHERE id = auth.uid()) OR 
        EXISTS (
            SELECT 1 FROM public.tenant_users 
            WHERE tenant_id = remove_user_from_tenant.tenant_id 
            AND user_id = auth.uid() 
            AND role IN ('owner', 'admin')
        ) OR
        auth.uid() = remove_user_from_tenant.user_id -- Người dùng có thể tự rời khỏi tenant
    ) THEN
        RAISE EXCEPTION 'Không có quyền xóa người dùng khỏi tenant';
    END IF;
    
    -- Kiểm tra xem người dùng có trong tenant không
    SELECT * INTO tenant_user_record 
    FROM public.tenant_users 
    WHERE tenant_id = remove_user_from_tenant.tenant_id 
    AND user_id = remove_user_from_tenant.user_id;
    
    IF tenant_user_record IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Người dùng không thuộc tenant này'
        );
    END IF;
    
    -- Không cho phép xóa owner cuối cùng
    IF tenant_user_record.role = 'owner' THEN
        SELECT COUNT(*) INTO tenant_owner_count
        FROM public.tenant_users
        WHERE tenant_id = remove_user_from_tenant.tenant_id
        AND role = 'owner';
        
        IF tenant_owner_count <= 1 THEN
            RETURN json_build_object(
                'success', false,
                'error', 'Không thể xóa owner cuối cùng của tenant'
            );
        END IF;
    END IF;
    
    -- Xóa người dùng khỏi tenant
    DELETE FROM public.tenant_users 
    WHERE tenant_id = remove_user_from_tenant.tenant_id 
    AND user_id = remove_user_from_tenant.user_id;
    
    RETURN json_build_object(
        'success', true,
        'message', 'Đã xóa người dùng khỏi tenant thành công'
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$$;


ALTER FUNCTION "public"."remove_user_from_tenant"("tenant_id" "uuid", "user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_current_tenant"("tenant_id" "uuid") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    tenant_record RECORD;
    has_access BOOLEAN;
BEGIN
    -- Kiểm tra xem người dùng có quyền truy cập tenant này không
    SELECT EXISTS (
        SELECT 1 FROM public.tenant_users
        WHERE tenant_id = set_current_tenant.tenant_id
        AND user_id = auth.uid()
        AND is_active = TRUE
    ) INTO has_access;
    
    IF NOT has_access THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Bạn không có quyền truy cập tenant này'
        );
    END IF;
    
    -- Lấy thông tin tenant
    SELECT t.*, tl.license_type, tl.expires_at AS license_expires_at, tu.role
    INTO tenant_record
    FROM public.tenants t
    LEFT JOIN public.tenant_licenses tl ON tl.tenant_id = t.id AND tl.is_active = true
    JOIN public.tenant_users tu ON tu.tenant_id = t.id AND tu.user_id = auth.uid()
    WHERE t.id = set_current_tenant.tenant_id;
    
    IF NOT FOUND OR NOT tenant_record.is_active THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Tenant không tồn tại hoặc không hoạt động'
        );
    END IF;
    
    -- Cập nhật last_login_at cho tenant user
    UPDATE public.tenant_users
    SET last_login_at = CURRENT_TIMESTAMP
    WHERE tenant_id = set_current_tenant.tenant_id
    AND user_id = auth.uid();
    
    RETURN json_build_object(
        'success', true,
        'message', 'Đã chuyển sang tenant: ' || tenant_record.name,
        'tenant', json_build_object(
            'id', tenant_record.id,
            'name', tenant_record.name,
            'domain', tenant_record.domain,
            'logo_url', tenant_record.logo_url,
            'primary_color', tenant_record.primary_color,
            'role', tenant_record.role,
            'license_type', tenant_record.license_type,
            'license_expires_at', tenant_record.license_expires_at
        )
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$$;


ALTER FUNCTION "public"."set_current_tenant"("tenant_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_organization_id_for_chat_room"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    org_id uuid;
BEGIN
    -- Nếu organization_id đã được cung cấp, giữ nguyên
    IF NEW.organization_id IS NOT NULL THEN
        RETURN NEW;
    END IF;
    
    -- Nếu hotel_id được cung cấp trong metadata, lấy organization_id từ đó
    IF NEW.metadata->>'hotel_id' IS NOT NULL THEN
        SELECT organization_id INTO org_id
        FROM public.hotels
        WHERE id = (NEW.metadata->>'hotel_id')::uuid;
        
        IF org_id IS NOT NULL THEN
            NEW.organization_id := org_id;
            -- Cũng cập nhật trong metadata để dễ truy cập
            NEW.metadata := NEW.metadata || jsonb_build_object('organization_id', org_id);
        END IF;
    END IF;
    
    RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."set_organization_id_for_chat_room"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_tenant_id_for_chat_room"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF NEW.tenant_id IS NULL THEN
        NEW.tenant_id := get_current_tenant_id();
    END IF;
    RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."set_tenant_id_for_chat_room"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_tenant_id_for_hotel"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF NEW.tenant_id IS NULL THEN
        NEW.tenant_id := get_current_tenant_id();
    END IF;
    RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."set_tenant_id_for_hotel"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_tenant_id_for_organization"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF NEW.tenant_id IS NULL THEN
        NEW.tenant_id := get_current_tenant_id();
    END IF;
    RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."set_tenant_id_for_organization"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."set_tenant_id_for_qr_code"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF NEW.tenant_id IS NULL THEN
        NEW.tenant_id := get_current_tenant_id();
    END IF;
    RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."set_tenant_id_for_qr_code"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."switch_tenant"("new_tenant_id" "uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    user_has_access BOOLEAN;
    tenant_info JSONB;
BEGIN
    -- Kiểm tra xem người dùng có quyền truy cập tenant này không
    SELECT EXISTS (
        SELECT 1
        FROM public.tenant_users
        WHERE tenant_id = new_tenant_id
        AND user_id = auth.uid()
        AND is_active = TRUE
    ) INTO user_has_access;
    
    IF NOT user_has_access THEN
        RAISE EXCEPTION 'Không có quyền truy cập tenant này';
    END IF;
    
    -- Cập nhật JWT claim
    -- Lưu ý: Cần triển khai phía client để cập nhật JWT sau khi gọi function này
    
    -- Trả về thông tin tenant
    SELECT jsonb_build_object(
        'tenant_id', t.id,
        'tenant_name', t.name,
        'tenant_domain', t.domain,
        'logo_url', t.logo_url,
        'role', tu.role
    )
    INTO tenant_info
    FROM public.tenants t
    JOIN public.tenant_users tu ON t.id = tu.tenant_id
    WHERE t.id = new_tenant_id AND tu.user_id = auth.uid();
    
    RETURN tenant_info;
END;

$$;


ALTER FUNCTION "public"."switch_tenant"("new_tenant_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_room_last_activity"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  UPDATE public.chat_rooms 
  SET last_activity_at = NEW.sent_at
  WHERE id = NEW.chat_room_id;
  RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."update_room_last_activity"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_tenant"("tenant_id" "uuid", "name" "text" DEFAULT NULL::"text", "domain" "text" DEFAULT NULL::"text", "contact_email" "text" DEFAULT NULL::"text", "contact_phone" "text" DEFAULT NULL::"text", "address" "text" DEFAULT NULL::"text", "logo_url" "text" DEFAULT NULL::"text", "primary_color" "text" DEFAULT NULL::"text", "is_active" boolean DEFAULT NULL::boolean, "metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    updated_tenant_record RECORD;
BEGIN
    -- Kiểm tra quyền
    IF NOT (
        (SELECT is_super_admin FROM auth.users WHERE id = auth.uid()) OR 
        EXISTS (
            SELECT 1 FROM public.tenant_users 
            WHERE tenant_id = update_tenant.tenant_id 
            AND user_id = auth.uid() 
            AND role IN ('owner', 'admin')
        )
    ) THEN
        RAISE EXCEPTION 'Không có quyền cập nhật tenant';
    END IF;
    
    -- Cập nhật tenant
    UPDATE public.tenants
    SET 
        name = COALESCE(update_tenant.name, public.tenants.name),
        domain = COALESCE(update_tenant.domain, public.tenants.domain),
        contact_email = COALESCE(update_tenant.contact_email, public.tenants.contact_email),
        contact_phone = COALESCE(update_tenant.contact_phone, public.tenants.contact_phone),
        address = COALESCE(update_tenant.address, public.tenants.address),
        logo_url = COALESCE(update_tenant.logo_url, public.tenants.logo_url),
        primary_color = COALESCE(update_tenant.primary_color, public.tenants.primary_color),
        is_active = COALESCE(update_tenant.is_active, public.tenants.is_active),
        metadata = COALESCE(update_tenant.metadata, public.tenants.metadata),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = update_tenant.tenant_id;
    
    -- Kiểm tra xem có bản ghi nào được cập nhật không
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Tenant không tồn tại'
        );
    END IF;
    
    -- Lấy thông tin tenant vừa cập nhật
    SELECT * INTO updated_tenant_record FROM public.tenants WHERE id = tenant_id;
    
    RETURN json_build_object(
        'success', true,
        'tenant', row_to_json(updated_tenant_record)
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$$;


ALTER FUNCTION "public"."update_tenant"("tenant_id" "uuid", "name" "text", "domain" "text", "contact_email" "text", "contact_phone" "text", "address" "text", "logo_url" "text", "primary_color" "text", "is_active" boolean, "metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_tenant_databases_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."update_tenant_databases_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_tenant_license"("license_id" "uuid", "license_type" "text" DEFAULT NULL::"text", "user_limit" integer DEFAULT NULL::integer, "storage_limit" bigint DEFAULT NULL::bigint, "features" "jsonb" DEFAULT NULL::"jsonb", "expires_at" timestamp with time zone DEFAULT NULL::timestamp with time zone, "is_active" boolean DEFAULT NULL::boolean, "payment_status" "text" DEFAULT NULL::"text", "payment_method" "text" DEFAULT NULL::"text", "billing_cycle" "text" DEFAULT NULL::"text", "price" numeric DEFAULT NULL::numeric, "currency" "text" DEFAULT NULL::"text", "last_payment_date" timestamp with time zone DEFAULT NULL::timestamp with time zone, "metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
    updated_license_record RECORD;
    tenant_id_from_license UUID;
    valid_license_type BOOLEAN;
    license_type_value license_type;
BEGIN
    -- Lấy tenant_id từ license
    SELECT tenant_id INTO tenant_id_from_license
    FROM public.tenant_licenses
    WHERE id = license_id;
    
    -- Kiểm tra quyền
    IF NOT (
        (SELECT is_super_admin FROM auth.users WHERE id = auth.uid()) OR 
        EXISTS (
            SELECT 1 FROM public.tenant_users 
            WHERE tenant_id = tenant_id_from_license
            AND user_id = auth.uid() 
            AND role IN ('owner', 'admin')
        )
    ) THEN
        RAISE EXCEPTION 'Không có quyền cập nhật license';
    END IF;
    
    -- Kiểm tra và chuyển đổi license_type nếu được cung cấp
    IF license_type IS NOT NULL THEN
        SELECT exists(
            SELECT 1 FROM pg_type 
            WHERE typname = 'license_type' 
            AND typtype = 'e' 
            AND typelem = 0
        ) INTO valid_license_type;

        IF valid_license_type THEN
            -- Nếu đúng là enum, kiểm tra giá trị có hợp lệ
            EXECUTE format('SELECT $1::license_type', license_type) INTO license_type_value;
        ELSE
            RAISE EXCEPTION 'Loại license không hợp lệ';
        END IF;
    END IF;
    
    -- Cập nhật license
    UPDATE public.tenant_licenses
    SET 
        license_type = COALESCE(license_type_value, public.tenant_licenses.license_type),
        user_limit = COALESCE(update_tenant_license.user_limit, public.tenant_licenses.user_limit),
        storage_limit = COALESCE(update_tenant_license.storage_limit, public.tenant_licenses.storage_limit),
        features = COALESCE(update_tenant_license.features, public.tenant_licenses.features),
        expires_at = COALESCE(update_tenant_license.expires_at, public.tenant_licenses.expires_at),
        is_active = COALESCE(update_tenant_license.is_active, public.tenant_licenses.is_active),
        payment_status = COALESCE(update_tenant_license.payment_status, public.tenant_licenses.payment_status),
        payment_method = COALESCE(update_tenant_license.payment_method, public.tenant_licenses.payment_method),
        billing_cycle = COALESCE(update_tenant_license.billing_cycle, public.tenant_licenses.billing_cycle),
        price = COALESCE(update_tenant_license.price, public.tenant_licenses.price),
        currency = COALESCE(update_tenant_license.currency, public.tenant_licenses.currency),
        last_payment_date = COALESCE(update_tenant_license.last_payment_date, public.tenant_licenses.last_payment_date),
        metadata = COALESCE(update_tenant_license.metadata, public.tenant_licenses.metadata),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = update_tenant_license.license_id;
    
    -- Kiểm tra xem có bản ghi nào được cập nhật không
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'License không tồn tại'
        );
    END IF;
    
    -- Lấy thông tin license vừa cập nhật
    SELECT * INTO updated_license_record FROM public.tenant_licenses WHERE id = license_id;
    
    RETURN json_build_object(
        'success', true,
        'license', row_to_json(updated_license_record)
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$_$;


ALTER FUNCTION "public"."update_tenant_license"("license_id" "uuid", "license_type" "text", "user_limit" integer, "storage_limit" bigint, "features" "jsonb", "expires_at" timestamp with time zone, "is_active" boolean, "payment_status" "text", "payment_method" "text", "billing_cycle" "text", "price" numeric, "currency" "text", "last_payment_date" timestamp with time zone, "metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_tenant_licenses_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."update_tenant_licenses_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_tenant_user_login_time"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  UPDATE public.tenant_users
  SET last_login_at = CURRENT_TIMESTAMP
  WHERE user_id = NEW.user_id AND tenant_id = (
    SELECT tenant_id FROM public.tenant_users
    WHERE user_id = NEW.user_id AND is_primary_tenant = true
  );
  RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."update_tenant_user_login_time"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_tenant_users_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."update_tenant_users_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_tenants_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."update_tenants_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_timestamp"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."update_timestamp"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."user_presence" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "status" character varying(20) DEFAULT 'offline'::character varying NOT NULL,
    "last_active_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "current_room_id" "uuid",
    "device_info" "jsonb" DEFAULT '{}'::"jsonb",
    "ip_address" character varying(45),
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."user_presence" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_user_presence"("p_user_id" "uuid", "p_status" character varying, "p_room_id" "uuid" DEFAULT NULL::"uuid", "p_device_info" "jsonb" DEFAULT NULL::"jsonb", "p_ip_address" character varying DEFAULT NULL::character varying) RETURNS "public"."user_presence"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    presence_record public.user_presence;
BEGIN
    INSERT INTO public.user_presence (
        user_id,
        status,
        last_active_at,
        current_room_id,
        device_info,
        ip_address
    ) VALUES (
        p_user_id,
        p_status,
        now(),
        p_room_id,
        COALESCE(p_device_info, '{}'::jsonb),
        p_ip_address
    )
    ON CONFLICT (user_id) DO UPDATE SET
        status = p_status,
        last_active_at = now(),
        current_room_id = COALESCE(p_room_id, public.user_presence.current_room_id),
        device_info = CASE 
            WHEN p_device_info IS NOT NULL THEN p_device_info 
            ELSE public.user_presence.device_info 
        END,
        ip_address = COALESCE(p_ip_address, public.user_presence.ip_address)
    RETURNING * INTO presence_record;
    
    RETURN presence_record;
END;

$$;


ALTER FUNCTION "public"."update_user_presence"("p_user_id" "uuid", "p_status" character varying, "p_room_id" "uuid", "p_device_info" "jsonb", "p_ip_address" character varying) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_user_role_in_tenant"("tenant_id" "uuid", "user_id" "uuid", "role" "text", "permissions" "jsonb" DEFAULT NULL::"jsonb", "is_primary_tenant" boolean DEFAULT NULL::boolean, "is_active" boolean DEFAULT NULL::boolean, "expiry_date" timestamp with time zone DEFAULT NULL::timestamp with time zone) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
    tenant_user_record RECORD;
    updated_record RECORD;
    valid_role BOOLEAN;
    role_value tenant_user_role;
    tenant_owner_count INTEGER;
BEGIN
    -- Kiểm tra quyền
    IF NOT (
        (SELECT is_super_admin FROM auth.users WHERE id = auth.uid()) OR 
        EXISTS (
            SELECT 1 FROM public.tenant_users 
            WHERE tenant_id = update_user_role_in_tenant.tenant_id 
            AND user_id = auth.uid() 
            AND role IN ('owner', 'admin')
        )
    ) THEN
        RAISE EXCEPTION 'Không có quyền cập nhật vai trò người dùng';
    END IF;
    
    -- Kiểm tra xem người dùng có trong tenant không
    SELECT * INTO tenant_user_record 
    FROM public.tenant_users 
    WHERE tenant_id = update_user_role_in_tenant.tenant_id 
    AND user_id = update_user_role_in_tenant.user_id;
    
    IF tenant_user_record IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Người dùng không thuộc tenant này'
        );
    END IF;
    
    -- Kiểm tra và chuyển đổi role nếu được cung cấp
    IF role IS NOT NULL THEN
        SELECT exists(
            SELECT 1 FROM pg_type 
            WHERE typname = 'tenant_user_role' 
            AND typtype = 'e' 
            AND typelem = 0
        ) INTO valid_role;

        IF valid_role THEN
            -- Nếu đúng là enum, kiểm tra giá trị có hợp lệ
            EXECUTE format('SELECT $1::tenant_user_role', role) INTO role_value;
        ELSE
            RAISE EXCEPTION 'Vai trò không hợp lệ';
        END IF;
        
        -- Nếu đang thay đổi owner thành role khác, kiểm tra số lượng owner còn lại
        IF tenant_user_record.role = 'owner' AND role_value != 'owner' THEN
            SELECT COUNT(*) INTO tenant_owner_count
            FROM public.tenant_users
            WHERE tenant_id = update_user_role_in_tenant.tenant_id
            AND role = 'owner';
            
            IF tenant_owner_count <= 1 THEN
                RETURN json_build_object(
                    'success', false,
                    'error', 'Không thể thay đổi vai trò của owner cuối cùng'
                );
            END IF;
        END IF;
    END IF;
    
    -- Cập nhật thông tin người dùng trong tenant
    UPDATE public.tenant_users
    SET 
        role = COALESCE(role_value, public.tenant_users.role),
        permissions = COALESCE(update_user_role_in_tenant.permissions, public.tenant_users.permissions),
        is_primary_tenant = COALESCE(update_user_role_in_tenant.is_primary_tenant, public.tenant_users.is_primary_tenant),
        is_active = COALESCE(update_user_role_in_tenant.is_active, public.tenant_users.is_active),
        expiry_date = COALESCE(update_user_role_in_tenant.expiry_date, public.tenant_users.expiry_date),
        updated_at = CURRENT_TIMESTAMP
    WHERE tenant_id = update_user_role_in_tenant.tenant_id
    AND user_id = update_user_role_in_tenant.user_id
    RETURNING * INTO updated_record;
    
    -- Nếu đã đặt is_primary_tenant = true, hãy cập nhật các tenant khác của người dùng này
    IF update_user_role_in_tenant.is_primary_tenant = true THEN
        UPDATE public.tenant_users
        SET is_primary_tenant = false
        WHERE user_id = update_user_role_in_tenant.user_id
        AND tenant_id != update_user_role_in_tenant.tenant_id;
    END IF;
    
    RETURN json_build_object(
        'success', true,
        'message', 'Đã cập nhật vai trò người dùng thành công',
        'tenant_user', row_to_json(updated_record)
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;

$_$;


ALTER FUNCTION "public"."update_user_role_in_tenant"("tenant_id" "uuid", "user_id" "uuid", "role" "text", "permissions" "jsonb", "is_primary_tenant" boolean, "is_active" boolean, "expiry_date" timestamp with time zone) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_users_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;

$$;


ALTER FUNCTION "public"."update_users_updated_at"() OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."chat_messages" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "chat_room_id" "uuid" NOT NULL,
    "sender_id" "uuid",
    "temporary_sender_id" "uuid",
    "content" "text" NOT NULL,
    "original_language" character varying(10),
    "sent_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "is_translated" boolean DEFAULT false,
    "metadata" "jsonb",
    "participant_id" "uuid",
    "content_type" character varying(50) DEFAULT 'text'::character varying,
    "reply_to" "uuid",
    "updated_at" timestamp with time zone,
    "deleted_at" timestamp with time zone,
    "sender_id_orphaned" boolean DEFAULT false,
    "read_at" timestamp with time zone,
    CONSTRAINT "chat_messages_check" CHECK ((("sender_id" IS NOT NULL) OR ("temporary_sender_id" IS NOT NULL)))
);


ALTER TABLE "public"."chat_messages" OWNER TO "postgres";


COMMENT ON COLUMN "public"."chat_messages"."metadata" IS 'Lưu trữ thông tin bổ sung như reactions, read receipts';



COMMENT ON COLUMN "public"."chat_messages"."participant_id" IS 'Liên kết với bản ghi participant thay vì trực tiếp đến user_id';



COMMENT ON COLUMN "public"."chat_messages"."content_type" IS 'Loại nội dung tin nhắn: text, image, file, audio, video, location';



COMMENT ON COLUMN "public"."chat_messages"."reply_to" IS 'ID của tin nhắn được trả lời';



CREATE TABLE IF NOT EXISTS "public"."chat_participants" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "chat_room_id" "uuid" NOT NULL,
    "user_id" "uuid",
    "temporary_user_id" "uuid",
    "joined_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "left_at" timestamp with time zone,
    "is_active" boolean DEFAULT true,
    "participant_role" character varying(50) DEFAULT 'member'::character varying NOT NULL,
    "preferred_language" character varying(10) DEFAULT 'en'::character varying,
    "last_read_at" timestamp with time zone DEFAULT "now"(),
    "is_muted" boolean DEFAULT false,
    "notification_preferences" "jsonb" DEFAULT '{"all": true}'::"jsonb",
    "display_name" character varying(255),
    "user_id_orphaned" boolean DEFAULT false,
    CONSTRAINT "chat_participants_check" CHECK ((("user_id" IS NOT NULL) OR ("temporary_user_id" IS NOT NULL)))
);


ALTER TABLE "public"."chat_participants" OWNER TO "postgres";


COMMENT ON COLUMN "public"."chat_participants"."last_read_at" IS 'Thời điểm cuối cùng người dùng đọc tin nhắn trong phòng';



COMMENT ON COLUMN "public"."chat_participants"."is_muted" IS 'Người dùng có tắt thông báo từ phòng chat này không';



COMMENT ON COLUMN "public"."chat_participants"."notification_preferences" IS 'Tùy chỉnh thông báo chi tiết cho người dùng';



COMMENT ON COLUMN "public"."chat_participants"."display_name" IS 'Tên hiển thị tùy chỉnh trong phòng chat này';



CREATE TABLE IF NOT EXISTS "public"."chat_rooms" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(255),
    "description" "text",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "is_active" boolean DEFAULT true,
    "room_type" character varying(50) DEFAULT 'general'::character varying,
    "metadata" "jsonb",
    "last_activity_at" timestamp with time zone DEFAULT "now"(),
    "organization_id" "uuid",
    "tenant_id" "uuid"
);


ALTER TABLE "public"."chat_rooms" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."hotel_rooms" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "hotel_id" "uuid" NOT NULL,
    "room_number" character varying(20) NOT NULL,
    "room_type" character varying(50),
    "floor" character varying(10),
    "capacity" integer,
    "is_occupied" boolean DEFAULT false,
    "status" character varying(50) DEFAULT 'available'::character varying,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."hotel_rooms" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."hotels" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(255) NOT NULL,
    "address" "text",
    "city" character varying(100),
    "country" character varying(100),
    "postal_code" character varying(20),
    "phone" character varying(50),
    "email" character varying(255),
    "website" character varying(255),
    "timezone" character varying(50),
    "check_in_time" time without time zone,
    "check_out_time" time without time zone,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "is_active" boolean DEFAULT true,
    "metadata" "jsonb",
    "organization_id" "uuid",
    "tenant_id" "uuid"
);


ALTER TABLE "public"."hotels" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."latest_chat_messages" AS
 SELECT DISTINCT ON ("cm"."chat_room_id") "cm"."chat_room_id",
    "cm"."id" AS "message_id",
    "cm"."content",
    "cm"."sender_id",
    "cm"."sent_at"
   FROM "public"."chat_messages" "cm"
  WHERE ("cm"."deleted_at" IS NULL)
  ORDER BY "cm"."chat_room_id", "cm"."sent_at" DESC;


ALTER TABLE "public"."latest_chat_messages" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."locations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "location_type" character varying(50) NOT NULL,
    "address" "text",
    "coordinates" "point",
    "floor" character varying(10),
    "room_number" character varying(20),
    "parent_location_id" "uuid",
    "contact_info" "jsonb" DEFAULT '{}'::"jsonb",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."locations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."message_attachments" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "message_id" "uuid" NOT NULL,
    "file_url" "text" NOT NULL,
    "file_name" character varying(255) NOT NULL,
    "file_type" character varying(100),
    "file_size" integer,
    "thumbnail_url" "text",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."message_attachments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."message_translations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "message_id" "uuid" NOT NULL,
    "language" character varying(10) NOT NULL,
    "translated_content" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "translation_provider" character varying(50),
    "is_automatic" boolean DEFAULT true,
    "source_table" character varying(50) DEFAULT 'unified'::character varying
);


ALTER TABLE "public"."message_translations" OWNER TO "postgres";


COMMENT ON TABLE "public"."message_translations" IS 'Lưu trữ bản dịch của tin nhắn chat trong nhiều ngôn ngữ. Bảng này là kết quả hợp nhất từ chat_message_translations và message_translations cũ.';



CREATE OR REPLACE VIEW "public"."message_details" AS
 SELECT "m"."id",
    "m"."content",
    "m"."chat_room_id",
    "m"."sender_id",
    "m"."temporary_sender_id",
    "m"."sent_at",
    "m"."original_language",
    "t"."language" AS "translation_language",
    "t"."translated_content"
   FROM ("public"."chat_messages" "m"
     LEFT JOIN "public"."message_translations" "t" ON (("m"."id" = "t"."message_id")));


ALTER TABLE "public"."message_details" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."message_drafts" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "chat_room_id" "uuid" NOT NULL,
    "participant_id" "uuid",
    "content" "text" NOT NULL,
    "content_type" character varying(20) DEFAULT 'text'::character varying NOT NULL,
    "attachments" "jsonb" DEFAULT '[]'::"jsonb",
    "original_language" character varying(10) DEFAULT 'en'::character varying NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."message_drafts" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."message_reactions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "message_id" "uuid" NOT NULL,
    "participant_id" "uuid" NOT NULL,
    "reaction" character varying(50) NOT NULL,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."message_reactions" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."message_read_overview" AS
SELECT
    NULL::"uuid" AS "message_id",
    NULL::"uuid" AS "chat_room_id",
    NULL::"uuid" AS "sender_id",
    NULL::"text" AS "content",
    NULL::timestamp with time zone AS "sent_at",
    NULL::bigint AS "read_by_count",
    NULL::timestamp with time zone AS "last_read_at",
    NULL::"json" AS "read_by_users";


ALTER TABLE "public"."message_read_overview" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."message_read_status" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "message_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "participant_id" "uuid",
    "read_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."message_read_status" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."message_status" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "message_id" "uuid" NOT NULL,
    "participant_id" "uuid" NOT NULL,
    "is_delivered" boolean DEFAULT false,
    "is_read" boolean DEFAULT false,
    "delivered_at" timestamp with time zone,
    "read_at" timestamp with time zone
);


ALTER TABLE "public"."message_status" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."tenant_users" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "tenant_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "role" "public"."tenant_user_role" DEFAULT 'member'::"public"."tenant_user_role" NOT NULL,
    "is_primary_tenant" boolean DEFAULT false,
    "is_active" boolean DEFAULT true,
    "joined_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "last_login_at" timestamp with time zone,
    "expiry_date" timestamp with time zone,
    "permissions" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb"
);


ALTER TABLE "public"."tenant_users" OWNER TO "postgres";


COMMENT ON TABLE "public"."tenant_users" IS 'Links users to tenants and manages their roles within each tenant';



CREATE TABLE IF NOT EXISTS "public"."tenants" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(255) NOT NULL,
    "domain" character varying(255),
    "contact_email" character varying(255) NOT NULL,
    "contact_phone" character varying(50),
    "address" "text",
    "logo_url" "text",
    "primary_color" character varying(20) DEFAULT '#3498db'::character varying,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb"
);


ALTER TABLE "public"."tenants" OWNER TO "postgres";


COMMENT ON TABLE "public"."tenants" IS 'Stores information about tenants/customers using the LoaLoa system';



CREATE OR REPLACE VIEW "public"."my_tenants" AS
 SELECT "t"."id" AS "tenant_id",
    "t"."name" AS "tenant_name",
    "t"."domain" AS "tenant_domain",
    "t"."logo_url",
    "t"."is_active" AS "tenant_is_active",
    "tu"."role" AS "my_role",
    "tu"."is_primary_tenant",
    "tu"."permissions" AS "my_permissions",
    "tu"."joined_at",
    "tu"."last_login_at"
   FROM ("public"."tenants" "t"
     JOIN "public"."tenant_users" "tu" ON (("t"."id" = "tu"."tenant_id")))
  WHERE ("tu"."user_id" = "auth"."uid"());


ALTER TABLE "public"."my_tenants" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."organization_member_roles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_member_id" "uuid",
    "organization_role_id" "uuid",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."organization_member_roles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."organization_members" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_id" "uuid",
    "user_id" "uuid",
    "role" character varying(50) DEFAULT 'member'::character varying NOT NULL,
    "is_primary" boolean DEFAULT false,
    "joined_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "metadata" "jsonb"
);


ALTER TABLE "public"."organization_members" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."organization_role_permissions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_role_id" "uuid",
    "permission_id" "uuid",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."organization_role_permissions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."organization_roles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "organization_id" "uuid",
    "name" character varying(50) NOT NULL,
    "description" "text",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."organization_roles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."organizations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" character varying(100) NOT NULL,
    "plan_type" character varying(50) DEFAULT 'free'::character varying,
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "metadata" "jsonb",
    "tenant_id" "uuid"
);


ALTER TABLE "public"."organizations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."orphaned_user_roles" (
    "id" "uuid" NOT NULL,
    "user_id" "uuid",
    "role_id" "uuid",
    "created_at" timestamp with time zone,
    "notes" "text" DEFAULT 'Role từ user không còn tồn tại trong auth.users'::"text"
);


ALTER TABLE "public"."orphaned_user_roles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."permissions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(100) NOT NULL,
    "description" "text",
    "resource" character varying(100) NOT NULL,
    "action" character varying(50) NOT NULL,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."permissions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."qr_code_scans" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "qr_code_id" "uuid" NOT NULL,
    "scanned_by_user_id" "uuid",
    "scanned_by_anonymous_id" character varying(255),
    "scanned_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "ip_address" character varying(45),
    "user_agent" "text",
    "device_info" "jsonb" DEFAULT '{}'::"jsonb",
    "location_info" "jsonb" DEFAULT '{}'::"jsonb",
    "action_taken" character varying(100),
    "action_result" character varying(50),
    "session_id" "uuid",
    "duration" integer
);


ALTER TABLE "public"."qr_code_scans" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."qr_codes" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "qr_type" character varying(50) NOT NULL,
    "title" character varying(255) NOT NULL,
    "description" "text",
    "owner_id" "uuid",
    "organization_id" "uuid",
    "reference_id" "uuid",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "usage_limit" integer,
    "usage_count" integer DEFAULT 0,
    "expire_at" timestamp with time zone,
    "is_active" boolean DEFAULT true,
    "is_dynamic" boolean DEFAULT false,
    "access_type" character varying(50) DEFAULT 'public'::character varying,
    "short_code" character varying(20),
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "tenant_id" "uuid"
);


ALTER TABLE "public"."qr_codes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."role_permissions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "role_id" "uuid" NOT NULL,
    "permission_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."role_permissions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."roles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(50) NOT NULL,
    "description" "text",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."roles" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."room_active_users" AS
 SELECT "cr"."id" AS "room_id",
    "cr"."name" AS "room_name",
    "count"(DISTINCT "up"."user_id") AS "active_user_count",
    "json_agg"("json_build_object"('user_id', "u"."id", 'email', "u"."email", 'status', "up"."status", 'last_active_at', "up"."last_active_at")) AS "active_users"
   FROM ((("public"."chat_rooms" "cr"
     JOIN "public"."chat_participants" "cp" ON ((("cp"."chat_room_id" = "cr"."id") AND ("cp"."is_active" = true))))
     JOIN "public"."user_presence" "up" ON ((("up"."user_id" = "cp"."user_id") AND ((("up"."status")::"text" = 'online'::"text") OR ("up"."current_room_id" = "cr"."id")))))
     JOIN "auth"."users" "u" ON (("u"."id" = "up"."user_id")))
  GROUP BY "cr"."id", "cr"."name";


ALTER TABLE "public"."room_active_users" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."temporary_users" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "qr_code_id" character varying(100) NOT NULL,
    "device_id" character varying(255),
    "preferred_language" character varying(50) DEFAULT 'en'::character varying,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "expires_at" timestamp with time zone NOT NULL,
    "is_activated" boolean DEFAULT false,
    "room_number" character varying(50),
    "hotel_id" "uuid",
    "metadata" "jsonb"
);


ALTER TABLE "public"."temporary_users" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."tenant_databases" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "tenant_id" "uuid" NOT NULL,
    "schema_name" "text" NOT NULL,
    "status" "public"."tenant_db_status" DEFAULT 'initializing'::"public"."tenant_db_status" NOT NULL,
    "connection_string" "text",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "last_backup_at" timestamp with time zone,
    "error_message" "text",
    "resource_usage" "jsonb" DEFAULT '{}'::"jsonb",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb"
);


ALTER TABLE "public"."tenant_databases" OWNER TO "postgres";


COMMENT ON TABLE "public"."tenant_databases" IS 'Manages database/schema information for each tenant';



CREATE TABLE IF NOT EXISTS "public"."tenant_licenses" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "tenant_id" "uuid" NOT NULL,
    "license_type" "public"."license_type" DEFAULT 'free'::"public"."license_type" NOT NULL,
    "user_limit" integer,
    "storage_limit" bigint,
    "features" "jsonb" DEFAULT '{}'::"jsonb",
    "starts_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "expires_at" timestamp with time zone,
    "is_active" boolean DEFAULT true,
    "last_payment_date" timestamp with time zone,
    "payment_status" character varying(50) DEFAULT 'pending'::character varying,
    "payment_method" character varying(50),
    "billing_cycle" character varying(20) DEFAULT 'monthly'::character varying,
    "price" numeric(10,2),
    "currency" character varying(3) DEFAULT 'USD'::character varying,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb"
);


ALTER TABLE "public"."tenant_licenses" OWNER TO "postgres";


COMMENT ON TABLE "public"."tenant_licenses" IS 'Stores license information for each tenant including subscription details';



CREATE TABLE IF NOT EXISTS "public"."translation_cache" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "source_text" "text" NOT NULL,
    "source_language" character varying(10) NOT NULL,
    "target_language" character varying(10) NOT NULL,
    "translated_text" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "last_used_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "use_count" integer DEFAULT 1
);


ALTER TABLE "public"."translation_cache" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_profiles" (
    "id" "uuid" NOT NULL,
    "full_name" character varying(255),
    "preferred_language" character varying(10) DEFAULT 'en'::character varying,
    "avatar_url" "text",
    "phone_number" character varying(50),
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "metadata" "jsonb",
    "last_active_at" timestamp with time zone
);


ALTER TABLE "public"."user_profiles" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."user_details" AS
 SELECT "au"."id",
    "au"."email",
    "au"."confirmed_at",
    "au"."last_sign_in_at",
    "au"."raw_user_meta_data",
    "up"."full_name",
    "up"."preferred_language",
    "up"."avatar_url",
    "up"."phone_number",
    "up"."created_at",
    "up"."updated_at",
    "up"."last_active_at",
    ( SELECT "array_agg"("om"."organization_id") AS "array_agg"
           FROM "public"."organization_members" "om"
          WHERE ("om"."user_id" = "au"."id")) AS "organizations"
   FROM ("auth"."users" "au"
     LEFT JOIN "public"."user_profiles" "up" ON (("au"."id" = "up"."id")));


ALTER TABLE "public"."user_details" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_orphans" (
    "id" "uuid" NOT NULL,
    "email" character varying(255),
    "full_name" character varying(255),
    "preferred_language" character varying(10),
    "avatar_url" "text",
    "phone_number" character varying(50),
    "created_at" timestamp with time zone,
    "updated_at" timestamp with time zone,
    "metadata" "jsonb",
    "notes" "text" DEFAULT 'User không có tương ứng trong auth.users'::"text"
);


ALTER TABLE "public"."user_orphans" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_profiles_temp" (
    "id" "uuid" NOT NULL,
    "full_name" character varying(255),
    "preferred_language" character varying(10) DEFAULT 'en'::character varying,
    "avatar_url" "text",
    "phone_number" character varying(50),
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "metadata" "jsonb",
    "last_active_at" timestamp with time zone,
    "source_table" character varying(20) DEFAULT 'public.users'::character varying
);


ALTER TABLE "public"."user_profiles_temp" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_roles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "role_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."user_roles" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."user_tenant_identity" AS
 SELECT "u"."id",
    "u"."email",
    "u"."raw_user_meta_data",
    "public"."get_current_tenant_id"() AS "current_tenant_id",
    "tu"."role" AS "tenant_role",
    "tu"."permissions",
    "t"."name" AS "tenant_name"
   FROM (("auth"."users" "u"
     LEFT JOIN "public"."tenant_users" "tu" ON ((("u"."id" = "tu"."user_id") AND ("tu"."tenant_id" = "public"."get_current_tenant_id"()))))
     LEFT JOIN "public"."tenants" "t" ON (("t"."id" = "tu"."tenant_id")))
  WHERE ("u"."id" = "auth"."uid"());


ALTER TABLE "public"."user_tenant_identity" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "email" character varying(255) NOT NULL,
    "password_hash" character varying(255),
    "full_name" character varying(255),
    "preferred_language" character varying(50) DEFAULT 'en'::character varying,
    "avatar_url" "text",
    "phone_number" character varying(50),
    "is_active" boolean DEFAULT true,
    "is_verified" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."users" OWNER TO "postgres";


ALTER TABLE ONLY "public"."chat_messages"
    ADD CONSTRAINT "chat_messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."chat_participants"
    ADD CONSTRAINT "chat_participants_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."chat_rooms"
    ADD CONSTRAINT "chat_rooms_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."hotel_rooms"
    ADD CONSTRAINT "hotel_rooms_hotel_id_room_number_key" UNIQUE ("hotel_id", "room_number");



ALTER TABLE ONLY "public"."hotel_rooms"
    ADD CONSTRAINT "hotel_rooms_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."hotels"
    ADD CONSTRAINT "hotels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."locations"
    ADD CONSTRAINT "locations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."message_attachments"
    ADD CONSTRAINT "message_attachments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."message_drafts"
    ADD CONSTRAINT "message_drafts_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."message_drafts"
    ADD CONSTRAINT "message_drafts_user_id_chat_room_id_key" UNIQUE ("user_id", "chat_room_id");



ALTER TABLE ONLY "public"."message_reactions"
    ADD CONSTRAINT "message_reactions_message_id_participant_id_reaction_key" UNIQUE ("message_id", "participant_id", "reaction");



ALTER TABLE ONLY "public"."message_reactions"
    ADD CONSTRAINT "message_reactions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."message_read_status"
    ADD CONSTRAINT "message_read_status_message_id_user_id_key" UNIQUE ("message_id", "user_id");



ALTER TABLE ONLY "public"."message_read_status"
    ADD CONSTRAINT "message_read_status_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."message_status"
    ADD CONSTRAINT "message_status_message_id_participant_id_key" UNIQUE ("message_id", "participant_id");



ALTER TABLE ONLY "public"."message_status"
    ADD CONSTRAINT "message_status_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."message_translations"
    ADD CONSTRAINT "message_translations_new_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."organization_member_roles"
    ADD CONSTRAINT "organization_member_roles_organization_member_id_organizati_key" UNIQUE ("organization_member_id", "organization_role_id");



ALTER TABLE ONLY "public"."organization_member_roles"
    ADD CONSTRAINT "organization_member_roles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."organization_members"
    ADD CONSTRAINT "organization_members_organization_id_user_id_key" UNIQUE ("organization_id", "user_id");



ALTER TABLE ONLY "public"."organization_members"
    ADD CONSTRAINT "organization_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."organization_role_permissions"
    ADD CONSTRAINT "organization_role_permissions_organization_role_id_permissi_key" UNIQUE ("organization_role_id", "permission_id");



ALTER TABLE ONLY "public"."organization_role_permissions"
    ADD CONSTRAINT "organization_role_permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."organization_roles"
    ADD CONSTRAINT "organization_roles_organization_id_name_key" UNIQUE ("organization_id", "name");



ALTER TABLE ONLY "public"."organization_roles"
    ADD CONSTRAINT "organization_roles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."organizations"
    ADD CONSTRAINT "organizations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."organizations"
    ADD CONSTRAINT "organizations_slug_key" UNIQUE ("description");



ALTER TABLE ONLY "public"."orphaned_user_roles"
    ADD CONSTRAINT "orphaned_user_roles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."permissions"
    ADD CONSTRAINT "permissions_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."permissions"
    ADD CONSTRAINT "permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."permissions"
    ADD CONSTRAINT "permissions_resource_action_key" UNIQUE ("resource", "action");



ALTER TABLE ONLY "public"."qr_code_scans"
    ADD CONSTRAINT "qr_code_scans_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."qr_codes"
    ADD CONSTRAINT "qr_codes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."qr_codes"
    ADD CONSTRAINT "qr_codes_short_code_key" UNIQUE ("short_code");



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_role_id_permission_id_key" UNIQUE ("role_id", "permission_id");



ALTER TABLE ONLY "public"."roles"
    ADD CONSTRAINT "roles_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."roles"
    ADD CONSTRAINT "roles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."temporary_users"
    ADD CONSTRAINT "temporary_users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."temporary_users"
    ADD CONSTRAINT "temporary_users_qr_code_id_key" UNIQUE ("qr_code_id");



ALTER TABLE ONLY "public"."tenant_databases"
    ADD CONSTRAINT "tenant_databases_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."tenant_databases"
    ADD CONSTRAINT "tenant_databases_schema_name_key" UNIQUE ("schema_name");



ALTER TABLE ONLY "public"."tenant_databases"
    ADD CONSTRAINT "tenant_databases_tenant_id_key" UNIQUE ("tenant_id");



ALTER TABLE ONLY "public"."tenant_licenses"
    ADD CONSTRAINT "tenant_licenses_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."tenant_users"
    ADD CONSTRAINT "tenant_users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."tenant_users"
    ADD CONSTRAINT "tenant_users_tenant_id_user_id_key" UNIQUE ("tenant_id", "user_id");



ALTER TABLE ONLY "public"."tenants"
    ADD CONSTRAINT "tenants_domain_key" UNIQUE ("domain");



ALTER TABLE ONLY "public"."tenants"
    ADD CONSTRAINT "tenants_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."translation_cache"
    ADD CONSTRAINT "translation_cache_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."translation_cache"
    ADD CONSTRAINT "translation_cache_source_text_source_language_target_langua_key" UNIQUE ("source_text", "source_language", "target_language");



ALTER TABLE ONLY "public"."chat_participants"
    ADD CONSTRAINT "unique_user_room" UNIQUE ("chat_room_id", "user_id");



ALTER TABLE ONLY "public"."user_orphans"
    ADD CONSTRAINT "user_orphans_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_presence"
    ADD CONSTRAINT "user_presence_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_presence"
    ADD CONSTRAINT "user_presence_user_id_key" UNIQUE ("user_id");



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_profiles_temp"
    ADD CONSTRAINT "user_profiles_temp_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_user_id_role_id_key" UNIQUE ("user_id", "role_id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_chat_messages_participant_id" ON "public"."chat_messages" USING "btree" ("participant_id");



CREATE INDEX "idx_chat_messages_room_id" ON "public"."chat_messages" USING "btree" ("chat_room_id");



CREATE INDEX "idx_chat_messages_sender_id" ON "public"."chat_messages" USING "btree" ("sender_id");



CREATE INDEX "idx_chat_messages_sent_at" ON "public"."chat_messages" USING "btree" ("sent_at");



CREATE INDEX "idx_chat_participants_room_id" ON "public"."chat_participants" USING "btree" ("chat_room_id");



CREATE INDEX "idx_chat_participants_temp_user_id" ON "public"."chat_participants" USING "btree" ("temporary_user_id") WHERE ("temporary_user_id" IS NOT NULL);



CREATE INDEX "idx_chat_participants_user_id" ON "public"."chat_participants" USING "btree" ("user_id") WHERE ("user_id" IS NOT NULL);



CREATE INDEX "idx_chat_rooms_activity" ON "public"."chat_rooms" USING "btree" ("last_activity_at" DESC);



CREATE INDEX "idx_chat_rooms_metadata_org_id" ON "public"."chat_rooms" USING "gin" ("metadata");



CREATE INDEX "idx_chat_rooms_organization_id" ON "public"."chat_rooms" USING "btree" ("organization_id");



CREATE INDEX "idx_chat_rooms_tenant_id" ON "public"."chat_rooms" USING "btree" ("tenant_id");



CREATE INDEX "idx_chat_rooms_type" ON "public"."chat_rooms" USING "btree" ("room_type");



CREATE INDEX "idx_hotels_organization_id" ON "public"."hotels" USING "btree" ("organization_id");



CREATE INDEX "idx_hotels_tenant_id" ON "public"."hotels" USING "btree" ("tenant_id");



CREATE INDEX "idx_locations_organization" ON "public"."locations" USING "btree" ("organization_id");



CREATE INDEX "idx_locations_parent" ON "public"."locations" USING "btree" ("parent_location_id");



CREATE INDEX "idx_locations_type" ON "public"."locations" USING "btree" ("location_type");



CREATE INDEX "idx_message_attachments_message_id" ON "public"."message_attachments" USING "btree" ("message_id");



CREATE INDEX "idx_message_drafts_chat_room_id" ON "public"."message_drafts" USING "btree" ("chat_room_id");



CREATE INDEX "idx_message_drafts_user_id" ON "public"."message_drafts" USING "btree" ("user_id");



CREATE INDEX "idx_message_read_status_message_id" ON "public"."message_read_status" USING "btree" ("message_id");



CREATE INDEX "idx_message_read_status_participant_id" ON "public"."message_read_status" USING "btree" ("participant_id");



CREATE INDEX "idx_message_read_status_user_id" ON "public"."message_read_status" USING "btree" ("user_id");



CREATE INDEX "idx_message_status_message_id" ON "public"."message_status" USING "btree" ("message_id");



CREATE INDEX "idx_message_status_participant_id" ON "public"."message_status" USING "btree" ("participant_id");



CREATE INDEX "idx_message_translations_language_new" ON "public"."message_translations" USING "btree" ("language");



CREATE INDEX "idx_messages_content_search" ON "public"."chat_messages" USING "gin" ("content" "public"."gin_trgm_ops");



CREATE INDEX "idx_messages_reply" ON "public"."chat_messages" USING "btree" ("reply_to") WHERE ("reply_to" IS NOT NULL);



CREATE INDEX "idx_messages_room" ON "public"."chat_messages" USING "btree" ("chat_room_id");



CREATE INDEX "idx_messages_sender" ON "public"."chat_messages" USING "btree" ("sender_id");



CREATE INDEX "idx_messages_time" ON "public"."chat_messages" USING "btree" ("sent_at" DESC);



CREATE INDEX "idx_organization_members_user_id" ON "public"."organization_members" USING "btree" ("user_id");



CREATE INDEX "idx_organizations_tenant_id" ON "public"."organizations" USING "btree" ("tenant_id");



CREATE INDEX "idx_participants_active" ON "public"."chat_participants" USING "btree" ("is_active") WHERE ("is_active" = true);



CREATE INDEX "idx_participants_read_at" ON "public"."chat_participants" USING "btree" ("last_read_at");



CREATE INDEX "idx_participants_room" ON "public"."chat_participants" USING "btree" ("chat_room_id");



CREATE INDEX "idx_participants_user" ON "public"."chat_participants" USING "btree" ("user_id");



CREATE INDEX "idx_participants_user_room" ON "public"."chat_participants" USING "btree" ("user_id", "chat_room_id");



CREATE INDEX "idx_qr_code_scans_action_taken" ON "public"."qr_code_scans" USING "btree" ("action_taken");



CREATE INDEX "idx_qr_code_scans_qr_code_id" ON "public"."qr_code_scans" USING "btree" ("qr_code_id");



CREATE INDEX "idx_qr_code_scans_scanned_at" ON "public"."qr_code_scans" USING "btree" ("scanned_at");



CREATE INDEX "idx_qr_code_scans_scanned_by_user" ON "public"."qr_code_scans" USING "btree" ("scanned_by_user_id");



CREATE INDEX "idx_qr_codes_organization" ON "public"."qr_codes" USING "btree" ("organization_id");



CREATE INDEX "idx_qr_codes_owner" ON "public"."qr_codes" USING "btree" ("owner_id");



CREATE INDEX "idx_qr_codes_reference_id" ON "public"."qr_codes" USING "btree" ("reference_id");



CREATE INDEX "idx_qr_codes_short_code" ON "public"."qr_codes" USING "btree" ("short_code");



CREATE INDEX "idx_qr_codes_tenant_id" ON "public"."qr_codes" USING "btree" ("tenant_id");



CREATE INDEX "idx_qr_codes_type" ON "public"."qr_codes" USING "btree" ("qr_type");



CREATE INDEX "idx_tenant_databases_status" ON "public"."tenant_databases" USING "btree" ("status");



CREATE INDEX "idx_tenant_databases_tenant_id" ON "public"."tenant_databases" USING "btree" ("tenant_id");



CREATE INDEX "idx_tenant_licenses_expires_at" ON "public"."tenant_licenses" USING "btree" ("expires_at");



CREATE INDEX "idx_tenant_licenses_is_active" ON "public"."tenant_licenses" USING "btree" ("is_active");



CREATE INDEX "idx_tenant_licenses_license_type" ON "public"."tenant_licenses" USING "btree" ("license_type");



CREATE INDEX "idx_tenant_licenses_tenant_id" ON "public"."tenant_licenses" USING "btree" ("tenant_id");



CREATE INDEX "idx_tenant_users_is_active" ON "public"."tenant_users" USING "btree" ("is_active");



CREATE INDEX "idx_tenant_users_is_primary_tenant" ON "public"."tenant_users" USING "btree" ("is_primary_tenant");



CREATE INDEX "idx_tenant_users_role" ON "public"."tenant_users" USING "btree" ("role");



CREATE INDEX "idx_tenant_users_tenant_id" ON "public"."tenant_users" USING "btree" ("tenant_id");



CREATE INDEX "idx_tenant_users_user_id" ON "public"."tenant_users" USING "btree" ("user_id");



CREATE INDEX "idx_tenants_domain" ON "public"."tenants" USING "btree" ("domain");



CREATE INDEX "idx_tenants_is_active" ON "public"."tenants" USING "btree" ("is_active");



CREATE INDEX "idx_tenants_name" ON "public"."tenants" USING "btree" ("name");



CREATE INDEX "idx_user_presence_current_room" ON "public"."user_presence" USING "btree" ("current_room_id");



CREATE INDEX "idx_user_presence_last_active" ON "public"."user_presence" USING "btree" ("last_active_at");



CREATE INDEX "idx_user_presence_status" ON "public"."user_presence" USING "btree" ("status");



CREATE INDEX "idx_user_profiles_preferred_language" ON "public"."user_profiles" USING "btree" ("preferred_language");



CREATE INDEX "idx_user_roles_user_id" ON "public"."user_roles" USING "btree" ("user_id");



CREATE OR REPLACE VIEW "public"."message_read_overview" AS
 SELECT "cm"."id" AS "message_id",
    "cm"."chat_room_id",
    "cm"."sender_id",
    "cm"."content",
    "cm"."sent_at",
    "count"(DISTINCT "mrs"."user_id") AS "read_by_count",
    "max"("mrs"."read_at") AS "last_read_at",
    ( SELECT "json_agg"("json_build_object"('user_id', "u"."id", 'email', "u"."email", 'read_at', "mrs2"."read_at")) AS "json_agg"
           FROM ("public"."message_read_status" "mrs2"
             JOIN "auth"."users" "u" ON (("u"."id" = "mrs2"."user_id")))
          WHERE ("mrs2"."message_id" = "cm"."id")) AS "read_by_users"
   FROM ("public"."chat_messages" "cm"
     LEFT JOIN "public"."message_read_status" "mrs" ON (("mrs"."message_id" = "cm"."id")))
  GROUP BY "cm"."id";



CREATE OR REPLACE TRIGGER "create_tenant_database_on_tenant_create" AFTER INSERT ON "public"."tenants" FOR EACH ROW EXECUTE FUNCTION "public"."create_tenant_database_on_tenant_create"();



CREATE OR REPLACE TRIGGER "create_tenant_schema_trigger" AFTER INSERT ON "public"."tenant_databases" FOR EACH ROW EXECUTE FUNCTION "public"."create_tenant_schema"();



CREATE OR REPLACE TRIGGER "generate_tenant_schema_name_trigger" BEFORE INSERT ON "public"."tenant_databases" FOR EACH ROW WHEN (("new"."schema_name" IS NULL)) EXECUTE FUNCTION "public"."generate_tenant_schema_name"();



CREATE OR REPLACE TRIGGER "set_organization_id_for_chat_room_trigger" BEFORE INSERT ON "public"."chat_rooms" FOR EACH ROW EXECUTE FUNCTION "public"."set_organization_id_for_chat_room"();



CREATE OR REPLACE TRIGGER "set_tenant_id_for_chat_room_trigger" BEFORE INSERT ON "public"."chat_rooms" FOR EACH ROW EXECUTE FUNCTION "public"."set_tenant_id_for_chat_room"();



CREATE OR REPLACE TRIGGER "set_tenant_id_for_hotel_trigger" BEFORE INSERT ON "public"."hotels" FOR EACH ROW EXECUTE FUNCTION "public"."set_tenant_id_for_hotel"();



CREATE OR REPLACE TRIGGER "set_tenant_id_for_organization_trigger" BEFORE INSERT ON "public"."organizations" FOR EACH ROW EXECUTE FUNCTION "public"."set_tenant_id_for_organization"();



CREATE OR REPLACE TRIGGER "set_tenant_id_for_qr_code_trigger" BEFORE INSERT ON "public"."qr_codes" FOR EACH ROW EXECUTE FUNCTION "public"."set_tenant_id_for_qr_code"();



CREATE OR REPLACE TRIGGER "trigger_automatic_translations" AFTER INSERT ON "public"."chat_messages" FOR EACH ROW EXECUTE FUNCTION "public"."manage_automatic_translations"();



CREATE OR REPLACE TRIGGER "trigger_create_user_qr_code" AFTER INSERT ON "public"."user_profiles" FOR EACH ROW EXECUTE FUNCTION "public"."create_user_qr_code"();



CREATE OR REPLACE TRIGGER "trigger_manage_translations" AFTER INSERT ON "public"."chat_messages" FOR EACH ROW EXECUTE FUNCTION "public"."manage_automatic_translations"();



CREATE OR REPLACE TRIGGER "trigger_mark_message_as_read_for_sender" AFTER INSERT ON "public"."chat_messages" FOR EACH ROW EXECUTE FUNCTION "public"."mark_message_as_read_for_sender"();



CREATE OR REPLACE TRIGGER "trigger_update_room_activity" AFTER INSERT ON "public"."chat_messages" FOR EACH ROW EXECUTE FUNCTION "public"."update_room_last_activity"();



CREATE OR REPLACE TRIGGER "trigger_update_timestamp" BEFORE UPDATE ON "public"."chat_messages" FOR EACH ROW EXECUTE FUNCTION "public"."update_timestamp"();



CREATE OR REPLACE TRIGGER "update_chat_rooms_updated_at" BEFORE UPDATE ON "public"."chat_rooms" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_hotel_rooms_updated_at" BEFORE UPDATE ON "public"."hotel_rooms" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_hotels_updated_at" BEFORE UPDATE ON "public"."hotels" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_locations_updated_at" BEFORE UPDATE ON "public"."locations" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_message_drafts_updated_at" BEFORE UPDATE ON "public"."message_drafts" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_message_translations_new_updated_at" BEFORE UPDATE ON "public"."message_translations" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_organizations_updated_at" BEFORE UPDATE ON "public"."organizations" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_qr_codes_updated_at" BEFORE UPDATE ON "public"."qr_codes" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_tenant_databases_updated_at" BEFORE UPDATE ON "public"."tenant_databases" FOR EACH ROW EXECUTE FUNCTION "public"."update_tenant_databases_updated_at"();



CREATE OR REPLACE TRIGGER "update_tenant_licenses_updated_at" BEFORE UPDATE ON "public"."tenant_licenses" FOR EACH ROW EXECUTE FUNCTION "public"."update_tenant_licenses_updated_at"();



CREATE OR REPLACE TRIGGER "update_tenant_users_updated_at" BEFORE UPDATE ON "public"."tenant_users" FOR EACH ROW EXECUTE FUNCTION "public"."update_tenant_users_updated_at"();



CREATE OR REPLACE TRIGGER "update_tenants_updated_at" BEFORE UPDATE ON "public"."tenants" FOR EACH ROW EXECUTE FUNCTION "public"."update_tenants_updated_at"();



CREATE OR REPLACE TRIGGER "update_user_presence_updated_at" BEFORE UPDATE ON "public"."user_presence" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "users_updated_at_trigger" BEFORE UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."update_users_updated_at"();



ALTER TABLE ONLY "public"."chat_messages"
    ADD CONSTRAINT "chat_messages_chat_room_id_fkey" FOREIGN KEY ("chat_room_id") REFERENCES "public"."chat_rooms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."chat_messages"
    ADD CONSTRAINT "chat_messages_participant_id_fkey" FOREIGN KEY ("participant_id") REFERENCES "public"."chat_participants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."chat_messages"
    ADD CONSTRAINT "chat_messages_reply_to_fkey" FOREIGN KEY ("reply_to") REFERENCES "public"."chat_messages"("id");



ALTER TABLE ONLY "public"."chat_messages"
    ADD CONSTRAINT "chat_messages_sender_id_fkey" FOREIGN KEY ("sender_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL NOT VALID;



ALTER TABLE ONLY "public"."chat_messages"
    ADD CONSTRAINT "chat_messages_temporary_sender_id_fkey" FOREIGN KEY ("temporary_sender_id") REFERENCES "public"."temporary_users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."chat_participants"
    ADD CONSTRAINT "chat_participants_chat_room_id_fkey" FOREIGN KEY ("chat_room_id") REFERENCES "public"."chat_rooms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."chat_participants"
    ADD CONSTRAINT "chat_participants_temporary_user_id_fkey" FOREIGN KEY ("temporary_user_id") REFERENCES "public"."temporary_users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."chat_participants"
    ADD CONSTRAINT "chat_participants_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."chat_rooms"
    ADD CONSTRAINT "chat_rooms_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."chat_rooms"
    ADD CONSTRAINT "chat_rooms_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."hotel_rooms"
    ADD CONSTRAINT "hotel_rooms_hotel_id_fkey" FOREIGN KEY ("hotel_id") REFERENCES "public"."hotels"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."hotels"
    ADD CONSTRAINT "hotels_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."hotels"
    ADD CONSTRAINT "hotels_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."locations"
    ADD CONSTRAINT "locations_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."locations"
    ADD CONSTRAINT "locations_parent_location_id_fkey" FOREIGN KEY ("parent_location_id") REFERENCES "public"."locations"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."message_attachments"
    ADD CONSTRAINT "message_attachments_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "public"."chat_messages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."message_drafts"
    ADD CONSTRAINT "message_drafts_chat_room_id_fkey" FOREIGN KEY ("chat_room_id") REFERENCES "public"."chat_rooms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."message_drafts"
    ADD CONSTRAINT "message_drafts_participant_id_fkey" FOREIGN KEY ("participant_id") REFERENCES "public"."chat_participants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."message_drafts"
    ADD CONSTRAINT "message_drafts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."message_reactions"
    ADD CONSTRAINT "message_reactions_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "public"."chat_messages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."message_reactions"
    ADD CONSTRAINT "message_reactions_participant_id_fkey" FOREIGN KEY ("participant_id") REFERENCES "public"."chat_participants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."message_read_status"
    ADD CONSTRAINT "message_read_status_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "public"."chat_messages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."message_read_status"
    ADD CONSTRAINT "message_read_status_participant_id_fkey" FOREIGN KEY ("participant_id") REFERENCES "public"."chat_participants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."message_read_status"
    ADD CONSTRAINT "message_read_status_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."message_status"
    ADD CONSTRAINT "message_status_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "public"."chat_messages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."message_status"
    ADD CONSTRAINT "message_status_participant_id_fkey" FOREIGN KEY ("participant_id") REFERENCES "public"."chat_participants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."message_translations"
    ADD CONSTRAINT "message_translations_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "public"."chat_messages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organization_member_roles"
    ADD CONSTRAINT "organization_member_roles_organization_member_id_fkey" FOREIGN KEY ("organization_member_id") REFERENCES "public"."organization_members"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organization_member_roles"
    ADD CONSTRAINT "organization_member_roles_organization_role_id_fkey" FOREIGN KEY ("organization_role_id") REFERENCES "public"."organization_roles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organization_members"
    ADD CONSTRAINT "organization_members_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organization_members"
    ADD CONSTRAINT "organization_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organization_role_permissions"
    ADD CONSTRAINT "organization_role_permissions_organization_role_id_fkey" FOREIGN KEY ("organization_role_id") REFERENCES "public"."organization_roles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organization_role_permissions"
    ADD CONSTRAINT "organization_role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "public"."permissions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organization_roles"
    ADD CONSTRAINT "organization_roles_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organizations"
    ADD CONSTRAINT "organizations_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."qr_code_scans"
    ADD CONSTRAINT "qr_code_scans_qr_code_id_fkey" FOREIGN KEY ("qr_code_id") REFERENCES "public"."qr_codes"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."qr_code_scans"
    ADD CONSTRAINT "qr_code_scans_scanned_by_user_id_fkey" FOREIGN KEY ("scanned_by_user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."qr_codes"
    ADD CONSTRAINT "qr_codes_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."qr_codes"
    ADD CONSTRAINT "qr_codes_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."qr_codes"
    ADD CONSTRAINT "qr_codes_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "public"."permissions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."tenant_databases"
    ADD CONSTRAINT "tenant_databases_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."tenant_licenses"
    ADD CONSTRAINT "tenant_licenses_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."tenant_users"
    ADD CONSTRAINT "tenant_users_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."tenant_users"
    ADD CONSTRAINT "tenant_users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_presence"
    ADD CONSTRAINT "user_presence_current_room_id_fkey" FOREIGN KEY ("current_room_id") REFERENCES "public"."chat_rooms"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."user_presence"
    ADD CONSTRAINT "user_presence_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



CREATE POLICY "Admins can update rooms" ON "public"."chat_rooms" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."chat_participants"
  WHERE (("chat_participants"."chat_room_id" = "chat_rooms"."id") AND ("chat_participants"."user_id" = "auth"."uid"()) AND (("chat_participants"."participant_role")::"text" = 'admin'::"text") AND ("chat_participants"."is_active" = true)))));



CREATE POLICY "Chat participants can view messages" ON "public"."chat_messages" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."chat_participants"
  WHERE (("chat_participants"."chat_room_id" = "chat_messages"."chat_room_id") AND (("chat_participants"."user_id" = "auth"."uid"()) OR ("chat_participants"."temporary_user_id" IS NOT NULL))))));



CREATE POLICY "Public chat rooms are viewable by everyone" ON "public"."chat_rooms" FOR SELECT USING ((("room_type")::"text" = 'general'::"text"));



CREATE POLICY "Users can create rooms" ON "public"."chat_rooms" FOR INSERT WITH CHECK (("auth"."uid"() IS NOT NULL));



CREATE POLICY "Users can join rooms" ON "public"."chat_participants" FOR INSERT WITH CHECK ((("user_id" = "auth"."uid"()) OR ("auth"."uid"() IS NOT NULL)));



CREATE POLICY "Users can see participants in their rooms" ON "public"."chat_participants" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."chat_participants" "my_participation"
  WHERE (("my_participation"."chat_room_id" = "chat_participants"."chat_room_id") AND ("my_participation"."user_id" = "auth"."uid"()) AND ("my_participation"."is_active" = true)))));



CREATE POLICY "Users can send messages in their rooms" ON "public"."chat_messages" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."chat_participants"
  WHERE (("chat_participants"."id" = "chat_messages"."participant_id") AND ("chat_participants"."user_id" = "auth"."uid"()) AND ("chat_participants"."is_active" = true)))));



CREATE POLICY "Users can update their own messages" ON "public"."chat_messages" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."chat_participants"
  WHERE (("chat_participants"."id" = "chat_messages"."participant_id") AND ("chat_participants"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can update their own participation" ON "public"."chat_participants" FOR UPDATE USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can view messages in their rooms" ON "public"."chat_messages" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."chat_participants"
  WHERE (("chat_participants"."chat_room_id" = "chat_messages"."chat_room_id") AND ("chat_participants"."user_id" = "auth"."uid"()) AND ("chat_participants"."is_active" = true)))));



CREATE POLICY "Users can view rooms they are participants in" ON "public"."chat_rooms" FOR SELECT USING (((("room_type")::"text" = 'general'::"text") OR (EXISTS ( SELECT 1
   FROM "public"."chat_participants"
  WHERE (("chat_participants"."chat_room_id" = "chat_rooms"."id") AND ("chat_participants"."user_id" = "auth"."uid"()) AND ("chat_participants"."is_active" = true))))));



CREATE POLICY "access_message_translations" ON "public"."message_translations" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM ("public"."chat_messages"
     JOIN "public"."chat_participants" ON (("chat_participants"."chat_room_id" = "chat_messages"."chat_room_id")))
  WHERE (("message_translations"."message_id" = "chat_messages"."id") AND ("chat_participants"."user_id" = "auth"."uid"()) AND ("chat_participants"."is_active" = true)))));



CREATE POLICY "admin_view_all_profiles" ON "public"."user_profiles";



CREATE POLICY "chat_message_org_isolation" ON "public"."chat_messages" TO "authenticated" USING (("chat_room_id" IN ( SELECT "cr"."id"
   FROM "public"."chat_rooms" "cr"
  WHERE (("cr"."metadata" ->> 'organization_id'::"text") IN ( SELECT ("organization_members"."organization_id")::"text" AS "organization_id"
           FROM "public"."organization_members"
          WHERE ("organization_members"."user_id" = "auth"."uid"()))))));



CREATE POLICY "chat_message_participant_access" ON "public"."chat_messages";



CREATE POLICY "chat_participant_org_isolation" ON "public"."chat_participants" TO "authenticated" USING (("chat_room_id" IN ( SELECT "cr"."id"
   FROM "public"."chat_rooms" "cr"
  WHERE (("cr"."metadata" ->> 'organization_id'::"text") IN ( SELECT ("organization_members"."organization_id")::"text" AS "organization_id"
           FROM "public"."organization_members"
          WHERE ("organization_members"."user_id" = "auth"."uid"()))))));



CREATE POLICY "chat_participant_self_access" ON "public"."chat_participants";



CREATE POLICY "chat_room_org_isolation" ON "public"."chat_rooms" TO "authenticated" USING ((("metadata" ->> 'organization_id'::"text") IN ( SELECT ("organization_members"."organization_id")::"text" AS "organization_id"
   FROM "public"."organization_members"
  WHERE ("organization_members"."user_id" = "auth"."uid"()))));



CREATE POLICY "chat_room_participant_access" ON "public"."chat_rooms";



ALTER TABLE "public"."chat_rooms" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "chat_rooms_tenant_policy" ON "public"."chat_rooms" USING (("tenant_id" = "public"."get_current_tenant_id"()));



CREATE POLICY "hotel_admin_all_access" ON "public"."hotels" TO "authenticated" USING (("organization_id" IN ( SELECT "organization_members"."organization_id"
   FROM "public"."organization_members"
  WHERE (("organization_members"."user_id" = "auth"."uid"()) AND (("organization_members"."role")::"text" = ANY ((ARRAY['admin'::character varying, 'owner'::character varying, 'manager'::character varying])::"text"[]))))));



ALTER TABLE "public"."hotel_rooms" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "hotel_staff_view_update" ON "public"."hotels";



ALTER TABLE "public"."hotels" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "hotels_tenant_policy" ON "public"."hotels" USING (("tenant_id" = "public"."get_current_tenant_id"()));



CREATE POLICY "member_view_own_record" ON "public"."organization_members";



ALTER TABLE "public"."message_attachments" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "message_drafts_delete_policy" ON "public"."message_drafts" FOR DELETE TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "message_drafts_insert_policy" ON "public"."message_drafts" FOR INSERT TO "authenticated" WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "message_drafts_select_policy" ON "public"."message_drafts" FOR SELECT TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "message_drafts_update_policy" ON "public"."message_drafts" FOR UPDATE TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "message_read_status_insert_policy" ON "public"."message_read_status" FOR INSERT TO "authenticated" WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "message_read_status_select_policy" ON "public"."message_read_status" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."chat_messages"
     JOIN "public"."chat_participants" ON (("chat_participants"."chat_room_id" = "chat_messages"."chat_room_id")))
  WHERE (("message_read_status"."message_id" = "chat_messages"."id") AND ("chat_participants"."user_id" = "auth"."uid"()) AND ("chat_participants"."is_active" = true)))));



CREATE POLICY "message_read_status_update_policy" ON "public"."message_read_status" FOR UPDATE TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "message_translation_org_isolation" ON "public"."message_translations" TO "authenticated" USING (("message_id" IN ( SELECT "cm"."id"
   FROM ("public"."chat_messages" "cm"
     JOIN "public"."chat_rooms" "cr" ON (("cm"."chat_room_id" = "cr"."id")))
  WHERE (("cr"."metadata" ->> 'organization_id'::"text") IN ( SELECT ("organization_members"."organization_id")::"text" AS "organization_id"
           FROM "public"."organization_members"
          WHERE ("organization_members"."user_id" = "auth"."uid"()))))));



CREATE POLICY "org_admin_all_access" ON "public"."organizations";



CREATE POLICY "org_admin_all_members" ON "public"."organization_members" TO "authenticated" USING (("organization_id" IN ( SELECT "organization_members_1"."organization_id"
   FROM "public"."organization_members" "organization_members_1"
  WHERE (("organization_members_1"."user_id" = "auth"."uid"()) AND (("organization_members_1"."role")::"text" = ANY ((ARRAY['admin'::character varying, 'owner'::character varying])::"text"[]))))));



CREATE POLICY "org_admin_view_profiles" ON "public"."user_profiles" FOR SELECT TO "authenticated" USING (("id" IN ( SELECT "organization_members"."user_id"
   FROM "public"."organization_members"
  WHERE ("organization_members"."organization_id" IN ( SELECT "organization_members_1"."organization_id"
           FROM "public"."organization_members" "organization_members_1"
          WHERE (("organization_members_1"."user_id" = "auth"."uid"()) AND (("organization_members_1"."role")::"text" = ANY ((ARRAY['admin'::character varying, 'owner'::character varying])::"text"[]))))))));



CREATE POLICY "org_member_view_access" ON "public"."organizations";



CREATE POLICY "org_member_view_members" ON "public"."organization_members" FOR SELECT TO "authenticated" USING (("organization_id" IN ( SELECT "organization_members_1"."organization_id"
   FROM "public"."organization_members" "organization_members_1"
  WHERE ("organization_members_1"."user_id" = "auth"."uid"()))));



CREATE POLICY "organization_isolation_policy" ON "public"."hotels" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."organization_members" "om"
  WHERE (("om"."organization_id" = "hotels"."organization_id") AND ("om"."user_id" = "auth"."uid"())))));



ALTER TABLE "public"."organizations" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "organizations_tenant_policy" ON "public"."organizations" USING (("tenant_id" = "public"."get_current_tenant_id"()));



ALTER TABLE "public"."qr_codes" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "qr_codes_tenant_policy" ON "public"."qr_codes" USING (("tenant_id" = "public"."get_current_tenant_id"()));



CREATE POLICY "room_admin_all_access" ON "public"."hotel_rooms" TO "authenticated" USING (("hotel_id" IN ( SELECT "h"."id"
   FROM ("public"."hotels" "h"
     JOIN "public"."organization_members" "om" ON (("h"."organization_id" = "om"."organization_id")))
  WHERE (("om"."user_id" = "auth"."uid"()) AND (("om"."role")::"text" = ANY ((ARRAY['admin'::character varying, 'owner'::character varying, 'manager'::character varying])::"text"[]))))));



CREATE POLICY "room_staff_view_update" ON "public"."hotel_rooms";



CREATE POLICY "super_admin_tenant_databases_policy" ON "public"."tenant_databases" USING (( SELECT "users"."is_super_admin"
   FROM "auth"."users"
  WHERE ("users"."id" = "auth"."uid"())));



CREATE POLICY "super_admin_tenant_licenses_policy" ON "public"."tenant_licenses" USING (( SELECT "users"."is_super_admin"
   FROM "auth"."users"
  WHERE ("users"."id" = "auth"."uid"())));



CREATE POLICY "super_admin_tenant_users_policy" ON "public"."tenant_users" USING (( SELECT "users"."is_super_admin"
   FROM "auth"."users"
  WHERE ("users"."id" = "auth"."uid"())));



CREATE POLICY "super_admin_tenants_policy" ON "public"."tenants" USING (( SELECT "users"."is_super_admin"
   FROM "auth"."users"
  WHERE ("users"."id" = "auth"."uid"())));



CREATE POLICY "temporary_user_org_isolation" ON "public"."temporary_users";



ALTER TABLE "public"."temporary_users" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "tenant_admin_manage_users_policy" ON "public"."tenant_users" USING ((("tenant_id" = "public"."get_current_tenant_id"()) AND (EXISTS ( SELECT 1
   FROM "public"."tenant_users" "tu"
  WHERE (("tu"."tenant_id" = "tu"."tenant_id") AND ("tu"."user_id" = "auth"."uid"()) AND ("tu"."role" = ANY (ARRAY['owner'::"public"."tenant_user_role", 'admin'::"public"."tenant_user_role"])))))));



ALTER TABLE "public"."tenant_databases" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "tenant_isolation_policy" ON "public"."chat_rooms" USING (("tenant_id" = "public"."get_current_tenant_id"()));



CREATE POLICY "tenant_isolation_policy" ON "public"."hotels" USING (("tenant_id" = "public"."get_current_tenant_id"()));



CREATE POLICY "tenant_isolation_policy" ON "public"."organizations" USING (("tenant_id" = "public"."get_current_tenant_id"()));



CREATE POLICY "tenant_isolation_policy" ON "public"."qr_codes" USING (("tenant_id" = "public"."get_current_tenant_id"()));



ALTER TABLE "public"."tenant_licenses" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."tenant_users" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "tenant_users_access_policy" ON "public"."tenant_users" USING ((("tenant_id" = "public"."get_current_tenant_id"()) OR ("user_id" = "auth"."uid"())));



ALTER TABLE "public"."tenants" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_presence" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "user_presence_insert_own" ON "public"."user_presence" FOR INSERT TO "authenticated" WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "user_presence_select_all" ON "public"."user_presence" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "user_presence_update_own" ON "public"."user_presence" FOR UPDATE TO "authenticated" USING (("user_id" = "auth"."uid"()));



ALTER TABLE "public"."user_profiles" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "user_update_own_profile" ON "public"."user_profiles" FOR UPDATE TO "authenticated" USING (("id" = "auth"."uid"()));



CREATE POLICY "user_view_own_profile" ON "public"."user_profiles" FOR SELECT TO "authenticated" USING (("id" = "auth"."uid"()));



CREATE POLICY "view_own_tenant_policy" ON "public"."tenants" USING (("id" IN ( SELECT "tenant_users"."tenant_id"
   FROM "public"."tenant_users"
  WHERE ("tenant_users"."user_id" = "auth"."uid"()))));





ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "service_role";











































































































































































GRANT ALL ON FUNCTION "public"."add_user_to_tenant"("tenant_id" "uuid", "email" "text", "role" "text", "is_primary_tenant" boolean, "permissions" "jsonb", "expiry_date" timestamp with time zone) TO "anon";
GRANT ALL ON FUNCTION "public"."add_user_to_tenant"("tenant_id" "uuid", "email" "text", "role" "text", "is_primary_tenant" boolean, "permissions" "jsonb", "expiry_date" timestamp with time zone) TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_user_to_tenant"("tenant_id" "uuid", "email" "text", "role" "text", "is_primary_tenant" boolean, "permissions" "jsonb", "expiry_date" timestamp with time zone) TO "service_role";



GRANT ALL ON FUNCTION "public"."check_tenant_license_validity"("tenant_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."check_tenant_license_validity"("tenant_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_tenant_license_validity"("tenant_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_location_qr_code"("p_location_id" "uuid", "p_title" character varying, "p_description" "text", "p_creator_id" "uuid", "p_organization_id" "uuid", "p_access_type" character varying, "p_expire_at" timestamp with time zone, "p_usage_limit" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."create_location_qr_code"("p_location_id" "uuid", "p_title" character varying, "p_description" "text", "p_creator_id" "uuid", "p_organization_id" "uuid", "p_access_type" character varying, "p_expire_at" timestamp with time zone, "p_usage_limit" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_location_qr_code"("p_location_id" "uuid", "p_title" character varying, "p_description" "text", "p_creator_id" "uuid", "p_organization_id" "uuid", "p_access_type" character varying, "p_expire_at" timestamp with time zone, "p_usage_limit" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."create_tenant"("name" "text", "domain" "text", "contact_email" "text", "contact_phone" "text", "address" "text", "logo_url" "text", "primary_color" "text", "metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."create_tenant"("name" "text", "domain" "text", "contact_email" "text", "contact_phone" "text", "address" "text", "logo_url" "text", "primary_color" "text", "metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_tenant"("name" "text", "domain" "text", "contact_email" "text", "contact_phone" "text", "address" "text", "logo_url" "text", "primary_color" "text", "metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_tenant_database_on_tenant_create"() TO "anon";
GRANT ALL ON FUNCTION "public"."create_tenant_database_on_tenant_create"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_tenant_database_on_tenant_create"() TO "service_role";



GRANT ALL ON FUNCTION "public"."create_tenant_license"("tenant_id" "uuid", "license_type" "text", "user_limit" integer, "storage_limit" bigint, "features" "jsonb", "starts_at" timestamp with time zone, "expires_at" timestamp with time zone, "payment_status" "text", "payment_method" "text", "billing_cycle" "text", "price" numeric, "currency" "text", "metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."create_tenant_license"("tenant_id" "uuid", "license_type" "text", "user_limit" integer, "storage_limit" bigint, "features" "jsonb", "starts_at" timestamp with time zone, "expires_at" timestamp with time zone, "payment_status" "text", "payment_method" "text", "billing_cycle" "text", "price" numeric, "currency" "text", "metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_tenant_license"("tenant_id" "uuid", "license_type" "text", "user_limit" integer, "storage_limit" bigint, "features" "jsonb", "starts_at" timestamp with time zone, "expires_at" timestamp with time zone, "payment_status" "text", "payment_method" "text", "billing_cycle" "text", "price" numeric, "currency" "text", "metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_tenant_schema"() TO "anon";
GRANT ALL ON FUNCTION "public"."create_tenant_schema"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_tenant_schema"() TO "service_role";



GRANT ALL ON FUNCTION "public"."create_user_qr_code"() TO "anon";
GRANT ALL ON FUNCTION "public"."create_user_qr_code"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_user_qr_code"() TO "service_role";



GRANT ALL ON FUNCTION "public"."delete_tenant"("tenant_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."delete_tenant"("tenant_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."delete_tenant"("tenant_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."generate_tenant_schema_name"() TO "anon";
GRANT ALL ON FUNCTION "public"."generate_tenant_schema_name"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."generate_tenant_schema_name"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_current_tenant_id"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_current_tenant_id"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_current_tenant_id"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_current_tenant_info"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_current_tenant_info"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_current_tenant_info"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_my_tenants"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_my_tenants"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_my_tenants"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_tenant_details"("tenant_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_tenant_details"("tenant_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_tenant_details"("tenant_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_tenant_licenses"("tenant_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_tenant_licenses"("tenant_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_tenant_licenses"("tenant_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_tenant_users"("tenant_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_tenant_users"("tenant_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_tenant_users"("tenant_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_unread_message_count"("p_user_id" "uuid", "p_room_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_unread_message_count"("p_user_id" "uuid", "p_room_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_unread_message_count"("p_user_id" "uuid", "p_room_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."has_tenant_permission"("permission_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."has_tenant_permission"("permission_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_tenant_permission"("permission_name" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."manage_automatic_translations"() TO "anon";
GRANT ALL ON FUNCTION "public"."manage_automatic_translations"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."manage_automatic_translations"() TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_message_as_read_for_sender"() TO "anon";
GRANT ALL ON FUNCTION "public"."mark_message_as_read_for_sender"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."mark_message_as_read_for_sender"() TO "service_role";



GRANT ALL ON FUNCTION "public"."process_qr_code_scan"("p_qr_code_id" "uuid", "p_user_id" "uuid", "p_anonymous_id" character varying, "p_ip_address" character varying, "p_user_agent" "text", "p_device_info" "jsonb", "p_location_info" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."process_qr_code_scan"("p_qr_code_id" "uuid", "p_user_id" "uuid", "p_anonymous_id" character varying, "p_ip_address" character varying, "p_user_agent" "text", "p_device_info" "jsonb", "p_location_info" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."process_qr_code_scan"("p_qr_code_id" "uuid", "p_user_id" "uuid", "p_anonymous_id" character varying, "p_ip_address" character varying, "p_user_agent" "text", "p_device_info" "jsonb", "p_location_info" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."remove_user_from_tenant"("tenant_id" "uuid", "user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."remove_user_from_tenant"("tenant_id" "uuid", "user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."remove_user_from_tenant"("tenant_id" "uuid", "user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."set_current_tenant"("tenant_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."set_current_tenant"("tenant_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_current_tenant"("tenant_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "postgres";
GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "anon";
GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "service_role";



GRANT ALL ON FUNCTION "public"."set_organization_id_for_chat_room"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_organization_id_for_chat_room"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_organization_id_for_chat_room"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_tenant_id_for_chat_room"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_tenant_id_for_chat_room"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_tenant_id_for_chat_room"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_tenant_id_for_hotel"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_tenant_id_for_hotel"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_tenant_id_for_hotel"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_tenant_id_for_organization"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_tenant_id_for_organization"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_tenant_id_for_organization"() TO "service_role";



GRANT ALL ON FUNCTION "public"."set_tenant_id_for_qr_code"() TO "anon";
GRANT ALL ON FUNCTION "public"."set_tenant_id_for_qr_code"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_tenant_id_for_qr_code"() TO "service_role";



GRANT ALL ON FUNCTION "public"."show_limit"() TO "postgres";
GRANT ALL ON FUNCTION "public"."show_limit"() TO "anon";
GRANT ALL ON FUNCTION "public"."show_limit"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."show_limit"() TO "service_role";



GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "postgres";
GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "anon";
GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "service_role";



GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."switch_tenant"("new_tenant_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."switch_tenant"("new_tenant_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."switch_tenant"("new_tenant_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_room_last_activity"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_room_last_activity"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_room_last_activity"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_tenant"("tenant_id" "uuid", "name" "text", "domain" "text", "contact_email" "text", "contact_phone" "text", "address" "text", "logo_url" "text", "primary_color" "text", "is_active" boolean, "metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."update_tenant"("tenant_id" "uuid", "name" "text", "domain" "text", "contact_email" "text", "contact_phone" "text", "address" "text", "logo_url" "text", "primary_color" "text", "is_active" boolean, "metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_tenant"("tenant_id" "uuid", "name" "text", "domain" "text", "contact_email" "text", "contact_phone" "text", "address" "text", "logo_url" "text", "primary_color" "text", "is_active" boolean, "metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_tenant_databases_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_tenant_databases_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_tenant_databases_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_tenant_license"("license_id" "uuid", "license_type" "text", "user_limit" integer, "storage_limit" bigint, "features" "jsonb", "expires_at" timestamp with time zone, "is_active" boolean, "payment_status" "text", "payment_method" "text", "billing_cycle" "text", "price" numeric, "currency" "text", "last_payment_date" timestamp with time zone, "metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."update_tenant_license"("license_id" "uuid", "license_type" "text", "user_limit" integer, "storage_limit" bigint, "features" "jsonb", "expires_at" timestamp with time zone, "is_active" boolean, "payment_status" "text", "payment_method" "text", "billing_cycle" "text", "price" numeric, "currency" "text", "last_payment_date" timestamp with time zone, "metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_tenant_license"("license_id" "uuid", "license_type" "text", "user_limit" integer, "storage_limit" bigint, "features" "jsonb", "expires_at" timestamp with time zone, "is_active" boolean, "payment_status" "text", "payment_method" "text", "billing_cycle" "text", "price" numeric, "currency" "text", "last_payment_date" timestamp with time zone, "metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_tenant_licenses_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_tenant_licenses_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_tenant_licenses_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_tenant_user_login_time"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_tenant_user_login_time"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_tenant_user_login_time"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_tenant_users_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_tenant_users_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_tenant_users_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_tenants_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_tenants_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_tenants_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_timestamp"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_timestamp"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_timestamp"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";



GRANT ALL ON TABLE "public"."user_presence" TO "anon";
GRANT ALL ON TABLE "public"."user_presence" TO "authenticated";
GRANT ALL ON TABLE "public"."user_presence" TO "service_role";



GRANT ALL ON FUNCTION "public"."update_user_presence"("p_user_id" "uuid", "p_status" character varying, "p_room_id" "uuid", "p_device_info" "jsonb", "p_ip_address" character varying) TO "anon";
GRANT ALL ON FUNCTION "public"."update_user_presence"("p_user_id" "uuid", "p_status" character varying, "p_room_id" "uuid", "p_device_info" "jsonb", "p_ip_address" character varying) TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_user_presence"("p_user_id" "uuid", "p_status" character varying, "p_room_id" "uuid", "p_device_info" "jsonb", "p_ip_address" character varying) TO "service_role";



GRANT ALL ON FUNCTION "public"."update_user_role_in_tenant"("tenant_id" "uuid", "user_id" "uuid", "role" "text", "permissions" "jsonb", "is_primary_tenant" boolean, "is_active" boolean, "expiry_date" timestamp with time zone) TO "anon";
GRANT ALL ON FUNCTION "public"."update_user_role_in_tenant"("tenant_id" "uuid", "user_id" "uuid", "role" "text", "permissions" "jsonb", "is_primary_tenant" boolean, "is_active" boolean, "expiry_date" timestamp with time zone) TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_user_role_in_tenant"("tenant_id" "uuid", "user_id" "uuid", "role" "text", "permissions" "jsonb", "is_primary_tenant" boolean, "is_active" boolean, "expiry_date" timestamp with time zone) TO "service_role";



GRANT ALL ON FUNCTION "public"."update_users_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_users_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_users_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "service_role";


















GRANT ALL ON TABLE "public"."chat_messages" TO "anon";
GRANT ALL ON TABLE "public"."chat_messages" TO "authenticated";
GRANT ALL ON TABLE "public"."chat_messages" TO "service_role";



GRANT ALL ON TABLE "public"."chat_participants" TO "anon";
GRANT ALL ON TABLE "public"."chat_participants" TO "authenticated";
GRANT ALL ON TABLE "public"."chat_participants" TO "service_role";



GRANT ALL ON TABLE "public"."chat_rooms" TO "anon";
GRANT ALL ON TABLE "public"."chat_rooms" TO "authenticated";
GRANT ALL ON TABLE "public"."chat_rooms" TO "service_role";



GRANT ALL ON TABLE "public"."hotel_rooms" TO "anon";
GRANT ALL ON TABLE "public"."hotel_rooms" TO "authenticated";
GRANT ALL ON TABLE "public"."hotel_rooms" TO "service_role";



GRANT ALL ON TABLE "public"."hotels" TO "anon";
GRANT ALL ON TABLE "public"."hotels" TO "authenticated";
GRANT ALL ON TABLE "public"."hotels" TO "service_role";



GRANT ALL ON TABLE "public"."latest_chat_messages" TO "anon";
GRANT ALL ON TABLE "public"."latest_chat_messages" TO "authenticated";
GRANT ALL ON TABLE "public"."latest_chat_messages" TO "service_role";



GRANT ALL ON TABLE "public"."locations" TO "anon";
GRANT ALL ON TABLE "public"."locations" TO "authenticated";
GRANT ALL ON TABLE "public"."locations" TO "service_role";



GRANT ALL ON TABLE "public"."message_attachments" TO "anon";
GRANT ALL ON TABLE "public"."message_attachments" TO "authenticated";
GRANT ALL ON TABLE "public"."message_attachments" TO "service_role";



GRANT ALL ON TABLE "public"."message_translations" TO "anon";
GRANT ALL ON TABLE "public"."message_translations" TO "authenticated";
GRANT ALL ON TABLE "public"."message_translations" TO "service_role";



GRANT ALL ON TABLE "public"."message_details" TO "anon";
GRANT ALL ON TABLE "public"."message_details" TO "authenticated";
GRANT ALL ON TABLE "public"."message_details" TO "service_role";



GRANT ALL ON TABLE "public"."message_drafts" TO "anon";
GRANT ALL ON TABLE "public"."message_drafts" TO "authenticated";
GRANT ALL ON TABLE "public"."message_drafts" TO "service_role";



GRANT ALL ON TABLE "public"."message_reactions" TO "anon";
GRANT ALL ON TABLE "public"."message_reactions" TO "authenticated";
GRANT ALL ON TABLE "public"."message_reactions" TO "service_role";



GRANT ALL ON TABLE "public"."message_read_overview" TO "anon";
GRANT ALL ON TABLE "public"."message_read_overview" TO "authenticated";
GRANT ALL ON TABLE "public"."message_read_overview" TO "service_role";



GRANT ALL ON TABLE "public"."message_read_status" TO "anon";
GRANT ALL ON TABLE "public"."message_read_status" TO "authenticated";
GRANT ALL ON TABLE "public"."message_read_status" TO "service_role";



GRANT ALL ON TABLE "public"."message_status" TO "anon";
GRANT ALL ON TABLE "public"."message_status" TO "authenticated";
GRANT ALL ON TABLE "public"."message_status" TO "service_role";



GRANT ALL ON TABLE "public"."tenant_users" TO "anon";
GRANT ALL ON TABLE "public"."tenant_users" TO "authenticated";
GRANT ALL ON TABLE "public"."tenant_users" TO "service_role";



GRANT ALL ON TABLE "public"."tenants" TO "anon";
GRANT ALL ON TABLE "public"."tenants" TO "authenticated";
GRANT ALL ON TABLE "public"."tenants" TO "service_role";



GRANT ALL ON TABLE "public"."my_tenants" TO "anon";
GRANT ALL ON TABLE "public"."my_tenants" TO "authenticated";
GRANT ALL ON TABLE "public"."my_tenants" TO "service_role";



GRANT ALL ON TABLE "public"."organization_member_roles" TO "anon";
GRANT ALL ON TABLE "public"."organization_member_roles" TO "authenticated";
GRANT ALL ON TABLE "public"."organization_member_roles" TO "service_role";



GRANT ALL ON TABLE "public"."organization_members" TO "anon";
GRANT ALL ON TABLE "public"."organization_members" TO "authenticated";
GRANT ALL ON TABLE "public"."organization_members" TO "service_role";



GRANT ALL ON TABLE "public"."organization_role_permissions" TO "anon";
GRANT ALL ON TABLE "public"."organization_role_permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."organization_role_permissions" TO "service_role";



GRANT ALL ON TABLE "public"."organization_roles" TO "anon";
GRANT ALL ON TABLE "public"."organization_roles" TO "authenticated";
GRANT ALL ON TABLE "public"."organization_roles" TO "service_role";



GRANT ALL ON TABLE "public"."organizations" TO "anon";
GRANT ALL ON TABLE "public"."organizations" TO "authenticated";
GRANT ALL ON TABLE "public"."organizations" TO "service_role";



GRANT ALL ON TABLE "public"."orphaned_user_roles" TO "anon";
GRANT ALL ON TABLE "public"."orphaned_user_roles" TO "authenticated";
GRANT ALL ON TABLE "public"."orphaned_user_roles" TO "service_role";



GRANT ALL ON TABLE "public"."permissions" TO "anon";
GRANT ALL ON TABLE "public"."permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."permissions" TO "service_role";



GRANT ALL ON TABLE "public"."qr_code_scans" TO "anon";
GRANT ALL ON TABLE "public"."qr_code_scans" TO "authenticated";
GRANT ALL ON TABLE "public"."qr_code_scans" TO "service_role";



GRANT ALL ON TABLE "public"."qr_codes" TO "anon";
GRANT ALL ON TABLE "public"."qr_codes" TO "authenticated";
GRANT ALL ON TABLE "public"."qr_codes" TO "service_role";



GRANT ALL ON TABLE "public"."role_permissions" TO "anon";
GRANT ALL ON TABLE "public"."role_permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."role_permissions" TO "service_role";



GRANT ALL ON TABLE "public"."roles" TO "anon";
GRANT ALL ON TABLE "public"."roles" TO "authenticated";
GRANT ALL ON TABLE "public"."roles" TO "service_role";



GRANT ALL ON TABLE "public"."room_active_users" TO "anon";
GRANT ALL ON TABLE "public"."room_active_users" TO "authenticated";
GRANT ALL ON TABLE "public"."room_active_users" TO "service_role";



GRANT ALL ON TABLE "public"."temporary_users" TO "anon";
GRANT ALL ON TABLE "public"."temporary_users" TO "authenticated";
GRANT ALL ON TABLE "public"."temporary_users" TO "service_role";



GRANT ALL ON TABLE "public"."tenant_databases" TO "anon";
GRANT ALL ON TABLE "public"."tenant_databases" TO "authenticated";
GRANT ALL ON TABLE "public"."tenant_databases" TO "service_role";



GRANT ALL ON TABLE "public"."tenant_licenses" TO "anon";
GRANT ALL ON TABLE "public"."tenant_licenses" TO "authenticated";
GRANT ALL ON TABLE "public"."tenant_licenses" TO "service_role";



GRANT ALL ON TABLE "public"."translation_cache" TO "anon";
GRANT ALL ON TABLE "public"."translation_cache" TO "authenticated";
GRANT ALL ON TABLE "public"."translation_cache" TO "service_role";



GRANT ALL ON TABLE "public"."user_profiles" TO "anon";
GRANT ALL ON TABLE "public"."user_profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_profiles" TO "service_role";



GRANT ALL ON TABLE "public"."user_details" TO "anon";
GRANT ALL ON TABLE "public"."user_details" TO "authenticated";
GRANT ALL ON TABLE "public"."user_details" TO "service_role";



GRANT ALL ON TABLE "public"."user_orphans" TO "anon";
GRANT ALL ON TABLE "public"."user_orphans" TO "authenticated";
GRANT ALL ON TABLE "public"."user_orphans" TO "service_role";



GRANT ALL ON TABLE "public"."user_profiles_temp" TO "anon";
GRANT ALL ON TABLE "public"."user_profiles_temp" TO "authenticated";
GRANT ALL ON TABLE "public"."user_profiles_temp" TO "service_role";



GRANT ALL ON TABLE "public"."user_roles" TO "anon";
GRANT ALL ON TABLE "public"."user_roles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_roles" TO "service_role";



GRANT ALL ON TABLE "public"."user_tenant_identity" TO "anon";
GRANT ALL ON TABLE "public"."user_tenant_identity" TO "authenticated";
GRANT ALL ON TABLE "public"."user_tenant_identity" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
