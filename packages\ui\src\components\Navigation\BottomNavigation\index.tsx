import React from 'react';

export interface BottomNavItem {
  /**
   * Item label
   */
  label: string;
  /**
   * Item URL or path
   */
  href: string;
  /**
   * Icon for the item
   */
  icon: React.ReactNode;
  /**
   * Is this the active item
   */
  active?: boolean;
}

export interface BottomNavigationProps {
  /**
   * Navigation items
   */
  items: BottomNavItem[];
  /**
   * Theme variant
   */
  variant?: 'light' | 'dark' | 'studio';
  /**
   * Optional shadow
   */
  shadow?: boolean;
  /**
   * Whether to center the label
   */
  centerLabel?: boolean;
  /**
   * Additional CSS properties
   */
  style?: React.CSSProperties;
  /**
   * Optional CSS class name
   */
  className?: string;
}

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  items,
  variant = 'light',
  shadow = true,
  centerLabel = true,
  style,
  className,
  ...props
}) => {
  // Theme-based colors
  const themeColors = {
    light: {
      background: '#FFFFFF',
      text: '#7D8491', // Slate Gray
      activeText: '#FF4D00', // Aerospace Orange
      border: '#EBEBEB',
    },
    dark: {
      background: '#161616', // Night
      text: '#7D8491', // Slate Gray
      activeText: '#F9F871', // Icterine
      border: '#1E1E1E', // Eerie Black
    },
    studio: {
      background: '#16262E', // Gunmetal
      text: '#7D8491', // Slate Gray
      activeText: '#FF4D00', // Aerospace Orange
      border: '#2E4756', // Charcoal
    },
  };

  // Container style
  const containerStyle: React.CSSProperties = {
    position: 'fixed',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    backgroundColor: themeColors[variant].background,
    borderTop: `1px solid ${themeColors[variant].border}`,
    boxShadow: shadow ? '0 -2px 4px rgba(0,0,0,0.08)' : 'none',
    ...style,
  };

  // Nav style
  const navStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: '8px 0',
    maxWidth: '600px',
    margin: '0 auto',
  };

  // Item style
  const getItemStyle = (active: boolean = false): React.CSSProperties => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: centerLabel ? 'center' : 'flex-start',
    gap: '4px',
    color: active ? themeColors[variant].activeText : themeColors[variant].text,
    textDecoration: 'none',
    padding: '8px 12px',
    borderRadius: '4px',
    transition: 'all 0.2s ease',
    flex: 1,
    textAlign: 'center',
  });

  // Icon style
  const iconStyle: React.CSSProperties = {
    fontSize: '24px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  };

  // Label style
  const labelStyle: React.CSSProperties = {
    fontSize: '12px',
    fontWeight: 500,
  };

  return (
    <div style={containerStyle} className={className} {...props}>
      <nav style={navStyle}>
        {items.map((item, index) => (
          <a
            key={index}
            href={item.href}
            style={getItemStyle(item.active)}
          >
            <span style={iconStyle}>{item.icon}</span>
            <span style={labelStyle}>{item.label}</span>
          </a>
        ))}
      </nav>
    </div>
  );
};

export default BottomNavigation;
