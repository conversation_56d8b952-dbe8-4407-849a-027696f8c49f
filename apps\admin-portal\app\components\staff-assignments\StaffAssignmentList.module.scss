.container {
  display: flex;
  flex-direction: column;
}

.tableWrapper {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 0.75rem 1rem;
    text-align: left;
  }

  th {
    background-color: #f9fafb;
    font-weight: 500;
    color: #4b5563;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    border-bottom: 1px solid #e5e7eb;
  }

  tr {
    border-bottom: 1px solid #e5e7eb;

    &:last-child {
      border-bottom: none;
    }
  }

  td {
    font-size: 0.875rem;
    color: #111827;
  }
}

.userCell {
  min-width: 200px;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  background-color: #e5e7eb;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.avatarPlaceholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: 600;
  color: #4b5563;
  background-color: #e5e7eb;
  text-transform: uppercase;
}

.userName {
  font-weight: 500;
  color: #111827;
}

.userEmail {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.125rem;
}

.priorityBadge {
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #4b5563;
  display: inline-block;
  text-align: center;
  min-width: 1.5rem;
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.125rem 0.625rem;
  font-size: 0.75rem;
  font-weight: 500;
  
  &::before {
    content: '';
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    margin-right: 0.375rem;
  }

  &.active {
    background-color: #d1fae5;
    color: #065f46;
    
    &::before {
      background-color: #10b981;
    }
  }

  &.inactive {
    background-color: #f3f4f6;
    color: #4b5563;
    
    &::before {
      background-color: #9ca3af;
    }
  }
}

.actions {
  display: flex;
  gap: 0.5rem;
  min-width: 120px;
}

.editButton, .deleteButton {
  padding: 0.375rem 0.625rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  font-weight: 500;
  cursor: pointer;
}

.editButton {
  color: #3b82f6;
  background-color: #eff6ff;
  border: 1px solid #dbeafe;
  text-decoration: none;
  
  &:hover {
    background-color: #dbeafe;
  }
}

.deleteButton {
  color: #ef4444;
  background-color: #fef2f2;
  border: 1px solid #fee2e2;
  
  &:hover {
    background-color: #fee2e2;
  }
}

.emptyState {
  padding: 3rem;
  text-align: center;
  background-color: white;
  
  h3 {
    font-size: 1rem;
    font-weight: 500;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #6b7280;
  }
}

.loading {
  padding: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  p {
    color: #6b7280;
  }
}

.error {
  padding: 2rem;
  background-color: #fef2f2;
  color: #b91c1c;
  border-radius: 0.375rem;
  text-align: center;
  
  p {
    margin-bottom: 1rem;
  }
  
  .retryButton {
    background-color: #b91c1c;
    color: white;
    border: none;
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    
    &:hover {
      background-color: #991b1b;
    }
  }
}

.paginationContainer {
  padding: 1rem;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e5e7eb;
}