import { NextRequest, NextResponse } from 'next/server';
import { LicenseService } from '../../services/LicenseService';

// API không yêu cầu xác thực để client on-premise có thể gọi
export async function POST(request: NextRequest) {
  try {
    const { licenseKey, hardwareFingerprint, ipAddress } = await request.json();
    
    // Validate input
    if (!licenseKey || !hardwareFingerprint) {
      return NextResponse.json(
        { error: 'License key and hardware fingerprint are required' },
        { status: 400 }
      );
    }
    
    // Gọi service để check-in hoặc kích hoạt license
    let result;
    
    if (request.nextUrl.searchParams.get('action') === 'activate') {
      // Kích hoạt license
      result = await LicenseService.activateLicense(
        licenseKey, 
        hardwareFingerprint,
        ipAddress || request.ip || '0.0.0.0'
      );
    } else {
      // Check-in license
      result = await LicenseService.checkInLicense(
        licenseKey, 
        hardwareFingerprint,
        ipAddress || request.ip || '0.0.0.0'
      );
    }
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('License check error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process license' },
      { status: 500 }
    );
  }
}