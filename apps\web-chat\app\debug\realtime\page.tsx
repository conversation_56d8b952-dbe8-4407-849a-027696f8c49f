'use client';

/**
 * Supabase Realtime Debugger
 * Test and debug Supabase Realtime connections
 */

import React, { useState, useEffect, useRef } from 'react';
import { createClientSupabase } from '@/lib/supabase';

export default function RealtimeDebugger() {
  const [logs, setLogs] = useState<string[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [subscriptionStatus, setSubscriptionStatus] = useState<string>('Not started');
  const [testMessage, setTestMessage] = useState('');
  const [tenantId, setTenantId] = useState('');
  
  const supabaseRef = useRef<any>(null);
  const channelRef = useRef<any>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[Realtime Debug] ${message}`);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  // Initialize Supabase
  const initializeSupabase = () => {
    try {
      supabaseRef.current = createClientSupabase();
      setIsConnected(true);
      addLog('✅ Supabase client initialized');
      addLog(`📡 URL: ${supabaseRef.current.supabaseUrl}`);
      addLog(`🔑 Key: ${supabaseRef.current.supabaseKey.substring(0, 20)}...`);
    } catch (err) {
      addLog(`❌ Failed to initialize Supabase: ${err}`);
      setIsConnected(false);
    }
  };

  // Test basic query
  const testBasicQuery = async () => {
    if (!supabaseRef.current) {
      addLog('❌ Supabase not initialized');
      return;
    }

    try {
      addLog('🔍 Testing basic query...');
      const { data, error } = await supabaseRef.current
        .from('tenant_chat_messages')
        .select('id, content, created_at')
        .limit(5);

      if (error) {
        addLog(`❌ Query error: ${error.message}`);
      } else {
        addLog(`✅ Query successful: ${data?.length || 0} messages found`);
        if (data && data.length > 0) {
          addLog(`📄 Sample message: ${data[0].id}`);
        }
      }
    } catch (err) {
      addLog(`❌ Query exception: ${err}`);
    }
  };

  // Test realtime subscription
  const testRealtimeSubscription = () => {
    if (!supabaseRef.current) {
      addLog('❌ Supabase not initialized');
      return;
    }

    if (channelRef.current) {
      addLog('🔄 Unsubscribing existing channel...');
      channelRef.current.unsubscribe();
    }

    addLog('🔄 Setting up realtime subscription...');
    
    const filter = tenantId ? `tenant_id=eq.${tenantId}` : undefined;
    addLog(`🎯 Filter: ${filter || 'No filter (all messages)'}`);

    channelRef.current = supabaseRef.current
      .channel('debug-messages')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'tenant_chat_messages',
          ...(filter && { filter })
        },
        (payload: any) => {
          addLog(`🔔 REALTIME MESSAGE RECEIVED!`);
          addLog(`📄 Message ID: ${payload.new.id}`);
          addLog(`👤 Sender: ${payload.new.sender_type} (${payload.new.sender_name})`);
          addLog(`💬 Content: ${payload.new.content?.substring(0, 50)}...`);
          addLog(`🏢 Tenant: ${payload.new.tenant_id}`);
          addLog(`⏰ Time: ${payload.new.created_at}`);
        }
      )
      .subscribe((status: string) => {
        setSubscriptionStatus(status);
        addLog(`📡 Subscription status: ${status}`);
        
        if (status === 'SUBSCRIBED') {
          addLog('✅ REALTIME SUBSCRIPTION ACTIVE!');
        } else if (status === 'CHANNEL_ERROR') {
          addLog('❌ REALTIME SUBSCRIPTION FAILED!');
        } else if (status === 'CLOSED') {
          addLog('⚠️ REALTIME SUBSCRIPTION CLOSED!');
        }
      });
  };

  // Create test session first
  const createTestSession = async () => {
    if (!tenantId.trim()) {
      addLog('❌ Please enter tenant ID first');
      return null;
    }

    try {
      addLog('🔄 Creating test session...');

      // Create a test session directly in database
      const { data: session, error } = await supabaseRef.current
        .from('tenant_chat_sessions')
        .insert({
          tenant_id: tenantId,
          guest_language: 'en',
          status: 'active',
          priority: 'normal',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        addLog(`❌ Failed to create session: ${error.message}`);
        return null;
      }

      addLog(`✅ Test session created: ${session.id}`);
      return session.id;
    } catch (err) {
      addLog(`❌ Create session error: ${err}`);
      return null;
    }
  };

  // Send test message
  const sendTestMessage = async () => {
    if (!testMessage.trim() || !tenantId.trim()) {
      addLog('❌ Please enter both tenant ID and test message');
      return;
    }

    try {
      // Create session first
      const sessionId = await createTestSession();
      if (!sessionId) {
        addLog('❌ Cannot send message without valid session');
        return;
      }

      addLog(`📤 Sending test message: "${testMessage}" to session: ${sessionId}`);

      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: sessionId,
          sender_type: 'staff',
          sender_name: 'Debug Tester',
          content: testMessage
        }),
      });

      if (response.ok) {
        const data = await response.json();
        addLog(`✅ Message sent successfully: ${data.message?.id}`);
        addLog(`🎯 Now watch for realtime event above!`);
        setTestMessage('');
      } else {
        const errorData = await response.json();
        addLog(`❌ Failed to send message: ${response.status} - ${errorData.error}`);
      }
    } catch (err) {
      addLog(`❌ Send message error: ${err}`);
    }
  };

  // Check realtime setup
  const checkRealtimeSetup = async () => {
    try {
      addLog('🔍 Checking realtime setup...');
      
      const response = await fetch('/api/realtime/setup');
      if (response.ok) {
        const data = await response.json();
        addLog(`📊 Realtime status: ${data.realtime_ready ? 'READY' : 'NOT READY'}`);
        
        if (data.tables_status) {
          data.tables_status.forEach((table: any) => {
            addLog(`📋 ${table.table}: ${table.realtime_enabled ? '✅' : '❌'}`);
          });
        }
        
        if (data.recommendations) {
          addLog(`💡 ${data.recommendations}`);
        }
      } else {
        addLog(`❌ Failed to check realtime setup: ${response.status}`);
      }
    } catch (err) {
      addLog(`❌ Realtime setup check error: ${err}`);
    }
  };

  // Setup realtime
  const setupRealtime = async () => {
    try {
      addLog('🔧 Setting up realtime...');
      
      const response = await fetch('/api/realtime/setup', { method: 'POST' });
      if (response.ok) {
        const data = await response.json();
        addLog(`✅ Realtime setup completed`);
        
        if (data.results) {
          data.results.forEach((result: any) => {
            addLog(`📋 ${result.table}: ${result.realtime_enabled ? '✅' : '❌'}`);
          });
        }
      } else {
        addLog(`❌ Failed to setup realtime: ${response.status}`);
      }
    } catch (err) {
      addLog(`❌ Realtime setup error: ${err}`);
    }
  };

  useEffect(() => {
    // Get tenant ID from localStorage if available
    const userData = localStorage.getItem('staff_user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        setTenantId(user.tenant_id || '');
      } catch (err) {
        // Ignore
      }
    }
  }, []);

  return (
    <div style={{ padding: '2rem', fontFamily: 'monospace' }}>
      <h1>🔍 Supabase Realtime Debugger</h1>
      
      {/* Controls */}
      <div style={{ marginBottom: '2rem', display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
        <button onClick={initializeSupabase} disabled={isConnected}>
          Initialize Supabase
        </button>
        <button onClick={testBasicQuery} disabled={!isConnected}>
          Test Basic Query
        </button>
        <button onClick={checkRealtimeSetup}>
          Check Realtime Setup
        </button>
        <button onClick={setupRealtime}>
          Setup Realtime
        </button>
        <button onClick={testRealtimeSubscription} disabled={!isConnected}>
          Start Realtime Test
        </button>
        <button onClick={createTestSession} disabled={!isConnected || !tenantId.trim()}>
          Create Test Session
        </button>
        <button onClick={clearLogs}>
          Clear Logs
        </button>
      </div>

      {/* Status */}
      <div style={{ marginBottom: '2rem', padding: '1rem', background: '#f5f5f5', borderRadius: '4px' }}>
        <div>🔗 Supabase Connected: {isConnected ? '✅' : '❌'}</div>
        <div>📡 Subscription Status: {subscriptionStatus}</div>
      </div>

      {/* Test Message */}
      <div style={{ marginBottom: '2rem' }}>
        <h3>📤 Send Test Message</h3>
        <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
          <input
            type="text"
            placeholder="Tenant ID"
            value={tenantId}
            onChange={(e) => setTenantId(e.target.value)}
            style={{ padding: '0.5rem', width: '200px' }}
          />
          <input
            type="text"
            placeholder="Test message"
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            style={{ padding: '0.5rem', flex: 1 }}
          />
          <button onClick={sendTestMessage}>
            Send Test Message
          </button>
        </div>
      </div>

      {/* Logs */}
      <div>
        <h3>📋 Debug Logs</h3>
        <div style={{ 
          background: '#000', 
          color: '#0f0', 
          padding: '1rem', 
          height: '400px', 
          overflow: 'auto',
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          {logs.map((log, index) => (
            <div key={index}>{log}</div>
          ))}
          {logs.length === 0 && (
            <div style={{ color: '#666' }}>No logs yet. Click "Initialize Supabase" to start.</div>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div style={{ marginTop: '2rem', padding: '1rem', background: '#e8f4fd', borderRadius: '4px' }}>
        <h3>📖 Instructions</h3>
        <ol>
          <li><strong>Initialize Supabase</strong> - Connect to Supabase</li>
          <li><strong>Test Basic Query</strong> - Verify database access</li>
          <li><strong>Check Realtime Setup</strong> - See if realtime is configured</li>
          <li><strong>Setup Realtime</strong> - Configure realtime if needed</li>
          <li><strong>Start Realtime Test</strong> - Subscribe to message changes</li>
          <li><strong>Send Test Message</strong> - Trigger a realtime event</li>
        </ol>
        <p><strong>Expected:</strong> When you send a test message, you should see "🔔 REALTIME MESSAGE RECEIVED!" in the logs within 1-2 seconds.</p>
      </div>
    </div>
  );
}
