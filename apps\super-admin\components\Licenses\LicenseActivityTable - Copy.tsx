import React, { useState } from 'react';
import { Table, Card } from '@loaloa/ui';
import useSWR from 'swr';
import styles from './LicenseActivityTable.module.scss';

interface LicenseActivity {
  id: string;
  license_id: string;
  activity_type: 'ACTIVATION' | 'CHECK_IN' | 'WARNING' | 'VIOLATION' | 'REVOCATION';
  hardware_fingerprint?: string;
  ip_address?: string;
  timestamp: string;
  details: any;
}

interface LicenseActivityTableProps {
  licenseId: string;
}

const LicenseActivityTable: React.FC<LicenseActivityTableProps> = ({ licenseId }) => {
  const [page, setPage] = useState(1);
  const limit = 10;
  
  // Fetch activities
  const { data, error, isLoading } = useSWR(
    `/api/licenses/${licenseId}/activities?page=${page}&limit=${limit}`, 
    async (url) => {
      const response = await fetch(url);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch activities');
      }
      return response.json();
    }
  );
  
  if (isLoading) {
    return <div className={styles.loading}>Loading activities...</div>;
  }
  
  if (error) {
    return <div className={styles.error}>Error loading activities: {error.message}</div>;
  }
  
  const activities = data?.data || [];
  
  if (activities.length === 0) {
    return <div className={styles.empty}>No activities recorded yet.</div>;
  }
  
  const getActivityTypeStyle = (type: string) => {
    switch (type) {
      case 'ACTIVATION':
        return styles.activation;
      case 'CHECK_IN':
        return styles.checkIn;
      case 'WARNING':
        return styles.warning;
      case 'VIOLATION':
        return styles.violation;
      case 'REVOCATION':
        return styles.revocation;
      default:
        return '';
    }
  };
  
  return (
    <div className={styles.activityTable}>
      <table className={styles.table}>
        <thead>
          <tr>
            <th>Type</th>
            <th>Timestamp</th>
            <th>IP Address</th>
            <th>Details</th>
          </tr>
        </thead>
        <tbody>
          {activities.map((activity: LicenseActivity) => (
            <tr key={activity.id}>
              <td>
                <span className={`${styles.badge} ${getActivityTypeStyle(activity.activity_type)}`}>
                  {activity.activity_type}
                </span>
              </td>
              <td>{new Date(activity.timestamp).toLocaleString()}</td>
              <td>{activity.ip_address || 'N/A'}</td>
              <td>
                {activity.details ? (
                  <div className={styles.details}>
                    {typeof activity.details === 'object' 
                      ? JSON.stringify(activity.details) 
                      : String(activity.details)
                    }
                  </div>
                ) : (
                  'No details'
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      
      {data?.meta?.pageCount > 1 && (
        <div className={styles.pagination}>
          <button 
            className={styles.pageButton} 
            disabled={page === 1}
            onClick={() => setPage(p => p - 1)}
          >
            Previous
          </button>
          <span className={styles.pageInfo}>
            Page {page} of {data.meta.pageCount}
          </span>
          <button 
            className={styles.pageButton} 
            disabled={page === data.meta.pageCount}
            onClick={() => setPage(p => p + 1)}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default LicenseActivityTable;