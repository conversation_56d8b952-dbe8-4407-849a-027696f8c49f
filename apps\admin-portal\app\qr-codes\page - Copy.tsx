'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Alert } from '@loaloa/ui';
import QRCodeCard from '../components/QRCodeCard';
import LimitIndicator from '../components/LimitIndicator';
import styles from './qrCodes.module.scss';

interface QRCode {
  id: string;
  code_value: string;
  location: string;
  description: string | null;
  room_number: string | null;
  status: string;
  scan_count: number;
  last_scan: string | null;
  created_at: string;
  updated_at: string;
}

interface LimitInfo {
  current_count: number;
  max_allowed: number | null;
  is_enforced: boolean;
  usage_percentage: number;
  remaining: number | null;
}

export default function QRCodesPage() {
  const router = useRouter();
  const [qrCodes, setQrCodes] = useState<QRCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [limitInfo, setLimitInfo] = useState<LimitInfo | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  
  // Filter states
  const [locationFilter, setLocationFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [qrTypeFilter, setQrTypeFilter] = useState('TENANT');

  // Pagination
  const [page, setPage] = useState(1);
  const [limit] = useState(12);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    fetchQRCodes();
  }, [page, limit, statusFilter, qrTypeFilter]);

  const fetchQRCodes = async () => {
  try {
    setLoading(true);
    // Create query string for filters
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    if (locationFilter) params.append('location', locationFilter);
    if (statusFilter) params.append('status', statusFilter);
    if (qrTypeFilter) params.append('qr_type', qrTypeFilter);

    // Send API request
    console.log('Fetching QR codes with params:', params.toString());
    const response = await fetch(`/api/qr-codes?${params.toString()}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('QR code fetch error response:', errorText);
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log('QR codes fetch result:', result);
    
    setQrCodes(result.data || []);
    setTotal(result.meta?.total || 0);
    setLimitInfo(result.limits);
    setError(null);
  } catch (err) {
    console.error('Error fetching QR codes:', err);
    setError('Could not load QR code list. Please try again later.');
  } finally {
    setLoading(false);
  }
};

  // Xử lý áp dụng bộ lọc
  const handleFilter = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    fetchQRCodes();
  };

  // Xử lý xóa QR code
  const handleDelete = async (id: string) => {
    if (!confirm('Bạn có chắc muốn xóa mã QR này?')) return;
    
    try {
      const response = await fetch(`/api/qr-codes/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error || 'Failed to delete QR code');
      }
      
      // Cập nhật state
      setQrCodes(qrCodes.filter(qr => qr.id !== id));
      
      // Cập nhật thông tin giới hạn nếu có
      const result = await response.json();
      if (result.limits) {
        setLimitInfo(result.limits);
      }
      
      alert('Xóa mã QR thành công!');
    } catch (error) {
      console.error('Error deleting QR code:', error);
      alert('Không thể xóa mã QR. Vui lòng thử lại.');
    }
  };

  // Calculate total pages
  const totalPages = Math.ceil(total / limit);

  // Handle page change
  const changePage = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    setPage(newPage);
  };
  
  // Component hiển thị QR code dạng hàng
  const QRCodeRow = ({ qr }: { qr: QRCode }) => {
    return (
      <div className={styles.qrRow}>
        <div className={styles.qrInfo}>
          <h3 className={styles.qrLocation}>{qr.location}</h3>
          <p className={styles.qrDescription}>
            {qr.description || 'Không có mô tả'}
          </p>
        </div>
        <div className={styles.qrStats}>
          <span className={styles.scanCount}>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M7.33333 11.4667L12.6667 6.13333L11.4667 4.93333L7.33333 9.06667L5.2 6.93333L4 8.13333L7.33333 11.4667ZM8 16C6.9 16 5.85833 15.7917 4.875 15.375C3.89167 14.9583 3.03333 14.3833 2.3 13.65C1.56667 12.9167 1 12.0583 0.6 11.075C0.2 10.0917 0 9.05 0 7.95C0 6.85 0.2 5.80833 0.6 4.825C1 3.84167 1.56667 2.98333 2.3 2.25C3.03333 1.51667 3.89167 0.941667 4.875 0.525C5.85833 0.108333 6.9 -0.0416666 8 0.0166667C9.1 0.0166667 10.1417 0.225 11.125 0.641667C12.1083 1.05833 12.9667 1.63333 13.7 2.36667C14.4333 3.1 15 3.95833 15.4 4.94167C15.8 5.925 16 6.96667 16 8.06667C16 9.16667 15.8 10.2083 15.4 11.1917C15 12.175 14.4333 13.0333 13.7 13.7667C12.9667 14.5 12.1083 15.075 11.125 15.4917C10.1417 15.9083 9.1 16.0583 8 16Z" fill="currentColor"/>
            </svg>
            {qr.scan_count || 0} lượt quét
          </span>
          <span className={styles.qrStatus}>
            <span className={`${styles.statusIndicator} ${qr.status === 'active' ? styles.active : styles.inactive}`}></span>
            {qr.status === 'active' ? 'Hoạt động' : 'Không hoạt động'}
          </span>
        </div>
        <div className={styles.qrActions}>
          <Link href={`/qr-codes/${qr.id}`} className={styles.actionButton} title="Xem chi tiết">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M8 3C4.66667 3 1.82 5.07333 0.666667 8C1.82 10.9267 4.66667 13 8 13C11.3333 13 14.18 10.9267 15.3333 8C14.18 5.07333 11.3333 3 8 3ZM8 11.3333C6.16 11.3333 4.66667 9.84 4.66667 8C4.66667 6.16 6.16 4.66667 8 4.66667C9.84 4.66667 11.3333 6.16 11.3333 8C11.3333 9.84 9.84 11.3333 8 11.3333ZM8 6C6.89333 6 6 6.89333 6 8C6 9.10667 6.89333 10 8 10C9.10667 10 10 9.10667 10 8C10 6.89333 9.10667 6 8 6Z" fill="currentColor"/>
            </svg>
          </Link>
          <Link href={`/qr-codes/${qr.id}/edit`} className={styles.actionButton} title="Chỉnh sửa">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M9.33333 5.33333L10.6667 6.66667M2 14H3.33333L10 7.33333L8.66667 6L2 12.6667V14Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </Link>
          <a 
            href={`/api/qr-codes/${qr.id}/download`} 
            target="_blank"
            rel="noopener noreferrer" 
            className={styles.actionButton}
            title="Tải xuống"
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M14 10V12.6667C14 13.0203 13.8595 13.3594 13.6095 13.6095C13.3594 13.8595 13.0203 14 12.6667 14H3.33333C2.97971 14 2.64057 13.8595 2.39052 13.6095C2.14048 13.3594 2 13.0203 2 12.6667V10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M4.66666 6.66667L8 10L11.3333 6.66667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8 10V2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </a>
        </div>
      </div>
    );
  };

 return (
    <div className={styles.container}>
      <div className={styles.pageHeader}>
        <div>
          <h1 className={styles.pageTitle}>Quản lý mã QR</h1>
          <p className={styles.pageDescription}>Tạo và quản lý các mã QR cho phòng và khu vực của bạn</p>
        </div>
        
        <div className={styles.headerActions}>
          <div className={styles.extraActions}>
            <button className={styles.actionButton} title="Nhập mã QR">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M3.33334 13.3333L10 6.66667L16.6667 13.3333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3.33334 6.66667H16.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Import
            </button>
            
            <button className={styles.actionButton} title="Xuất mã QR">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M3.33334 6.66667L10 13.3333L16.6667 6.66667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M3.33334 13.3333H16.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Export
            </button>
            
            <button className={styles.actionButton} title="Tải xuống tất cả">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M17.5 12.5V15.8333C17.5 16.2754 17.3244 16.6993 17.0118 17.0118C16.6993 17.3244 16.2754 17.5 15.8333 17.5H4.16667C3.72464 17.5 3.30072 17.3244 2.98816 17.0118C2.67559 16.6993 2.5 16.2754 2.5 15.8333V12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M5.83334 8.33333L10 12.5L14.1667 8.33333" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M10 12.5V2.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Download All
            </button>
          </div>
          
          <div className={styles.viewToggle}>
            <button 
              className={`${styles.viewButton} ${viewMode === 'grid' ? styles.active : ''}`}
              onClick={() => setViewMode('grid')}
              title="Chế độ xem lưới"
            >
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M8.33333 2.5H2.5V8.33333H8.33333V2.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M17.5 2.5H11.6667V8.33333H17.5V2.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M17.5 11.6667H11.6667V17.5H17.5V11.6667Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M8.33333 11.6667H2.5V17.5H8.33333V11.6667Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            
            <button 
              className={`${styles.viewButton} ${viewMode === 'list' ? styles.active : ''}`}
              onClick={() => setViewMode('list')}
              title="Chế độ xem danh sách"
            >
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M6.66666 5H17.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M6.66666 10H17.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M6.66666 15H17.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2.5 5H2.50833" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2.5 10H2.50833" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2.5 15H2.50833" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
          
          <Link 
            href="/qr-codes/create" 
            className={styles.addButton} 
            title={limitInfo?.remaining <= 0 && limitInfo.is_enforced ? 'Đã đạt giới hạn số lượng mã QR' : 'Tạo mã QR mới'} 
            style={{ 
              opacity: limitInfo?.remaining <= 0 && limitInfo.is_enforced ? 0.6 : 1, 
              pointerEvents: limitInfo?.remaining <= 0 && limitInfo.is_enforced ? 'none' : 'auto' 
            }}
          >
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM15 11H11V15H9V11H5V9H9V5H11V9H15V11Z" fill="currentColor"/>
            </svg>
            Tạo mã QR mới
          </Link>
        </div>
      </div>

      {error && (
        <Alert variant="error" title="Lỗi tải dữ liệu" closable={true} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {limitInfo && (
        <div className={styles.limitSection}>
          <LimitIndicator 
            current={limitInfo.current_count} 
            maximum={limitInfo.max_allowed} 
            isEnforced={limitInfo.is_enforced}
            label="Mã QR"
            showDetails={true} 
          />
        </div>
      )}

      <div className={styles.filterBar}>
        <form onSubmit={handleFilter} className={styles.filterForm}>
          <div className={styles.filterGroup}>
            <input 
              type="text" 
              placeholder="Tìm theo vị trí" 
              value={locationFilter} 
              onChange={(e) => setLocationFilter(e.target.value)} 
              className={styles.filterInput} 
            />
          </div>
          
          <div className={styles.filterGroup}>
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)} 
              className={styles.filterSelect}
            >
              <option value="">Tất cả trạng thái</option>
              <option value="active">Đang hoạt động</option>
              <option value="inactive">Không hoạt động</option>
            </select>
          </div>
          
          <div className={styles.filterGroup}>
            <select 
              value={qrTypeFilter} 
              onChange={(e) => setQrTypeFilter(e.target.value)} 
              className={styles.filterSelect}
            >
              <option value="TENANT">Mã QR Tenant</option>
              <option value="USER">Mã QR User</option>
              <option value="TEMPORARY">Mã QR Tạm thời</option>
              <option value="">Tất cả loại mã QR</option>
            </select>
          </div>
          
          <button type="submit" className={styles.filterButton}>
            Lọc
          </button>
        </form>
      </div>

      {loading && page === 1 ? (
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>Đang tải danh sách mã QR...</p>
        </div>
      ) : qrCodes.length === 0 ? (
        <div className={styles.emptyState}>
          <div className={styles.emptyIcon}>
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
              <path d="M3 11H11V3H3V11ZM5 5H9V9H5V5Z" fill="currentColor"/>
              <path d="M13 3V11H21V3H13ZM19 9H15V5H19V9Z" fill="currentColor"/>
              <path d="M3 21H11V13H3V21ZM5 15H9V19H5V15Z" fill="currentColor"/>
              <path d="M18 21H16V19H18V21Z" fill="currentColor"/>
              <path d="M13 15H15V21H13V15Z" fill="currentColor"/>
              <path d="M21 13H13V17H21V13Z" fill="currentColor"/>
              <path d="M18 19H21V21H18V19Z" fill="currentColor"/>
            </svg>
          </div>
          <h3>Không tìm thấy mã QR nào</h3>
          <p>Tạo mã QR đầu tiên của bạn hoặc thay đổi bộ lọc xem các mã QR khác.</p>
          <Link href="/qr-codes/create" className={styles.emptyButton}>
            Tạo mã QR mới
          </Link>
        </div>
      ) : (
        viewMode === 'grid' ? (
          <div className={styles.qrGrid}>
            {qrCodes.map(qr => (
              <div key={qr.id} className={styles.qrGridItem}>
                <QRCodeCard 
                  id={qr.id}
                  location={qr.location}
                  description={qr.description || undefined}
                  roomNumber={qr.room_number || undefined}
                  status={qr.status}
                  scanCount={qr.scan_count}
                  lastScan={qr.last_scan || undefined}
                  createdAt={qr.created_at}
                  code={qr.code_value}
                />
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.qrList}>
            {qrCodes.map(qr => (
              <QRCodeRow key={qr.id} qr={qr} />
            ))}
          </div>
        )
      )}

      {totalPages > 1 && (
        <div className={styles.pagination}>
          <button 
            className={styles.paginationButton} 
            onClick={() => changePage(page - 1)} 
            disabled={page === 1 || loading}
          >
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
              <path d="M11.25 13.5L6.75 9L11.25 4.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Trước
          </button>
          
          <div className={styles.paginationPages}>
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum = page;
              
              if (totalPages <= 5) {
                // If 5 or fewer pages, show all pages
                pageNum = i + 1;
              } else if (page <= 3) {
                // If near the start, show pages 1-5
                pageNum = i + 1;
              } else if (page >= totalPages - 2) {
                // If near the end, show the last 5 pages
                pageNum = totalPages - 4 + i;
              } else {
                // Otherwise, show 2 before and 2 after current page
                pageNum = page - 2 + i;
              }
              
              return (
                <button 
                  key={pageNum} 
                  className={`${styles.paginationPage} ${pageNum === page ? styles.active : ''}`} 
                  onClick={() => changePage(pageNum)} 
                  disabled={loading}
                >
                  {pageNum}
                </button>
              );
            })}
          </div>
          
          <button 
            className={styles.paginationButton} 
            onClick={() => changePage(page + 1)} 
            disabled={page === totalPages || loading}
          >
            Sau
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
              <path d="M6.75 4.5L11.25 9L6.75 13.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
      )}
    </div>
  );
}
