'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DashboardLayout from '../../dashboard-layout';
import styles from './reception-point-detail.module.scss';
import { <PERSON><PERSON>, Button } from '@ui';
import DeleteConfirmModal from '../../components/modals/DeleteConfirmModal';

interface ReceptionPointDetailParams {
  params: {
    id: string;
  };
}

export default function ReceptionPointDetailPage({ params }: ReceptionPointDetailParams) {
  const router = useRouter();
  const pointId = params.id;
  
  const [point, setPoint] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  
  // Associated rooms and areas
  const [associatedRooms, setAssociatedRooms] = useState<any[]>([]);
  const [associatedAreas, setAssociatedAreas] = useState<any[]>([]);
  const [loadingAssociations, setLoadingAssociations] = useState(false);

  useEffect(() => {
    const fetchPoint = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/reception-points/${pointId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Reception point not found');
          }
          throw new Error('Failed to fetch reception point details');
        }
        
        const data = await response.json();
        setPoint(data.data);
        
        // After getting the point, fetch associated rooms and areas
        fetchAssociations(data.data.id);
      } catch (err) {
        console.error('Error fetching reception point:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    const fetchAssociations = async (id: string) => {
      try {
        setLoadingAssociations(true);
        
        // Fetch rooms with this reception point
        const roomsResponse = await fetch(`/api/rooms?reception_point_id=${id}&limit=100`);
        if (roomsResponse.ok) {
          const roomsData = await roomsResponse.json();
          setAssociatedRooms(roomsData.data || []);
        }
        
        // Fetch areas with this reception point
        const areasResponse = await fetch(`/api/areas?reception_point_id=${id}&limit=100`);
        if (areasResponse.ok) {
          const areasData = await areasResponse.json();
          setAssociatedAreas(areasData.data || []);
        }
      } catch (err) {
        console.error('Error fetching associations:', err);
      } finally {
        setLoadingAssociations(false);
      }
    };

    if (pointId) {
      fetchPoint();
    }
  }, [pointId]);

  const handleDelete = async () => {
    try {
      setDeleteError(null);
      
      const response = await fetch(`/api/reception-points/${pointId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete reception point');
      }

      router.push('/reception-points');
    } catch (err) {
      console.error('Error deleting reception point:', err);
      setDeleteError(err instanceof Error ? err.message : 'Failed to delete reception point');
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading reception point details...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className={styles.error}>
          <Alert variant="error" title="Error" closable={false}>
            {error}
          </Alert>
          <Button
            variant="secondary" 
            onClick={() => router.push('/reception-points')}
          >
            Back to Reception Points
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  if (!point) return null;

  return (
    <DashboardLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Link href="/reception-points" className={styles.backLink}>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M15.8333 10H4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M10 15.8333L4.16667 10L10 4.16667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Back to Reception Points
          </Link>
          <div className={styles.actions}>
            <Link href={`/reception-points/${pointId}/edit`} className={styles.editButton}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M11.3333 2.00001C11.5085 1.82494 11.7163 1.68605 11.9451 1.59129C12.1739 1.49653 12.4187 1.44775 12.6667 1.44775C12.9146 1.44775 13.1595 1.49653 13.3883 1.59129C13.6171 1.68605 13.8248 1.82494 14 2.00001C14.1751 2.17508 14.314 2.38283 14.4088 2.61162C14.5035 2.84041 14.5523 3.08536 14.5523 3.33334C14.5523 3.58132 14.5035 3.82627 14.4088 4.05506C14.314 4.28385 14.1751 4.4916 14 4.66667L5 13.6667L1.33334 14.6667L2.33334 11L11.3333 2.00001Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Edit
            </Link>
            <button onClick={() => setShowDeleteModal(true)} className={styles.deleteButton}>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M2 4H3.33333H14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M5.33334 4.00001V2.66667C5.33334 2.31305 5.47381 1.97391 5.7239 1.72386C5.97399 1.47381 6.31313 1.33334 6.66667 1.33334H9.33334C9.68688 1.33334 10.026 1.47381 10.2761 1.72386C10.5262 1.97391 10.6667 2.31305 10.6667 2.66667V4.00001" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12.6667 4V13.3333C12.6667 13.687 12.5262 14.0261 12.2761 14.2761C12.026 14.5262 11.6869 14.6667 11.3333 14.6667H4.66667C4.31305 14.6667 3.9739 14.5262 3.72386 14.2761C3.47381 14.0261 3.33334 13.687 3.33334 13.3333V4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Delete
            </button>
          </div>
        </div>

        <div className={styles.pointCard}>
          <div className={styles.pointHeader}>
            <div className={styles.nameSection}>
              <h1 className={styles.pointName}>{point.name}</h1>
              <span className={styles.pointCode}>{point.code}</span>
            </div>
            <span className={`${styles.statusBadge} ${point.is_active ? styles.active : styles.inactive}`}>
              {point.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
          
          <div className={styles.detailsContainer}>
            <div className={styles.section}>
              <h3 className={styles.sectionTitle}>Details</h3>
              <div className={styles.detailsGrid}>
                <div className={styles.detailItem}>
                  <span className={styles.detailLabel}>Priority</span>
                  <span className={styles.detailValue}>{point.priority}</span>
                </div>
                
                <div className={styles.detailItem}>
                  <span className={styles.detailLabel}>Created</span>
                  <span className={styles.detailValue}>{formatDate(point.created_at)}</span>
                </div>
                
                <div className={styles.detailItem}>
                  <span className={styles.detailLabel}>Last Updated</span>
                  <span className={styles.detailValue}>{formatDate(point.updated_at)}</span>
                </div>
                
                {point.icon_url && (
                  <div className={styles.detailItem}>
                    <span className={styles.detailLabel}>Icon URL</span>
                    <a href={point.icon_url} target="_blank" rel="noopener noreferrer" className={styles.iconLink}>
                      {point.icon_url}
                    </a>
                  </div>
                )}
              </div>
              
              {point.description && (
                <div className={styles.descriptionContainer}>
                  <span className={styles.detailLabel}>Description</span>
                  <p className={styles.description}>{point.description}</p>
                </div>
              )}
            </div>
            
            <div className={styles.section}>
              <h3 className={styles.sectionTitle}>Associated Locations</h3>
              
              {loadingAssociations ? (
                <div className={styles.loadingAssociations}>
                  <div className={styles.miniSpinner}></div>
                  <span>Loading associations...</span>
                </div>
              ) : (
                <>
                  {associatedRooms.length === 0 && associatedAreas.length === 0 ? (
                    <div className={styles.noAssociations}>
                      <p>No rooms or areas are currently using this reception point.</p>
                    </div>
                  ) : (
                    <div className={styles.associationsContainer}>
                      {associatedRooms.length > 0 && (
                        <div className={styles.associationGroup}>
                          <h4 className={styles.associationTitle}>Rooms</h4>
                          <div className={styles.associationItems}>
                            {associatedRooms.map(room => (
                              <Link 
                                key={room.id} 
                                href={`/rooms-areas/rooms/${room.id}`}
                                className={styles.associationItem}
                              >
                                Room {room.room_number}
                              </Link>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {associatedAreas.length > 0 && (
                        <div className={styles.associationGroup}>
                          <h4 className={styles.associationTitle}>Areas</h4>
                          <div className={styles.associationItems}>
                            {associatedAreas.map(area => (
                              <Link 
                                key={area.id} 
                                href={`/rooms-areas/areas/${area.id}`}
                                className={styles.associationItem}
                              >
                                {area.name}
                              </Link>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <DeleteConfirmModal
        isOpen={showDeleteModal}
        title="Delete Reception Point"
        message={`Are you sure you want to delete "${point.name}" (${point.code})? This action cannot be undone.${
          associatedRooms.length > 0 || associatedAreas.length > 0 
            ? ' Warning: This reception point is in use by rooms or areas.'
            : ''
        }`}
        onConfirm={handleDelete}
        onCancel={() => {
          setShowDeleteModal(false);
          setDeleteError(null);
        }}
        error={deleteError}
      />
    </DashboardLayout>
  );
}
