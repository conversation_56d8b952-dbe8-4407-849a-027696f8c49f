import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import authRoutes from './routes/authRoutes';
import temporaryUserRoutes from './routes/temporaryUserRoutes';

// Load env variables
dotenv.config();

// Initialize express app
const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: '*', // Trong môi trường sản xuất, hãy giới hạn điều này
  credentials: true
}));
app.use(express.json());

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/temporary-users', temporaryUserRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', service: 'authentication-service' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Authentication service running on port ${PORT}`);
});

export default app;
