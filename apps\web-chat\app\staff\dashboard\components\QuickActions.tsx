'use client';

import { useState } from 'react';
import styles from './QuickActions.module.scss';

interface QuickActionsProps {
  onAction: (action: string) => void;
}

export default function QuickActions({ onAction }: QuickActionsProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const actions = [
    {
      id: 'broadcast',
      icon: '📢',
      label: 'Broadcast Message',
      description: 'Send message to all active chats',
      color: '#3b82f6'
    },
    {
      id: 'templates',
      icon: '📝',
      label: 'Quick Templates',
      description: 'Use predefined message templates',
      color: '#10b981'
    },
    {
      id: 'transfer',
      icon: '📨',
      label: 'Transfer All',
      description: 'Transfer all chats to another staff',
      color: '#f59e0b'
    },
    {
      id: 'reports',
      icon: '📊',
      label: 'Generate Report',
      description: 'Create chat activity report',
      color: '#8b5cf6'
    },
    {
      id: 'settings',
      icon: '⚙️',
      label: 'Chat Settings',
      description: 'Configure chat preferences',
      color: '#6b7280'
    },
    {
      id: 'break',
      icon: '☕',
      label: 'Take Break',
      description: 'Set status to away temporarily',
      color: '#ef4444'
    }
  ];

  const handleAction = (actionId: string) => {
    onAction(actionId);
    setIsExpanded(false);
  };

  return (
    <div className={styles.quickActions}>
      <button
        className={styles.toggleButton}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <span className={styles.toggleIcon}>⚡</span>
        <span className={styles.toggleText}>Quick Actions</span>
        <span className={`${styles.arrow} ${isExpanded ? styles.expanded : ''}`}>
          ▼
        </span>
      </button>

      {isExpanded && (
        <div className={styles.actionsPanel}>
          <div className={styles.actionsGrid}>
            {actions.map((action) => (
              <button
                key={action.id}
                className={styles.actionItem}
                onClick={() => handleAction(action.id)}
                style={{ borderLeftColor: action.color }}
              >
                <div className={styles.actionIcon}>{action.icon}</div>
                <div className={styles.actionContent}>
                  <span className={styles.actionLabel}>{action.label}</span>
                  <span className={styles.actionDescription}>{action.description}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
