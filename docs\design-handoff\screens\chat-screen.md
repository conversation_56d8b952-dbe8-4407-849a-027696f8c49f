# Screen Design Handoff: Chat Screen

## Overview
- **Screen Name:** Chat
- **Designer:** UI Team
- **Last Updated:** 2025-04-28
- **Status:** Ready for Handoff

## Visual Design

### Desktop View
[Screenshot from Figma - desktop chat layout]

### Mobile View
[Screenshot from Figma - mobile chat layout]

## Layout & Components

### Header
- **Height:** 64px
- **Components:** 
  - Back button (mobile only)
  - Avatar (40px)
  - Name/Title
  - Status indicator
  - Menu button (three dots)

### Message List
- **Layout:** Scrollable area, taking majority of screen
- **Components:**
  - Message bubbles (see MessageBubble component spec)
  - Date separators
  - "New messages" divider when applicable
  - Typing indicator
- **Scroll Behavior:** Auto-scroll to bottom on new messages

### Input Area
- **Height:** Auto with minimum 64px
- **Components:**
  - Text input (expandable)
  - Attachment button
  - Send button
  - Language indicator
- **States:** 
  - Empty
  - Typing
  - Sending
  - With attachment

## User Flows

### Starting a New Chat
1. User scans QR code or selects from list
2. Chat screen appears with welcome message
3. Language is automatically detected
4. User can start typing

### Sending/Receiving Messages
1. User types message in input area
2. Original message is shown in user's bubble
3. Translation appears below original in italics
4. Response appears in other person's bubble (left side)
5. Original and translation shown in response as well

## Responsive Behavior
- **Desktop:** Two-column layout with chat list on left
- **Tablet:** Similar to desktop but with narrower list
- **Mobile:** Single column, full screen chat

## Accessibility
- **Keyboard Navigation:** Full keyboard support for navigation
- **Screen Reader:** Proper ARIA roles and announcements for new messages
- **High Contrast:** All text meets AA standards for contrast

## Implementation Notes
- Implement virtualized list for performance with many messages
- Support for different message types (text, image, system)
- Cache translations for offline viewing
- Handle network interruptions gracefully
- Support for RTL languages in bubbles

## Animation Specifications
- **New Message:** Subtle fade in
- **Typing Indicator:** Animated dots
- **Send Button:** Transforms to spinner while sending

## Open Questions
- How to handle very long messages?
- Should we limit number of cached/loaded messages?
- Auto-translation toggle location?