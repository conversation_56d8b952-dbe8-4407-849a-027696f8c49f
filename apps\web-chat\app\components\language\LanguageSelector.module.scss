.languageSelector {
  position: relative;
}

.trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    border-color: #9ca3af;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  &:focus {
    outline: none;
    border-color: #f97316;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
  }
  
  &.open {
    border-color: #f97316;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f9fafb;
  }
  
  &.compact {
    font-size: 0.875rem;
    padding: 0.375rem 0.5rem;
  }
}

.flag {
  font-size: 1.125rem;
  line-height: 1;
}

.languageInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
}

.code {
  font-weight: 500;
  font-family: 'Courier New', monospace;
  letter-spacing: 0.05em;
}

.nativeName {
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 8rem;
}

.chevron {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
  transition: transform 0.2s ease;
  flex-shrink: 0;
  
  &.open {
    transform: rotate(180deg);
  }
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 0.25rem;
  width: 18rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 50;
  animation: dropdownOpen 0.15s ease-out;
  
  @media (max-width: 640px) {
    width: 16rem;
    left: 50%;
    transform: translateX(-50%);
  }
}

.searchContainer {
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.searchInput {
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #f97316;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
}

.languageList {
  max-height: 15rem;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f5f9;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    
    &:hover {
      background: #94a3b8;
    }
  }
}

.languageOption {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.15s ease;
  
  &:hover {
    background: #f9fafb;
  }
  
  &.selected {
    background: #fff7ed;
    color: #ea580c;
  }
  
  &.rtl {
    flex-direction: row-reverse;
  }
}

.optionFlag {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.optionInfo {
  flex: 1;
  min-width: 0;
}

.optionName {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.125rem;
}

.optionNativeName {
  font-size: 0.875rem;
  color: #6b7280;
}

.optionCode {
  font-size: 0.75rem;
  color: #9ca3af;
  font-family: 'Courier New', monospace;
  letter-spacing: 0.05em;
  flex-shrink: 0;
}

.checkmark {
  color: #f97316;
  font-weight: 600;
  flex-shrink: 0;
}

.emptyState {
  padding: 1rem;
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
}

.footer {
  padding: 0.5rem 1rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  text-align: center;
  font-size: 0.75rem;
  color: #6b7280;
  border-radius: 0 0 0.5rem 0.5rem;
}

// Animations
@keyframes dropdownOpen {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive adjustments
@media (max-width: 640px) {
  .trigger {
    padding: 0.5rem;
    
    .nativeName {
      display: none;
    }
  }
  
  .dropdown {
    width: 90vw;
    max-width: 20rem;
  }
  
  .optionInfo {
    .optionNativeName {
      font-size: 0.75rem;
    }
  }
}
