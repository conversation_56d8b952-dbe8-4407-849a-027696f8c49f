import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Lấy chi tiết quy tắc định tuyến chat theo ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const ruleId = params.id;

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Truy vấn chi tiết quy tắc
    const { data, error } = await supabase
  .from('tenant_chat_routing_rules')
  .select(`
    *,
    target_user:target_user_id (
      id,
      role,
      tenant_users_details (
        email,
        display_name,
        avatar_url
      )
    ),
    target_reception_point:tenant_message_reception_points!tenant_chat_routing_rules_target_reception_point_id_fkey (
      id,
      name,
      code
    )
  `)
  .eq('id', ruleId)
  .eq('tenant_id', tenant_id)
  .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Routing rule not found' }, { status: 404 });
      }
      console.error('Error fetching routing rule:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Error in GET chat routing rule:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}

// PUT: Cập nhật quy tắc định tuyến chat
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const ruleId = params.id;
    const updateData = await request.json();

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra quy tắc có tồn tại không
    const { data: existingRule, error: checkError } = await supabase
      .from('tenant_chat_routing_rules')
      .select('id')
      .eq('id', ruleId)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Routing rule not found' }, { status: 404 });
      }
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    // Nếu có target_user_id, kiểm tra user có tồn tại không
    if (updateData.target_user_id) {
      const { data: existingUser, error: userCheckError } = await supabase
        .from('tenant_users')
        .select('id')
        .eq('id', updateData.target_user_id)
        .eq('tenant_id', tenant_id)
        .single();

      if (userCheckError || !existingUser) {
        return NextResponse.json({ error: 'Target user not found' }, { status: 404 });
      }
    }

    // Nếu có target_reception_point_id, kiểm tra reception point có tồn tại không
    if (updateData.target_reception_point_id) {
      const { data: existingPoint, error: pointCheckError } = await supabase
        .from('tenant_message_reception_points')
        .select('id')
        .eq('id', updateData.target_reception_point_id)
        .eq('tenant_id', tenant_id)
        .single();

      if (pointCheckError || !existingPoint) {
        return NextResponse.json({ error: 'Target reception point not found' }, { status: 404 });
      }
    }

    // Kiểm tra rule_type hợp lệ nếu có cập nhật
    if (updateData.rule_type) {
      const validRuleTypes = ['qr_code', 'guest', 'time', 'language', 'custom'];
      if (!validRuleTypes.includes(updateData.rule_type)) {
        return NextResponse.json({ error: `Invalid rule type. Must be one of: ${validRuleTypes.join(', ')}` }, { status: 400 });
      }
    }

    // Chuẩn bị dữ liệu cập nhật
    const ruleUpdateData: any = {
      updated_at: new Date().toISOString()
    };

    // Chỉ cập nhật các trường có cung cấp
    if (updateData.rule_name !== undefined) ruleUpdateData.rule_name = updateData.rule_name;
    if (updateData.rule_type !== undefined) ruleUpdateData.rule_type = updateData.rule_type;
    if (updateData.rule_condition !== undefined) ruleUpdateData.rule_condition = updateData.rule_condition;
    if (updateData.priority !== undefined) ruleUpdateData.priority = updateData.priority;
    if (updateData.target_department !== undefined) ruleUpdateData.target_department = updateData.target_department;
    if (updateData.target_user_id !== undefined) ruleUpdateData.target_user_id = updateData.target_user_id;
    if (updateData.target_reception_point_id !== undefined) ruleUpdateData.target_reception_point_id = updateData.target_reception_point_id;
    if (updateData.is_active !== undefined) ruleUpdateData.is_active = updateData.is_active;

    // Cập nhật quy tắc
    const { data, error } = await supabase
  .from('tenant_chat_routing_rules')
  .update(ruleUpdateData)
  .eq('id', ruleId)
  .eq('tenant_id', tenant_id)
  .select(`
    *,
    target_user:target_user_id (
      id,
      tenant_users_details (
        email,
        display_name,
        avatar_url
      )
    ),
    target_reception_point:tenant_message_reception_points!tenant_chat_routing_rules_target_reception_point_id_fkey (
      id,
      name,
      code
    )
  `)
  .single();

    if (error) {
      console.error('Error updating chat routing rule:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({ data, message: 'Chat routing rule updated successfully' });
  } catch (error: any) {
    console.error('Error in PUT chat routing rule:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}

// DELETE: Xóa quy tắc định tuyến chat
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const ruleId = params.id;

    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }

    // Tạo Supabase client
    const supabase = createAdminClient(cookies());

    // Kiểm tra quy tắc có tồn tại không
    const { data: existingRule, error: checkError } = await supabase
      .from('tenant_chat_routing_rules')
      .select('id')
      .eq('id', ruleId)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Routing rule not found' }, { status: 404 });
      }
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    // Xóa quy tắc
    const { error } = await supabase
      .from('tenant_chat_routing_rules')
      .delete()
      .eq('id', ruleId)
      .eq('tenant_id', tenant_id);

    if (error) {
      console.error('Error deleting chat routing rule:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Trả về kết quả
    return NextResponse.json({ message: 'Chat routing rule deleted successfully' });
  } catch (error: any) {
    console.error('Error in DELETE chat routing rule:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}
