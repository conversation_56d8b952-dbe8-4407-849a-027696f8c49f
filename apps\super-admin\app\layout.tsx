import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ToastProvider } from './providers/ToastProvider';

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'LoaLoa - Super Admin',
  description: 'Super Admin Dashboard for LoaLoa platform',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ToastProvider>
          {children}
        </ToastProvider>
      </body>
    </html>
  )
}
