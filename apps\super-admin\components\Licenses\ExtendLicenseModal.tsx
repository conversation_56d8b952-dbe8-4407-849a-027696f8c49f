import React, { useState } from 'react';
import { Form, Button } from '@loaloa/ui';
import styles from './ExtendLicenseModal.module.scss';

interface ExtendLicenseModalProps {
  licenseId: string;
  currentExpiryDate: string;
  onClose: () => void;
  onSuccess: () => void;
}

const ExtendLicenseModal: React.FC<ExtendLicenseModalProps> = ({
  licenseId,
  currentExpiryDate,
  onClose,
  onSuccess
}) => {
  // State
  const [additionalDays, setAdditionalDays] = useState(365);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // New expiry date
  const newExpiryDate = new Date(currentExpiryDate);
  newExpiryDate.setDate(newExpiryDate.getDate() + additionalDays);
  
  // Handle submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/licenses/${licenseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ additionalDays }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to extend license');
      }
      
      onSuccess();
    } catch (err: any) {
      setError(err.message || 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modal}>
        <div className={styles.modalHeader}>
          <h2>Extend License</h2>
          <button className={styles.closeButton} onClick={onClose}>×</button>
        </div>
        
        {error && (
          <div className={styles.errorMessage}>{error}</div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            <div className={styles.expiryInfo}>
              <div className={styles.expiryItem}>
                <span className={styles.expiryLabel}>Current Expiry Date:</span>
                <span className={styles.expiryValue}>{new Date(currentExpiryDate).toLocaleDateString()}</span>
              </div>
              <div className={styles.expiryItem}>
                <span className={styles.expiryLabel}>New Expiry Date:</span>
                <span className={styles.expiryValue}>{newExpiryDate.toLocaleDateString()}</span>
              </div>
            </div>
            
            <Form.FormGroup label="Additional Days" htmlFor="additionalDays">
              <Form.Select
                id="additionalDays"
                value={additionalDays.toString()}
                onChange={(e) => setAdditionalDays(Number(e.target.value))}
                options={[
                  { value: '30', label: '30 days' },
                  { value: '90', label: '90 days' },
                  { value: '180', label: '180 days' },
                  { value: '365', label: '1 year (365 days)' },
                  { value: '730', label: '2 years (730 days)' }
                ]}
              />
            </Form.FormGroup>
          </div>
          
          <div className={styles.modalFooter}>
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading}
            >
              {isLoading ? 'Extending...' : 'Extend License'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ExtendLicenseModal;