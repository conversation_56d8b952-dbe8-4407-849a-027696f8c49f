#!/bin/bash
# Script cài đặt hệ thống LoaLoa On-Premise

# Kiểm tra Docker và Docker Compose
if ! command -v docker &> /dev/null || ! command -v docker-compose &> /dev/null; then
    echo "Docker và Docker Compose là bắt buộc. Vui lòng cài đặt trước khi tiếp tục."
    exit 1
fi

# Tạo file .env nếu chưa tồn tại
if [ ! -f .env ]; then
    echo "Tạo file .env..."
    
    # Tạo các khóa ngẫu nhiên
    POSTGRES_PASSWORD=$(openssl rand -base64 24)
    SUPABASE_JWT_SECRET=$(openssl rand -base64 32)
    SUPABASE_ANON_KEY=$(openssl rand -base64 24)
    SUPABASE_SERVICE_ROLE_KEY=$(openssl rand -base64 24)
    
    cat > .env << EOF
POSTGRES_PASSWORD=$POSTGRES_PASSWORD
POSTGRES_USER=postgres
SUPABASE_JWT_SECRET=$SUPABASE_JWT_SECRET
SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=$SUPABASE_SERVICE_ROLE_KEY
LICENSE_SERVER_URL=https://license.loaloa.app
EOF

    echo "File .env đã được tạo với các khóa ngẫu nhiên."
fi

# Khởi chạy hệ thống
echo "Khởi động hệ thống LoaLoa On-Premise..."
docker-compose up -d

# Kiểm tra trạng thái các container
echo "Kiểm tra trạng thái các container..."
docker-compose ps

echo "Cài đặt hoàn tất. Vui lòng truy cập Admin Portal tại http://localhost:3000 để kích hoạt license."
