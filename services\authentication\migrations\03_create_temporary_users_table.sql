-- Create temporary_users table
CREATE TABLE IF NOT EXISTS temporary_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  qr_code_id VARCHAR(100) UNIQUE NOT NULL,
  device_id VARCHAR(255),
  preferred_language VARCHAR(50) DEFAULT 'en',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_activated BOOLEAN DEFAULT false,
  room_number VARCHAR(50),
  hotel_id UUID,
  metadata JSONB
);
