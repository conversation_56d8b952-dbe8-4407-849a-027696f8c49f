import { NextRequest,NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import os from 'os';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

// Tạo hardware fingerprint từ thông tin hệ thống
function generateHardwareFingerprint() {
  const cpus = os.cpus();
  const network = os.networkInterfaces();
  const platform = os.platform();
  const release = os.release();
  const hostname = os.hostname();
  
  const systemInfo = JSON.stringify({
    cpuModel: cpus[0]?.model || '',
    cpuCount: cpus.length,
    network: Object.keys(network),
    platform,
    release,
    hostname
  });
  
  return crypto.createHash('sha256').update(systemInfo).digest('hex');
}

// Hàm đọc thông tin license từ file cấu hình cục bộ
// Trong thực tế, bạn sẽ cần một cách an toàn hơn để lưu trữ license key
function getLicenseFromConfig() {
  try {
    const configPath = path.join(process.cwd(), 'license_config.json');
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8');
      return JSON.parse(configData);
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET() {
  try {
    // Đọc thông tin license từ cấu hình cục bộ
    const licenseConfig = getLicenseFromConfig();
    
    // Nếu không tìm thấy cấu hình
    if (!licenseConfig || !licenseConfig.licenseKey) {
      return NextResponse.json({ 
        valid: false, 
        message: 'License chưa được kích hoạt.'
      });
    }
    
    // Tạo Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Kiểm tra license key có tồn tại không
    const { data: license, error } = await supabase
      .from('licenses')
      .select('*')
      .eq('license_key', licenseConfig.licenseKey)
      .single();
    
    if (error || !license) {
      return NextResponse.json({ 
        valid: false, 
        message: 'License key không hợp lệ.'
      });
    }
    
    // Kiểm tra xem license có còn active và chưa hết hạn không
    const now = new Date();
    const expiryDate = new Date(license.expiry_date);
    
    if (!license.is_active) {
      return NextResponse.json({ 
        valid: false, 
        message: 'License đã bị vô hiệu hóa.'
      });
    }
    
    if (now > expiryDate) {
      return NextResponse.json({ 
        valid: false, 
        message: 'License đã hết hạn.'
      });
    }
    
    // Tạo hardware fingerprint hiện tại
    const currentFingerprint = generateHardwareFingerprint();
    
    // Kiểm tra hardware fingerprint có khớp không
    if (license.hardware_fingerprint && license.hardware_fingerprint !== currentFingerprint) {
      // Ghi nhận clone case
      await supabase.from('license_clones').insert({
        license_id: license.id,
        original_fingerprint: license.hardware_fingerprint,
        clone_fingerprint: currentFingerprint,
        status: 'DETECTED'
      });
      
      // Tùy thuộc vào chính sách của bạn, bạn có thể quyết định cho phép hoặc từ chối
      // Trong ví dụ này, chúng ta chấp nhận nhưng ghi log
    }
    
    return NextResponse.json({ 
      valid: true,
      expiresAt: license.expiry_date
    });
    
  } catch (err) {
    console.error('Error validating license:', err);
    return NextResponse.json({ 
      valid: false, 
      message: 'Lỗi hệ thống khi xác thực license.'
    });
  }
}
