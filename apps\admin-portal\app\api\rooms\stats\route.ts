import { createClient } from '../../../../lib/supabase/server';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Hàm đọc tenant_id từ file license_config.json
function getTenantIdFromConfig() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Lấy tenant_id từ config file
    const tenant_id = getTenantIdFromConfig();
    
    if (!tenant_id) {
      return NextResponse.json({
        success: false,
        error: 'Không thể tìm thấy Tenant ID. Vui lòng kích hoạt license.'
      }, { status: 400 });
    }
    
    console.log('Using tenant_id for room stats:', tenant_id);
    
    // Lấy tổng số phòng theo tenant_id
    const { count: totalRooms, error: totalError } = await supabase
      .from('tenant_rooms')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenant_id);
    
    if (totalError) {
      return NextResponse.json({ 
        success: false, 
        error: totalError.message 
      }, { status: 500 });
    }
    
    // Lấy số phòng theo trạng thái, lọc theo tenant_id
    const { data: statusData, error: statusError } = await supabase
      .from('tenant_rooms')
      .select('status')
      .eq('tenant_id', tenant_id)
      .not('status', 'is', null);
    
    if (statusError) {
      return NextResponse.json({ 
        success: false, 
        error: statusError.message 
      }, { status: 500 });
    }
    
    // Tính toán thống kê
    const availableRooms = statusData?.filter(room => room.status === 'available').length || 0;
    const occupiedRooms = statusData?.filter(room => room.status === 'occupied').length || 0;
    const maintenanceRooms = statusData?.filter(room => room.status === 'maintenance').length || 0;
    
    return NextResponse.json({
      success: true,
      stats: {
        total: totalRooms || 0,
        available: availableRooms,
        occupied: occupiedRooms,
        maintenance: maintenanceRooms
      }
    });
  } catch (error: any) {
    console.error('Error fetching room stats:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal Server Error' 
    }, { status: 500 });
  }
}
