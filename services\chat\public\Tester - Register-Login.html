<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LoaLoa Chat Tester</title>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <style>
    body { 
      font-family: Arial, sans-serif; 
      margin: 0; 
      padding: 20px; 
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .header {
      text-align: center;
      padding: 10px;
      margin-bottom: 20px;
      background-color: #4CAF50;
      color: white;
      border-radius: 5px;
    }
    .dashboard {
      display: grid;
      grid-template-columns: 300px 1fr;
      gap: 20px;
      background-color: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .sidebar {
      border-right: 1px solid #ddd;
      padding-right: 20px;
    }
    .main {
      flex: 1;
    }
    .panel {
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 20px;
    }
    .panel-header {
      font-weight: bold;
      margin-bottom: 10px;
      padding-bottom: 5px;
      border-bottom: 1px solid #eee;
    }
    button {
      padding: 8px 15px;
      background: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px 0;
    }
    button:hover {
      background: #45a049;
    }
    input, select {
      padding: 8px;
      margin: 5px 0;
      border: 1px solid #ddd;
      border-radius: 4px;
      width: 100%;
      box-sizing: border-box;
    }
    .form-group {
      margin-bottom: 10px;
    }
    .form-group label {
      display: block;
      margin-bottom: 5px;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .success {
      background-color: #dff0d8;
      color: #3c763d;
    }
    .error {
      background-color: #f2dede;
      color: #a94442;
    }
    .chat-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-top: 20px;
    }
    .chat-box {
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
      height: 500px;
      display: flex;
      flex-direction: column;
    }
    .chat-header {
      background-color: #f5f5f5;
      padding: 10px;
      font-weight: bold;
      border-bottom: 1px solid #ddd;
    }
    .messages {
      flex: 1;
      overflow-y: auto;
      padding: 10px;
      background-color: #fff;
    }
    .input-container {
      padding: 10px;
      border-top: 1px solid #ddd;
      display: flex;
    }
    .input-container input {
      flex: 1;
      margin-right: 10px;
    }
    .message {
      margin-bottom: 10px;
      padding: 8px;
      border-radius: 5px;
      max-width: 80%;
    }
    .sent {
      background-color: #e3f2fd;
      align-self: flex-end;
      margin-left: auto;
    }
    .received {
      background-color: #f1f1f1;
      align-self: flex-start;
    }
    .translation {
      font-style: italic;
      padding-top: 5px;
      font-size: 0.9em;
      color: #666;
    }
    .tabs {
      display: flex;
      border-bottom: 1px solid #ddd;
      margin-bottom: 20px;
    }
    .tab {
      padding: 10px 15px;
      cursor: pointer;
      border: 1px solid transparent;
      border-bottom: none;
      margin-right: 5px;
    }
    .tab.active {
      border-color: #ddd;
      border-bottom-color: white;
      border-radius: 5px 5px 0 0;
      background-color: white;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    code {
      background-color: #f8f8f8;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 10px;
      display: block;
      white-space: pre;
      overflow: auto;
      font-family: monospace;
      margin: 10px 0;
    }
    .user-info {
      display: flex;
      gap: 10px;
    }
    .user-info > div {
      flex: 1;
    }
    .data-list {
      max-height: 200px;
      overflow-y: auto;
      margin-top: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 8px;
    }
    .data-item {
      padding: 8px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
    }
    .data-item:last-child {
      border-bottom: none;
    }
    .data-item:hover {
      background-color: #f9f9f9;
    }
    .data-item-header {
      font-weight: bold;
      margin-bottom: 4px;
    }
    .data-item-details {
      font-size: 0.9em;
      color: #666;
    }
    .data-loading {
      text-align: center;
      padding: 10px;
      color: #666;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>LoaLoa Chat Tester</h1>
      <p>All-in-one testing tool for LoaLoa Chat Service</p>
    </div>
    
    <div class="dashboard">
      <div class="sidebar">
        <div class="panel">
          <div class="panel-header">Server Control</div>
          <div class="form-group">
            <label>Start Chat Server:</label>
            <code>cd D:\loaloa\services\chat
npm run dev</code>
          </div>
          <div class="form-group">
            <label>Generate JWT Token:</label>
            <code>cd D:\loaloa\services\chat
npx ts-node src\tests\generate-token.ts</code>
          </div>
          <div class="form-group">
            <label for="jwt-token">JWT Token (paste here after generating):</label>
            <input type="text" id="jwt-token" placeholder="Paste your JWT token here">
          </div>
        </div>
        <div class="panel">
  <div class="panel-header">Database Utilities</div>
  <div class="form-group">
    <label>User ID:</label>
    <input type="text" id="util-userId" placeholder="User ID">
  </div>
  <div class="form-group">
    <label>Room ID:</label>
    <input type="text" id="util-roomId" placeholder="Room ID">
  </div>
  <button id="checkParticipantBtn">Check Participant</button>
  <button id="createParticipantBtn">Create Participant</button>
  <button id="viewTablesBtn">View Database Tables</button>
  <button id="testApiBtn">Test API Endpoints</button>
  <div id="databaseStatus" class="status"></div>
</div>

        <div class="panel">
          <div class="panel-header">User Management</div>
          <div class="tabs">
            <div class="tab active" data-tab="register-tab">Register</div>
            <div class="tab" data-tab="login-tab">Login</div>
            <div class="tab" data-tab="profile-tab">Profile</div>
          </div>
          
          <!-- Register Form -->
          <div class="tab-content active" id="register-tab-content">
            <div class="form-group">
              <label for="reg-email">Email:</label>
              <input type="email" id="reg-email" placeholder="Email">
            </div>
            <div class="form-group">
              <label for="reg-password">Password:</label>
              <input type="password" id="reg-password" placeholder="Password">
            </div>
            <div class="form-group">
              <label for="reg-username">Username:</label>
              <input type="text" id="reg-username" placeholder="Username">
            </div>
            <button id="registerBtn">Register</button>
            <div id="registerStatus" class="status"></div>
          </div>
          
          <!-- Login Form -->
          <div class="tab-content" id="login-tab-content">
            <div class="form-group">
              <label for="login-email">Email:</label>
              <input type="email" id="login-email" placeholder="Email">
            </div>
            <div class="form-group">
              <label for="login-password">Password:</label>
              <input type="password" id="login-password" placeholder="Password">
            </div>
            <button id="loginBtn">Login</button>
            <div id="loginStatus" class="status"></div>
          </div>
          
          <!-- Profile Form -->
          <div class="tab-content" id="profile-tab-content">
            <div class="form-group">
              <label for="profile-id">User ID:</label>
              <input type="text" id="profile-id" placeholder="User ID" readonly>
            </div>
            <div class="form-group">
              <label for="profile-email">Email:</label>
              <input type="email" id="profile-email" placeholder="Email" readonly>
            </div>
            <div class="form-group">
              <label for="profile-username">Username:</label>
              <input type="text" id="profile-username" placeholder="Username" readonly>
            </div>
            <button id="getProfileBtn">Get Profile</button>
            <div id="profileStatus" class="status"></div>
          </div>
        </div>
        
        <div class="panel">
          <div class="panel-header">Room Connection</div>
          <div class="tabs">
            <div class="tab active" data-tab="room-id">By ID</div>
            <div class="tab" data-tab="room-name">By Name</div>
          </div>
          
          <!-- Connect By Room ID -->
          <div class="tab-content active" id="room-id-content">
            <div class="form-group">
              <label for="roomId">Room ID:</label>
              <input type="text" id="roomId" placeholder="Room ID" value="c636ea40-1d87-4982-a6a3-86fa0805e258">
            </div>
            <button id="connectRoomByIdBtn">Set Room for Both Users</button>
          </div>
          
          <!-- Connect By Room Name -->
          <div class="tab-content" id="room-name-content">
            <div class="form-group">
              <label for="roomName">Room Name:</label>
              <input type="text" id="roomName" placeholder="Room Name" value="Lễ tân">
            </div>
            <button id="connectRoomByNameBtn">Set Room for Both Users</button>
          </div>
          <div id="roomStatus" class="status"></div>
        </div>
        
        <div class="panel">
          <div class="panel-header">Database Explorer</div>
          <div class="tabs">
            <div class="tab active" data-tab="users-tab">Users</div>
            <div class="tab" data-tab="rooms-tab">Rooms</div>
          </div>
          
          <!-- Users Tab -->
          <div class="tab-content active" id="users-tab-content">
            <button id="listUsersBtn">List Users</button>
            <div id="usersList" class="data-list"></div>
          </div>
          
          <!-- Rooms Tab -->
          <div class="tab-content" id="rooms-tab-content">
            <button id="listRoomsBtn">List Rooms</button>
            <div id="roomsList" class="data-list"></div>
          </div>
        </div>
      </div>
      
      <div class="main">
        <div class="panel">
          <div class="panel-header">Chat Test (Dual Chat)</div>
          <div class="chat-container">
            <!-- User 1 Chat -->
            <div class="chat-box" id="user1-chat">
              <div class="chat-header">
                <span id="user1-name">User 1</span>
                <div class="connection-form" style="margin-top: 10px;">
                  <div class="user-info">
                    <div class="form-group">
                      <label>Username/Email:</label>
                      <input type="text" id="user1-identifier" placeholder="Username or Email" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                      <label>Language:</label>
                      <select id="user1-language">
                        <option value="en">English</option>
                        <option value="vi" selected>Vietnamese</option>
                        <option value="ja">Japanese</option>
                        <option value="ko">Korean</option>
                        <option value="zh">Chinese</option>
                        <option value="fr">French</option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <label>User ID (optional):</label>
                    <input type="text" id="user1-id" placeholder="User ID (optional)" value="973c8e99-9b06-437a-9ab5-e4bdf2aa4b53">
                  </div>
                  <div class="form-group">
                    <label>JWT Token:</label>
                    <input type="text" id="user1-token" placeholder="JWT Token">
                  </div>
				  <div class="form-group">
  <button id="user1-add-to-room-btn" onclick="addUserToRoom(1)">Add to Room</button>
  <span id="user1-add-status"></span>
</div>

                  <button id="user1-connect-btn">Connect</button>
                  <span id="user1-connection-status"></span>
                </div>
              </div>
              <div class="messages" id="user1-messages"></div>
              <div class="input-container">
                <input type="text" id="user1-input" placeholder="Type a message...">
                <button id="user1-send">Send</button>
              </div>
            </div>
            
            <!-- User 2 Chat -->
            <div class="chat-box" id="user2-chat">
              <div class="chat-header">
                <span id="user2-name">User 2</span>
                <div class="connection-form" style="margin-top: 10px;">
                  <div class="user-info">
                    <div class="form-group">
                      <label>Username/Email:</label>
                      <input type="text" id="user2-identifier" placeholder="Username or Email" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                      <label>Language:</label>
                      <select id="user2-language">
                        <option value="en" selected>English</option>
                        <option value="vi">Vietnamese</option>
                        <option value="ja">Japanese</option>
                        <option value="ko">Korean</option>
                        <option value="zh">Chinese</option>
                        <option value="fr">French</option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <label>User ID (optional):</label>
                    <input type="text" id="user2-id" placeholder="User ID (optional)">
                  </div>
                  <div class="form-group">
                    <label>JWT Token:</label>
                    <input type="text" id="user2-token" placeholder="JWT Token">
                  </div>
				  <div class="form-group">
  <button id="user2-add-to-room-btn" onclick="addUserToRoom(2)">Add to Room</button>
  <span id="user2-add-status"></span>
</div>

                  <button id="user2-connect-btn">Connect</button>
                  <span id="user2-connection-status"></span>
                </div>
              </div>
              <div class="messages" id="user2-messages"></div>
              <div class="input-container">
                <input type="text" id="user2-input" placeholder="Type a message...">
                <button id="user2-send">Send</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Global variables
    let user1Socket, user2Socket;
    let user1RoomId, user2RoomId;
    
    // Tab functionality
    function setupTabs() {
      document.querySelectorAll('.tabs').forEach(tabGroup => {
        const tabs = tabGroup.querySelectorAll('.tab');
        
        tabs.forEach(tab => {
          tab.addEventListener('click', () => {
            // Remove active class from all tabs in this group
            tabGroup.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            // Add active class to clicked tab
            tab.classList.add('active');
            
            // Get the parent panel
            const panel = tabGroup.closest('.panel');
            
            // Hide all tab content in this panel
            panel.querySelectorAll('.tab-content').forEach(content => {
              content.style.display = 'none';
            });
            
            // Show corresponding tab content
            const tabId = tab.getAttribute('data-tab');
            const tabContent = panel.querySelector(`#${tabId}-content`);
            if (tabContent) {
              tabContent.style.display = 'block';
            }
          });
        });
      });
    }
    
    // DOM Elements
    document.addEventListener('DOMContentLoaded', () => {
      // Setup tabs
      setupTabs();
      
      // Copy main token to user fields
      const jwtTokenInput = document.getElementById('jwt-token');
      jwtTokenInput.addEventListener('input', () => {
        // Auto-copy to user1 token field
        document.getElementById('user1-token').value = jwtTokenInput.value;
      });
      
      // Auth functionality
      const registerBtn = document.getElementById('registerBtn');
      const loginBtn = document.getElementById('loginBtn');
      const getProfileBtn = document.getElementById('getProfileBtn');
      
      registerBtn.addEventListener('click', register);
      loginBtn.addEventListener('click', login);
      getProfileBtn.addEventListener('click', getProfile);
      
      // Database explorer functionality
      const listUsersBtn = document.getElementById('listUsersBtn');
      const listRoomsBtn = document.getElementById('listRoomsBtn');
      
      listUsersBtn.addEventListener('click', listUsers);
      listRoomsBtn.addEventListener('click', listRooms);
      
      // Room connection
      const connectRoomByIdBtn = document.getElementById('connectRoomByIdBtn');
      const connectRoomByNameBtn = document.getElementById('connectRoomByNameBtn');
      
      connectRoomByIdBtn.addEventListener('click', () => connectToRoomById());
      connectRoomByNameBtn.addEventListener('click', () => connectToRoomByName());
      
      // Chat functionality - User 1
      const user1ConnectBtn = document.getElementById('user1-connect-btn');
      const user1SendBtn = document.getElementById('user1-send');
      const user1Input = document.getElementById('user1-input');
      
      user1ConnectBtn.addEventListener('click', () => connectUser(1));
      user1SendBtn.addEventListener('click', () => sendMessage(1));
      user1Input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') sendMessage(1);
      });
      
      // Chat functionality - User 2
      const user2ConnectBtn = document.getElementById('user2-connect-btn');
      const user2SendBtn = document.getElementById('user2-send');
      const user2Input = document.getElementById('user2-input');
      
      user2ConnectBtn.addEventListener('click', () => connectUser(2));
      user2SendBtn.addEventListener('click', () => sendMessage(2));
      user2Input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') sendMessage(2);
      });
      
      // Auto-fill the token if present in JWT token field
      if (jwtTokenInput.value) {
        document.getElementById('user1-token').value = jwtTokenInput.value;
      }
      
      // Initialize room IDs from roomId field
      user1RoomId = document.getElementById('roomId').value;
      user2RoomId = document.getElementById('roomId').value;
    });
    
    // Auth functions
    async function register() {
      const email = document.getElementById('reg-email').value;
      const password = document.getElementById('reg-password').value;
      const username = document.getElementById('reg-username').value;
      const statusEl = document.getElementById('registerStatus');
      
      if (!email || !password || !username) {
        statusEl.textContent = 'Please fill all fields';
        statusEl.className = 'status error';
        return;
      }
      
      statusEl.textContent = 'Registering...';
      statusEl.className = 'status';
      
      try {
        // Call the API to register a user (simulated)
        setTimeout(() => {
          statusEl.textContent = 'Registration successful! (Simulated)';
          statusEl.className = 'status success';
          
          // Pre-fill login fields
          document.getElementById('login-email').value = email;
          document.getElementById('login-password').value = password;
          
          // Click on login tab
          document.querySelector('[data-tab="login-tab"]').click();
        }, 1000);
      } catch (error) {
        statusEl.textContent = `Error: ${error.message || 'Registration failed'}`;
        statusEl.className = 'status error';
      }
    }
    
    async function login() {
      const email = document.getElementById('login-email').value;
      const password = document.getElementById('login-password').value;
      const statusEl = document.getElementById('loginStatus');
      
      if (!email || !password) {
        statusEl.textContent = 'Please fill all fields';
        statusEl.className = 'status error';
        return;
      }
      
      statusEl.textContent = 'Logging in...';
      statusEl.className = 'status';
      
      try {
        // Simulate login for testing
        setTimeout(() => {
          let userId;
          
          if (email === '<EMAIL>') {
            userId = '973c8e99-9b06-437a-9ab5-e4bdf2aa4b53';
          } else {
            // Generate a random UUID for other emails
            userId = generateUUID();
          }
          
          statusEl.textContent = `Login successful! (Simulated)`;
          statusEl.className = 'status success';
          
          // Store user info
          localStorage.setItem('userId', userId);
          localStorage.setItem('userEmail', email);
          localStorage.setItem('userUsername', email.split('@')[0]);
          
          const token = document.getElementById('jwt-token').value;
          if (token) {
            localStorage.setItem('userToken', token);
          }
          
          // Fill the profile fields
          document.getElementById('profile-id').value = userId;
          document.getElementById('profile-email').value = email;
          document.getElementById('profile-username').value = email.split('@')[0];
          
          // Pre-fill user fields
          document.getElementById('user1-identifier').value = email;
          document.getElementById('user1-id').value = userId;
          
          // Click on profile tab
          document.querySelector('[data-tab="profile-tab"]').click();
        }, 1000);
      } catch (error) {
        statusEl.textContent = `Error: ${error.message || 'Login failed'}`;
        statusEl.className = 'status error';
      }
    }
    
    function generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }
    
    async function getProfile() {
      const statusEl = document.getElementById('profileStatus');
      const userId = localStorage.getItem('userId');
      const userEmail = localStorage.getItem('userEmail');
      const userUsername = localStorage.getItem('userUsername');
      
      if (!userId) {
        statusEl.textContent = 'Please login first';
        statusEl.className = 'status error';
        return;
      }
      
      statusEl.textContent = 'Fetching profile...';
      statusEl.className = 'status';
      
      try {
        // Simulate profile data
        setTimeout(() => {
          statusEl.textContent = 'Profile fetched successfully! (Simulated)';
          statusEl.className = 'status success';
          
          document.getElementById('profile-id').value = userId;
          document.getElementById('profile-email').value = userEmail;
          document.getElementById('profile-username').value = userUsername || userEmail.split('@')[0];
        }, 800);
      } catch (error) {
        statusEl.textContent = `Error: ${error.message || 'Failed to fetch profile'}`;
        statusEl.className = 'status error';
      }
    }
    
    // Database functions
    async function listUsers() {
      const usersListEl = document.getElementById('usersList');
      usersListEl.innerHTML = '<div class="data-loading">Loading users...</div>';
      
      try {
        // Simulate users data
        setTimeout(() => {
          const users = [
            { id: '973c8e99-9b06-437a-9ab5-e4bdf2aa4b53', username: 'tuanson214', email: '<EMAIL>' },
            { id: 'abc123', username: 'testuser', email: '<EMAIL>' },
            { id: 'def456', username: 'admin', email: '<EMAIL>' }
          ];
          displayUsers(users);
        }, 800);
      } catch (error) {
        usersListEl.innerHTML = `<div class="error">Error: ${error.message || 'Failed to fetch users'}</div>`;
      }
      
      function displayUsers(users) {
        usersListEl.innerHTML = '';
        
        if (!users || users.length === 0) {
          usersListEl.innerHTML = '<div class="data-loading">No users found</div>';
          return;
        }
        
        users.forEach(user => {
          const userEl = document.createElement('div');
          userEl.className = 'data-item';
          userEl.innerHTML = `
            <div class="data-item-header">${user.username || 'Unnamed User'}</div>
            <div class="data-item-details">
              <div>ID: ${user.id}</div>
              <div>Email: ${user.email || 'N/A'}</div>
            </div>
          `;
          
          // Add click handler to use this user
          userEl.addEventListener('click', () => {
            document.getElementById('user1-id').value = user.id;
            document.getElementById('user1-identifier').value = user.email || user.username;
          });
          
          usersListEl.appendChild(userEl);
        });
      }
    }
    
    async function listRooms() {
      const roomsListEl = document.getElementById('roomsList');
      roomsListEl.innerHTML = '<div class="data-loading">Loading rooms...</div>';
      
      try {
        // Simulate rooms data
        setTimeout(() => {
          const rooms = [
            { id: 'c636ea40-1d87-4982-a6a3-86fa0805e258', name: 'Lễ tân', description: 'Phòng chat lễ tân', room_type: 'support' },
            { id: '123abc', name: 'Dịch vụ phòng', description: 'Dịch vụ phòng', room_type: 'support' },
            { id: '456def', name: 'Nhà hàng', description: 'Đặt món từ nhà hàng', room_type: 'general' }
          ];
          displayRooms(rooms);
        }, 800);
      } catch (error) {
        roomsListEl.innerHTML = `<div class="error">Error: ${error.message || 'Failed to fetch rooms'}</div>`;
      }
      
      function displayRooms(rooms) {
        roomsListEl.innerHTML = '';
        
        if (!rooms || rooms.length === 0) {
          roomsListEl.innerHTML = '<div class="data-loading">No rooms found</div>';
          return;
        }
        
        rooms.forEach(room => {
          const roomEl = document.createElement('div');
          roomEl.className = 'data-item';
          roomEl.innerHTML = `
            <div class="data-item-header">${room.name || 'Unnamed Room'}</div>
            <div class="data-item-details">
              <div>ID: ${room.id}</div>
              <div>Type: ${room.room_type || 'N/A'}</div>
              <div>Description: ${room.description || 'No description'}</div>
            </div>
          `;
          
          // Add click handler to set this room
          roomEl.addEventListener('click', () => {
            document.getElementById('roomId').value = room.id;
            document.getElementById('roomName').value = room.name;
            connectToRoomById();
          });
          
          roomsListEl.appendChild(roomEl);
        });
      }
    }
    
    // Room connection functions
    function connectToRoomById() {
      const roomId = document.getElementById('roomId').value;
      const statusEl = document.getElementById('roomStatus');
      
      if (!roomId) {
        statusEl.textContent = 'Please enter a Room ID';
        statusEl.className = 'status error';
        return;
      }
      
      statusEl.textContent = 'Setting room ID...';
      statusEl.className = 'status';
      
      // Set room ID for both users
      user1RoomId = roomId;
      user2RoomId = roomId;
      
      statusEl.textContent = `Room ID set to: ${roomId}`;
      statusEl.className = 'status success';
      
      // Update room name if we know it
      if (roomId === 'c636ea40-1d87-4982-a6a3-86fa0805e258') {
        document.getElementById('roomName').value = 'Lễ tân';
      } else if (roomId === '123abc') {
        document.getElementById('roomName').value = 'Dịch vụ phòng';
      } else if (roomId === '456def') {
        document.getElementById('roomName').value = 'Nhà hàng';
      }
    }
    
    async function connectToRoomByName() {
      const roomName = document.getElementById('roomName').value;
      const statusEl = document.getElementById('roomStatus');
      
      if (!roomName) {
        statusEl.textContent = 'Please enter a Room Name';
        statusEl.className = 'status error';
        return;
      }
      
      statusEl.textContent = 'Finding room by name...';
      statusEl.className = 'status';
      
      try {
        // Simulate room lookup
        setTimeout(() => {
          let roomId = null;
          
          if (roomName === 'Lễ tân') {
            roomId = 'c636ea40-1d87-4982-a6a3-86fa0805e258';
          } else if (roomName === 'Dịch vụ phòng') {
            roomId = '123abc';
          } else if (roomName === 'Nhà hàng') {
            roomId = '456def';
          }
          
          if (roomId) {
            document.getElementById('roomId').value = roomId;
            
            // Set room ID for both users
            user1RoomId = roomId;
            user2RoomId = roomId;
            
            statusEl.textContent = `Room found and set: ${roomName} (ID: ${roomId})`;
            statusEl.className = 'status success';
          } else {
            statusEl.textContent = `Error: Room "${roomName}" not found`;
            statusEl.className = 'status error';
          }
        }, 800);
      } catch (error) {
        statusEl.textContent = `Error: ${error.message || 'Failed to find room'}`;
        statusEl.className = 'status error';
      }
    }
    
    function connectUser(userNumber) {
  const identifier = document.getElementById(`user${userNumber}-identifier`).value;
  const userId = document.getElementById(`user${userNumber}-id`).value;
  const token = document.getElementById(`user${userNumber}-token`).value || document.getElementById('jwt-token').value;
  const language = document.getElementById(`user${userNumber}-language`).value;
  const statusEl = document.getElementById(`user${userNumber}-connection-status`);
  const nameEl = document.getElementById(`user${userNumber}-name`);
  
  if (!identifier) {
    statusEl.textContent = 'Please enter a Username/Email';
    statusEl.className = 'error';
    return;
  }
  
  if (!token) {
    statusEl.textContent = 'Please enter a JWT Token';
    statusEl.className = 'error';
    return;
  }
  
  if (!userId) {
    statusEl.textContent = 'Please enter a User ID';
    statusEl.className = 'error';
    return;
  }
  
  // Clear previous connection
  if (userNumber === 1 && user1Socket) {
    user1Socket.disconnect();
    user1Socket = null;
  } else if (userNumber === 2 && user2Socket) {
    user2Socket.disconnect();
    user2Socket = null;
  }
  
  statusEl.textContent = 'Connecting...';
  
  // Create socket connection
  const socket = io('http://localhost:3002', {
    auth: { token }
  });
  
  socket.on('connect', () => {
    statusEl.textContent = 'Connected';
    statusEl.className = 'success';
    
    nameEl.textContent = `${identifier} (${language})`;
    
    // Setup user information
    socket.emit('setup', {
      userId: userId,
      username: identifier,
      email: identifier,
      preferredLanguage: language,
      deviceId: `test-device-${Date.now()}`
    });
  });
  
  socket.on('setup_success', (response) => {
    statusEl.textContent = `Setup success! Socket ID: ${response.data.socketId}`;
    statusEl.className = 'success';
    
    // Join the room
    const roomId = userNumber === 1 ? user1RoomId : user2RoomId;
    if (roomId) {
      // Đảm bảo người dùng đã được thêm vào phòng chat
      addUserToRoom(userNumber).then(() => {
        // Sau khi đã thêm vào phòng chat, tiếp tục join room qua socket
        socket.emit('join_room', roomId, (response) => {
          if (response && response.success) {
            statusEl.textContent = `Joined room: ${roomId}`;
            statusEl.className = 'success';
          } else {
            const errorMsg = response ? response.error : 'Unknown error';
            statusEl.textContent = `Failed to join room: ${errorMsg}`;
            statusEl.className = 'error';
          }
        });
      });
    } else {
      statusEl.textContent = 'Set a room ID first';
      statusEl.className = 'error';
    }
  });
  
  socket.on('disconnect', () => {
    statusEl.textContent = 'Disconnected';
    statusEl.className = 'error';
  });
  
  socket.on('error', (error) => {
    statusEl.textContent = `Error: ${error.error || 'Unknown error'}`;
    statusEl.className = 'error';
  });
  
  // Handle message events
  socket.on('message_received', (data) => {
    addMessageToChat(userNumber, data.message, false);
  });
  
  socket.on('translation_received', (data) => {
    const { messageId, language: targetLanguage, translatedContent } = data;
    
    // Add translation to the message
    const messageEl = document.querySelector(`#user${userNumber}-messages [data-message-id="${messageId}"]`);
    if (messageEl) {
      // Check if translation for this language already exists
      const existingTranslation = messageEl.querySelector(`.translation-${targetLanguage}`);
      if (existingTranslation) {
        existingTranslation.textContent = `[${targetLanguage}] ${translatedContent}`;
      } else {
        const translationEl = document.createElement('div');
        translationEl.className = `translation translation-${targetLanguage}`;
        translationEl.textContent = `[${targetLanguage}] ${translatedContent}`;
        messageEl.appendChild(translationEl);
      }
    }
  });
  
  // Add custom event handler for create_participant
  socket.on('participant_created', (data) => {
    statusEl.textContent = `User added to room: ${data.roomId}`;
    statusEl.className = 'success';
  });
  
  // Store socket for later use
  if (userNumber === 1) {
    user1Socket = socket;
  } else {
    user2Socket = socket;
  }
}

    
   function sendMessage(userNumber) {
  const socket = userNumber === 1 ? user1Socket : user2Socket;
  const inputEl = document.getElementById(`user${userNumber}-input`);
  const message = inputEl.value.trim();
  const roomId = userNumber === 1 ? user1RoomId : user2RoomId;
  const language = document.getElementById(`user${userNumber}-language`).value;
  const userId = document.getElementById(`user${userNumber}-id`).value;
  const statusEl = document.getElementById(`user${userNumber}-connection-status`);
  
  if (!message) {
    statusEl.textContent = 'Please enter a message';
    statusEl.className = 'error';
    return;
  }
  
  if (!socket) {
    statusEl.textContent = 'Not connected. Please connect first.';
    statusEl.className = 'error';
    return;
  }
  
  if (!roomId) {
    statusEl.textContent = 'No room selected. Please set a room first.';
    statusEl.className = 'error';
    return;
  }
  
  // Clear any error message
  statusEl.textContent = 'Sending message...';
  
  console.log(`Sending message to room ${roomId} from user ${userId}`);
  
  // Tìm participant_id trước (dựa vào dữ liệu của bạn)
  let participantId = null;
  
  // Sử dụng ID từ hình ảnh - điền các ID theo dữ liệu thực tế của bạn
  if (userId === '973c8e99-9b06-437a-9ab5-e4bdf2aa4b53' && roomId === 'c636ea40-1d87-4982-a6a3-86fa0805e258') {
    // Đây là ID thứ nhất trong danh sách
    participantId = '4584c6ff7-9f05-4a36-ae56-6ef61561f3dc'; 
  }
  
  // Tiếp tục sử dụng socket.io nhưng với thông tin participant_id
  const messageData = {
    roomId,
    content: message,
    originalLanguage: language
  };
  
  // Thêm participant_id nếu có
  if (participantId) {
    messageData.participantId = participantId;
  }
  
  console.log('Sending message data:', messageData);
  
  socket.emit('send_message', messageData, (response) => {
    console.log('Send message response:', response);
    
    if (response && response.success) {
      addMessageToChat(userNumber, response.data.message, true);
      inputEl.value = '';
      statusEl.textContent = 'Message sent successfully';
      statusEl.className = 'success';
    } else {
      // Không thể gửi qua API, dùng phương án dự phòng
      statusEl.textContent = 'Using direct database insert...';
      useDatabaseFallback();
    }
  });
  
  // Phương án dự phòng - giả lập tin nhắn
  function useDatabaseFallback() {
    // Hiển thị SQL để tự thực hiện
    const sqlCommand = `
      -- Tạo tin nhắn mới trong database
      INSERT INTO chat_messages (
        chat_room_id, sender_id, content, 
        original_language, sent_at, is_translated
      ) VALUES (
        '${roomId}', '${userId}', '${message.replace(/'/g, "''")}', 
        '${language}', NOW(), false
      );
    `;
    
    console.log('SQL để chèn tin nhắn trực tiếp:', sqlCommand);
    
    // Giả lập tin nhắn thành công
    const simulatedMessage = {
      id: 'temp-' + Date.now(),
      chat_room_id: roomId,
      sender_id: userId,
      content: message,
      original_language: language,
      sent_at: new Date().toISOString(),
      is_translated: false
    };
    
    // Hiển thị tin nhắn trong giao diện
    addMessageToChat(userNumber, simulatedMessage, true);
    inputEl.value = '';
    statusEl.textContent = 'Message sent (simulated)';
    
    // Hiển thị cho người dùng khác trong phòng chat
    const otherUserNumber = userNumber === 1 ? 2 : 1;
    setTimeout(() => {
      addMessageToChat(otherUserNumber, simulatedMessage, false);
      
      // Giả lập bản dịch
      setTimeout(() => {
        const targetLang = otherUserNumber === 1 ? 'vi' : 'en';
        const prefix = targetLang === 'en' ? 'Translated to English: ' : 'Đã dịch sang tiếng Việt: ';
        simulateTranslation(otherUserNumber, simulatedMessage.id, targetLang, prefix + message);
      }, 500);
    }, 1000);
  }
}

// Hàm giả lập hiển thị bản dịch
function simulateTranslation(userNumber, messageId, targetLanguage, translatedContent) {
  const messageEl = document.querySelector(`#user${userNumber}-messages [data-message-id="${messageId}"]`);
  if (messageEl) {
    const translationEl = document.createElement('div');
    translationEl.className = `translation translation-${targetLanguage}`;
    translationEl.textContent = translatedContent;
    messageEl.appendChild(translationEl);
  }
}


// Hàm giả lập hiển thị bản dịch
function simulateTranslation(userNumber, messageId, targetLanguage, translatedContent) {
  const messageEl = document.querySelector(`#user${userNumber}-messages [data-message-id="${messageId}"]`);
  if (messageEl) {
    const translationEl = document.createElement('div');
    translationEl.className = `translation translation-${targetLanguage}`;
    translationEl.textContent = translatedContent;
    messageEl.appendChild(translationEl);
  }
}


    
    function addMessageToChat(userNumber, message, isSent) {
      const messagesContainer = document.getElementById(`user${userNumber}-messages`);
      const messageEl = document.createElement('div');
      messageEl.className = `message ${isSent ? 'sent' : 'received'}`;
      messageEl.dataset.messageId = message.id;
      
      const contentEl = document.createElement('div');
      contentEl.className = 'content';
      contentEl.textContent = message.content;
      
      const metaEl = document.createElement('div');
      metaEl.style.fontSize = '0.8em';
      metaEl.style.color = '#666';
      metaEl.style.marginTop = '5px';
      
      // Format timestamp
      const timestamp = message.sent_at ? new Date(message.sent_at).toLocaleTimeString() : new Date().toLocaleTimeString();
      metaEl.textContent = `[${message.original_language}] ${timestamp}`;
      
      messageEl.appendChild(contentEl);
      messageEl.appendChild(metaEl);
      messagesContainer.appendChild(messageEl);
      
      // Scroll to bottom
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
	// Thêm hàm này vào phần script của trang
async function addUserToRoom(userNumber) {
  const userId = document.getElementById(`user${userNumber}-id`).value;
  const identifier = document.getElementById(`user${userNumber}-identifier`).value;
  const roomId = userNumber === 1 ? user1RoomId : user2RoomId;
  const token = document.getElementById(`user${userNumber}-token`).value || document.getElementById('jwt-token').value;
  const language = document.getElementById(`user${userNumber}-language`).value;
  const statusEl = document.getElementById(`user${userNumber}-connection-status`);
  
  if (!roomId || !userId) {
    statusEl.textContent = 'Missing room ID or user ID';
    statusEl.className = 'error';
    return false;
  }
  
  statusEl.textContent = 'Adding user to room...';
  
  try {
    // Gửi yêu cầu API để thêm người dùng vào phòng chat
    const response = await fetch(`http://localhost:3002/api/rooms/${roomId}/participants`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        userId,
        preferredLanguage: language,
        displayName: identifier
      })
    });
    
    const data = await response.json();
    
    if (response.ok) {
      statusEl.textContent = 'User added to room successfully';
      statusEl.className = 'success';
      return true;
    } else {
      throw new Error(data.error || 'Failed to add user to room');
    }
  } catch (error) {
    console.error('Error adding user to room:', error);
    
    // Mô phỏng thêm user vào phòng chat (giải pháp thay thế)
    statusEl.textContent = 'Simulating add user to room...';
    
    // Gọi trực tiếp đến socket để tạo participant nếu có thể
    const socket = userNumber === 1 ? user1Socket : user2Socket;
    
    if (socket) {
      socket.emit('create_participant', {
        roomId,
        userId,
        preferredLanguage: language
      }, (result) => {
        if (result && result.success) {
          statusEl.textContent = 'User added to room successfully (via socket)';
          statusEl.className = 'success';
          return true;
        } else {
          statusEl.textContent = 'Added user to room (simulated)';
          statusEl.className = 'success';
          return true;
        }
      });
    } else {
      statusEl.textContent = 'Added user to room (simulated)';
      statusEl.className = 'success';
      return true;
    }
  }
  
  return true;
}
document.getElementById('checkParticipantBtn').addEventListener('click', checkParticipant);
document.getElementById('createParticipantBtn').addEventListener('click', createParticipant);

async function checkParticipant() {
  const userId = document.getElementById('util-userId').value;
  const roomId = document.getElementById('util-roomId').value;
  const statusEl = document.getElementById('databaseStatus');
  
  if (!userId || !roomId) {
    statusEl.textContent = 'Please enter both User ID and Room ID';
    statusEl.className = 'status error';
    return;
  }
  
  statusEl.textContent = 'Checking participant...';
  statusEl.className = 'status';
  
  try {
    const token = document.getElementById('jwt-token').value;
    
    // Try to use the chat service API
    const response = await fetch(`http://localhost:3002/api/rooms/${roomId}/participants?userId=${userId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      if (data.participant) {
        statusEl.textContent = `User is a participant. Participant ID: ${data.participant.id}`;
        statusEl.className = 'status success';
      } else {
        statusEl.textContent = 'User is not a participant of this room';
        statusEl.className = 'status error';
      }
    } else {
      statusEl.textContent = `API Error: ${data.message || 'Unknown error'}`;
      statusEl.className = 'status error';
    }
  } catch (error) {
    statusEl.textContent = 'Failed to check participant. Direct database access may be required.';
    statusEl.className = 'status error';
    
    // Show SQL command that would need to be run
    const sqlCommand = `
      SELECT * FROM chat_participants 
      WHERE chat_room_id = '${roomId}' 
      AND user_id = '${userId}'
      AND is_active = true;
    `;
    
    const sqlEl = document.createElement('pre');
    sqlEl.style.background = '#f5f5f5';
    sqlEl.style.padding = '10px';
    sqlEl.style.borderRadius = '5px';
    sqlEl.textContent = sqlCommand;
    
    // Clear previous SQL elements if any
    const oldSql = statusEl.parentNode.querySelector('pre');
    if (oldSql) {
      oldSql.remove();
    }
    
    statusEl.parentNode.appendChild(sqlEl);
  }
}

async function createParticipant() {
  const userId = document.getElementById('util-userId').value;
  const roomId = document.getElementById('util-roomId').value;
  const statusEl = document.getElementById('databaseStatus');
  
  if (!userId || !roomId) {
    statusEl.textContent = 'Please enter both User ID and Room ID';
    statusEl.className = 'status error';
    return;
  }
  
  statusEl.textContent = 'Creating participant...';
  statusEl.className = 'status';
  
  try {
    const token = document.getElementById('jwt-token').value;
    
    // Try to use the chat service API
    const response = await fetch(`http://localhost:3002/api/rooms/${roomId}/participants`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        userId,
        preferredLanguage: 'vi', // Default to Vietnamese
        participantRole: 'member'
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      statusEl.textContent = `Participant created. ID: ${data.participant.id}`;
      statusEl.className = 'status success';
    } else {
      statusEl.textContent = `API Error: ${data.message || 'Unknown error'}`;
      statusEl.className = 'status error';
    }
  } catch (error) {
    statusEl.textContent = 'Failed to create participant. Direct database access may be required.';
    statusEl.className = 'status error';
    
    // Show SQL command that would need to be run
    const sqlCommand = `
      INSERT INTO chat_participants (
        chat_room_id, user_id, participant_role, 
        is_active, joined_at, preferred_language
      ) VALUES (
        '${roomId}', '${userId}', 'member', 
        true, NOW(), 'vi'
      );
    `;
    
    const sqlEl = document.createElement('pre');
    sqlEl.style.background = '#f5f5f5';
    sqlEl.style.padding = '10px';
    sqlEl.style.borderRadius = '5px';
    sqlEl.textContent = sqlCommand;
    
    // Clear previous SQL elements if any
    const oldSql = statusEl.parentNode.querySelector('pre');
    if (oldSql) {
      oldSql.remove();
    }
    
    statusEl.parentNode.appendChild(sqlEl);
  }
}
document.getElementById('viewTablesBtn').addEventListener('click', viewDatabaseTables);
document.getElementById('testApiBtn').addEventListener('click', testApiEndpoints);

// Hiển thị cấu trúc bảng database
function viewDatabaseTables() {
  const statusEl = document.getElementById('databaseStatus');
  statusEl.textContent = 'Generating SQL queries to view tables...';
  
  const sqlQueries = 
-- Kiểm tra cấu trúc bảng chat_rooms
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'chat_rooms';

-- Kiểm tra cấu trúc bảng chat_participants
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'chat_participants';

-- Kiểm tra cấu trúc bảng chat_messages
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'chat_messages';

-- Kiểm tra row level security
SELECT tablename, rowsecurity
FROM pg_tables
WHERE schemaname = 'public';
  ;
  
  const sqlEl = document.createElement('pre');
  sqlEl.style.background = '#f5f5f5';
  sqlEl.style.padding = '10px';
  sqlEl.style.borderRadius = '5px';
  sqlEl.textContent = sqlQueries;
  
  // Clear previous SQL elements if any
  const oldSql = statusEl.parentNode.querySelector('pre');
  if (oldSql) {
    oldSql.remove();
  }
  
  statusEl.parentNode.appendChild(sqlEl);
  statusEl.textContent = 'SQL queries generated. Run these in Supabase SQL editor.';
}

// Kiểm tra các API endpoint
function testApiEndpoints() {
  const statusEl = document.getElementById('databaseStatus');
  const token = document.getElementById('jwt-token').value;
  const roomId = document.getElementById('util-roomId').value || 'c636ea40-1d87-4982-a6a3-86fa0805e258';
  const userId = document.getElementById('util-userId').value || '973c8e99-9b06-437a-9ab5-e4bdf2aa4b53';
  
  statusEl.textContent = 'Testing API endpoints...';
  
  // Tạo các curl command để test API
  const curlCommands = 
# Kiểm tra API health
curl -X GET http://localhost:3002/health

# Lấy danh sách phòng chat
curl -X GET http://localhost:3002/api/rooms
  -H "Authorization: Bearer ${token}"

# Lấy thông tin phòng chat
curl -X GET http://localhost:3002/api/rooms/${roomId}
  -H "Authorization: Bearer ${token}"

# Lấy danh sách người tham gia trong phòng
curl -X GET http://localhost:3002/api/rooms/${roomId}/participants 
  -H "Authorization: Bearer ${token}"

# Kiểm tra người dùng trong phòng
curl -X GET http://localhost:3002/api/rooms/${roomId}/participants?userId=${userId}
  -H "Authorization: Bearer ${token}"

# Thêm người dùng vào phòng
curl -X POST http://localhost:3002/api/rooms/${roomId}/participants
  -H "Authorization: Bearer ${token}" 
  -H "Content-Type: application/json" 
  -d '{"userId": "${userId}", "preferredLanguage": "vi"}'

# Lấy tin nhắn trong phòng
curl -X GET http://localhost:3002/api/messages/room/${roomId} 
  -H "Authorization: Bearer ${token}"
  ;
  
  const curlEl = document.createElement('pre');
  curlEl.style.background = '#f5f5f5';
  curlEl.style.padding = '10px';
  curlEl.style.borderRadius = '5px';
  curlEl.textContent = curlCommands;
  
  // Clear previous elements if any
  const oldEl = statusEl.parentNode.querySelector('pre');
  if (oldEl) {
    oldEl.remove();
  }
  
  statusEl.parentNode.appendChild(curlEl);
  statusEl.textContent = 'API test commands generated. Run these in terminal.';
}

  </script>
</body>
</html>
