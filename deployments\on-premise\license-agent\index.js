const fs = require('fs');
const path = require('path');
const os = require('os');
const crypto = require('crypto');
const http = require('http');
const https = require('https');

// Cấu hình
const CONFIG = {
  checkInterval: parseInt(process.env.CHECK_INTERVAL || '86400', 10) * 1000, // mặc định 24h
  licenseServerUrl: process.env.LICENSE_SERVER_URL || 'https://license.loaloa.app',
  licenseConfigPath: '/app/data/license_config.json',
  adminPortalUrl: 'http://admin-portal:3000'
};

// Hàm tạo hardware fingerprint
function generateHardwareFingerprint() {
  const cpus = os.cpus();
  const network = os.networkInterfaces();
  const platform = os.platform();
  const release = os.release();
  const hostname = os.hostname();
  
  const systemInfo = JSON.stringify({
    cpuModel: cpus[0]?.model || '',
    cpuCount: cpus.length,
    network: Object.keys(network),
    platform,
    release,
    hostname
  });
  
  return crypto.createHash('sha256').update(systemInfo).digest('hex');
}

// Hàm đọc cấu hình license
function getLicenseConfig() {
  try {
    if (fs.existsSync(CONFIG.licenseConfigPath)) {
      const configData = fs.readFileSync(CONFIG.licenseConfigPath, 'utf8');
      return JSON.parse(configData);
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// Hàm gửi request check-in
async function checkInLicense() {
  const licenseConfig = getLicenseConfig();
  
  if (!licenseConfig) {
    console.log('No license config found. Skipping check-in.');
    return;
  }
  
  const hardwareFingerprint = generateHardwareFingerprint();
  
  try {
    console.log(`Checking in license ${licenseConfig.licenseKey}...`);
    
    // Tạo request check-in đến Admin Portal
    const requestData = JSON.stringify({
      licenseKey: licenseConfig.licenseKey,
      hardwareFingerprint
    });
    
    const options = {
      hostname: 'admin-portal',
      port: 3000,
      path: '/api/license/checkin',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': requestData.length
      }
    };
    
    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          if (res.statusCode === 200) {
            console.log('License check-in successful:', data);
            resolve(JSON.parse(data));
          } else {
            console.error(`License check-in failed with status ${res.statusCode}:`, data);
            reject(new Error(`Check-in failed with status ${res.statusCode}`));
          }
        });
      });
      
      req.on('error', (error) => {
        console.error('Error during license check-in:', error);
        reject(error);
      });
      
      req.write(requestData);
      req.end();
    });
  } catch (error) {
    console.error('Error checking in license:', error);
    throw error;
  }
}

// Hàm chính để chạy kiểm tra định kỳ
async function startPeriodicChecks() {
  console.log(`License Agent started. Will check-in every ${CONFIG.checkInterval / 1000} seconds.`);
  
  // Check-in ngay lập tức khi khởi động
  try {
    await checkInLicense();
  } catch (error) {
    console.error('Initial check-in failed:', error);
  }
  
  // Thiết lập check-in định kỳ
  setInterval(async () => {
    try {
      await checkInLicense();
    } catch (error) {
      console.error('Periodic check-in failed:', error);
    }
  }, CONFIG.checkInterval);
}

// Khởi động agent
startPeriodicChecks();
