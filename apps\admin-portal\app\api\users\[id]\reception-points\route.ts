import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createAdminClient } from '../../../../../lib/supabase/admin';
import fs from 'fs';
import path from 'path';

// Function lấy tenant_id từ file config
function getTenantId() {
  try {
    const configPath = path.resolve('./license_config.json');
    if (fs.existsSync(configPath)) {
      const fileContent = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(fileContent);
      return config.tenant_id;
    }
  } catch (error) {
    console.error('Error reading license config:', error);
  }
  return null;
}

// GET: Lấy danh sách reception points của user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id;
    
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }
    
    // Tạo Supabase client
    const supabase = createAdminClient(cookies());
    
    // Kiểm tra user có tồn tại không
    const { data: user, error: userError } = await supabase
      .from('tenant_users')
      .select('id')
      .eq('id', userId)
      .eq('tenant_id', tenant_id)
      .single();
    
    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Lấy danh sách reception points của user
    const { data, error } = await supabase
      .from('tenant_user_reception_points')
      .select(`
        *,
        reception_point:reception_point_id (
          id, 
          name, 
          code
        )
      `)
      .eq('tenant_id', tenant_id)
      .eq('tenant_user_id', userId);
    
    if (error) {
      console.error('Error fetching user reception points:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Error in GET user reception points:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}

// POST: Gán user vào reception point
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id;
    const { reception_point_id, priority = 1, is_primary = false } = await request.json();
    
    // Validate required fields
    if (!reception_point_id) {
      return NextResponse.json({ error: 'Reception point ID is required' }, { status: 400 });
    }
    
    // Lấy tenant_id từ config
    const tenant_id = getTenantId();
    if (!tenant_id) {
      return NextResponse.json({ error: 'Tenant ID not found. Please activate your license.' }, { status: 400 });
    }
    
    // Tạo Supabase client
    const supabase = createAdminClient(cookies());
    
    // Kiểm tra user có tồn tại không
    const { data: user, error: userError } = await supabase
      .from('tenant_users')
      .select('id')
      .eq('id', userId)
      .eq('tenant_id', tenant_id)
      .single();
    
    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Kiểm tra reception point có tồn tại không
    const { data: receptionPoint, error: receptionPointError } = await supabase
      .from('tenant_message_reception_points')
      .select('id')
      .eq('id', reception_point_id)
      .eq('tenant_id', tenant_id)
      .single();
    
    if (receptionPointError || !receptionPoint) {
      return NextResponse.json({ error: 'Reception point not found' }, { status: 404 });
    }
    
    // Kiểm tra xem đã có liên kết này chưa
    const { data: existingLink, error: linkCheckError } = await supabase
      .from('tenant_user_reception_points')
      .select('id')
      .eq('tenant_id', tenant_id)
      .eq('tenant_user_id', userId)
      .eq('reception_point_id', reception_point_id)
      .maybeSingle();
    
    if (existingLink) {
      return NextResponse.json({ error: 'User is already assigned to this reception point' }, { status: 409 });
    }
    
    // Nếu is_primary = true, cập nhật tất cả các liên kết khác của user thành is_primary = false
    if (is_primary) {
      await supabase
        .from('tenant_user_reception_points')
        .update({ is_primary: false })
        .eq('tenant_id', tenant_id)
        .eq('tenant_user_id', userId);
    }
    
    // Tạo liên kết mới
    const { data, error } = await supabase
  .from('tenant_user_reception_points')
  .insert({
    tenant_id,
    tenant_user_id: userId,
    reception_point_id,
    priority: priority || 1,
    is_primary: is_primary || false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  })
  .select(`
    *,
    reception_point:reception_point_id (
      id, 
      name, 
      code
    )
  `)
  .single();
    
    if (error) {
      console.error('Error assigning user to reception point:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ data, message: 'User assigned to reception point successfully' }, { status: 201 });
  } catch (error: any) {
    console.error('Error in POST user reception point:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}
