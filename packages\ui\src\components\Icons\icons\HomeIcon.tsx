import React from 'react';
import { IconProps } from '../Icon';

interface HomeIconSvgProps {
  width?: number;
  height?: number;
  color?: string;
  [key: string]: any;
}

// SVG icon component
const HomeIconSvg: React.FC<HomeIconSvgProps> = ({ width = 24, height = 24, color = '#464646', ...props }) => {
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path 
        d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z" 
        fill={color} 
      />
    </svg>
  );
};

// Outlined version
const HomeOutlinedIconSvg: React.FC<HomeIconSvgProps> = ({ width = 24, height = 24, color = '#464646', ...props }) => {
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path 
        d="M12 5.69L17 10.19V18H15V12H9V18H7V10.19L12 5.69ZM12 3L2 12H5V20H11V14H13V20H19V12H22L12 3Z" 
        fill={color} 
      />
    </svg>
  );
};

// Export the filled version as default
export const HomeIcon = React.memo(HomeIconSvg);

// Export the outlined version
export const HomeOutlinedIcon = React.memo(HomeOutlinedIconSvg);