import React, { useRef, useEffect } from 'react';
import styles from './MessageList.module.scss';
import MessageBubble from '../MessageBubble';

interface Message {
  id: string;
  content: string;
  timestamp: string | Date;
  isSender: boolean;
  status?: 'sending' | 'sent' | 'delivered' | 'read';
}

interface MessageGroup {
  date: string;
  messages: Message[];
}

export interface MessageListProps {
  /**
   * Messages to display
   */
  messages: Message[];
  /**
   * Should scroll to bottom on new messages
   */
  autoScroll?: boolean;
  /**
   * Additional CSS class
   */
  className?: string;
}

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  autoScroll = true,
  className = '',
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Group messages by date
  const groupedMessages: MessageGroup[] = React.useMemo(() => {
    if (!messages.length) return [];
    
    return messages.reduce((groups: MessageGroup[], message: Message) => {
      const date = new Date(message.timestamp);
      const dateString = date.toLocaleDateString();
      
      // Find existing group or create new one
      const existingGroup = groups.find(group => group.date === dateString);
      if (existingGroup) {
        existingGroup.messages.push(message);
      } else {
        groups.push({
          date: dateString,
          messages: [message]
        });
      }
      
      return groups;
    }, []);
  }, [messages]);
  
  // Scroll to bottom on new messages
  useEffect(() => {
    if (autoScroll && containerRef.current && messages.length > 0) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [messages, autoScroll]);

  // Format date for display
  const formatDate = (dateString: string): string => {
    const today = new Date().toLocaleDateString();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayString = yesterday.toLocaleDateString();
    
    if (dateString === today) {
      return 'Today';
    } else if (dateString === yesterdayString) {
      return 'Yesterday';
    } else {
      return new Date(dateString).toLocaleDateString(undefined, { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    }
  };

  return (
    <div
      ref={containerRef}
      className={`${styles.container} ${className}`}
    >
      {groupedMessages.map((group, groupIndex) => (
        <div key={group.date} className={styles.messageGroup}>
          <div className={styles.dateHeader}>
            <span>{formatDate(group.date)}</span>
          </div>
          
          {group.messages.map((message) => (
            <MessageBubble
              key={message.id}
              content={message.content}
              isSender={message.isSender}
              timestamp={message.timestamp}
              status={message.status}
            />
          ))}
        </div>
      ))}
      
      {messages.length === 0 && (
        <div className={styles.emptyState}>
          <p>No messages yet</p>
        </div>
      )}
    </div>
  );
};

export default MessageList;
