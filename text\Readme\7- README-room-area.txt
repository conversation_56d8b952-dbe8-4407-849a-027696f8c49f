# LoaLoa Admin Portal - Quản lý Phòng & Khu vực

## Tổng quan

Module Quản lý Phòng & Khu vực trong Admin Portal của LoaLoa giúp quản lý các phòng và khu vực công cộng trong khách sạn/resort. Module này cung cấp giao diện trực quan để thêm, sửa, xóa và theo dõi trạng thái của phòng và khu vực.

## Cấu trúc thư mục

rooms-areas/ 
├── page.tsx # Trang tổng quan phòng & khu vực 
├── rooms-areas.module.scss # CSS cho trang tổng quan 
├── rooms/ # Quản lý phòng 
│ ├── page.tsx # Danh sách phòng 
│ ├── rooms.module.scss # CSS cho danh sách phòng 
│ ├── create/ # Tạo phòng mới 
│ │ ├── page.tsx 
│ │ └── create-room.module.scss 
│ └── [id]/ # Chi tiết & chỉnh sửa phòng 
│ ├── page.tsx # Trang chi tiết phòng 
│ └── edit/ # Trang chỉnh sửa phòng 
│ └── page.tsx 
├── areas/ # Quản lý khu vực 
│ ├── page.tsx # Danh sách khu vực 
│ ├── areas.module.scss # CSS cho danh sách khu vực 
│ ├── create/ # Tạo khu vực mới 
│ │ ├── page.tsx 
│ │ └── create-area.module.scss 
│ └── [id]/ # Chi tiết & chỉnh sửa khu vực 
│ ├── page.tsx # Trang chi tiết khu vực 
│ └── edit/ # Trang chỉnh sửa khu vực 
│ └── page.tsx 
└── README.md # Tài liệu mô tả (file này)


## Components

Các component sử dụng trong module:

components/ 
├── rooms/ 
│ ├── RoomCard.tsx # Hiển thị thông tin phòng dạng card 
│ ├── RoomCard.module.scss 
│ ├── RoomForm.tsx # Form tạo/chỉnh sửa phòng 
│ └── RoomForm.module.scss 
├── areas/ 
│ ├── AreaCard.tsx # Hiển thị thông tin khu vực dạng card 
│ ├── AreaCard.module.scss 
│ ├── AreaForm.tsx # Form tạo/chỉnh sửa khu vực 
│ └── AreaForm.module.scss 
└── MainSidebar.tsx # Sidebar chính (đã cập nhật để bao gồm mục phòng & khu vực)


## API Endpoints

### Quản lý phòng
- `GET /api/rooms` - Lấy danh sách phòng (hỗ trợ các tham số lọc: category, floor, status)
- `POST /api/rooms` - Tạo phòng mới
- `GET /api/rooms/[id]` - Lấy thông tin chi tiết phòng
- `PUT /api/rooms/[id]` - Cập nhật thông tin phòng
- `DELETE /api/rooms/[id]` - Xóa phòng

### Quản lý khu vực
- `GET /api/areas` - Lấy danh sách khu vực (hỗ trợ các tham số lọc: area_type, floor)
- `POST /api/areas` - Tạo khu vực mới
- `GET /api/areas/[id]` - Lấy thông tin chi tiết khu vực
- `PUT /api/areas/[id]` - Cập nhật thông tin khu vực
- `DELETE /api/areas/[id]` - Xóa khu vực

## Các tính năng chính

### Quản lý phòng
1. **Xem danh sách phòng**
   - Hiển thị theo dạng lưới hoặc bảng
   - Lọc theo loại phòng, tầng, trạng thái
   - Tìm kiếm phòng

2. **Tạo phòng mới**
   - Form đầy đủ thông tin: số phòng, loại phòng, phân loại, tầng, mô tả
   - Hiển thị thông báo thành công và điều hướng sau khi tạo

3. **Xem chi tiết phòng**
   - Hiển thị đầy đủ thông tin phòng
   - Hiển thị thông tin khách đang lưu trú (nếu có)

4. **Chỉnh sửa thông tin phòng**
   - Form có sẵn thông tin hiện tại
   - Cập nhật trạng thái phòng (trống, có khách, bảo trì, đang dọn)

### Quản lý khu vực
1. **Xem danh sách khu vực**
   - Hiển thị theo dạng lưới hoặc bảng
   - Lọc theo loại khu vực, tầng
   - Tìm kiếm khu vực

2. **Tạo khu vực mới**
   - Form với các thông tin: tên, loại khu vực, tầng, vị trí, giờ hoạt động, số nhân viên
   - Hiển thị thông báo thành công và điều hướng sau khi tạo

3. **Xem chi tiết khu vực**
   - Hiển thị đầy đủ thông tin khu vực
   - Hiển thị thông tin mã QR liên kết (nếu có)

4. **Chỉnh sửa thông tin khu vực**
   - Form có sẵn thông tin hiện tại
   - Cập nhật thông tin như giờ hoạt động, số nhân viên, trạng thái

## Liên kết với các module khác

- **Quản lý khách hàng**: Hiển thị thông tin khách đang lưu trú trong một phòng
- **Quản lý mã QR**: Liên kết mã QR với phòng và khu vực
- **Dashboard thống kê**: Hiển thị dữ liệu về tình trạng phòng và lượng sử dụng khu vực

## Quy trình làm việc

1. **Thiết lập ban đầu**
   - Tạo các phòng và khu vực phục vụ trong khách sạn/resort
   - Liên kết mã QR với các phòng và khu vực

2. **Khi khách check-in**
   - Cập nhật trạng thái phòng từ "trống" sang "có khách"
   - Liên kết thông tin khách với phòng

3. **Trong quá trình vận hành**
   - Theo dõi trạng thái phòng
   - Quản lý nhân viên phục vụ tại các khu vực

4. **Khi khách check-out**
   - Cập nhật trạng thái phòng từ "có khách" sang "đang dọn"
   - Sau khi phòng được dọn dẹp, cập nhật trạng thái sang "trống" để sẵn sàng đón khách mới