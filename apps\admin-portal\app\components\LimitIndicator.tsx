import React from 'react';
import styles from './LimitIndicator.module.scss';

interface LimitIndicatorProps {
  current: number;
  maximum: number | null;
  isEnforced: boolean;
  label: string;
  showDetails?: boolean;
}

const LimitIndicator: React.FC<LimitIndicatorProps> = ({ 
  current, 
  maximum, 
  isEnforced,
  label,
  showDetails = false
}) => {
  // Tính toán phần trăm sử dụng
  const percentage = maximum ? Math.min(Math.round((current / maximum) * 100), 100) : 0;
  
  // Xác định màu sắc dựa trên phần trăm sử dụng
  let colorClass = styles.good;
  if (percentage >= 90) {
    colorClass = styles.danger;
  } else if (percentage >= 70) {
    colorClass = styles.warning;
  }

  return (
    <div className={styles.limitContainer}>
      <div className={styles.limitInfo}>
        <span className={styles.limitLabel}>{label} đã sử dụng:</span>
        <span className={styles.limitValues}>
          <strong>{current}</strong>
          {maximum !== null && ` / ${maximum}`}
          {!isEnforced && <span className={styles.unlimited}> (Không giới hạn)</span>}
        </span>
      </div>
      
      {maximum !== null && (
        <div className={styles.progressContainer}>
          <div 
            className={`${styles.progressBar} ${colorClass}`}
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
      )}
      
      {showDetails && maximum !== null && (
  <div className={styles.detailInfo}>
    {percentage >= 100 ? (
      <div className={styles.alertMessage}>
        <span className={styles.alertContent}>
          <strong>Đã đạt giới hạn!</strong> Không thể tạo thêm mã QR. Hãy xóa mã QR hiện có hoặc liên hệ quản trị viên để nâng cấp giới hạn.
        </span>
      </div>
    ) : (
      <span className={styles.remaining}>
        Còn lại: <strong>{maximum - current}</strong> mã QR có thể tạo
      </span>
    )}
  </div>
)}
    </div>
  );
};

export default LimitIndicator;