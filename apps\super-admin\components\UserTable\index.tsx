import React, { useCallback } from 'react';
import { Table, Button } from '@loaloa/ui';
import Link from 'next/link';
import { TableSkeleton } from '@/components/SkeletonLoader';
import { User } from '@/services/UserService';
import styles from './styles.module.scss';

interface UserTableProps {
  users: User[];
  isLoading: boolean;
  page: number;
  pageCount: number;
  onPageChange: (page: number) => void;
  onUserStatusChange: (userId: string, currentStatus: boolean) => void;
}

const UserTable: React.FC<UserTableProps> = ({
  users,
  isLoading,
  page,
  pageCount,
  onPageChange,
  onUserStatusChange
}) => {
  // Sử dụng useCallback để đảm bảo các handler không bị tạo lại khi component re-render
  const handleViewUser = useCallback((userId: string) => {
    window.location.href = `/users/${userId}`;
  }, []);
  
  const handleStatusChange = useCallback((userId: string, status: boolean) => {
    onUserStatusChange(userId, status);
  }, [onUserStatusChange]);

  const columns = [
    {
      header: 'User',
      accessor: (user: User) => (
        <div>
          <div><Link href={`/users/${user.id}`} className={styles.tenantLink}>
            {user.full_name}
          </Link></div>
          <div className={styles.userEmail}>{user.email}</div>
        </div>
      ),
      width: '22%',
    },
    {
      header: 'Role',
      accessor: (user: User) => (
        <span className={`${styles.roleBadge} ${styles[user.role]}`}>
          {user.role.replace('_', ' ')}
        </span>
      ),
      width: '12%',
    },
    {
      header: 'Tenants',
accessor: (user: User) => (
  <div className={styles.tenantsCell}>
    {user.tenant_users && user.tenant_users.length > 0 ? (
      user.tenant_users.map((tu, i) => (
        <div key={i}>
          <Link href={`/tenants/${tu.tenant_id}`} className={styles.tenantLink}>
            {tu.tenant?.name || 'Unknown Tenant'}
          </Link>
          <span className={styles.tenantBadge}>{tu.role}</span>
        </div>
      ))
    ) : (
      <span className={styles.noTenants}>No tenants</span>
    )}
  </div>
),
      width: '25%',
    },
    {
      header: 'Status',
      accessor: (user: User) => {
        const statusClass = user.is_active ? 'status-active' : 'status-inactive';
        return <span className={`status-badge ${statusClass}`}>{user.is_active ? 'Active' : 'Inactive'}</span>;
      },
      width: '10%',
    },
    {
      header: 'Created',
      accessor: (user: User) => new Date(user.created_at).toLocaleDateString(),
      width: '10%',
    },
    {
      header: 'Last Login',
      accessor: (user: User) => user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never',
      width: '10%',
    },
    {
      header: 'Actions',
      accessor: (user: User) => (
        <div className={styles.actions}>
          <Button
            size="small"
            variant="outline"
            label="View"
            onClick={() => handleViewUser(user.id)}
          />
          {!user.is_active ? (
            <Button 
              size="small" 
              variant="success" 
              label="Activate" 
              onClick={() => handleStatusChange(user.id, false)}
            />
          ) : (
            <Button 
              size="small" 
              variant="danger" 
              label="Deactivate" 
              onClick={() => handleStatusChange(user.id, true)}
            />
          )}
        </div>
      ),
      width: '14%',
    },
  ];

  if (isLoading) {
    return <TableSkeleton rowCount={5} columnCount={7} />;
  }

  return (
    <Table
      columns={columns}
      data={users}
      pagination={{
        currentPage: page,
        totalPages: pageCount,
        onPageChange: onPageChange
      }}
    />
  );
};

// Sử dụng React.memo để tránh re-render không cần thiết
export default React.memo(UserTable);
